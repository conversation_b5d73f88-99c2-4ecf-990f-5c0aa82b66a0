#include "testlog_ui.h"
#include "ui_testlog_ui.h"
#include<QDateTime>

Testlog_ui::Testlog_ui(QWidget *parent) :
    QWidget(parent),
    ui(new Ui::Testlog_ui)
{
    ui->setupUi(this);


    ui->tableWidget_2->setColumnCount(2);
    QTableWidgetItem *pitem = new QTableWidgetItem();
    pitem->setText("测试项");
    ui->tableWidget_2->setHorizontalHeaderItem(0, pitem);

    pitem = new QTableWidgetItem();
    pitem->setText("测试结果");
    ui->tableWidget_2->setHorizontalHeaderItem(1, pitem);

    ui->tabWidget_2->setCurrentIndex(0);


}

Testlog_ui::~Testlog_ui()
{
    delete ui;
}
void Testlog_ui::onslotsRevtest(QString s)
{
    ui->textBrowser_2->append(s);
}
void Testlog_ui::onslotsRevShell(QString s)
{
    ui->textBrowser_3->append(s);
}
void Testlog_ui::onslotsTestItem(QString testItem,QString Res)
{

    ui->tableWidget_2->setRowCount(Row+1);
    ui->tableWidget_2->setItem(Row, 0, new QTableWidgetItem(testItem));
    QTableWidgetItem *pItem = new QTableWidgetItem();
    pItem->setText(Res);
    if(Res == "不合格")
    {
        pItem->setForeground(QBrush(Qt::red));
    }
    ui->tableWidget_2->setItem(Row++, 1, pItem);
}
QVector<QStringList> Testlog_ui::GetTableData()
{
    int RowLength = ui->tableWidget_2->rowCount();
    QVector<QStringList> ResSave;
    for(int row = 0;row < RowLength; row++)
    {
        QStringList RowResList ;
        RowResList << ui->tableWidget_2->item(row,1)->text() << ui->tableWidget_2->item(row,2)->text();
        ResSave.append(RowResList);

    }
    return ResSave;
}


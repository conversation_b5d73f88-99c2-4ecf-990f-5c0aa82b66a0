#ifndef CSERIALWIDGET_H
#define CSERIALWIDGET_H

#include <QWidget>
#include<QSerialPort>
#include"csetconfigure.h"
#include"cscancode.h"

#include"ctestlog.h"
#include"cportset.h"
#include"comequipment.h"
#include"mythread.h"
#include"testlog_ui.h"
#include"database.h"
#include"csresults.h"



namespace Ui {
class cserialWidget;
}

class cserialWidget : public QWidget
{
    Q_OBJECT

public:
    explicit cserialWidget(QWidget *parent = nullptr);
    ~cserialWidget();

    void SerialConect();

    void CreateThead();

signals:
    void signNUM(int num);
    void signstart();
    void signValues(float Ia,float Ib,float Ic,float Va,float Vb,float Vc);


private slots:
    void on_pushButton_6_clicked();
    void on_pushButton_3_clicked();
    void on_pushButton_5_clicked();
    void on_pushButton_clicked();
    void on_checkBox_stateChanged(int arg1);
    void on_pcBtn_clicked();
    void on_tableWidget_itemDoubleClicked(QTableWidgetItem *item);
    void on_EndTestBtn_clicked();

    void on_checkBox_2_stateChanged(int arg1);

    void on_checkBox_3_stateChanged(int arg1);

public slots:

    void ontimer();
    void onslotsjbFinshed(int m);
    void onslotsallFinshed(QString s);
    void onslotsTableRes(const TestResult &result);

private:
    Ui::cserialWidget *ui;
    cSetConfigure *m_set;
    cScanCode *m_scancode;
    Testlog_ui *m_log ;
    Database *m_database;
    csResults *m_result;

    QStringList m_TestResList;

    QTimer *m_timer;
    float IA;
    float IB;
    float IC;
    float VA;
    float VB;
    float VC;

    COMEquipment::EquipmentNz2230 *m_pequipmentsjj ;
    COMEquipment::EquipmentWc *m_pequimentwc;
    void comeEquipmentInit();
    void comeNCheckInit();

    QList<QThread*> threadList;
    std::map<int, Testlog_ui*> m_mapTestUI;
    std::map<int, cTestLog*> m_testmap;
    QMap<int,int > m_jbFinished;
    int m_CheckedSum;
    QStringList m_allfinishedSum;
    void CreatExcel();
    QMap<int,QString> m_IsFinshed;
    QList<TestResult> m_allTestResults;
};

#endif // CSERIALWIDGET_H

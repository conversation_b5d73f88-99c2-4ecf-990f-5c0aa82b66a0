QT       += core gui serialport sql mqtt axcontainer network xml

greaterThan(QT_MAJOR_VERSION, 4): QT += widgets

CONFIG += c++11
CODECFORSRC = UTF-8

LIBS += -lWs2_32

# You can make your code fail to compile if it uses deprecated APIs.
# In order to do so, uncomment the following line.
#DEFINES += QT_DISABLE_DEPRECATED_BEFORE=0x060000    # disables all the APIs deprecated before Qt 6.0.0

SOURCES += \
    caboutdialog.cpp \
    caddfa.cpp \
    caqmsdialog.cpp \
    ccheckwindow.cpp \
    cclasseswidget.cpp \
    ccomportdialog.cpp \
    cdbthread.cpp \
    cfinishaccorddialog.cpp \
    cfinishdialog.cpp \
    cgntparamdialog.cpp \
    cgntreportdialog.cpp \
    cgntscuinfodialog.cpp \
    cgnttest.cpp \
    cgnttestprocess.cpp \
    cgnttestwidget.cpp \
    cgorgewidget.cpp \
    cgwtxpzdialog.cpp \
    chandle698.cpp \
    chomedialog.cpp \
    cmmdialog.cpp \
    comequipment.cpp \
    copenclosecapdialog.cpp \
    cpcadddialog.cpp \
    cpcdialog.cpp \
    cresultquery.cpp \
    cscanverifydialog.cpp \
    cshistoryinfo.cpp \
    csresults.cpp \
    cswdialog.cpp \
    ctestbatchinfo.cpp \
    ctsddialog.cpp \
    cverdictquerywg.cpp \
    cytwtxpzdialog.cpp \
    czztxcsdialog.cpp \
    cportset.cpp \
    cscancode.cpp \
    cserialwidget.cpp \
    csetconfigure.cpp \
    ctestlog.cpp \
    database.cpp \
    main.cpp \
    cfunctionwindow.cpp \
    msk_global.cpp \
    tcpserver.cpp \
    mythread.cpp \
    testlog_ui.cpp

HEADERS += \
    caboutdialog.h \
    ccheckwindow.h \
    caddfa.h \
    caqmsdialog.h \
    cclasseswidget.h \
    ccomportdialog.h \
    cdbthread.h \
    cfinishaccorddialog.h \
    cfinishdialog.h \
    cfunctionwindow.h \
    cgntparamdialog.h \
    cgntreportdialog.h \
    cgntscuinfodialog.h \
    cgnttest.h \
    cgnttestprocess.h \
    cgnttestwidget.h \
    copenclosecapdialog.h \
    cportset.h \
    cscancode.h \
    cserialwidget.h \
    csetconfigure.h \
    cshistoryinfo.h \
    csresults.h \
    ctestbatchinfo.h \
    ctestlog.h \
    cgorgewidget.h \
    cgwtxpzdialog.h \
    chandle698.h \
    chomedialog.h \
    cmmdialog.h \
    comequipment.h \
    cpcadddialog.h \
    cpcdialog.h \
    cresultquery.h \
    cscanverifydialog.h \
    cswdialog.h \
    ctsddialog.h \
    cverdictquerywg.h \
    cytwtxpzdialog.h \
    czztxcsdialog.h \
    database.h \
    msk_global.h \
    tcpserver.h \
    mythread.h \
    testlog_ui.h

FORMS += \
    caboutdialog.ui \
    ccheckwindow.ui \
    caddfa.ui \
    caqmsdialog.ui \
    cclasseswidget.ui \
    ccomportdialog.ui \
    cfinishaccorddialog.ui \
    cfinishdialog.ui \
    cfunctionwindow.ui \
    cgntparamdialog.ui \
    cgntreportdialog.ui \
    cgntscuinfodialog.ui \
    cgnttestwidget.ui \
    cgorgewidget.ui \
    cgwtxpzdialog.ui \
    chomedialog.ui \
    cmmdialog.ui \
    copenclosecapdialog.ui \
    cpcadddialog.ui \
    cpcdialog.ui \
    cscanverifydialog.ui \
    cshistoryinfo.ui \
    csresults.ui \
    cswdialog.ui \
    ctestbatchinfo.ui \
    ctsddialog.ui \
    cverdictquerywg.ui \
    cytwtxpzdialog.ui \
    czztxcsdialog.ui \
        cportset.ui \
    cscancode.ui \
    cserialwidget.ui \
    csetconfigure.ui \
    testlog_ui.ui



INCLUDEPATH += .\
                ./ssh \

# Default rules for deployment.
TEMPLATE 	= app
TARGET          = mskscuoutautocheck
DESTDIR		= ../bin
OBJECTS_DIR     = ../obj
MOC_DIR         = ../moc

RESOURCES += \
    backdrop.qrc




win32:CONFIG(release, debug|release): LIBS += -L$$PWD/../lib/ -lQSsh
else:win32:CONFIG(debug, debug|release): LIBS += -L$$PWD/../lib/ -lQSshd

INCLUDEPATH += $$PWD/../
DEPENDPATH += $$PWD/../


RC_ICONS = mskscuoutautocheck.ico

﻿#ifndef MSK_GLOBAL_H
#define MSK_GLOBAL_H
#include <QSettings>
#include <QTextCodec>
#include <QString>
#include <QEvent>
#include <map>
#include <vector>
#include <QApplication>
#include <QMutex>
#include <QDateTime>
extern QMutex g_mtx;
extern QMutex g_yxmtx;
extern std::map<QString, int> g_mapLyName;
extern std::map<int, int> g_mapMcNum;       // 脉冲个数
extern QString strScuOutAutoCheckIni;
extern QString strScuRqAppIni;
extern QString g_strScuoutcheckdb;
extern QString CheckItemIni;
extern QString installIni;
extern bool g_bygMc[15];
extern bool g_bwgMc[15];
extern bool g_bmMc[15];
extern bool g_bTestSign;
extern bool g_bHbdy;
extern bool g_bSW;
const int g_njcjlNum = 125;
extern bool g_bJcjl[g_njcjlNum];
extern bool g_bYx[41];
extern bool g_bLyQuery[10];
extern bool g_bYxFinish[15];
extern bool g_bRtcYx[5];
extern bool g_bSingleExportFinish;
extern bool g_bCertExport;
extern bool g_btcpserver;
extern bool g_bopenclosecapFinish;
extern bool g_bqueryLj;
extern bool g_bScuModifyip;
extern bool g_bstartExport;
extern bool g_bstartImport;
extern bool g_blxdldy[2];
extern QWidget *g_pFuncWindow;
extern QMutex  g_mtxCertExport;


extern std::vector<QString> g_vtChkItem;

#define MSB(x)	(((x) >> 8) & 0xff)	  /* most signif byte of 2-byte integer */
#define LSB(x)	((x) & 0xff)		  /* least signif byte of 2-byte integer*/
#define MAKE_WORD(hbyte,lbyte)		(((quint8)(lbyte)) | (((quint8)(hbyte))<<8))
#define SHORTSWAP(x) ((LSB(x) << 8) |(MSB(x)))

QString queryCfg(QString strIniPath, QString strGroup, QString strKey);

void setCfg(QString strIniPath, QString strGroup, QString strKey, QVariant ss);

void delKeyCfg(QString strIniPath, QString strGroup, QString strKey);

void delALLKeyCfg(QString, QString);

QByteArray convertQStringToAsciiCodes(const QString & s);

struct  SMonitor
{
    QString sU1;
    QString sU2;
    QString sU3;
    QString sI1;
    QString sI2;
    QString sI3;
    QString sP1;
    QString sP2;
    QString sP3;
    QString sQ1;
    QString sQ2;
    QString sQ3;
    QString sX1;
    QString sX2;
    QString sX3;
};

extern SMonitor g_SMonitor;
extern QMutex g_mtxMonitor;

struct sGorge
{
    QString strName;
    QString strIp;
    QString strPort;
};

struct sClasses
{
    QString strXh;
    QString strJclb;
};



struct FRAME_LINK_OBJ_ORIENTED
{
    // 帧头
    quint8		ucBegin;					// 起始字符68H
    // 长度域L
    quint8		ucLengthLow;
    quint8		ucLengthHigh;
    // 控制域C
    unsigned	ucFunctionCode		: 3;	// 功能码
    unsigned    ucScrambleCode      : 1;    // 扰码标志
    unsigned	ucNull				: 1;	// 保留
    unsigned	ucSplitSign			: 1;	// 分帧标志位
    unsigned	ucPRM				: 1;	// 启动标志位
    unsigned	ucDIR				: 1;	// 传输方向位
    // 地址域A
    // 服务器地址SA
    unsigned	ucServerAddrLen		: 4;	// 服务器地址长度N
    unsigned	ucLogicAddr			: 2;	// 逻辑地址
    unsigned	ucServerAddrType	: 2;	// 服务器地址类别
    /******* 服务器地址内容 *******/
    // 客户机地址CA
    quint8		ucClientAddr;
    // 帧头校验HCS
    quint8		ucHCRCLow;
    quint8		ucHCRCHigh;
/******* APDU 应用层 *******/
    // 帧校验FCS
    quint8		ucFCRCLow;
    quint8		ucFCRCHigh;
    // 结束字符
    quint8		ucEnd;						// 结束字符16H
} ;

struct OAD
{
    quint16	attr_OI;  // 对象标识
    quint8	attr_ID;    // 属性标识及特征
    quint8	attr_index;  // 索引

    OAD()
    {
        init();
    }
    void init()
    {
        attr_OI = 0;
        attr_ID = 0;
        attr_index = 0;
    }
} ;



struct SComParam
{
    bool bAutoConnect;      // 自动连接
    QString strComName;     // 串口名称
    QString strBtl;         // 波特率
    QString strJyw;         // 校验位
    QString strSjw;         // 数据位
    QString strTzw;         // 停止位
    QString strLkz;         // 流控制
};
struct SSCUParam
{
    QString strIp;
    QString strLjdz;           // 逻辑地址
    QString strZdLx;            // 类型
    QString strCs;              // 厂商
    QString strXh;              // 型号
    QString strID;              // ID;
    QString strEsn;             // Esn
    QString strYjBb;            // 硬件版本
    QString strScrq;            // 生产日期
    int nBw;                    // 表位
    QString strFA;              // 方案
    QString strPC;              // 批次
    qint32 nFAID;               // 方案ID
    qint32 nPCID;               // 批次ID
};

// scu系统参数
struct SSCUXTParam
{
    QString strDB1;
    QString strDB2;
    QString strZKB;
    QString strYJB;
    QString strNHB;
    QString strBHB;
    QString strRQB;
    QString strJCB;
    QString strGJXT;
    QString strCZXT;
    QString strCPU;
    QString strMem;
};

struct SRqApp
{
    std::map<QString, bool> mapScuRq;           // 已经配置的容器
    std::map<QString, bool> mapScuApp;          // 已经配置的App
    std::map<QString, QString> mapScuRqHash;           // 已经配置的容器Hash
    std::map<QString, QString> mapScuAppHash;          // 已经配置的App Hash
    std::map<QString, bool> mapRqwApp;          // 容器外App;
    std::map<QString, QString>mapScuRqApp;      // APP对应的容器
};

struct SGwtxpz
{
    bool bvalid;
    int ngzms;
    int nzxfs;
    int nljfs;
    int nljyyfs;
    std::vector<int>vtztdklb;
    QString strapn;
    QString stryhm;
    QString strmm;
    QString strdlfwq;
    int ndldk;
    int ncssjcs;
    int nxtzq;
};

struct SYtwtxpz
{
    bool bvalid;
    int ngzms;
    int nljfs;
    int nljyyfs;
    int ncssjcs;
    int nxtzq;
    QString strdlfwq;
    int ndldk;
    std::vector<int>vtztdklb;
};

struct SAqjmpz
{
    bool bvalid;
    int njmms;
};

struct SZztxdz
{
    bool bvalid;
    bool bzy;
    bool by;
    QString strzyIp;
    int nzydk;
    QString strbyIp;
    int nbydk;
};

struct SOther
{
    QString strFirv;
    QString strHarv;
    QString strMqttIotIp;
    QString strBdtxmk;
    QString strLicense;     // 导入License文件路径
    QString strZdzsdclj;        // 终端证书导出路径
    QString strLyqdHash;        // 蓝牙驱动文件hash
    int nSCUqd;
    float fdywc;
    float fdlwc;
    float fglyswc;
    float fygglwc;
    float fwgglwc;
    bool bsm4g;
    float frtcwc;

};

struct STsdpzcs
{
    bool bvalid;
    int ncjbz;
    int ntdsjcdjg;
    int ntdsjcdxz;
    std::vector<QString>vtdnb;
    int ntdsjzxyxjg;
    int ntdsjzdyxjg;
    int ntdsjqzsjpzxz;
    int ntdsjsjqdpcxz;
    int ntdfsdzxz;
    int ntdhfdyxz;
};

struct SGntParam
{
    quint64 noid;
    int nJyCs;              // 校验次数
    int nBw;                  // 表位
    QString strTypeCCom;             // type-C对应的串口号
    QString strRs485;
    SSCUParam scuParam;                     // SCU扫码信息
    SSCUXTParam scuXTParam;                     // scu系统信息
    std::vector<QString> vtItem;            // 检查项
    SRqApp   rqApp;  // scu容器
    SGwtxpz  gwtxpz;        // 公网1通信配置
    SYtwtxpz ytwtxpz;       // 以太网1通信配置
    SZztxdz  gwtxdz;        // 公网通信地址
    SZztxdz  ytwtxdz;       // 以太网通信地址
    STsdpzcs tsdpzcs;       // 停上电配置参数
    SAqjmpz  aqjmpz;        // 安全加密配置
    SOther   other;         // 其他参数配置
};


struct SComRead
{
    int nType;  // 1 是ip 2 是逻辑地址 3 是二维码信息 4 表位
    QString strIp;
    QString strLjdz;           // 逻辑地址
    QString strZdLx;            // 类型
    QString strCs;              // 厂商
    QString strXh;              // 型号
    QString strID;              // ID;
    QString strEsn;             // Esn
    QString strYjBb;            // 硬件版本
    QString strScrq;            // 生产日期
    QString strBw;          // 表位

    SComRead()
    {
        init();
    }
    void init()
    {
        nType = 0;
        strIp.clear();
        strLjdz.clear();
        strZdLx.clear();
        strCs.clear();
        strXh.clear();
        strID.clear();
        strEsn.clear();
        strYjBb.clear();
        strScrq.clear();
        strBw.clear();
    }
};

struct SPC
{
    QString strID;          // ID
    QString strName;        // 名称
};

struct SGntJyXlGc
{
    quint64 oid;
    int nBw;
    QString strCsDl;    // 测试大类
    QString strCsXl;    // 测试小类
    QString strCsGc;     // 测试过程
};


struct SGntJyXlJg
{
    quint64 oid;
    int nBw;
    QString strCsDl;        // 测试大类
    QString strCsXl;    // 测试小类
    bool bCsXlJg;     // 测试小类结果
};

struct SGntJyXlParam
{
    QString strCsXl;
    bool bCsXlJg;
    std::vector<QString> vtCsXlGc;
};

struct SGntJyParam
{
    QString strCsDl;        // 测试大类
    bool  bCsJg;            // 测试结果
    std::vector<SGntJyXlParam> gntCsParam;  // 测试详情
    QString strGc;
};

struct SGCQuery
{
    QString strDL;
    QString strXL;
    QString strGc;
};

struct SXLQuery
{
    quint64 noid;
    QString strDL;
    QString strXL;
    QString strRes;
};

struct SDLQuery
{
    QString strRq;
    QString strFA;
    QString strPC;
    QString strEsn;
    QString strDevID;
    QString strLjdz;
    QString CHECKRESULT;
    QString USRPASSWD;
    QString strcleardata;               // 清空多余文件
    QString SYSTEMPARAM;
    QString SCUPARAM;
    QString IOTPARAM;
    QString PARAM698;
    QString SCUTIMING;
    QString GORGE;
    QString YXDPI;
    QString TGWGMMC;
    QString MEASURE;
    QString BLUETOOTH;
    QString HPLC4G;
    QString DATACLEAR;
    QString BASICINFOR;
    QString RESERVEPOWER;
    QString PORTS;
    QString SWTEST;
    QString strSCURQ;
    QString strApp;
    QString strHLXJ;
    QString strCCZT;
    quint64 nOID;
    QString strIp;
    QString strZdIp;
    QString strExPort;
    QString strImPort;
    QString strLxDyDl;
};

class CMmiMsg : public QEvent
{
public:
    enum MM_Type
    {
        MM_CSGC	= QEvent::User + 10,     // 用户密码校验
        MM_CSXL,
        MM_CSJS,
        MM_LYCX,
        MM_HBDY,
        MM_SW,
        MM_JCJL,
        MM_JLCX,
        MM_XLCX,
        MM_GC,
        MM_TTKZ,
        MM_YGMC,
        MM_WGMC,
        MM_MMC,
        MM_YX,
        MM_RTC,
        MM_CERTRESULT,
        MM_CERTEXPORT,
        MM_NORMAL,
        MM_SSHOFF,
    };

    CMmiMsg(MM_Type type) : QEvent((QEvent::Type) type)	{};
    virtual ~CMmiMsg()	{};
};


class CSSHOFF : public CMmiMsg
{
public:
    CSSHOFF(int nBw, QString strcheckItem) :CMmiMsg(MM_SSHOFF)
    {
        m_nBw = nBw;
        m_strCheckItem = strcheckItem;
    }
    QString getCheckItem()
    {
        return m_strCheckItem;
    }

private:
    int m_nBw;
    QString m_strCheckItem;
};

class CNormalMsg : public CMmiMsg
{
public:
    CNormalMsg(int nlx, int nBw) :CMmiMsg(MM_NORMAL)
    {
        m_nLx = nlx;
        m_nBw = nBw;
    }
    int getLx()
    {
        return m_nLx;
    }
    int getBw()
    {
        return m_nBw;
    }
private:
    int m_nLx;
    int m_nBw;
};

class CErtExportMsg : public CMmiMsg
{
public:
     CErtExportMsg() :CMmiMsg(MM_CERTEXPORT)
     {

     }
};

class CCertMsg: public CMmiMsg
{
public:
    CCertMsg(int nBw, int nLx) :CMmiMsg(MM_CERTRESULT)   // lx:0 成功， 其他值失败
    {
        m_nBw = nBw;
        m_nLx = nLx;
    }
    int getBw(){return m_nBw;}
    int getJl(){return m_nLx;}
private:
    int m_nBw;
    int m_nLx;
};

class CRtcMsg :public CMmiMsg
{
public:
    CRtcMsg(int num) :CMmiMsg(MM_RTC)
    {
        m_nNum = num;
    }
    int getNum()
    {
        return m_nNum;
    }
private:
    int m_nNum;
};

class CCsYxMsg :public CMmiMsg
{
public:
    CCsYxMsg(int num) :CMmiMsg(MM_YX)
    {
        m_nNum = num;
    }
    int getNum(){return m_nNum;}
private:
    int m_nNum;
};

class CCsmmcMsg :public CMmiMsg
{
public:
     CCsmmcMsg(int num) :CMmiMsg(MM_MMC)
     {
        m_nNum = num;
     }
     int getNum()
     {
         return m_nNum;
     }
private:
     int m_nNum;
};

class CCsWgmcMsg :public CMmiMsg
{
public:
     CCsWgmcMsg(int num) :CMmiMsg(MM_WGMC)
     {
        m_nNum = num;
     }
     int getNum()
     {
         return  m_nNum;
     }
private:
     int m_nNum;
};

class CCsYgmcMsg :public CMmiMsg
{
public:
     CCsYgmcMsg(int num) :CMmiMsg(MM_YGMC)
     {
        m_nNum = num;
     }
     int getNum()
     {
         return m_nNum;
     }
private:
     int m_nNum;
};

class CCsTtKzMsg :public CMmiMsg
{
public:
    CCsTtKzMsg(int nttkz) :CMmiMsg(MM_TTKZ)
    {
        m_nTtkz = nttkz;
    }
    int getTtkz(){return m_nTtkz;}
private:
    int  m_nTtkz;
};

class CCsGcMsg :public CMmiMsg
{
public:
    CCsGcMsg(QString gc) :CMmiMsg(MM_GC)
    {
        m_gc = gc;
    }
    QString getGc(){return m_gc;}
private:
    QString  m_gc;
};

class CCsXlCxMsg :public CMmiMsg
{
public:
    CCsXlCxMsg(std::vector<SXLQuery> vtxl) :CMmiMsg(MM_XLCX)
    {
        m_vtxl = vtxl;
    }
    std::vector<SXLQuery> getXl(){return m_vtxl;}
private:
    std::vector<SXLQuery>  m_vtxl;
};

class CCsJlcxMsg : public CMmiMsg
{
public:
    CCsJlcxMsg(std::vector<SDLQuery> dl) :CMmiMsg(MM_JLCX)
    {
        m_dl = dl;
    }
    std::vector<SDLQuery> getVtdl() {return m_dl;}
private:
    std::vector<SDLQuery> m_dl;
};

class CCsJcjlMsg:public CMmiMsg
{
public:
    CCsJcjlMsg(int nLx) :CMmiMsg(MM_JCJL)
    {
        m_nLx = nLx;
    }
    int getLx()
    {
        return m_nLx;
    }

private:
    int m_nLx;
};

class CCsSWMsg:public CMmiMsg
{
public:
    CCsSWMsg() :CMmiMsg(MM_SW)
    {}
};


class CCsHbdyMsg:public CMmiMsg
{
public:
    CCsHbdyMsg() :CMmiMsg(MM_HBDY)
    {}
};

class CCsLyMsg:public CMmiMsg
{
public:
    CCsLyMsg(int num) :CMmiMsg(MM_LYCX)
    {
        m_nNum = num;
    }
    int getNum()
    {
        return m_nNum;
    }
private:
    int m_nNum;
};

class CCsJsMsg :public CMmiMsg
{
public:
    CCsJsMsg(int nBw):CMmiMsg(MM_CSJS)
    {
        m_nBw = nBw;
    }
    virtual ~CCsJsMsg() {};
    int getBw(){return m_nBw;}

protected:
    int m_nBw;
};

class CCsXlJgMsg :public CMmiMsg
{
public:
    CCsXlJgMsg(SGntJyXlJg gntJyXl) : CMmiMsg(MM_CSXL)
    {
        m_gntJyXl = gntJyXl;
    };
    virtual ~CCsXlJgMsg() {};

    SGntJyXlJg getJyXlJg(){return m_gntJyXl;}

protected:
    SGntJyXlJg  m_gntJyXl;;
};

// MmiMsg
class CCsXlGcMsg : public CMmiMsg
{
public:
    CCsXlGcMsg(SGntJyXlGc gntCsXlGc) : CMmiMsg(MM_CSGC)
    {
        m_gntCsXlGc = gntCsXlGc;
    };
    virtual ~CCsXlGcMsg() {};

    SGntJyXlGc getCsXlGc(){return m_gntCsXlGc;}

protected:
    SGntJyXlGc  m_gntCsXlGc;
};


struct SSCURecoreResult
{
    QDateTime dtRq;
    int nfaid;  // 方案id
    int npcid;  // 批次id
    quint64 noid;   // oid;
    QString strEsn;
    QString strDevid;           // 终端ID
    QString strLogicaddr;       // 逻辑地址
    QString strIp;
};

struct SSCUGC
{
    quint64 noid;
    QString strDl;
    QString strXl;
    QString strGc;
};
struct SSCUXL
{
    quint64 noid;
    QString strDl;
    QString strXl;
    bool bresult;
};

struct SSCUUpdateResult
{
    quint64 noid;
    QString strDlName;
    int bresult;
};

#endif // MSK_GLOBAL_H

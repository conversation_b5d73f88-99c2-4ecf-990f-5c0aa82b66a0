﻿#include "cscanverifydialog.h"
#include "ui_cscanverifydialog.h"
#include <QDebug>

CScanVerifyDialog::CScanVerifyDialog(QWidget *parent) :
    QDialog(parent),
    ui(new Ui::CScanVerifyDialog)
{
    ui->setupUi(this);
    tableInit();
    ui->lineEdit->hide();
    m_pc = nullptr;
}

CScanVerifyDialog::~CScanVerifyDialog()
{
    delete ui;
}

void CScanVerifyDialog::init(int nfaid, QString strname)
{
    m_nFAID = nfaid;
    m_strfamc = strname;
    ui->label_3->setText(strname);


    m_vtPc.clear();
    QSettings  settings(strScuOutAutoCheckIni, QSettings::IniFormat);
    settings.setIniCodec(QTextCodec::codecForName("UTF-8"));

    settings.beginGroup("PC_"+QString::number(nfaid));
    QStringList sList = settings.allKeys();
    for (int m = 0; m < sList.size(); ++m)
    {
        SPC pc;
        pc.strID = sList[m];
        pc.strName = settings.value(sList[m]).toString();
        m_vtPc.push_back(pc);
    }

    settings.endGroup();

    pcCb();

    ui->tableWidget->clearContents();
    ui->tableWidget->setRowCount(0);



    return;
    //// tested///////
    SComRead read;

    read.nType = 4;
    read.strBw = "1";
    scanInfo(read);
    read.nType = 1;
    read.strIp = "**************";
    scanInfo(read);
    read.nType = 2;
    read.strLjdz = "900000013897";
    scanInfo(read);
    read.nType = 3;
    read.strCs = "140800";
    read.strEsn = "2102314PSUHFP8000009";
    read.strID = "SCU140800001202409180001";
    read.strScrq = "20240918";
    read.strXh = "iPAC-2110H";
    read.strYjBb = "HV01.01";
    read.strZdLx = "SCU";
    scanInfo(read);

return;
    read.nType = 4;
    read.strBw = "2";
    scanInfo(read);
    read.nType = 1;
    read.strIp = "***************";
    scanInfo(read);
    read.nType = 2;
    read.strLjdz = "900000013388";
    scanInfo(read);
    read.nType = 3;
    read.strCs = "140800";
    read.strEsn = "2102314PSUHFP8000009";
    read.strID = "SCU140800001202409180001";
    read.strScrq = "20240918";
    read.strXh = "iPAC-2110H";
    read.strYjBb = "HV01.01";
    read.strZdLx = "SCU";
    scanInfo(read);


     read.nType = 4;
    read.strBw = "3";
    scanInfo(read);
    read.nType = 1;
    read.strIp = "***************";
    scanInfo(read);
    read.nType = 2;
    read.strLjdz = "900000013397";
    scanInfo(read);
    read.nType = 3;
    read.strCs = "140800";
    read.strEsn = "2102314PSUHFP8000009";
    read.strID = "SCU140800001202409180001";
    read.strScrq = "20240918";
    read.strXh = "iPAC-2110H";
    read.strYjBb = "HV01.01";
    read.strZdLx = "SCU";
    scanInfo(read);



    read.nType = 4;
    read.strBw = "4";
    scanInfo(read);
    read.nType = 1;
    read.strIp = "***************";
    scanInfo(read);
    read.nType = 2;
    read.strLjdz = "900000013396";
    scanInfo(read);
    read.nType = 3;
    read.strCs = "140800";
    read.strEsn = "2102314PSUHFP8000009";
    read.strID = "SCU140800001202409180001";
    read.strScrq = "20240918";
    read.strXh = "iPAC-2110H";
    read.strYjBb = "HV01.01";
    read.strZdLx = "SCU";
    scanInfo(read);

    read.nType = 4;
    read.strBw = "5";
    scanInfo(read);
    read.nType = 1;
    read.strIp = "***************";
    scanInfo(read);
    read.nType = 2;
    read.strLjdz = "900000013395";
    scanInfo(read);
    read.nType = 3;
    read.strCs = "140800";
    read.strEsn = "2102314PSUHFP8000009";
    read.strID = "SCU140800001202409180001";
    read.strScrq = "20240918";
    read.strXh = "iPAC-2110H";
    read.strYjBb = "HV01.01";
    read.strZdLx = "SCU";
    scanInfo(read);

    read.nType = 4;
    read.strBw = "6";
    scanInfo(read);
    read.nType = 1;
    read.strIp = "***************";
    scanInfo(read);
    read.nType = 2;
    read.strLjdz = "900000013394";
    scanInfo(read);
    read.nType = 3;
    read.strCs = "140800";
    read.strEsn = "2102314PSUHFP8000009";
    read.strID = "SCU140800001202409180001";
    read.strScrq = "20240918";
    read.strXh = "iPAC-2110H";
    read.strYjBb = "HV01.01";
    read.strZdLx = "SCU";
    scanInfo(read);



    read.nType = 4;
    read.strBw = "7";
    scanInfo(read);
    read.nType = 1;
    read.strIp = "***************";
    scanInfo(read);
    read.nType = 2;
    read.strLjdz = "900000013392";
    scanInfo(read);
    read.nType = 3;
    read.strCs = "140800";
    read.strEsn = "2102314PSUHFP8000009";
    read.strID = "SCU140800001202409180001";
    read.strScrq = "20240918";
    read.strXh = "iPAC-2110H";
    read.strYjBb = "HV01.01";
    read.strZdLx = "SCU";
    scanInfo(read);

    read.nType = 4;
    read.strBw = "8";
    scanInfo(read);
    read.nType = 1;
    read.strIp = "***************";
    scanInfo(read);
    read.nType = 2;
    read.strLjdz = "900000013391";
    scanInfo(read);
    read.nType = 3;
    read.strCs = "140800";
    read.strEsn = "2102314PSUHFP8000009";
    read.strID = "SCU140800001202409180001";
    read.strScrq = "20240918";
    read.strXh = "iPAC-2110H";
    read.strYjBb = "HV01.01";
    read.strZdLx = "SCU";
    scanInfo(read);

    read.nType = 4;
    read.strBw = "9";
    scanInfo(read);
    read.nType = 1;
    read.strIp = "***************";
    scanInfo(read);
    read.nType = 2;
    read.strLjdz = "900000013390";
    scanInfo(read);
    read.nType = 3;
    read.strCs = "140800";
    read.strEsn = "2102314PSUHFP8000009";
    read.strID = "SCU140800001202409180001";
    read.strScrq = "20240918";
    read.strXh = "iPAC-2110H";
    read.strYjBb = "HV01.01";
    read.strZdLx = "SCU";
    scanInfo(read);

    read.nType = 4;
    read.strBw = "10";
    scanInfo(read);
    read.nType = 1;
    read.strIp = "***************";
    scanInfo(read);
    read.nType = 2;
    read.strLjdz = "900000013389";
    scanInfo(read);
    read.nType = 3;
    read.strCs = "140800";
    read.strEsn = "2102314PSUHFP8000009";
    read.strID = "SCU140800001202409180001";
    read.strScrq = "20240918";
    read.strXh = "iPAC-2110H";
    read.strYjBb = "HV01.01";
    read.strZdLx = "SCU";
    scanInfo(read);

    read.nType = 4;
    read.strBw = "11";
    scanInfo(read);
    read.nType = 1;
    read.strIp = "***************";
    scanInfo(read);
    read.nType = 2;
    read.strLjdz = "900000013393";
    scanInfo(read);
    read.nType = 3;
    read.strCs = "140800";
    read.strEsn = "2102314PSUHFP8000009";
    read.strID = "SCU140800001202409180001";
    read.strScrq = "20240918";
    read.strXh = "iPAC-2110H";
    read.strYjBb = "HV01.01";
    read.strZdLx = "SCU";
    scanInfo(read);


    read.nType = 4;
    read.strBw = "12";
    scanInfo(read);
    read.nType = 1;
    read.strIp = "***************";
    scanInfo(read);
    read.nType = 2;
    read.strLjdz = "900000013387";
    scanInfo(read);
    read.nType = 3;
    read.strCs = "140800";
    read.strEsn = "2102314PSUHFP8000009";
    read.strID = "SCU140800001202409180001";
    read.strScrq = "20240918";
    read.strXh = "iPAC-2110H";
    read.strYjBb = "HV01.01";
    read.strZdLx = "SCU";
    scanInfo(read);

    read.nType = 4;
    read.strBw = "13";
    scanInfo(read);
    read.nType = 1;
    read.strIp = "***************";
    scanInfo(read);
    read.nType = 2;
    read.strLjdz = "900000013386";
    scanInfo(read);
    read.nType = 3;
    read.strCs = "140800";
    read.strEsn = "2102314PSUHFP8000009";
    read.strID = "SCU140800001202409180001";
    read.strScrq = "20240918";
    read.strXh = "iPAC-2110H";
    read.strYjBb = "HV01.01";
    read.strZdLx = "SCU";
    scanInfo(read);

    read.nType = 4;
    read.strBw = "12";
    scanInfo(read);
    read.nType = 1;
    read.strIp = "***************";
    scanInfo(read);
    read.nType = 2;
    read.strLjdz = "900000013385";
    scanInfo(read);
    read.nType = 3;
    read.strCs = "140800";
    read.strEsn = "2102314PSUHFP8000009";
    read.strID = "SCU140800001202409180001";
    read.strScrq = "20240918";
    read.strXh = "iPAC-2110H";
    read.strYjBb = "HV01.01";
    read.strZdLx = "SCU";
    scanInfo(read);

    read.nType = 4;
    read.strBw = "13";
    scanInfo(read);
    read.nType = 1;
    read.strIp = "***************";
    scanInfo(read);
    read.nType = 2;
    read.strLjdz = "900000013384";
    scanInfo(read);
    read.nType = 3;
    read.strCs = "140800";
    read.strEsn = "2102314PSUHFP8000009";
    read.strID = "SCU140800001202409180001";
    read.strScrq = "20240918";
    read.strXh = "iPAC-2110H";
    read.strYjBb = "HV01.01";
    read.strZdLx = "SCU";
    scanInfo(read);

    read.nType = 4;
    read.strBw = "14";
    scanInfo(read);
    read.nType = 1;
    read.strIp = "***************";
    scanInfo(read);
    read.nType = 2;
    read.strLjdz = "900000013383";
    scanInfo(read);
    read.nType = 3;
    read.strCs = "140800";
    read.strEsn = "2102314PSUHFP8000009";
    read.strID = "SCU140800001202409180001";
    read.strScrq = "20240918";
    read.strXh = "iPAC-2110H";
    read.strYjBb = "HV01.01";
    read.strZdLx = "SCU";
    scanInfo(read);

    read.nType = 4;
    read.strBw = "15";
    scanInfo(read);
    read.nType = 1;
    read.strIp = "***************";
    scanInfo(read);
    read.nType = 2;
    read.strLjdz = "900000013382";
    scanInfo(read);
    read.nType = 3;
    read.strCs = "140800";
    read.strEsn = "2102314PSUHFP8000009";
    read.strID = "SCU140800001202409180001";
    read.strScrq = "20240918";
    read.strXh = "iPAC-2110H";
    read.strYjBb = "HV01.01";
    read.strZdLx = "SCU";
    scanInfo(read);

    read.nType = 4;
    read.strBw = "16";
    scanInfo(read);
    read.nType = 1;
    read.strIp = "***************";
    scanInfo(read);
    read.nType = 2;
    read.strLjdz = "900000013381";
    scanInfo(read);
    read.nType = 3;
    read.strCs = "140800";
    read.strEsn = "2102314PSUHFP8000009";
    read.strID = "SCU140800001202409180001";
    read.strScrq = "20240918";
    read.strXh = "iPAC-2110H";
    read.strYjBb = "HV01.01";
    read.strZdLx = "SCU";
    scanInfo(read);

    read.nType = 4;
    read.strBw = "17";
    scanInfo(read);
    read.nType = 1;
    read.strIp = "***************";
    scanInfo(read);
    read.nType = 2;
    read.strLjdz = "900000013380";
    scanInfo(read);
    read.nType = 3;
    read.strCs = "140800";
    read.strEsn = "2102314PSUHFP8000009";
    read.strID = "SCU140800001202409180001";
    read.strScrq = "20240918";
    read.strXh = "iPAC-2110H";
    read.strYjBb = "HV01.01";
    read.strZdLx = "SCU";
    scanInfo(read);

    read.nType = 4;
    read.strBw = "18";
    scanInfo(read);
    read.nType = 1;
    read.strIp = "***************";
    scanInfo(read);
    read.nType = 2;
    read.strLjdz = "900000013379";
    scanInfo(read);
    read.nType = 3;
    read.strCs = "140800";
    read.strEsn = "2102314PSUHFP8000009";
    read.strID = "SCU140800001202409180001";
    read.strScrq = "20240918";
    read.strXh = "iPAC-2110H";
    read.strYjBb = "HV01.01";
    read.strZdLx = "SCU";
    scanInfo(read);

    read.nType = 4;
    read.strBw = "19";
    scanInfo(read);
    read.nType = 1;
    read.strIp = "***************";
    scanInfo(read);
    read.nType = 2;
    read.strLjdz = "900000013378";
    scanInfo(read);
    read.nType = 3;
    read.strCs = "140800";
    read.strEsn = "2102314PSUHFP8000009";
    read.strID = "SCU140800001202409180001";
    read.strScrq = "20240918";
    read.strXh = "iPAC-2110H";
    read.strYjBb = "HV01.01";
    read.strZdLx = "SCU";
    scanInfo(read);

    read.nType = 4;
    read.strBw = "20";
    scanInfo(read);
    read.nType = 1;
    read.strIp = "***************";
    scanInfo(read);
    read.nType = 2;
    read.strLjdz = "900000013377";
    scanInfo(read);
    read.nType = 3;
    read.strCs = "140800";
    read.strEsn = "2102314PSUHFP8000009";
    read.strID = "SCU140800001202409180001";
    read.strScrq = "20240918";
    read.strXh = "iPAC-2110H";
    read.strYjBb = "HV01.01";
    read.strZdLx = "SCU";
    scanInfo(read);


    return;
    read.nType = 4;
    read.strBw = "21";
    scanInfo(read);
    read.nType = 1;
    read.strIp = "***************";
    scanInfo(read);
    read.nType = 2;
    read.strLjdz = "900000013376";
    scanInfo(read);
    read.nType = 3;
    read.strCs = "140800";
    read.strEsn = "2102314PSUHFP8000009";
    read.strID = "SCU140800001202409180001";
    read.strScrq = "20240918";
    read.strXh = "iPAC-2110H";
    read.strYjBb = "HV01.01";
    read.strZdLx = "SCU";
    scanInfo(read);

    read.nType = 4;
    read.strBw = "22";
    scanInfo(read);
    read.nType = 1;
    read.strIp = "***************";
    scanInfo(read);
    read.nType = 2;
    read.strLjdz = "900000013375";
    scanInfo(read);
    read.nType = 3;
    read.strCs = "140800";
    read.strEsn = "2102314PSUHFP8000009";
    read.strID = "SCU140800001202409180001";
    read.strScrq = "20240918";
    read.strXh = "iPAC-2110H";
    read.strYjBb = "HV01.01";
    read.strZdLx = "SCU";
    scanInfo(read);


    read.nType = 4;
    read.strBw = "23";
    scanInfo(read);
    read.nType = 1;
    read.strIp = "***************";
    scanInfo(read);
    read.nType = 2;
    read.strLjdz = "900000013374";
    scanInfo(read);
    read.nType = 3;
    read.strCs = "140800";
    read.strEsn = "2102314PSUHFP8000009";
    read.strID = "SCU140800001202409180001";
    read.strScrq = "20240918";
    read.strXh = "iPAC-2110H";
    read.strYjBb = "HV01.01";
    read.strZdLx = "SCU";
    scanInfo(read);

    read.nType = 4;
    read.strBw = "24";
    scanInfo(read);
    read.nType = 1;
    read.strIp = "***************";
    scanInfo(read);
    read.nType = 2;
    read.strLjdz = "900000013373";
    scanInfo(read);
    read.nType = 3;
    read.strCs = "140800";
    read.strEsn = "2102314PSUHFP8000009";
    read.strID = "SCU140800001202409180001";
    read.strScrq = "20240918";
    read.strXh = "iPAC-2110H";
    read.strYjBb = "HV01.01";
    read.strZdLx = "SCU";
    scanInfo(read);

    read.nType = 4;
    read.strBw = "25";
    scanInfo(read);
    read.nType = 1;
    read.strIp = "***************";
    scanInfo(read);
    read.nType = 2;
    read.strLjdz = "900000013372";
    scanInfo(read);
    read.nType = 3;
    read.strCs = "140800";
    read.strEsn = "2102314PSUHFP8000009";
    read.strID = "SCU140800001202409180001";
    read.strScrq = "20240918";
    read.strXh = "iPAC-2110H";
    read.strYjBb = "HV01.01";
    read.strZdLx = "SCU";
    scanInfo(read);

    read.nType = 4;
    read.strBw = "26";
    scanInfo(read);
    read.nType = 1;
    read.strIp = "***************";
    scanInfo(read);
    read.nType = 2;
    read.strLjdz = "900000013371";
    scanInfo(read);
    read.nType = 3;
    read.strCs = "140800";
    read.strEsn = "2102314PSUHFP8000009";
    read.strID = "SCU140800001202409180001";
    read.strScrq = "20240918";
    read.strXh = "iPAC-2110H";
    read.strYjBb = "HV01.01";
    read.strZdLx = "SCU";
    scanInfo(read);

    read.nType = 4;
    read.strBw = "27";
    scanInfo(read);
    read.nType = 1;
    read.strIp = "***************";
    scanInfo(read);
    read.nType = 2;
    read.strLjdz = "900000013370";
    scanInfo(read);
    read.nType = 3;
    read.strCs = "140800";
    read.strEsn = "2102314PSUHFP8000009";
    read.strID = "SCU140800001202409180001";
    read.strScrq = "20240918";
    read.strXh = "iPAC-2110H";
    read.strYjBb = "HV01.01";
    read.strZdLx = "SCU";
    scanInfo(read);


    read.nType = 4;
    read.strBw = "28";
    scanInfo(read);
    read.nType = 1;
    read.strIp = "***************";
    scanInfo(read);
    read.nType = 2;
    read.strLjdz = "900000013369";
    scanInfo(read);
    read.nType = 3;
    read.strCs = "140800";
    read.strEsn = "2102314PSUHFP8000009";
    read.strID = "SCU140800001202409180001";
    read.strScrq = "20240918";
    read.strXh = "iPAC-2110H";
    read.strYjBb = "HV01.01";
    read.strZdLx = "SCU";
    scanInfo(read);


    read.nType = 4;
    read.strBw = "29";
    scanInfo(read);
    read.nType = 1;
    read.strIp = "***************";
    scanInfo(read);
    read.nType = 2;
    read.strLjdz = "900000013368";
    scanInfo(read);
    read.nType = 3;
    read.strCs = "140800";
    read.strEsn = "2102314PSUHFP8000009";
    read.strID = "SCU140800001202409180001";
    read.strScrq = "20240918";
    read.strXh = "iPAC-2110H";
    read.strYjBb = "HV01.01";
    read.strZdLx = "SCU";
    scanInfo(read);

    read.nType = 4;
    read.strBw = "30";
    scanInfo(read);
    read.nType = 1;
    read.strIp = "***************";
    scanInfo(read);
    read.nType = 2;
    read.strLjdz = "900000013367";
    scanInfo(read);
    read.nType = 3;
    read.strCs = "140800";
    read.strEsn = "2102314PSUHFP8000009";
    read.strID = "SCU140800001202409180001";
    read.strScrq = "20240918";
    read.strXh = "iPAC-2110H";
    read.strYjBb = "HV01.01";
    read.strZdLx = "SCU";
    scanInfo(read);


    read.nType = 4;
    read.strBw = "31";
    scanInfo(read);
    read.nType = 1;
    read.strIp = "***************";
    scanInfo(read);
    read.nType = 2;
    read.strLjdz = "900000013365";
    scanInfo(read);
    read.nType = 3;
    read.strCs = "140800";
    read.strEsn = "2102314PSUHFP8000009";
    read.strID = "SCU140800001202409180001";
    read.strScrq = "20240918";
    read.strXh = "iPAC-2110H";
    read.strYjBb = "HV01.01";
    read.strZdLx = "SCU";
    scanInfo(read);

    read.nType = 4;
    read.strBw = "32";
    scanInfo(read);
    read.nType = 1;
    read.strIp = "**************";
    scanInfo(read);
    read.nType = 2;
    read.strLjdz = "900000013364";
    scanInfo(read);
    read.nType = 3;
    read.strCs = "140800";
    read.strEsn = "2102314PSUHFP8000009";
    read.strID = "SCU140800001202409180001";
    read.strScrq = "20240918";
    read.strXh = "iPAC-2110H";
    read.strYjBb = "HV01.01";
    read.strZdLx = "SCU";
    scanInfo(read);

    read.nType = 4;
    read.strBw = "33";
    scanInfo(read);
    read.nType = 1;
    read.strIp = "***************";
    scanInfo(read);
    read.nType = 2;
    read.strLjdz = "900000013363";
    scanInfo(read);
    read.nType = 3;
    read.strCs = "140800";
    read.strEsn = "2102314PSUHFP8000009";
    read.strID = "SCU140800001202409180001";
    read.strScrq = "20240918";
    read.strXh = "iPAC-2110H";
    read.strYjBb = "HV01.01";
    read.strZdLx = "SCU";
    scanInfo(read);

    return;

    read.nType = 4;
    read.strBw = "34";
    scanInfo(read);
    read.nType = 1;
    read.strIp = "**************";
    scanInfo(read);
    read.nType = 2;
    read.strLjdz = "900000013362";
    scanInfo(read);
    read.nType = 3;
    read.strCs = "140800";
    read.strEsn = "2102314PSUHFP8000009";
    read.strID = "SCU140800001202409180001";
    read.strScrq = "20240918";
    read.strXh = "iPAC-2110H";
    read.strYjBb = "HV01.01";
    read.strZdLx = "SCU";
    scanInfo(read);

    read.nType = 4;
    read.strBw = "35";
    scanInfo(read);
    read.nType = 1;
    read.strIp = "**************";
    scanInfo(read);
    read.nType = 2;
    read.strLjdz = "900000013361";
    scanInfo(read);
    read.nType = 3;
    read.strCs = "140800";
    read.strEsn = "2102314PSUHFP8000009";
    read.strID = "SCU140800001202409180001";
    read.strScrq = "20240918";
    read.strXh = "iPAC-2110H";
    read.strYjBb = "HV01.01";
    read.strZdLx = "SCU";
    scanInfo(read);

    read.nType = 4;
    read.strBw = "36";
    scanInfo(read);
    read.nType = 1;
    read.strIp = "**************";
    scanInfo(read);
    read.nType = 2;
    read.strLjdz = "900000013360";
    scanInfo(read);
    read.nType = 3;
    read.strCs = "140800";
    read.strEsn = "2102314PSUHFP8000009";
    read.strID = "SCU140800001202409180001";
    read.strScrq = "20240918";
    read.strXh = "iPAC-2110H";
    read.strYjBb = "HV01.01";
    read.strZdLx = "SCU";
    scanInfo(read);

    read.nType = 4;
    read.strBw = "37";
    scanInfo(read);
    read.nType = 1;
    read.strIp = "**************";
    scanInfo(read);
    read.nType = 2;
    read.strLjdz = "900000013359";
    scanInfo(read);
    read.nType = 3;
    read.strCs = "140800";
    read.strEsn = "2102314PSUHFP8000009";
    read.strID = "SCU140800001202409180001";
    read.strScrq = "20240918";
    read.strXh = "iPAC-2110H";
    read.strYjBb = "HV01.01";
    read.strZdLx = "SCU";
    scanInfo(read);

    read.nType = 4;
    read.strBw = "38";
    scanInfo(read);
    read.nType = 1;
    read.strIp = "**************";
    scanInfo(read);
    read.nType = 2;
    read.strLjdz = "900000013358";
    scanInfo(read);
    read.nType = 3;
    read.strCs = "140800";
    read.strEsn = "2102314PSUHFP8000009";
    read.strID = "SCU140800001202409180001";
    read.strScrq = "20240918";
    read.strXh = "iPAC-2110H";
    read.strYjBb = "HV01.01";
    read.strZdLx = "SCU";
    scanInfo(read);

    read.nType = 4;
    read.strBw = "39";
    scanInfo(read);
    read.nType = 1;
    read.strIp = "**************";
    scanInfo(read);
    read.nType = 2;
    read.strLjdz = "900000013357";
    scanInfo(read);
    read.nType = 3;
    read.strCs = "140800";
    read.strEsn = "2102314PSUHFP8000009";
    read.strID = "SCU140800001202409180001";
    read.strScrq = "20240918";
    read.strXh = "iPAC-2110H";
    read.strYjBb = "HV01.01";
    read.strZdLx = "SCU";
    scanInfo(read);

    read.nType = 4;
    read.strBw = "40";
    scanInfo(read);
    read.nType = 1;
    read.strIp = "**************";
    scanInfo(read);
    read.nType = 2;
    read.strLjdz = "900000013356";
    scanInfo(read);
    read.nType = 3;
    read.strCs = "140800";
    read.strEsn = "2102314PSUHFP8000009";
    read.strID = "SCU140800001202409180001";
    read.strScrq = "20240918";
    read.strXh = "iPAC-2110H";
    read.strYjBb = "HV01.01";
    read.strZdLx = "SCU";
    scanInfo(read);
    /////////////////


}

void CScanVerifyDialog::scanInfo(SComRead &comRead)
{
    // 判断是否存在相同的表位， 如果存在，则覆盖之前信息
    if(comRead.nType == 4)
    {
        scanInfoBw(comRead);
    }
    else if(comRead.nType == 1)
    {
        scanInfoIp(comRead);
    }
    else if(comRead.nType == 3)
    {
        scanInfoZdcs(comRead);
    }
}



void CScanVerifyDialog::keyReleaseEvent(QKeyEvent *event)
{
    QString s1 =  event->text();
    if(s1.isEmpty())
        return;
    ui->lineEdit->setText(s1);
    if(s1 == "\r")
    {
        ui->lineEdit->setText(m_strSm);

        SComRead sComRead;
        if(m_strSm.startsWith("台区智能融合终端"))
        {
            sComRead.nType = 3;
            QStringList sList = m_strSm.split("，");
            if(sList.size() < 9)
                return;
            for (int m = 0; m < sList.size(); ++m)
            {
                QStringList strList = sList[m].split("：");
                if(strList.size() != 2)
                    continue;
                QString s1 = strList[0];
                QString s2 = strList[1];
                if(s1 == "类型")
                    sComRead.strZdLx = s2;
                else if(s1 == "厂商")
                    sComRead.strCs = s2;
                else if(s1 == "型号")
                    sComRead.strXh = s2;
                else if(s1 == "ID")
                    sComRead.strID = s2;
                else if(s1 == "ESN")
                    sComRead.strEsn = s2;
                else if(s1 == "硬件版本")
                    sComRead.strYjBb = s2;
                else if(s1 == "计量条码")
                    sComRead.strLjdz = s2.mid(9, 12);
                else if(s1 == "生产日期")
                {
                    int nYear = s2.left(4).toInt();

                    int npos = s2.indexOf("月");
                    int nMon = s2.mid(5, npos-5).toInt();
                    int nDay = s2.mid(npos+1, s2.size()-2-npos).toInt();

                    sComRead.strScrq.sprintf("%04d%02d%02d", nYear, nMon, nDay);
                }
            }
        }
        else if(m_strSm.indexOf(".") != -1)
        {
            sComRead.nType = 1;
            sComRead.strIp = m_strSm;
        }
        else if(m_strSm.size() == 2)
        {
            sComRead.nType = 4;
            sComRead.strBw = m_strSm;
        }
        else
        {
            m_strSm.clear();
            event->accept();
            return;
        }

        scanInfo(sComRead);
        m_strSm.clear();
    }
    else
    {
        if(s1.isEmpty())
            return;
        m_strSm += s1;
    }
    event->accept();
}

void CScanVerifyDialog::scanInfoBw(SComRead &comRead)
{
    bool bFind = false;
    for (int m = 0; m < ui->tableWidget->rowCount(); ++m)
    {
        QTableWidgetItem *pitem = ui->tableWidget->item(m, SScanInfo_BWID);
        if(pitem == nullptr)
        {
            continue;
        }
        if(pitem->text().toInt() != comRead.strBw.toInt())
        {
            continue;
        }
        bFind = true;
        m_nCurRow = m;
        break;
    }

    if(bFind)
        return;

    m_nCurRow = ui->tableWidget->rowCount();
    ui->tableWidget->setRowCount(m_nCurRow + EScanParam_Max);

    QTableWidgetItem *pitem = new QTableWidgetItem();
    pitem->setText(comRead.strBw);
    ui->tableWidget->setItem(m_nCurRow, SScanInfo_BWID, pitem);

    ui->tableWidget->setSpan(m_nCurRow, SScanInfo_BWID, EScanParam_Max, 1);

}

void CScanVerifyDialog::scanInfoIp(SComRead &comRead)
{
    QTableWidgetItem *pitem = new QTableWidgetItem();
    pitem->setText(comRead.strIp);
    ui->tableWidget->setItem(m_nCurRow + EScanParam_IP, SScanInfo_CsJg, pitem);

    pitem = new QTableWidgetItem();
    pitem->setText("IP");
    ui->tableWidget->setItem(m_nCurRow + EScanParam_IP, SScanInfo_Cs, pitem);
}


void CScanVerifyDialog::scanInfoZdcs(SComRead &comRead)
{
    QTableWidgetItem *pitem = new QTableWidgetItem();
    pitem->setText("类型");
    ui->tableWidget->setItem(m_nCurRow + EScanParam_ZdLx, SScanInfo_Cs, pitem);

    pitem = new QTableWidgetItem();
    pitem->setText(comRead.strZdLx);
    ui->tableWidget->setItem(m_nCurRow + EScanParam_ZdLx, SScanInfo_CsJg, pitem);

    pitem = new QTableWidgetItem();
    pitem->setText("厂商");
    ui->tableWidget->setItem(m_nCurRow + EScanParam_Cs, SScanInfo_Cs, pitem);

    pitem = new QTableWidgetItem();
    pitem->setText(comRead.strCs);
    ui->tableWidget->setItem(m_nCurRow + EScanParam_Cs, SScanInfo_CsJg, pitem);

    pitem = new QTableWidgetItem();
    pitem->setText("型号");
    ui->tableWidget->setItem(m_nCurRow + EScanParam_Xh, SScanInfo_Cs, pitem);

    pitem = new QTableWidgetItem();
    pitem->setText(comRead.strXh);
    ui->tableWidget->setItem(m_nCurRow + EScanParam_Xh, SScanInfo_CsJg, pitem);

    pitem = new QTableWidgetItem();
    pitem->setText("ID");
    ui->tableWidget->setItem(m_nCurRow + EScanParam_ID, SScanInfo_Cs, pitem);

    pitem = new QTableWidgetItem();
    pitem->setText(comRead.strID);
    ui->tableWidget->setItem(m_nCurRow + EScanParam_ID, SScanInfo_CsJg, pitem);

    pitem = new QTableWidgetItem();
    pitem->setText("ESN");
    ui->tableWidget->setItem(m_nCurRow + EScanParam_Esn, SScanInfo_Cs, pitem);

    pitem = new QTableWidgetItem();
    pitem->setText(comRead.strEsn);
    ui->tableWidget->setItem(m_nCurRow + EScanParam_Esn, SScanInfo_CsJg, pitem);

    pitem = new QTableWidgetItem();
    pitem->setText("硬件版本");
    ui->tableWidget->setItem(m_nCurRow + EScanParam_YjBb, SScanInfo_Cs, pitem);

    pitem = new QTableWidgetItem();
    pitem->setText(comRead.strYjBb);
    ui->tableWidget->setItem(m_nCurRow + EScanParam_YjBb, SScanInfo_CsJg, pitem);

    pitem = new QTableWidgetItem();
    pitem->setText("生产日期");
    ui->tableWidget->setItem(m_nCurRow + EScanParam_Scrq, SScanInfo_Cs, pitem);

    pitem = new QTableWidgetItem();
    pitem->setText(comRead.strScrq);
    ui->tableWidget->setItem(m_nCurRow + EScanParam_Scrq, SScanInfo_CsJg, pitem);

    pitem = new QTableWidgetItem();
    pitem->setText("逻辑地址");
    ui->tableWidget->setItem(m_nCurRow + EScanParam_Ljdz, SScanInfo_Cs, pitem);

    pitem = new QTableWidgetItem();
    pitem->setText(comRead.strLjdz);
    ui->tableWidget->setItem(m_nCurRow + EScanParam_Ljdz, SScanInfo_CsJg, pitem);
}

void CScanVerifyDialog::pcCb()
{
    ui->comboBox->clear();

    for (int m = 0; m < m_vtPc.size(); ++m)
    {
        ui->comboBox->addItem(m_vtPc[m].strName);
    }
}

void CScanVerifyDialog::tableInit()
{
    ui->tableWidget->setEditTriggers(QTableView::NoEditTriggers);
    ui->tableWidget->setColumnCount(SScanInfo_max);

    QTableWidgetItem *pitem = new QTableWidgetItem();
    pitem->setText("表位");
    ui->tableWidget->setHorizontalHeaderItem(SScanInfo_BWID, pitem);

    pitem = new QTableWidgetItem();
    pitem->setText("参数");
    ui->tableWidget->setHorizontalHeaderItem(SScanInfo_Cs, pitem);

    pitem = new QTableWidgetItem();
    pitem->setText("参数值");
    ui->tableWidget->setHorizontalHeaderItem(SScanInfo_CsJg, pitem);

    ui->tableWidget->setAlternatingRowColors(true);
    ui->tableWidget->setColumnWidth(SScanInfo_CsJg, 500);
}

// 批次管理
void CScanVerifyDialog::on_pcBtn_clicked()
{
    if(m_pc == nullptr)
    {
        m_pc = new CPcDialog(this);
        connect(m_pc, SIGNAL(refreshCfg(std::map<QString, QString>)), this, SLOT(on_refreshCfg(std::map<QString, QString>)));
    }
    m_pc->initPc(m_nFAID, m_vtPc);
    m_pc->show();
}

void CScanVerifyDialog::on_refreshCfg(std::map<QString, QString>mapCfg)
{
    m_vtPc.clear();


    QSettings  settings(strScuOutAutoCheckIni, QSettings::IniFormat);
    settings.setIniCodec(QTextCodec::codecForName("UTF-8"));
    settings.beginGroup("PC_"+QString::number(m_nFAID));
    QStringList allKey = settings.allKeys();
    for (int m = 0; m < allKey.size(); ++m)
    {
        settings.remove(allKey[m]);
    }
    settings.endGroup();

    settings.beginGroup("PC_"+QString::number(m_nFAID));
    auto iter = mapCfg.begin();
    for (;iter != mapCfg.end() ; ++iter)
    {
         settings.setValue(iter->first, iter->second);
    }

    settings.endGroup();



    settings.beginGroup("PC_"+QString::number(m_nFAID));
    QStringList sList = settings.allKeys();
    for (int m = 0; m < sList.size(); ++m)
    {
        SPC pc;
        pc.strID = sList[m];
        pc.strName = settings.value(sList[m]).toString();
        m_vtPc.push_back(pc);
    }

    settings.endGroup();

    pcCb();


}

// 开始检测
void CScanVerifyDialog::on_okBtn_clicked()
{
    if(ui->label_3->text().isEmpty())
    {
        QMessageBox::about(this, "确定", "方案未选择， 请先选择方案");
        return;
    }
    if(ui->comboBox->currentText().isEmpty())
    {
        QMessageBox::about(this, "确定", "请先选择批次");
        return;
    }
    std::map<int, SSCUParam>mapComParam;

    int nPCID = -1;
    QString ss = ui->comboBox->currentText();
    for (int m = 0; m < m_vtPc.size(); ++m)
    {
        if(m_vtPc[m].strName == ss)
        {
            nPCID = m_vtPc[m].strID.toInt();
            break;
        }
    }

    for (int m = 0; m < ui->tableWidget->rowCount(); ++m)
    {
        QTableWidgetItem *pitem = ui->tableWidget->item(m, SScanInfo_CsJg)  ;
        if(pitem == nullptr || pitem->text().isEmpty())
        {
            QString s3 = "第" + QString::number(m+1) + "行数据未扫码或者扫码错误";
            QMessageBox::about(this, "确定", s3);
            return;
        }
    }

    std::map<QString, int>mapIp;            // Ip重复性检测
    for (int m = 0;  m < ui->tableWidget->rowCount();)
    {
        QString s1;
        SSCUParam comRead;
        QTableWidgetItem *pitem = ui->tableWidget->item(m, SScanInfo_BWID);
        if(pitem != nullptr)
        {
            s1 = pitem->text();
            comRead.nBw = s1.toInt();
        }
        pitem = ui->tableWidget->item(m, SScanInfo_CsJg);
        if(pitem != nullptr)
        {
            comRead.strIp = pitem->text();
            auto it = mapIp.find(comRead.strIp);
            if(it == mapIp.end())
            {
                mapIp[comRead.strIp] = comRead.nBw;
            }
            else
            {
                QString strTip = "错误 表位：" + QString::number(comRead.nBw) + "和表位：" + QString::number(it->second) + "存在相同的IP";
                QMessageBox::about(this, "IP检测", strTip);
                return;
            }
        }
        ++m;
        pitem = ui->tableWidget->item(m, SScanInfo_CsJg);
        if(pitem != nullptr)
        {
            comRead.strLjdz = pitem->text();
        }
        ++m;
        pitem = ui->tableWidget->item(m, SScanInfo_CsJg);
        if(pitem != nullptr)
        {
            comRead.strZdLx = pitem->text();
        }
        ++m;
        pitem = ui->tableWidget->item(m, SScanInfo_CsJg);
        if(pitem != nullptr)
        {
            comRead.strCs = pitem->text();
        }
        ++m;
        pitem = ui->tableWidget->item(m, SScanInfo_CsJg);
        if(pitem != nullptr)
        {
            comRead.strXh = pitem->text();
        }
        ++m;
        pitem = ui->tableWidget->item(m, SScanInfo_CsJg);
        if(pitem != nullptr)
        {
            comRead.strID = pitem->text();
        }
        ++m;
        pitem = ui->tableWidget->item(m, SScanInfo_CsJg);
        if(pitem != nullptr)
        {
            comRead.strEsn = pitem->text();
        }
        ++m;
        pitem = ui->tableWidget->item(m, SScanInfo_CsJg);
        if(pitem != nullptr)
        {
            comRead.strYjBb = pitem->text();
        }
        ++m;
        pitem = ui->tableWidget->item(m, SScanInfo_CsJg);
        if(pitem != nullptr)
        {
            comRead.strScrq = pitem->text();
        }
        ++m;
        comRead.nFAID = m_nFAID;
        comRead.strFA = m_strfamc;
        comRead.strPC = ui->comboBox->currentText();
        comRead.nPCID = nPCID;
        mapComParam[s1.toInt()] = comRead;
    }

    emit(scanInfoSig(mapComParam));
    close();
}

#ifndef DATABASE_H
#define DATABASE_H
#include<QSqlDatabase>
#include<QSqlError>
#include<QSqlQuery>


typedef struct
{
    //int id;
    QString esnNum;
    QString esamNum;
    QString CurBatch;
}w2dba;

typedef  struct
{
    QString testItem1;
    QString testItem2;
    QString Res2;
    QString sDate;
}testItem;

typedef  struct
{
    QString esnNum;
    QString esamNUm;
    QString TestData;
    QString TestBatch;
    QString TestScheme;
    QString TestAllRes;
    QString UpdataDbRes;
    QString LicenseFileRes;
    QString EsnFileRes;
    QString EsamFileRes;
    QString ClearFileRes;
    QString InstallContianerRes;
    QString InstallAppRes;
    QString AppRunningRes;
    QString JbRes;
}TestRes;

typedef struct
{
    QString esnNum;
    QString esamNum;
    QString testBatch;
    QString testScheme;
    QString testTime;
    QString testMainItem;
    QString testSubItem;
    QString testResult;
    int stationId;
}TestItemDetail;


class Database
{
public:
    Database();
    // 打开数据库
    bool openDb();

    // 创建数据表
    void createTable();
    void createTable(QString tablename);

    // 创建测试项详情表
    void createTestItemTable();

    // 判断数据表是否存在
    bool isTableExist(QString& tableName);

    // 查询全部数据
    QStringList queryTable(QString s);

    // 插入数据
    void singleInsertData(w2dba &singleData);

    // 插入测试项详情数据
    void insertTestItemData(const TestItemDetail &itemData);

    // 关闭数据库
    void closeDb();

    // 按批次查询测试项详情数据
    QStringList queryTestItemDataByBatch(QString batchName);

    // 获取数据库连接
    QSqlDatabase getDatabase() { return database; }

private:
    QSqlDatabase database;
    //cTestLog *m_log;

    //void onSlotsRevNum(QString curbathch,QString esnNum,QString esamNum);

};

#endif // DATABASE_H

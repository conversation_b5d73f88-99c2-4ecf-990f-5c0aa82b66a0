﻿#ifndef CPCDIALOG_H
#define CPCDIALOG_H

#include <QTableWidget>
#include <QDialog>
#include <msk_global.h>
#include "cpcadddialog.h"
#include <map>

namespace Ui {
class CPcDialog;
}

enum EPC
{
    EPC_ID,
    EPC_NAME,
    EPC_MAX
};

class CPcDialog : public QDialog
{
    Q_OBJECT

public:
    explicit CPcDialog(QWidget *parent = nullptr);
    ~CPcDialog();

    void initPc(quint64 nfaID, std::vector<SPC>&);
private slots:
    void on_addBtn_clicked();

    void on_okBtn_clicked();

    void on_delBtn_clicked();

    void on_tableWidget_itemChged(QTableWidgetItem *);

Q_SIGNALS:
    void refreshCfg(std::map<QString, QString>);

private:
    void tableInit();
private:
    quint64 m_nFAID;        // 方案ID
private:
    Ui::CPcDialog *ui;

};

#endif // CPCDIALOG_H

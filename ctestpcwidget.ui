<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>cTestpcWidget</class>
 <widget class="QWidget" name="cTestpcWidget">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>553</width>
    <height>367</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Form</string>
  </property>
  <layout class="QGridLayout" name="gridLayout">
   <item row="0" column="0" colspan="2">
    <spacer name="horizontalSpacer_3">
     <property name="orientation">
      <enum>Qt::Horizontal</enum>
     </property>
     <property name="sizeHint" stdset="0">
      <size>
       <width>40</width>
       <height>20</height>
      </size>
     </property>
    </spacer>
   </item>
   <item row="1" column="0" colspan="2">
    <spacer name="horizontalSpacer">
     <property name="orientation">
      <enum>Qt::Horizontal</enum>
     </property>
     <property name="sizeHint" stdset="0">
      <size>
       <width>538</width>
       <height>20</height>
      </size>
     </property>
    </spacer>
   </item>
   <item row="2" column="0">
    <widget class="QComboBox" name="comboBox"/>
   </item>
   <item row="2" column="1">
    <spacer name="horizontalSpacer_2">
     <property name="orientation">
      <enum>Qt::Horizontal</enum>
     </property>
     <property name="sizeHint" stdset="0">
      <size>
       <width>472</width>
       <height>20</height>
      </size>
     </property>
    </spacer>
   </item>
   <item row="3" column="0" colspan="2">
    <widget class="QTableView" name="tableView"/>
   </item>
  </layout>
 </widget>
 <resources/>
 <connections/>
</ui>

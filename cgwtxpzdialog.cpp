﻿#include "cgwtxpzdialog.h"
#include "ui_cgwtxpzdialog.h"

CGwtxpzDialog::CGwtxpzDialog(QWidget *parent) :
    QDialog(parent),
    ui(new Ui::CGwtxpzDialog)
{
    ui->setupUi(this);
}

CGwtxpzDialog::~CGwtxpzDialog()
{
    delete ui;
}

void CGwtxpzDialog::setFAID(int nFAID)
{
    m_nFAID = nFAID;
    m_strName = "gwtxpz_" + QString::number(nFAID);
    queryCfg();
}

void CGwtxpzDialog::queryCfg()
{
    QSettings  settings(strScuOutAutoCheckIni, QSettings::IniFormat);
    settings.setIniCodec(QTextCodec::codecForName("UTF-8"));

    settings.beginGroup(m_strName);
    ui->gzmsCB->setCurrentText(settings.value("zfms").toString());
    ui->ljfsCB->setCurrentText(settings.value("ljfs").toString());
    ui->zxfsCB->setCurrentText(settings.value("zxfs").toString());
    ui->ljyyfsCB->setCurrentText(settings.value("ljyyfs").toString());
    ui->apnLE->setText(settings.value("apn").toString());
    ui->dlfwqLE->setText(settings.value("dlfwq").toString());
    ui->yhmLE->setText(settings.value("yhm").toString());
    ui->dldkLE->setText(settings.value("dldk").toString());
    ui->mmLE->setText(settings.value("mm").toString());
    ui->cssjLE->setText(settings.value("cssj").toString());
    ui->cfcsLE->setText(settings.value("cfcs").toString());
    ui->xtzqLE->setText(settings.value("xtzq").toString());

    QString s = settings.value("ztdklb").toString();
    QStringList s2 = s.split("@");
    for (int m = 0; m < s2.size(); ++m)
    {
        if(s2[m].isEmpty())
            continue;
        QListWidgetItem *pitem = new QListWidgetItem();
        pitem->setText(s2[m]);
        ui->listWidget->addItem(pitem);
    }
    settings.endGroup();
}

void CGwtxpzDialog::on_ok_clicked()
{
    QString sdk = ui->dkLE->text();
    if(sdk.isEmpty())
        return;
    ui->dkLE->clear();

    QListWidgetItem *pitem = new QListWidgetItem();
    pitem->setText(sdk);
    ui->listWidget->addItem(pitem);
}

void CGwtxpzDialog::on_del_clicked()
{
    int nRow = ui->listWidget->currentRow();
    if(nRow != -1)
    {
        ui->listWidget->takeItem(nRow);
    }
}

// 保存
void CGwtxpzDialog::on_pushButton_clicked()
{
    int nCfcs = ui->cfcsLE->text().toInt();
    if(nCfcs >3 || nCfcs < 0)
    {
        QMessageBox::about(this, "参数配置", "重发次数值错误");
        return;
    }
    int ncssj = ui->cssjLE->text().toInt();
    if(ncssj < 0 || ncssj > 255)
    {
        QMessageBox::about(this, "参数配置", "超时时间值错误");
        return;
    }
    QSettings  settings(strScuOutAutoCheckIni, QSettings::IniFormat);
    settings.setIniCodec(QTextCodec::codecForName("UTF-8"));

    settings.beginGroup(m_strName);
    settings.setValue("gzms", ui->gzmsCB->currentText());
    settings.setValue("zxfs", ui->zxfsCB->currentText());
    settings.setValue("apn", ui->apnLE->text());
    settings.setValue("yhm", ui->yhmLE->text());
    settings.setValue("mm", ui->mmLE->text());
    settings.setValue("cssj", ui->cssjLE->text());
    settings.setValue("cfcs", ui->cfcsLE->text());
    settings.setValue("xtzq", ui->xtzqLE->text());
    settings.setValue("ljfs", ui->ljfsCB->currentText());
    settings.setValue("ljyyfs", ui->ljyyfsCB->currentText());
    settings.setValue("dlfwq", ui->dlfwqLE->text());
    settings.setValue("dldk", ui->dldkLE->text());
    QString s;
    for (int m = 0; m < ui->listWidget->count(); ++m)
    {
        s += ui->listWidget->item(m)->text();
        s += "@";
    }
    QString s2 = s.left(s.size()-1);
    settings.setValue("ztdklb", s2);
    settings.endGroup();
}

﻿#ifndef CPCADDDIALOG_H
#define CPCADDDIALOG_H
#include "msk_global.h"
#include <QMessageBox>
#include <QDialog>

namespace Ui {
class CPcAddDialog;
}

class CPcAddDialog : public QDialog
{
    Q_OBJECT

public:
    explicit CPcAddDialog(QWidget *parent = nullptr);
    ~CPcAddDialog();
    void setFaId(int faid);
    void setPcList(QStringList);
    QString getPcName();
private slots:
    void on_pushButton_clicked();

private:
    Ui::CPcAddDialog *ui;
    int m_nFaId;
    QStringList  m_strListPcName;
};

#endif // CPCADDDIALOG_H

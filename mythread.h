#ifndef MYTHREAD_H
#define MYTHREAD_H

#include <QObject>
#include<QThread>
#include"ctestlog.h"

class MyThread : public QThread
{
    Q_OBJECT
public:
    explicit MyThread(QObject *parent = nullptr);

protected:
    void run();

public slots:
    void onslotsRevNum(int i);
    void onslotsthreadRevLog(QString s);

signals:
    QString signnum();

    void signtheadSendLog(QString s);

private:
    cTestLog *m_test;
    int m_num;

};

#endif // MYTHREAD_H

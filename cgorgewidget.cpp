﻿#include "cgorgewidget.h"
#include "ui_cgorgewidget.h"
#include <QDebug>
#include "msk_global.h"
#include "cmmdialog.h"
#include <QFileDialog>

CGorgeWidget::CGorgeWidget(QWidget *parent) :
    QWidget(parent),
    ui(new Ui::CGorgeWidget)
{
    ui->setupUi(this);
    tableWidgetInit();
    rs485Init();
    rs485_2Init();
}

CGorgeWidget::~CGorgeWidget()
{
    delete ui;
}

void CGorgeWidget::initGorge()
{
    QSettings  settings(strScuOutAutoCheckIni, QSettings::IniFormat);
    settings.setIniCodec(QTextCodec::codecForName("UTF-8"));

    std::map<QString, QString>mapCom;
    settings.beginGroup("comdeploy");
    QStringList sList = settings.allKeys();
    for (int m = 0; m < sList.size();  ++m)
    {
        if(sList[m] == "xtcom" || sList[m] == "wccom" || sList[m] == "bzbcom")
        {
            continue;
        }
        mapCom[sList[m]] = settings.value(sList[m]).toString();
    }

    QString s1 = settings.value("xtcom").toString();
    QString s2 = settings.value("wccom").toString();
    QString s3 = settings.value("bzbcom").toString();

    ui->systemTW->item(1, 1)->setText(s2);
    ui->systemTW->item(0, 1)->setText(s3);
    ui->systemTW->item(2, 1)->setText(s1);

    ui->systemTW_2->item(1, 1)->setText(s2);
    ui->systemTW_2->item(0, 1)->setText(s3);
    ui->systemTW_2->item(2, 1)->setText(s1);

    settings.endGroup();


    for (int m = 0; m < ui->tableWidget->rowCount(); ++m)
    {
        QString s = ui->tableWidget->item(m, gorgeBW_bw)->text();
        auto iter = mapCom.find(s);
        if(iter != mapCom.end())
        {
            QTableWidgetItem *pitem = new QTableWidgetItem();
            pitem->setText(iter->second);
            ui->tableWidget->setItem(m, gorgeBW_mc, pitem);

            pitem = new QTableWidgetItem();
            pitem->setText(iter->second);
            ui->tableWidget_2->setItem(m, gorgeBW_mc, pitem);

        }

        s = ui->tableWidget->item(m, gorgeBW_bw2)->text();

        iter = mapCom.find(s);
        if(iter != mapCom.end())
        {
            QTableWidgetItem *pitem = new QTableWidgetItem();
            pitem->setText(iter->second);
            ui->tableWidget->setItem(m, gorgeBW_mc2, pitem);

            pitem = new QTableWidgetItem();
            pitem->setText(iter->second);
            ui->tableWidget_2->setItem(m, gorgeBW_mc2, pitem);
        }
    }


    settings.beginGroup("gorge_rs485");
    sList = settings.allKeys();
    std::map<QString, QString> mapList;
    for (int m = 0; m < sList.size(); ++m)
    {
        QString s6 = settings.value(sList[m]).toString();
        mapList[sList[m]] = s6;
    }
    settings.endGroup();


    for (int m = 0; m < 80; m += 4)
    {
        QTableWidgetItem *pitem = ui->rs485TW_2->item(m, 0);
        if(pitem == nullptr || pitem->text().isEmpty())
            continue;
        QString sbw = pitem->text();
        auto iter = mapList.find(sbw);
        if(iter == mapList.end())
            continue;
        sList = iter->second.split("@");

        for (int n = 0; n < sList.size(); ++n)
        {
            QTableWidgetItem *pitem = new QTableWidgetItem();
            pitem->setText(sList[n]);
            ui->rs485TW_2->setItem(m + n, 2, pitem);
        }

        pitem = ui->rs485TW_2->item(m, 3);
        if(pitem == nullptr || pitem->text().isEmpty())
            continue;
        sbw = pitem->text();
        iter = mapList.find(sbw);
        if(iter == mapList.end())
            continue;
        sList = iter->second.split("@");

        for (int n = 0; n < sList.size(); ++n)
        {
            QTableWidgetItem *pitem = new QTableWidgetItem();
            pitem->setText(sList[n]);
            ui->rs485TW_2->setItem(m + n, 5, pitem);
        }
    }


    for (int m = 0; m < 80; m += 4)
    {
        QTableWidgetItem *pitem = ui->rs485TW->item(m, 0);
        if(pitem == nullptr || pitem->text().isEmpty())
            continue;
        QString sbw = pitem->text();
        auto iter = mapList.find(sbw);
        if(iter == mapList.end())
            continue;
        sList = iter->second.split("@");

        for (int n = 0; n < sList.size(); ++n)
        {
            QTableWidgetItem *pitem = new QTableWidgetItem();
            pitem->setText(sList[n]);
            ui->rs485TW->setItem(m + n, 2, pitem);
        }

        pitem = ui->rs485TW->item(m, 3);
        if(pitem == nullptr || pitem->text().isEmpty())
            continue;
        sbw = pitem->text();
        iter = mapList.find(sbw);
        if(iter == mapList.end())
            continue;
        sList = iter->second.split("@");

        for (int n = 0; n < sList.size(); ++n)
        {
            QTableWidgetItem *pitem = new QTableWidgetItem();
            pitem->setText(sList[n]);
            ui->rs485TW->setItem(m + n, 5, pitem);
        }
    }

    settings.beginGroup("zdzsdclj");
    ui->lineEdit->setText(settings.value("zdzsdclj").toString());
    settings.endGroup();

    settings.beginGroup("baogaosclj");
    ui->lineEdit_2->setText(settings.value("baogaosclj").toString());
    settings.endGroup();

    settings.beginGroup("Licenselj");
    ui->lineEdit_3->setText(settings.value("Licenselj").toString());
    settings.endGroup();
}


void CGorgeWidget::tableWidgetInit()
{
    ui->tableWidget->verticalHeader()->setVisible(false);
    ui->tableWidget->setAlternatingRowColors(true);
    ui->tableWidget->setColumnCount(gorgeBW_max);

    QTableWidgetItem *pitem = new QTableWidgetItem();
    pitem->setText("表位");
    ui->tableWidget->setHorizontalHeaderItem(gorgeBW_bw, pitem);

    pitem = new QTableWidgetItem();
    pitem->setText("串口号");
    ui->tableWidget->setHorizontalHeaderItem(gorgeBW_mc, pitem);

    pitem = new QTableWidgetItem();
    pitem->setText("表位");
    ui->tableWidget->setHorizontalHeaderItem(gorgeBW_bw2, pitem);

    pitem = new QTableWidgetItem();
    pitem->setText("串口号");
    ui->tableWidget->setHorizontalHeaderItem(gorgeBW_mc2, pitem);

    ui->tableWidget->setRowCount(20);

    int nBWH = 1;       // 表位号
    for (int m = 0;  m < ui->tableWidget->rowCount(); ++m)
    {
        QTableWidgetItem *item = new QTableWidgetItem();
        item->setFlags(pitem->flags() & ~Qt::ItemIsEnabled);
        item->setText(QString::number(nBWH));
        ui->tableWidget->setItem(m, gorgeBW_bw, item);

        item = new QTableWidgetItem();
        item->setFlags(pitem->flags() & ~Qt::ItemIsEditable);
        item->setText(QString::number(20 + nBWH));
        ui->tableWidget->setItem(m, gorgeBW_bw2, item);

        ++nBWH;
    }


    ui->tableWidget_2->verticalHeader()->setVisible(false);
    ui->tableWidget_2->setAlternatingRowColors(true);
    ui->tableWidget_2->setColumnCount(gorgeBW_max);
    ui->tableWidget_2->setEditTriggers(QTableWidget::NoEditTriggers);

    pitem = new QTableWidgetItem();
    pitem->setText("表位");
    ui->tableWidget_2->setHorizontalHeaderItem(gorgeBW_bw, pitem);

    pitem = new QTableWidgetItem();
    pitem->setText("串口号");
    ui->tableWidget_2->setHorizontalHeaderItem(gorgeBW_mc, pitem);

    pitem = new QTableWidgetItem();
    pitem->setText("表位");
    ui->tableWidget_2->setHorizontalHeaderItem(gorgeBW_bw2, pitem);

    pitem = new QTableWidgetItem();
    pitem->setText("串口号");
    ui->tableWidget_2->setHorizontalHeaderItem(gorgeBW_mc2, pitem);

    ui->tableWidget_2->setRowCount(20);

    nBWH = 1;       // 表位号
    for (int m = 0;  m < ui->tableWidget_2->rowCount(); ++m)
    {
        QTableWidgetItem *item = new QTableWidgetItem();
        item->setText(QString::number(nBWH));
        ui->tableWidget_2->setItem(m, gorgeBW_bw, item);

        item = new QTableWidgetItem();
        item->setText(QString::number(20 + nBWH));
        ui->tableWidget_2->setItem(m, gorgeBW_bw2, item);
        ++nBWH;
    }

    ui->systemTW->verticalHeader()->setVisible(false);
    ui->systemTW->setAlternatingRowColors(true);
    ui->systemTW->setColumnCount(gorge_max);
    ui->systemTW->setRowCount(3);

    pitem = new QTableWidgetItem();
    pitem->setText("串口名称");
    ui->systemTW->setHorizontalHeaderItem(gorge_bw, pitem);

    pitem = new QTableWidgetItem();
    pitem->setText("串口号");
    ui->systemTW->setHorizontalHeaderItem(gorge_mc, pitem);

    pitem = new QTableWidgetItem();
    pitem->setFlags(pitem->flags() & ~Qt::ItemIsEditable);
    pitem->setText("标准表通讯口");
    ui->systemTW->setItem(0, 0, pitem);

    pitem = new QTableWidgetItem();
    pitem->setText("COM4");
    ui->systemTW->setItem(0, 1, pitem);

    pitem = new QTableWidgetItem();
    pitem->setFlags(pitem->flags() & ~Qt::ItemIsEditable);
    pitem->setText("误差通讯口");
    ui->systemTW->setItem(1, 0, pitem);

    pitem = new QTableWidgetItem();
    pitem->setText("COM3");
    ui->systemTW->setItem(1, 1, pitem);

    pitem = new QTableWidgetItem();
    pitem->setFlags(pitem->flags() & ~Qt::ItemIsEditable);
    pitem->setText("系统通讯口");
    ui->systemTW->setItem(2, 0, pitem);

    pitem = new QTableWidgetItem();
    pitem->setText("COM2");
    ui->systemTW->setItem(2, 1,pitem);


    ui->systemTW_2->verticalHeader()->setVisible(false);
    ui->systemTW_2->setAlternatingRowColors(true);
    ui->systemTW_2->setColumnCount(gorge_max);
    ui->systemTW_2->setRowCount(3);
    ui->systemTW_2->setEditTriggers(QTableWidget::NoEditTriggers);

    pitem = new QTableWidgetItem();
    pitem->setText("串口名称");
    ui->systemTW_2->setHorizontalHeaderItem(gorge_bw, pitem);

    pitem = new QTableWidgetItem();
    pitem->setText("串口号");
    ui->systemTW_2->setHorizontalHeaderItem(gorge_mc, pitem);

    pitem = new QTableWidgetItem();
    pitem->setText("标准表通讯口");
    ui->systemTW_2->setItem(0, 0, pitem);

    pitem = new QTableWidgetItem();
    pitem->setText("COM4");
    ui->systemTW_2->setItem(0, 1, pitem);

    pitem = new QTableWidgetItem();
    pitem->setText("误差通讯口");
    ui->systemTW_2->setItem(1, 0, pitem);

    pitem = new QTableWidgetItem();
    pitem->setText("COM3");
    ui->systemTW_2->setItem(1, 1, pitem);

    pitem = new QTableWidgetItem();
    pitem->setText("系统通讯口");
    ui->systemTW_2->setItem(2, 0, pitem);

    pitem = new QTableWidgetItem();
    pitem->setText("COM2");
    ui->systemTW_2->setItem(2, 1,pitem);

}

void CGorgeWidget::rs485Init()
{
    ui->rs485TW->verticalHeader()->setVisible(false);
    ui->rs485TW->setAlternatingRowColors(true);
    ui->rs485TW->setColumnCount(6);
    ui->rs485TW->setColumnWidth(2, 300);
    ui->rs485TW->setColumnWidth(5, 300);

    QTableWidgetItem *pitem = new QTableWidgetItem();
    pitem->setText("表位");
    ui->rs485TW->setHorizontalHeaderItem(0, pitem);

    pitem = new QTableWidgetItem();
    pitem->setText("RS485");
    ui->rs485TW->setHorizontalHeaderItem(1, pitem);

    pitem = new QTableWidgetItem();
    pitem->setText("串口号");
    ui->rs485TW->setHorizontalHeaderItem(2, pitem);

    pitem = new QTableWidgetItem();
    pitem->setText("表位");
    ui->rs485TW->setHorizontalHeaderItem(3, pitem);

    pitem = new QTableWidgetItem();
    pitem->setText("RS485");
    ui->rs485TW->setHorizontalHeaderItem(4, pitem);

    pitem = new QTableWidgetItem();
    pitem->setText("串口号");
    ui->rs485TW->setHorizontalHeaderItem(5, pitem);

    ui->rs485TW->setRowCount(80);
    for (int m = 0; m < 80; ++m)
    {
        pitem = new QTableWidgetItem();
        if(m % 4 == 0)
            pitem->setText("RS485_I");
        else if((m-1) % 4 == 0 )
            pitem->setText("RS485_II");
        else if((m-2) % 4 == 0 )
            pitem->setText("RS485_III");
        else if((m-3) % 4 == 0)
            pitem->setText("RS485_IV");
        ui->rs485TW->setItem(m, 1, pitem);

        pitem = new QTableWidgetItem();
        if(m % 4 == 0)
            pitem->setText("RS485_I");
        else if((m-1) % 4 == 0 )
            pitem->setText("RS485_II");
        else if((m-2) % 4 == 0 )
            pitem->setText("RS485_III");
        else if((m-3) % 4 == 0)
            pitem->setText("RS485_IV");
        ui->rs485TW->setItem(m, 4, pitem);

        if(m % 4 == 0)
        {
            pitem = new QTableWidgetItem();
            pitem->setText(QString::number(m/4 + 1));
            ui->rs485TW->setItem(m, 0, pitem);
            ui->rs485TW->setSpan(m, 0, 4, 1);


            pitem = new QTableWidgetItem();
            pitem->setText(QString::number((m/4+21)));
            ui->rs485TW->setItem(m, 3, pitem);
            ui->rs485TW->setSpan(m, 3, 4, 1);
        }
    }
}

void CGorgeWidget::rs485_2Init()
{
    ui->rs485TW_2->verticalHeader()->setVisible(false);
    ui->rs485TW_2->setAlternatingRowColors(true);
    ui->rs485TW_2->setEditTriggers(QTableView::NoEditTriggers);
    ui->rs485TW_2->setColumnCount(6);
    ui->rs485TW_2->setColumnWidth(2, 300);
    ui->rs485TW_2->setColumnWidth(5, 300);

    QTableWidgetItem *pitem = new QTableWidgetItem();
    pitem->setText("表位");
    ui->rs485TW_2->setHorizontalHeaderItem(0, pitem);

    pitem = new QTableWidgetItem();
    pitem->setText("RS485");
    ui->rs485TW_2->setHorizontalHeaderItem(1, pitem);

    pitem = new QTableWidgetItem();
    pitem->setText("串口号");
    ui->rs485TW_2->setHorizontalHeaderItem(2, pitem);

    pitem = new QTableWidgetItem();
    pitem->setText("表位");
    ui->rs485TW_2->setHorizontalHeaderItem(3, pitem);

    pitem = new QTableWidgetItem();
    pitem->setText("RS485");
    ui->rs485TW_2->setHorizontalHeaderItem(4, pitem);

    pitem = new QTableWidgetItem();
    pitem->setText("串口号");
    ui->rs485TW_2->setHorizontalHeaderItem(5, pitem);

    ui->rs485TW_2->setRowCount(80);
    for (int m = 0; m < 80; ++m)
    {
        pitem = new QTableWidgetItem();
        if(m % 4 == 0)
            pitem->setText("RS485_I");
        else if((m-1) % 4 == 0 )
            pitem->setText("RS485_II");
        else if((m-2) % 4 == 0 )
            pitem->setText("RS485_III");
        else if((m-3) % 4 == 0)
            pitem->setText("RS485_IV");
        ui->rs485TW_2->setItem(m, 1, pitem);

        pitem = new QTableWidgetItem();
        if(m % 4 == 0)
            pitem->setText("RS485_I");
        else if((m-1) % 4 == 0 )
            pitem->setText("RS485_II");
        else if((m-2) % 4 == 0 )
            pitem->setText("RS485_III");
        else if((m-3) % 4 == 0)
            pitem->setText("RS485_IV");
        ui->rs485TW_2->setItem(m, 4, pitem);

        if(m % 4 == 0)
        {
            pitem = new QTableWidgetItem();
            pitem->setText(QString::number(m/4 + 1));
            ui->rs485TW_2->setItem(m, 0, pitem);
            ui->rs485TW_2->setSpan(m, 0, 4, 1);


            pitem = new QTableWidgetItem();
            pitem->setText(QString::number((m/4+21)));
            ui->rs485TW_2->setItem(m, 3, pitem);
            ui->rs485TW_2->setSpan(m, 3, 4, 1);
        }
    }
}


// 确定
void CGorgeWidget::on_pushButton_clicked()
{
    CMMDialog   mm;
    if(mm.exec() != QDialog::Accepted)
    {
        return;
    }

    QSettings  settings(strScuOutAutoCheckIni, QSettings::IniFormat);
    settings.setIniCodec(QTextCodec::codecForName("UTF-8"));

    settings.beginGroup("comdeploy");

    for (int m = 0; m < ui->tableWidget->rowCount(); ++m)
    {
        if(ui->tableWidget->item(m, gorgeBW_mc) != nullptr)
        {
            settings.setValue(ui->tableWidget->item(m, gorgeBW_bw)->text(),  ui->tableWidget->item(m, gorgeBW_mc)->text());
        }

        if(ui->tableWidget->item(m, gorgeBW_mc2) != nullptr)
        {
            settings.setValue(ui->tableWidget->item(m, gorgeBW_bw2)->text(),  ui->tableWidget->item(m, gorgeBW_mc2)->text());
        }
    }

    settings.setValue("xtcom", ui->systemTW->item(2,1)->text());
    settings.setValue("wccom", ui->systemTW->item(1,1)->text());
    settings.setValue("bzbcom", ui->systemTW->item(0, 1)->text());
    settings.endGroup();


    QSettings  setting(strScuOutAutoCheckIni, QSettings::IniFormat);
    setting.setIniCodec(QTextCodec::codecForName("UTF-8"));

    std::map<QString, QString>mapCom;
    setting.beginGroup("comdeploy");
    QStringList sList = setting.allKeys();
    for (int m = 0; m < sList.size();  ++m)
    {
        if(sList[m] == "xtcom" || sList[m] == "wccom" || sList[m] == "bzbcom")
        {
            continue;
        }
        mapCom[sList[m]] = setting.value(sList[m]).toString();
    }
    setting.endGroup();


    for (int m = 0; m < ui->tableWidget->rowCount(); ++m)
    {
        QString s = ui->tableWidget->item(m, gorgeBW_bw)->text();
        auto iter = mapCom.find(s);
        if(iter != mapCom.end())
        {
            QTableWidgetItem *pitem = new QTableWidgetItem();
            pitem->setText(iter->second);
            ui->tableWidget_2->setItem(m, gorgeBW_mc, pitem);

        }
        s = ui->tableWidget->item(m, gorgeBW_bw2)->text();

        iter = mapCom.find(s);
        if(iter != mapCom.end())
        {
            QTableWidgetItem *pitem = new QTableWidgetItem();
            pitem->setText(iter->second);
            ui->tableWidget_2->setItem(m, gorgeBW_mc2, pitem);
        }
    }
}


// 连续串口号
void CGorgeWidget::on_lxckhBtn_clicked()
{
    QTableWidgetItem *pitem = ui->tableWidget->item(0, 1);
    if(pitem == nullptr)
        return;
    QString s = pitem->text().right(pitem->text().size()-3);

    int nNum =  s.toInt();
    int num2 = nNum + 20;
    ++nNum;
    ++num2;

    for (int m = 1;  m < ui->tableWidget->rowCount(); ++m)
    {
        if(ui->tableWidget->item(m, gorgeBW_mc) == nullptr)
        {
            QTableWidgetItem *pitem = new QTableWidgetItem();
            pitem->setText("COM" + QString::number(nNum));
            ui->tableWidget->setItem(m, gorgeBW_mc, pitem);
        }
        else
        {
            ui->tableWidget->item(m, gorgeBW_mc)->setText("COM" + QString::number(nNum));
        }

        if(ui->tableWidget->item(m, gorgeBW_mc2) == nullptr)
        {
            QTableWidgetItem *pitem = new QTableWidgetItem();
            pitem->setText("COM" + QString::number(num2));
            ui->tableWidget->setItem(m, gorgeBW_mc2, pitem);
        }
        else
        {
            ui->tableWidget->item(m, gorgeBW_mc2)->setText("COM" + QString::number(num2));
        }
        ++nNum;
        ++num2;
    }
    if(ui->tableWidget->item(0, gorgeBW_mc2) != nullptr)
    {
        ui->tableWidget->item(0, gorgeBW_mc2)->setText("COM" + QString::number(s.toInt() + 20));
    }
    else
    {
        QTableWidgetItem *item = new QTableWidgetItem();
        item->setText("COM" + QString::number(s.toInt() + 20));
        ui->tableWidget->setItem(0, gorgeBW_mc2, item);
    }

}

void CGorgeWidget::on_pushButton_2_clicked()
{
    CMMDialog   mm;
    if(mm.exec() != QDialog::Accepted)
    {
        return;
    }
    QSettings  settings(strScuOutAutoCheckIni, QSettings::IniFormat);
    settings.setIniCodec(QTextCodec::codecForName("UTF-8"));

    settings.beginGroup("comdeploy");
    settings.setValue("xtcom", ui->systemTW->item(2,1)->text());
    settings.setValue("wccom", ui->systemTW->item(1,1)->text());
    settings.setValue("bzbcom", ui->systemTW->item(0, 1)->text());
    settings.endGroup();


    QSettings  setting(strScuOutAutoCheckIni, QSettings::IniFormat);
    setting.setIniCodec(QTextCodec::codecForName("UTF-8"));
    setting.beginGroup("comdeploy");

    QString s1 = setting.value("xtcom").toString();
    QString s2 = setting.value("wccom").toString();
    QString s3 = setting.value("bzbcom").toString();

    ui->systemTW_2->item(1, 1)->setText(s2);
    ui->systemTW_2->item(0, 1)->setText(s3);
    ui->systemTW_2->item(2, 1)->setText(s1);
    settings.endGroup();
}

// 连续串口号
void CGorgeWidget::on_pushButton_4_clicked()
{
    QTableWidgetItem *pitem = ui->rs485TW->item(0,2);
    if(pitem == nullptr || pitem->text().isEmpty())
        return;

    QString s = pitem->text();
    int nckh= s.right(s.size()-3).toInt();

    int num = 0;
    for (int m = 0; m < 80; ++m)
    {
        pitem = new QTableWidgetItem();
        pitem->setText("COM" + QString::number(nckh));
        ui->rs485TW->setItem(m, 2, pitem);
        ++nckh;
        ++num;
        if(num % 4 == 0 && num != 0)
        {
            ++nckh;
        }
    }

    num = 0;
    for (int m = 0; m < 80; ++m)
    {
        pitem = new QTableWidgetItem();
        pitem->setText("COM" + QString::number(nckh));
        ui->rs485TW->setItem(m, 5, pitem);
        ++nckh;
        ++num;
        if(num % 4 == 0 && num != 0)
        {
            ++nckh;
        }
    }
}

// 保存
void CGorgeWidget::on_pushButton_3_clicked()
{
    CMMDialog   mm;
    if(mm.exec() != QDialog::Accepted)
    {
        return;
    }
    QSettings  settings(strScuOutAutoCheckIni, QSettings::IniFormat);
    settings.setIniCodec(QTextCodec::codecForName("UTF-8"));

    settings.beginGroup("gorge_rs485");

    QStringList sList = settings.allKeys();

    for (int m = 0; m < sList.size(); ++m)
    {
        settings.remove(sList[m]);
    }

    QString s1, s2, s3, s4, sbw;
    for (int m = 0; m < 80; ++m)
    {
        if(m % 4 == 0)
        {
            sbw.clear();
            s1.clear();
            QTableWidgetItem *pitem = ui->rs485TW->item(m, 2);
            if(pitem != nullptr)
            {
                s1 = pitem->text();
            }
            sbw = ui->rs485TW->item(m, 0)->text();
        }
        else if( (m -1) % 4 == 0)
        {
            s2.clear();
            QTableWidgetItem *pitem = ui->rs485TW->item(m, 2);
            if(pitem != nullptr)
            {
                s2 = pitem->text();
            }
        }
        else if( (m -2) % 4 == 0)
        {
            s3.clear();
            QTableWidgetItem *pitem = ui->rs485TW->item(m, 2);
            if(pitem != nullptr)
            {
                s3 = pitem->text();
            }
        }
        else if( (m - 3) % 4 == 0)
        {
            s4.clear();
            QTableWidgetItem *pitem = ui->rs485TW->item(m, 2);
            if(pitem != nullptr)
            {
                s4 = pitem->text();
            }
            settings.setValue(sbw, s1 + "@" + s2 + "@" + s3 + "@"+ s4);
        }

    }

    for (int m = 0; m < 80; ++m)
    {
        if(m % 4 == 0)
        {
            sbw.clear();
            s1.clear();
            QTableWidgetItem *pitem = ui->rs485TW->item(m, 5);
            if(pitem != nullptr)
            {
                s1 = pitem->text();
            }
            sbw = ui->rs485TW->item(m, 3)->text();
        }
        else if( (m -1) % 4 == 0)
        {
            s2.clear();
            QTableWidgetItem *pitem = ui->rs485TW->item(m, 5);
            if(pitem != nullptr)
            {
                s2 = pitem->text();
            }
        }
        else if( (m -2) % 4 == 0)
        {
            s3.clear();
            QTableWidgetItem *pitem = ui->rs485TW->item(m, 5);
            if(pitem != nullptr)
            {
                s3 = pitem->text();
            }
        }
        else if( (m - 3) % 4 == 0)
        {
            s4.clear();
            QTableWidgetItem *pitem = ui->rs485TW->item(m, 5);
            if(pitem != nullptr)
            {
                s4 = pitem->text();
            }
            settings.setValue(sbw, s1 + "@" + s2 + "@" + s3 + "@"+ s4);
        }
    }
    settings.endGroup();


    settings.beginGroup("gorge_rs485");
    sList = settings.allKeys();
    std::map<QString, QString> mapList;
    for (int m = 0; m < sList.size(); ++m)
    {
        QString s6 = settings.value(sList[m]).toString();
        mapList[sList[m]] = s6;
    }

    for (int m = 0; m < 80; m += 4)
    {
        QTableWidgetItem *pitem = ui->rs485TW_2->item(m, 0);
        if(pitem == nullptr || pitem->text().isEmpty())
            continue;
        QString sbw = pitem->text();
        auto iter = mapList.find(sbw);
        if(iter == mapList.end())
            continue;
        sList = iter->second.split("@");

        for (int n = 0; n < sList.size(); ++n)
        {
            QTableWidgetItem *pitem = new QTableWidgetItem();
            pitem->setText(sList[n]);
            ui->rs485TW_2->setItem(m + n, 2, pitem);
        }

        pitem = ui->rs485TW_2->item(m, 3);
        if(pitem == nullptr || pitem->text().isEmpty())
            continue;
        sbw = pitem->text();
        iter = mapList.find(sbw);
        if(iter == mapList.end())
            continue;
        sList = iter->second.split("@");

        for (int n = 0; n < sList.size(); ++n)
        {
            QTableWidgetItem *pitem = new QTableWidgetItem();
            pitem->setText(sList[n]);
            ui->rs485TW_2->setItem(m + n, 5, pitem);
        }
    }
    settings.endGroup();
}

void CGorgeWidget::on_pushButton_5_clicked()
{

    QString folderPath = QFileDialog::getExistingDirectory(
        nullptr, // 父窗口
        "选择文件夹", // 对话框标题
        QDir::homePath(), // 默认打开的路径
        QFileDialog::ShowDirsOnly | QFileDialog::DontResolveSymlinks // 选项
    );

    if(folderPath.isEmpty())
        return;
    if(!folderPath.endsWith("/"))
        folderPath += "/";
    ui->lineEdit->setText(folderPath);


    QSettings  settings(strScuOutAutoCheckIni, QSettings::IniFormat);
    settings.setIniCodec(QTextCodec::codecForName("UTF-8"));

    settings.beginGroup("zdzsdclj");
    settings.setValue("zdzsdclj", folderPath);
    settings.endGroup();

    folderPath.replace("/", "//");
    settings.beginGroup("zdzsdclj");
    settings.setValue("zdzsdcljzz", folderPath);
    settings.endGroup();


}

void CGorgeWidget::on_pushButton_6_clicked()
{
    QString folderPath = QFileDialog::getExistingDirectory(
        nullptr, // 父窗口
        "选择文件夹", // 对话框标题
        QDir::homePath(), // 默认打开的路径
        QFileDialog::ShowDirsOnly | QFileDialog::DontResolveSymlinks // 选项
    );

    if(folderPath.isEmpty())
        return;
    if(!folderPath.endsWith("/"))
        folderPath += "/";
    ui->lineEdit_2->setText(folderPath);


    QSettings  settings(strScuOutAutoCheckIni, QSettings::IniFormat);
    settings.setIniCodec(QTextCodec::codecForName("UTF-8"));

    settings.beginGroup("baogaosclj");
    settings.setValue("baogaosclj", folderPath);
    settings.endGroup();

}

// 设置License文件读取路径
void CGorgeWidget::on_pushButton_7_clicked()
{
    QString folderPath = QFileDialog::getExistingDirectory(
        nullptr, // 父窗口
        "选择文件夹", // 对话框标题
        QDir::homePath(), // 默认打开的路径
        QFileDialog::ShowDirsOnly | QFileDialog::DontResolveSymlinks // 选项
    );

    if(folderPath.isEmpty())
        return;
    if(!folderPath.endsWith("/"))
        folderPath += "/";
    ui->lineEdit_3->setText(folderPath);


    QSettings  settings(strScuOutAutoCheckIni, QSettings::IniFormat);
    settings.setIniCodec(QTextCodec::codecForName("UTF-8"));

    settings.beginGroup("Licenselj");
    settings.setValue("Licenselj", folderPath);
    settings.endGroup();
}

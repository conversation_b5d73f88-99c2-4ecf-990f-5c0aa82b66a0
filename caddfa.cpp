﻿#include "caddfa.h"
#include "ui_caddfa.h"
#include <QMessageBox>
#include "msk_global.h"
CAddFa::CAddFa(QWidget *parent) :
    QDialog(parent),
    ui(new Ui::CAddFa)
{
    ui->setupUi(this);
    queryFa();
}

CAddFa::~CAddFa()
{
    delete ui;
}

QString CAddFa::getFa()
{
    return ui->lineEdit->text();
}

QString CAddFa::getCpFa()
{
    if(!ui->fafzCB->isChecked())
        return "";
    return ui->FACB->currentText();
}

void CAddFa::on_ok_clicked()
{
    if(ui->lineEdit->text().isEmpty())
    {
        QMessageBox::about(this, "确定", "方案名称不能为空");
        return;
    }

    QString sfa = ui->lineEdit->text();

    QSettings  settings(strScuOutAutoCheckIni, QSettings::IniFormat);
    settings.setIniCodec(QTextCodec::codecForName("UTF-8"));

    settings.beginGroup("FA");
    QStringList sList = settings.allKeys();

    for (int m = 0; m < sList.size(); ++m)
    {
        if(sList[m] == "FAID" || sList[m] == "FAXH")
            continue;
        QString s = settings.value(sList[m]).toString();

        if(sfa == s)
        {
            settings.endGroup();
            QMessageBox::about(this, "方案增加", "存在相同名称的方案");
            return;
        }
    }
    settings.endGroup();


    accept();
}

void CAddFa::on_fafzCB_toggled(bool checked)
{
    ui->FACB->setEnabled(checked);
}


// 查找方案
void CAddFa::queryFa()
{
    QSettings  settings(strScuOutAutoCheckIni, QSettings::IniFormat);
    settings.setIniCodec(QTextCodec::codecForName("UTF-8"));

    QStringList strFAList;
    settings.beginGroup("FA");
    QStringList sList = settings.allKeys();
    for (int m = 0; m < sList.size(); ++m)
    {
        if(sList[m] == "FAID" || sList[m] == "FAXH")
        {
            continue;
        }
        strFAList << settings.value(sList[m]).toString();
    }
    settings.endGroup();

    ui->FACB->clear();
    ui->FACB->addItems(strFAList);
}

﻿#include "cresultquery.h"
#include <QtDebug>
CResultQuery::CResultQuery()
{
    m_bdlQuery = false;
    m_bxlQuery = false;
    m_bgcQuery = false;
}

void CResultQuery::run()
{
    while (true)
    {
        if(m_bdlQuery)
            queryDLRes();
        if(m_bxlQuery)
            queryXlRes();
        if(m_bgcQuery)
            queryGcRes();

        sleep(1);
    }
}

void CResultQuery::queryDl(SDLQueryParam &query)
{
    m_dlQuery = query;
    m_bdlQuery = true;
}

void CResultQuery::queryXl(quint64 oid)
{
    m_noid = oid;
    m_bxlQuery = true;
}

void CResultQuery::queryGc(SGntJyXlGc &gc)
{
    m_gcQuery = gc;
    m_bgcQuery = true;
}

bool CResultQuery::isQuery()
{
    if(m_bdlQuery || m_bgcQuery || m_bxlQuery)
        return true;
    return false;
}

void CResultQuery::queryDLRes()
{
    QSqlDatabase db = QSqlDatabase::addDatabase("QSQLITE");
    db.setDatabaseName(g_strScuoutcheckdb);
    if(!db.isOpen())
    {
        db.open();
    }
    if(!db.isOpen())
    {
        return;
    }

    QString sql;
    sql.sprintf("SELECT RQ, CATEGORYID,BATCHID,ESN,DEVID,LOGICADDR,CHECKRESULT,USRPASSWD,SYSTEMPARAM,SCUPARAM,IOTPARAM,"
                "PARAM698,SCUTIMING,GORGE,YXDPI,TGWGMMC,MEASURE,BLUETOOTH,HPLC4G,DATACLEAR,BASICINFOR,RESERVEPOWER,PORTS,SWTEST, SCURQ, SCUAPP, HLXJ,CCZT,OID,IP,CLEARDATA, ZDIP,EXPORT, IMPORT,LXDYDL FROM SCUDETECTIONRESULT  "
                " WHERE RQ >= :RQ1 AND RQ <= :RQ2 ");

    int nfaid = -1;
    QSettings  settings(strScuOutAutoCheckIni, QSettings::IniFormat);
    settings.setIniCodec(QTextCodec::codecForName("UTF-8"));

    settings.beginGroup("FA");
    QStringList sList = settings.allKeys();
    for (int m = 0; m < sList.size(); ++m)
    {
        if(sList[m] == "FAXH" || sList[m] == "FAID")
            continue;
        if(settings.value(sList[m]).toString() == m_dlQuery.strFA)
        {
            nfaid = sList[m].toInt();
            break;
        }
    }
    settings.endGroup();

    sql += " AND CATEGORYID = ";
    sql += QString::number(nfaid);


    if(m_dlQuery.strPC != "全部")
    {
        int npcid = 0;
        QSettings  settings(strScuOutAutoCheckIni, QSettings::IniFormat);
        settings.setIniCodec(QTextCodec::codecForName("UTF-8"));
        settings.beginGroup("PC_" + QString::number(nfaid));
        sList = settings.allKeys();
        for (int m = 0; m < sList.size(); ++m)
        {
            if(settings.value(sList[m]) == m_dlQuery.strPC)
            {
                npcid = sList[m].toInt();
                break;
            }
        }
        settings.endGroup();

        sql += " AND BATCHID = ";
        sql += QString::number(npcid);

    }

    if(m_dlQuery.bID)
    {
        sql += " AND DEVID = '";
        sql += m_dlQuery.strID;
        sql += "'";
    }

    QSqlQuery execSql(db);
    bool bsuc = execSql.prepare(sql);
    qDebug() << execSql.lastError().text();
    execSql.bindValue(":RQ1", m_dlQuery.beginDT);
    execSql.bindValue(":RQ2", m_dlQuery.endDT);

    bool cc = execSql.exec();
    std::vector<SDLQuery> vtDl;

    while(execSql.next())
    {
        SDLQuery dl;
        dl.strRq = execSql.value(0).toDateTime().toString("yyyy-MM-dd HH:mm:ss");
        int nfaid = execSql.value(1).toInt();
        dl.strFA = queryCfg(strScuOutAutoCheckIni, "FA", QString::number(nfaid));
        int npcid = execSql.value(2).toInt();
        dl.strPC = queryCfg(strScuOutAutoCheckIni, "PC_"+QString::number(nfaid), QString::number(npcid));
        dl.strEsn = execSql.value(3).toString();
        dl.strDevID = execSql.value(4).toString();
        dl.strLjdz = execSql.value(5).toString();
        QString s3 = translate(execSql.value(6).toInt());
        dl.CHECKRESULT = s3 == "未测试" ? "未完成": s3;
        dl.USRPASSWD = translate(execSql.value(7).toInt());
        dl.SYSTEMPARAM = translate(execSql.value(8).toInt());
        dl.SCUPARAM = translate(execSql.value(9).toInt());
        dl.IOTPARAM = translate(execSql.value(10).toInt());
        dl.PARAM698 = translate(execSql.value(11).toInt());
        dl.SCUTIMING = translate(execSql.value(12).toInt());
        dl.GORGE = translate(execSql.value(13).toInt());
        dl.YXDPI = translate(execSql.value(14).toInt());
        dl.TGWGMMC = translate(execSql.value(15).toInt());
        dl.MEASURE = translate(execSql.value(16).toInt());
        dl.BLUETOOTH = translate(execSql.value(17).toInt());
        dl.HPLC4G = translate(execSql.value(18).toInt());
        dl.DATACLEAR = translate(execSql.value(19).toInt());
        dl.BASICINFOR = translate(execSql.value(20).toInt());
        dl.RESERVEPOWER = translate(execSql.value(21).toInt());
        dl.PORTS = translate(execSql.value(22).toInt());
        dl.SWTEST = translate(execSql.value(23).toInt());
        dl.strSCURQ = translate(execSql.value(24).toInt());
        dl.strApp = translate(execSql.value(25).toInt());
        dl.strHLXJ = translate(execSql.value(26).toInt());
        dl.strCCZT = translate(execSql.value(27).toInt());
        dl.nOID = execSql.value(28).toULongLong();
        dl.strIp = execSql.value(29).toString();
        dl.strcleardata = translate(execSql.value(30).toInt());
        dl.strZdIp = translate(execSql.value(31).toInt());
        dl.strExPort = translate(execSql.value(32).toInt());
        dl.strImPort = translate(execSql.value(33).toInt());
        dl.strLxDyDl = translate(execSql.value(34).toInt());
        vtDl.push_back(dl);
    }
    db.close();
    QApplication::postEvent((QObject*)g_pFuncWindow, new CCsJlcxMsg(vtDl));

    m_bdlQuery = false;
}

void CResultQuery::queryXlRes()
{
    QSqlDatabase db = QSqlDatabase::addDatabase("QSQLITE");
    db.setDatabaseName(g_strScuoutcheckdb);
    if(!db.isOpen())
    {
        db.open();
    }
    if(!db.isOpen())
    {
        return;
    }

    QString sql;
    sql.sprintf("SELECT DLNAME, XLNAME, RESULT FROM SCUXLDETECTIONRESULT  WHERE OID = %lld ", m_noid);
    QSqlQuery execSql(db);
    execSql.exec(sql);
    std::vector<SXLQuery> vtXl;
    while (execSql.next())
    {
        SXLQuery xl;
        xl.noid = m_noid;
        xl.strDL = execSql.value(0).toString();
        xl.strXL = execSql.value(1).toString();
        xl.strRes = translate(execSql.value(2).toInt());
        vtXl.push_back(xl);
    }

    QApplication::postEvent((QObject*)g_pFuncWindow, new CCsXlCxMsg(vtXl));


    // 查询过程
    sql.sprintf("SELECT DLNAME, XLNAME, GC FROM SCUDETECTIONGC WHERE OID = %lld", m_noid);
    execSql.exec(sql);
    QString ss;
    while (execSql.next())
    {
        SGCQuery gc;
        gc.strDL = execSql.value(0).toString();
        gc.strXL = execSql.value(1).toString();
        gc.strGc = execSql.value(2).toString();

        ss += "\n";
        ss += "【" + gc.strDL+"】-【" + gc.strXL + "】 ";
        ss += gc.strGc;

    }
    QApplication::postEvent((QObject*)g_pFuncWindow, new CCsGcMsg(ss));
    m_bxlQuery = false;
    db.close();
}

void CResultQuery::queryGcRes()
{
    QSqlDatabase db = QSqlDatabase::addDatabase("QSQLITE");
    db.setDatabaseName(g_strScuoutcheckdb);
    if(!db.isOpen())
    {
        db.open();
    }
    if(!db.isOpen())
    {
        return;
    }

    QString sql;

    sql.sprintf("SELECT GC FROM SCUDETECTIONGC WHERE OID = %lld AND DLNAME = '%s' AND XLNAME = '%s'", m_gcQuery.oid, m_gcQuery.strCsDl.toStdString().c_str(), m_gcQuery.strCsXl.toStdString().c_str());
    QSqlQuery execSql(db);
    execSql.exec(sql);
    QString ss;
    while (execSql.next())
    {
        SGCQuery gc;
        gc.strDL = m_gcQuery.strCsDl;
        gc.strXL = m_gcQuery.strCsXl;
        gc.strGc = execSql.value(0).toString();

        ss += "\n";
        ss += "【" + gc.strDL+"】-【" + gc.strXL + "】 ";
        ss += gc.strGc;
    }

    QApplication::postEvent((QObject*)g_pFuncWindow, new CCsGcMsg(ss));

    m_bgcQuery = false;
    db.close();
}

QString CResultQuery::translate(int rs)
{
    QString ss;
    switch (rs)
    {
    case 1:
        ss = "合格";
        break;
    case 2:
        ss = "不合格";
        break;
    case 3:
        ss = "未完成";
        break;
    default:
        ss = "未测试";
        break;
    }
    return ss;
}

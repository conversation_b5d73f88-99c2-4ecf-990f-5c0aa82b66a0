﻿#ifndef CGNTPARAMDIALOG_H
#define CGNTPARAMDIALOG_H
#include "msk_global.h"
#include "cgwtxpzdialog.h"
#include "czztxcsdialog.h"
#include "ctsddialog.h"
#include "cytwtxpzdialog.h"
#include "caqmsdialog.h"
#include <QDialog>
#include <QTreeWidget>

namespace Ui {
class CGntParamdialog;
}

class CGntParamdialog : public QDialog
{
    Q_OBJECT

public:
    explicit CGntParamdialog(QWidget *parent = nullptr);
    ~CGntParamdialog();
    void initParam(QString strName, quint64);

private slots:
    void on_iotCB_toggl(bool);
    void on_wirCB_toggl(bool);
    void on_secCB_toggl(bool);

    void on_rqTW_itemChg(QTreeWidgetItem *, int);
    void on_rqOk_clicked();

    void on_chkitemOk_clicked();

    void on_scuOK_clicked();        //SCU系统参数确定

    void on_gwtxpzCB_toggled(bool checked);

    void on_gwzztxCB_toggled(bool checked);

    void on_ytwzztxCB_toggled(bool checked);

    void on_ytwtxCB_toggled(bool checked);

    void on_zdtsdcsCB_toggled(bool checked);

    void on_otherok_clicked();    // 其他参数

    void on_allBtn_clicked();

    void on_noallBtn_clicked();

    void on_query698Btn_clicked();

    void on_allXZBtn_clicked();

    void on_allnotBtn_clicked();

    void on_aqmscsCB_toggled(bool checked);

    void on_dm4gCB_toggled(bool checked);

    void on_sm4gCB_toggled(bool checked);

private:
    void rqAppInit();
    void rqwtoggled(bool on);

    void checkItemInit();
    void checkItem(QString);

private:
    quint64 m_nFAID;
    std::map<QString, std::vector<QString> > m_mapRqApp;        // 所有的容器和App;
    std::map<QString, QString> m_mapChkItem;

    QString m_strRqName;
    QString m_strRqAppName;
    QString m_strRqHashName;
    QString m_strRqAppHashName;
    QString m_strRqwAppName;
    QString m_strJCXName;
    QString m_strScuSysName;
    QString m_strGwtxpzName;
    QString m_strGwzztxdzName;
    QString m_strYtwzztxdzName;
    QString m_strYtwtxpzName;
    QString m_strTsdcspzName;
    QString m_strAqmscsName;
    QString m_strOtherName;
private:
    Ui::CGntParamdialog *ui;
    CGwtxpzDialog *m_pGwtxpz;
    CZztxcsDialog *m_pZztxdz;
    CYtwtxpzDialog *m_pYtwtxpz;
    CTsdDialog *m_pTsd;
    CAqmsDialog *m_paqjmpz;
};

#endif // CGNTPARAMDIALOG_H

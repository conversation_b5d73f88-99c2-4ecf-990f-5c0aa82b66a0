#include "ctestlog.h"

#include<QDateTime>
#include<QStandardItemModel>
#include<QDomDocument>
#include<QFile>
#include<QDebug>
#include<QDomElement>
#include<QDomNode>
#include<QThread>
#include"msk_global.h"
#include<QMessageBox>
#include<QAxObject>
#include<QTcpSocket>
#include<QDir>

cTestLog::cTestLog(QWidget *parent) :
    QObject(parent)
{
    // 注册自定义类型
    qRegisterMetaType<TestResult>("TestResult");
    qRegisterMetaType<TestItemResult>("TestItemResult");
    
    // 初始化成员变量
    m_serialport = nullptr;
    m_timer = new QTimer(this);
    timer = nullptr;
    m_currentStep = 0;
    m_pclient = nullptr;
    m_NetConnect = false;
    m_ComConnect = false;
    m_jnTestStart = false;
    UpdateDbVersion = false;
    tcpConNum = 5;
    m_dbTestRes = false;
    m_liceseFile = true;
    
    // 初始化电流电压值
    IA = 0.0f;
    IB = 0.0f;
    IC = 0.0f;
    VA = 0.0f;
    VB = 0.0f;
    VC = 0.0f;
    
    // 初始化SSH相关指针
    m_sshSocket = nullptr;
    m_shell = nullptr;
    m_channel = nullptr;
    
    // 初始化计数器
    m_ClearBTcount = 0;
    m_SshConCount = 0;
    m_NUM = 0;
    m_nLx = 0;
    m_portNum = 0;
    m_testItemStep = 0;
    m_dbFalseStep = 0;
    m_dbtimer = nullptr;
    m_currentUploadIndex = -1;
    
    // 初始化校表相关
    jbTimer = nullptr;
    tcpsocket = nullptr;
    m_jbTestRes = true;
    
    // 连接定时器信号
    connect(m_timer, &QTimer::timeout, this, &cTestLog::executeNextStep);
    
    // 读取配置
    if(queryCfg(CheckItemIni,"testitem","updatadb")=="1")
    {
        UpdateDbVersion = true;
    }
    else {
        UpdateDbVersion = false;
    }

    m_containList.clear();
    QSettings  settings(CheckItemIni, QSettings::IniFormat);
    settings.setIniCodec(QTextCodec::codecForName("UTF-8"));
    settings.beginGroup("installcontainer");
    QStringList clist = settings.allKeys();
    for(auto item:clist)
    {
        QString s =queryCfg(CheckItemIni,"installcontainer",item);
        if(s == "1")
        {
            m_containList << item;
        }
    }
}

cTestLog::~cTestLog()
{
       this->EndTest();
}
void cTestLog::EndTest()
{
    if (m_timer && m_timer->isActive()) {
        m_timer->stop();
        delete m_timer;
        m_timer = nullptr;
    }

    if (m_serialport && m_serialport->isOpen()) {
        m_serialport->close();
        delete m_serialport;
        m_serialport = nullptr;
    }
    if (m_channel) {
        m_channel->closeChannel();
        m_channel = nullptr;
    }

    if (m_sshSocket && m_sshSocket->state() == QSsh::SshConnection::Connected) {
        m_sshSocket->disconnectFromHost();
        delete m_sshSocket;
        m_sshSocket = nullptr;
    }
    if (tcpsocket && tcpsocket->state() == QAbstractSocket::ConnectedState) {
        tcpsocket->disconnectFromHost();
        delete tcpsocket;
        tcpsocket = nullptr;
    }
    if(m_pclient != nullptr)
    {
        m_pclient->disconnect();
        m_pclient->disconnectFromHost();
        delete  m_pclient;
        m_pclient = nullptr;
    }
//    if(m_dbtimer->isActive())
//    {
//        m_dbtimer->stop();
//    }
    disconnect(m_timer, &QTimer::timeout, this, &cTestLog::executeNextStep);
}

void cTestLog::onSlotsValues(float Ia,float Ib,float Ic,float Va,float Vb,float Vc)
{
    IA = Ia;
    IB = Ib;
    IC = Ic;
    VA = Va;
    VB = Vb;
    VC = Vc;
}
void cTestLog::addTestLog(int type,QString s)
{
   QString sDT = QDateTime::currentDateTime().toString("yyyy-MM-dd hh:mm:ss");
   if(type == 1)
   {
       QString ss = "["+ sDT +"]" + s + "\n";
       emit signtestlog(ss);
       this->SaveTestFile(ss.toUtf8());
   }
   else if(type == 2)
   {
       QString ss = "["+ sDT +"]" +"[error]"+ s + "\n";
       emit signtestlog(ss);
       this->SaveTestFile(ss.toUtf8());
   }

}
void cTestLog::SaveTestFile(QByteArray s)
{
   QString spc = queryCfg(CheckItemIni,"currentpc","currentpc");
   QDir dir1("E:/scu/log/");
   dir1.mkdir(spc);
   QString filePath = "E:/scu/log/"+spc+"/"+m_esnNum+"/log.txt";
   QDir diresn("E:/scu/log/"+spc+"/");
   diresn.mkdir(m_esnNum);
   QString filePath2 = "E:/scu/log/"+spc+"/"+m_esnNum+"/shell.txt";
   QFile file1(filePath);
   QFile file2(filePath2);

   if (file1.open(QIODevice::Append | QIODevice::Text)) {
       file1.write(s);
       file1.close();
   }
   if (file2.open(QIODevice::Append | QIODevice::Text)) {
      file2.close();
   } else {
       qDebug() << "Failed to open shell file:" << file2.errorString();
   }
}
void cTestLog::SaveShellFile(QByteArray s)
{
   QString spc = queryCfg(CheckItemIni,"currentpc","currentpc");
   QString filePath2 = "E:/scu/log/"+spc+"/"+m_esnNum+"/shell.txt";;
   QFile file1(filePath2);
   if (file1.open(QIODevice::Append | QIODevice::Text)) {
       file1.write(s);
       file1.close();
   }
}
void cTestLog::onSlotNUM(int n)
{
    this->addTestLog(1,"等待设备启动中");
    QThread::sleep(180);
    m_NUM = n+1 ;
    m_NetConnect = false;
    m_SshConCount = 0;

    // 清空之前的测试结果，重新开始统计
    m_currentTestResult.subItems.clear();
    m_currentTestResult.stationId = m_NUM;

    QSettings  settings(CheckItemIni, QSettings::IniFormat);
    settings.setIniCodec(QTextCodec::codecForName("UTF-8"));
    settings.beginGroup("testitem");
    QStringList clist = settings.allKeys();

    QString ss = queryCfg(CheckItemIni,"COMLIST" ,"1");
    int portnum = ss.mid(3).toInt();
    QString ComNum = QString("COM%1").arg(portnum+m_NUM-1);
    m_serialport = new QSerialPort();
    m_serialport->setPortName(ComNum);
    m_serialport->setBaudRate(115200);
    m_serialport->setDataBits(QSerialPort::Data8);
    m_serialport->setStopBits(QSerialPort::OneStop);
    m_serialport->setParity(QSerialPort::NoParity);
    m_serialport->setFlowControl(QSerialPort::NoFlowControl);
    m_serialport->setReadBufferSize(0);

    if (!m_serialport->open(QIODevice::ReadWrite))
    {
         QString errorMsg = QString("无法打开串口 %1: %2").arg(ComNum).arg(m_serialport->errorString());
         this->addTestLog(2,errorMsg);
         QMessageBox::critical(nullptr, "串口错误", errorMsg);

         delete m_serialport;
         m_serialport = nullptr;
         addTestItem("串口检测", "串口连接", "不合格");
         signJBTestRes(m_NUM);
         //m_allRes << "不合格";
         emit signTestResult("不合格");
         return;
    }

    this->addTestLog(1,ComNum + "串口已连接");
    this->changeIP();
    this->m_IP = QString("192.168.127.%1").arg(m_NUM + 100);

    connect(m_serialport, &QSerialPort::errorOccurred, this, [this](QSerialPort::SerialPortError error) {
          if (error != QSerialPort::NoError)
          {
                QString errorMsg = QString("串口错误: %1").arg(m_serialport->errorString());
                this->addTestLog(2,errorMsg);
                QMessageBox::critical(nullptr, "串口错误", errorMsg);

                m_serialport->close();
                delete m_serialport;
                m_serialport = nullptr;
                addTestItem("串口检测", "串口连接", "不合格");
                emit signTestResult("不合格");
          }
    });
}
void cTestLog::changeIP()
{
     signCurrentTestItem("串口连接");
     m_serialport->write("\n");
     this->addTestLog(1,"正在进行串口连接");
     connect(m_serialport,&QSerialPort::readyRead,[this](){
         QByteArray RevData = m_serialport->readAll();
         this->signTestShell(RevData);
         this->SaveShellFile(RevData);
         if(RevData.contains("sh: "))
         {
             m_serialport->write("\n");
         }
         else if(RevData.contains("sysadm@SCU:"))
         {
             m_portNum ++;
             if(m_portNum==1)
             {
                 m_currentStep = 0;
                 m_timer->start(0);
                 m_ComConnect = true;
             }
             m_portNum = 2;
         }
         else if (RevData.contains("SCU login:")) {
             m_serialport->write("sysadm\n");
         }
         else if(RevData.indexOf("setip success") != -1)
         {
             this->addTestLog(1,"修改IP成功");
             //m_serialport->close();
         }
         else if (RevData.contains("Password:"))
         {
             m_serialport->write("Zgdky@guest123\n");
         }
         else if(RevData.indexOf("setip fail,the failure reason: The ip is exist, cannot be the same.") != -1)
         {
            this->addTestLog(1,"ip已存在，正在进行连接");
            //m_serialport->close();
         }
         else if(RevData.contains("esn :"))
         {
             QString s= RevData;
             QStringList sl = s.split(":");
             m_esnNum = sl[1].remove("\r\n");
             this->addTestLog(1,"esn:"+m_esnNum);
             emit signEsn(m_esnNum);
             this->createFile(m_esnNum.remove(" "));
         }
         else if(RevData.contains("esam serial number(S/N)"))
         {
             QString s= RevData;
             QStringList sl = s.split(":");
             m_esamNUM = sl[1].remove("\r\n");
             emit signEsam(m_esamNUM);
             this->addTestLog(1,"esam:"+m_esamNUM);
             this->createFile(m_esamNUM.remove(" "));
         }
         else if (RevData.contains("build_version="))
         {
              QString s = queryCfg(CheckItemIni,"compare","db1");
              QString ss = RevData;
              QStringList sl = ss.split("=");
              ss = sl[1].remove(" ").remove("\r\n");
              if(ss == s)
              {
                  this->addTestLog(1,"大包版本一致");
                  signTestItemRes("大包版本","合格");
                  addTestItem("升级大包", "大包版本", "合格");
              }
         }
         else if(RevData.startsWith("Proceed anyway? (y,N)")){
             m_serialport->write("y\r");
         }
         //QRegularExpression regex("\\b-1[46]\\b");
         if(RevData.startsWith("100") == true)
         {
             m_dbTestRes =true;
             this->addTestLog(1,"升级大包成功");
         }
         if(RevData.startsWith("-16")||RevData.startsWith("-14"))
         {
              this->addTestLog(1,"升级大包失败");
              emit signCurrentTestItem("升级大包失败");
         }
     });
     QTimer::singleShot(20000, [this](){
          if(m_ComConnect == false)
          {
              emit signState(false);
              emit signJBTestRes(m_NUM);
              //m_allRes<<"不合格";
              addTestItem("串口检测", "串口连接", "不合格");
              this->executeNextStep1(9);

          }
     });
}
void cTestLog::netConnect(QString ip)
{
    m_SshConCount++;
    m_NetConnect = false;
    this->addTestLog(1,"正在进行网口连接");
    emit signCurrentTestItem("正在网口连接");
    QSsh::SshConnectionParameters params;
    params.host = ip ;
    params.port = 8888;
    params.userName = "sysadm";
    params.password = "Zgdky@guest123";
    params.timeout = 10;
    //params.authenticationType = QSsh::SshConnectionParameters::AuthenticationByPassword;
    params.authenticationType = QSsh::SshConnectionParameters::AuthenticationTypePassword;
    m_sshSocket = new QSsh::SshConnection(params);
    connect(m_sshSocket, &QSsh::SshConnection::connected, this, &cTestLog::onSshConnected);
    connect(m_sshSocket, &QSsh::SshConnection::error, this, &cTestLog::onSshError);
    m_sshSocket->connectToHost();
}
void cTestLog::SendMsg(QByteArray s)
{
    m_shell = m_sshSocket->createRemoteShell();
    connect(m_shell.data(), SIGNAL(readyReadStandardOutput()), this, SLOT(OnReadyReadStandardOutput()));
    connect(m_shell.data(), SIGNAL(readAllStandardError()), this, SLOT(OnReadError()));
    m_shell->start();
}
void cTestLog::onSshConnected()
{
    if(m_sshSocket->state()==QSsh::SshConnection::Connected)
    {
        m_NetConnect = true;
        emit signState(true);
        this->addTestLog(1,"网口已连接");

        QString txzFile = queryCfg(CheckItemIni,"testitem","txzFile");
        QString lieceseFile = queryCfg(CheckItemIni,"testitem","licenseFile");
        QString AppFile = queryCfg(CheckItemIni,"testitem","installapp");
        QString dbFile = queryCfg(CheckItemIni,"testitem","updatadb");
        if(UpdateDbVersion == false)
        {
            this->addTestLog(1,"正在上传文件");
            if(txzFile=="1")
            {
                this->createFile(m_esnNum.remove(" "));
                this->createFile(m_esamNUM.remove(" "));
                m_uploadLocalPaths << "E:/scu/txzFile/"+ m_esnCodeNum;
                m_uploadRemotePaths << "/tmp/"+ m_esnCodeNum;
                m_uploadLocalPaths << "E:/scu/txzFile/"+ m_esamCodeNum;
                m_uploadRemotePaths << "/tmp/"+ m_esamCodeNum;
            }
            if(lieceseFile == "1")
            {
//                m_uploadLocalPaths << "E:/scu/license/"+m_esamNUM+".txt";
//                m_uploadRemotePaths << "/tmp/"+m_esamNUM+".txt";
            }
            if(queryCfg(CheckItemIni,"testitem","installapp")=="1")
            {
                this->addTestLog(1,"上传APP安装文件");
                QString appfile = queryCfg(CheckItemIni,"compare","appFile");
                QDir dir(appfile);
                QStringList folderList = dir.entryList();
                dir.setFilter(QDir::Dirs | QDir::NoDotAndDotDot);
                for (const QString &folder : folderList) {
                    if(folder == "."||folder == "..")
                    {
                        continue;
                    }
                    m_uploadLocalPaths << appfile+"/"+folder;
                    m_uploadRemotePaths << "/tmp/"+folder;
                }
            }
            this->m_currentUploadIndex = -1;
            startNextUpload();
        }
        else if(UpdateDbVersion == true)
        {
            m_uploadLocalPaths.clear();
            m_uploadRemotePaths.clear();
            QString s = queryCfg(CheckItemIni,"compare","dbFile");
            m_uploadLocalPaths << s;
            m_uploadRemotePaths << "/tmp/rootfs_rw_BM320.hpm";
            this->m_currentUploadIndex = -1;
            startNextUpload();
        }
    }
}
void cTestLog::onSshError(QSsh::SshError sshError)//ssh失败
{
    if( m_jnTestStart == false )
    {
        if(m_SshConCount <3)
        {
            this->netConnect(this->m_IP);
        }
        else {
            emit signJBTestRes(m_NUM);
            addTestItem("网络检测", "网口连接", "不合格");
            if(UpdateDbVersion == true)
            {
                if (m_dbtimer && m_dbtimer->isActive()) {
                    m_dbtimer->stop();
                    delete m_dbtimer;
                    m_dbtimer = nullptr;
                }
            }
            this->executeNextStep1(9);
        }
    }
   switch(sshError)
      {
      case QSsh::SshNoError:
       this->addTestLog(2,"sshConnectError SshNoError") ;
          break;
      case QSsh::SshSocketError:
       this->addTestLog(2, "sshConnectError SshSocketError" );
          break;
      case QSsh::SshTimeoutError:
        this->addTestLog(2, "sshConnectError SshTimeoutError" );
          break;
      case QSsh::SshProtocolError:
         this->addTestLog(2, "sshConnectError SshProtocolError") ;
          break;
      case QSsh::SshHostKeyError:
         this->addTestLog(2, "sshConnectError SshHostKeyError" );
          break;
      case QSsh::SshKeyFileError:
         this->addTestLog(2, "sshConnectError SshKeyFileError") ;
          break;
      case QSsh::SshAuthenticationError:
         this->addTestLog(2, "sshConnectError SshAuthenticationError") ;
          break;
      case QSsh::SshClosedByServerError:
         this->addTestLog(2, "sshConnectError SshClosedByServerError") ;
          break;
      case QSsh::SshInternalError:
         this->addTestLog(2, "sshConnectError SshInternalError");
         break;
      default:
          break;
      }
}
void cTestLog::executeNextStep()
{
    switch (m_currentStep)
    {
    case 0:
        m_serialport->write("devctl -e\r");

        break;
    case 1:
        m_serialport->write("devcfg -esam\r");
        break;
    case 2:
        if(queryCfg(CheckItemIni,"testitem","updatadb")=="1")
        {
            m_serialport->write("cat /etc/build_version\r");
        }
        break;
    case 3:
        m_serialport->write(QString("setip FE0:1 %1 --netmask ************* -gateway *************\n").arg(this->m_IP).toUtf8());
        break;
    case 4:
        if(UpdateDbVersion == false)
        {
            this->netConnect(this->m_IP);
        }
        else {
            m_dbtimer = new QTimer(this);
            connect(m_dbtimer, &QTimer::timeout, this, &cTestLog::dbvisionFalse);
            m_dbFalseStep=0;
            m_dbtimer->start(0);
//            this->dbvisionFalse();
            this->addTestLog(1,"升级大包中");
            emit signCurrentTestItem("升级大包");
        }
        break;
    }
    m_currentStep++;
    if (m_currentStep >= 5)
    {
        m_timer->stop();
    }
    else {
        m_timer->start(2000);
    }
}
void cTestLog::dbvisionFalse() //大包不合格测试项
{
    emit signCurrentTestItem("升级大包");
    switch (m_dbFalseStep)
    {
    case 0:
        m_serialport->write("cd /tmp\r");
        m_serialport->write("echo 'Zgdky@guest123' | sudo -S mkdir temp\r");
        m_serialport->write("sudo mount /dev/nvm_gold temp\r");
        m_serialport->write("cd temp/mnt/\r");
        m_serialport->write("sudo cp -r * /mnt\r");
        m_serialport->write("ls /mnt\r\n");
        m_serialport->write("cd /tmp\r\n");
        break;
    case 1:
        this->addTestLog(1,"上传第一次大包文件");
        this->netConnect(this->m_IP);
        break;
    case 2:
        m_sshSocket->disconnectFromHost();
        m_serialport->write("echo 'Zgdky@guest123' |sudo -S /opt/upgrade/upgrade /tmp/rootfs_rw_BM320.hpm\r\n");
        this->addTestLog(1,"开始第一次升级大包");
        break;
    case 3:
        this->addTestLog(1,"第一次升级大包完成");
        m_serialport->write("cat /data/upgrade/upgrading\r\n");
        m_serialport->waitForBytesWritten(2000);
        m_serialport->write("sudo reboot\r\n");
        break;
    case 4:
        if(this->m_dbTestRes == false)
        {
            signTestItemRes("第一次升级大包","不合格");
            addTestItem("升级大包", "第一次升级大包", "不合格");
        }
        else if(this->m_dbTestRes == true)
        {
            signTestItemRes("第一次升级大包","合格");
            addTestItem("升级大包", "第一次升级大包", "合格");
        }
        this->addTestLog(1,"设备重启");
        this->m_dbTestRes = false;
        break;
    case 5:
        this->addTestLog(1,"修改IP");
        m_serialport->write(QString("setip FE0:1 %1 --netmask ************* -gateway ************\n").arg(this->m_IP).toUtf8());
        break;
    case 6:
        this->addTestLog(1,"上传第二次大包文件");
        this->netConnect(this->m_IP);
        break;
    case 7:
        m_sshSocket->disconnectFromHost();
        m_serialport->write("echo 'Zgdky@guest123' |sudo -S /opt/upgrade/upgrade /tmp/rootfs_rw_BM320.hpm\r\n");
        this->addTestLog(1,"开始第二次升级大包");
        break;
    case 8:
        m_serialport->write("cat /data/upgrade/upgrading\r\n");
        this->addTestLog(1,"第二次升级大包完成");
        m_serialport->waitForBytesWritten(2000);
        m_serialport->write("sudo reboot\r\n");
    break;
    case 9:
        m_uploadLocalPaths.clear();
        m_uploadRemotePaths.clear();
        break;
    }
    m_dbFalseStep++;
    if (m_dbFalseStep >= 10)
    {
        this->addTestLog(1,"升级大包完成");
        m_dbtimer->stop();
        if(this->m_dbTestRes == false)
        {
            signTestItemRes("第二次升级大包","不合格");
            addTestItem("升级大包", "第二次升级大包", "不合格");
        }
        else if(this->m_dbTestRes == true)
        {
            signTestItemRes("第二次升级大包","合格");
            addTestItem("升级大包", "第二次升级大包", "合格");
        }
        m_serialport->write(QString("setip FE0:1 %1 --netmask ************* -gateway ************\n").arg(this->m_IP).toUtf8());
        UpdateDbVersion = false;
        this->netConnect(this->m_IP);
    }
    else if(m_dbFalseStep == 2 || m_dbFalseStep == 7){
        m_dbtimer->start(120000);
    }
    else if(m_dbFalseStep == 3 || m_dbFalseStep == 8){
        m_dbtimer->start(300000);
    }
    else if(m_dbFalseStep == 4 || m_dbFalseStep == 9){
        m_dbtimer->start(120000);
    }
    else if(m_dbFalseStep == 6){
        m_dbtimer->start(10000);
    }
    else  {
        m_dbtimer->start(2000);
    }
}

void cTestLog::OnReadyReadStandardOutput()  //ssh连接接收数据
{
   QByteArray outputByte = m_shell->readAll();
   QString s =  QString::fromUtf8(outputByte);
   this->signTestShell(s);
   this->SaveShellFile(outputByte);

   m_buffer.append(outputByte);
   processBuffer();

   m_nLx++;
   if(s.contains("sysadm@SCU:~$"))
   {
       if(m_nLx !=1)
       {
           return;
       }
       m_shell->write("\n");
       m_testItemStep = 1;
       this->executeNextStep1(1);

       this->m_nLx=2;
   }
   else if(s.contains("Password:"))
   {
      //m_shell->write("Zgdky@guest123\r\n");
   }
   emit sigRevdata(s);

}
void cTestLog::processBuffer()
{
    int newlineIndex = m_buffer.indexOf("sysadm@SCU:");
    while (newlineIndex != -1) {
        QByteArray line = m_buffer.left(newlineIndex);
        m_buffer.remove(0, newlineIndex +1);
        handleLine(line);
        newlineIndex = m_buffer.indexOf("sysadm@SCU:");
    }
}


void cTestLog::handleLine(const QByteArray &line)
{
    QString data = QString::fromUtf8(line);
    
    if(data.contains("ls /data/devinfo"))
    {
        if(data.contains("license"))
        {
            this->addTestLog(1,"license合格");
            signTestItemRes("license文件","合格");
            addTestItem("license文件", "license文件", "合格");
        }
        else {
            this->addTestLog(1,"license不合格");
            signTestItemRes("license文件","不合格");
            addTestItem("license文件", "license文件", "不合格");
        }
    }
    else if(data.contains("ls /data/back_up"))
    {
        if(data.contains(m_esamCodeNum))
        {
            this->addTestLog(1,"esam通行证合格");
            signTestItemRes("esam通行证","合格");
            addTestItem("通行证检测", "esam通行证", "合格");
        }
        else {
            this->addTestLog(1,"esam通行证不合格");
            signTestItemRes("esam通行证","不合格");
            addTestItem("通行证检测", "esam通行证", "不合格");
        }
    }
    else if(data.contains("ls /data/app/common"))
    {
        if(data.contains(m_esnCodeNum))
        {
            signTestItemRes("esn通行证","合格");
            this->addTestLog(1,"esn通行证合格");
            addTestItem("通行证检测", "esn通行证", "合格");
        }
        else {
            signTestItemRes("esn通行证","不合格");
            this->addTestLog(1,"esn通行证不合格");
            addTestItem("通行证检测", "esn通行证", "不合格");
        }
    }
    else if(data.contains("cat /etc/build_version"))
    {
        if(data.contains("build_version="))
        {
            emit signCurrentTestItem("版本对比");
            QStringList sldb2=data.split("=");
            QString Revdb2=sldb2[1].remove(" ").remove("\n").remove("\r");
            QString setdb2 = queryCfg(CheckItemIni,"compare","db1");
            this->addTestLog(1,"当前版本:"+Revdb2);
            this->addTestLog(1,"配置的版本号:"+setdb2);
            if(Revdb2 == setdb2)
            {
                this->addTestLog(1,"大包版本一致");
                addTestItem("版本对比", "大包版本1", "合格");
            }
            else
            {
                this->addTestLog(1,"大包版本不一致");
                addTestItem("版本对比", "大包版本1", "不合格");
                signTestItemRes("大包版本","不合格");
            }
        }
    }
    else if(data.contains("version -b"))
    {
        if (data.contains("software version"))
        {
                QStringList sldb1=data.split(":");
                QString Rev =sldb1[2].remove(" ").remove("\n").remove("\r");
                QString set = queryCfg(CheckItemIni,"compare","db2").remove(" ");
                this->addTestLog(1,"当前版本:"+Rev);
                this->addTestLog(1,"配置的版本号:"+set);
                if(Rev==set)
                {
                    this->addTestLog(1,"大包版本一致");
                    addTestItem("版本对比", "大包版本2", "合格");
                }
                else
                {
                    this->addTestLog(1,"大包版本不一致");
                    addTestItem("版本对比", "大包版本2", "不合格");
                }
         }
    }
    else if(data.contains("devcfg -mbn"))
    {
        if (data.contains("main board NO")) {
                    QStringList slzk=data.split(":");
                    QString Rev =slzk[2].remove(" ").remove("\n").remove("\r");
                    QString set = queryCfg(CheckItemIni,"compare","zk").remove(" ");
                    this->addTestLog(1,"当前版本:"+Rev);
                    this->addTestLog(1,"配置的版本号:"+set);
                    if(Rev == set)
                    {
                        this->addTestLog(1,"主控版本一致");
                        addTestItem("版本对比", "主控版本", "合格");
                    }
                    else
                    {
                        this->addTestLog(1,"主控版本不一致");
                        addTestItem("版本对比", "主控版本", "不合格");
                    }
                }
    }
    else if(data.contains("devctl -H"))
    {
        if (data.contains("hardware version")) {
                    QStringList sl=data.split(":");
                    QString Rev =sl[2].remove(" ").remove("\n").remove("\r");
                    QString set = queryCfg(CheckItemIni,"compare","yj").remove(" ");
                    this->addTestLog(1,"当前版本:"+Rev);
                    this->addTestLog(1,"配置的版本号:"+set);
                    if(Rev == set)
                    {
                        this->addTestLog(1,"硬件版本一致");
                        addTestItem("版本对比", "硬件版本", "合格");
                    }
                    else
                    {
                        this->addTestLog(1,"硬件版本不一致");
                        addTestItem("版本对比", "硬件版本", "不合格");
                    }
         }
    }
    else if(data.contains("verssion -k"))
    {
        if (data.contains("Kernel version")) {
                    QStringList slzk=data.split(":");
                    QString Rev =slzk[2].remove(" ").remove("\n").remove("\r");
                    QString set = queryCfg(CheckItemIni,"compare","bh").remove(" ");
                    this->addTestLog(1,"当前版本:"+Rev);
                    this->addTestLog(1,"配置的版本号:"+set);
                    if(Rev == set)
                    {
                        this->addTestLog(1,"备核版本一致");
                        addTestItem("版本对比", "备核版本", "合格");
                    }
                    else
                    {
                        this->addTestLog(1,"备核版本不一致");
                        addTestItem("版本对比", "备核版本", "不合格");
                    }
                }
    }
    else if(data.contains("version -k"))
    {
        if (data.contains("Kernel version")) {
                   QStringList sl=data.split(":");
                   QString Rev =sl[2].remove(" ").remove("\n").remove("\r");
                   QString set = queryCfg(CheckItemIni,"compare","nh").remove(" ");
                   this->addTestLog(1,"当前版本:"+Rev);
                   this->addTestLog(1,"配置的版本号:"+set);
                   if(Rev == set)
                   {
                       this->addTestLog(1,"内核版本一致");
                       addTestItem("版本对比", "内核版本", "合格");
                   }
                   else
                   {
                       this->addTestLog(1,"内核版本不一致");
                       addTestItem("版本对比", "内核版本", "不合格");
                   }
               }
    }
    else if(data.contains("docker -v"))
    {
        if (data.contains("Docker version")) {
                   QStringList slzk=data.split("build");
                   QString Rev =slzk[1].remove(" ").remove("\n").remove("\r");
                   QString set = queryCfg(CheckItemIni,"compare","rq").remove(" ");
                   this->addTestLog(1,"当前版本:"+Rev);
                   this->addTestLog(1,"配置的版本号:"+set);
                   if(Rev == set)
                   {
                       this->addTestLog(1,"容器版本一致");
                       addTestItem("版本对比", "容器版本", "合格");
                   }
                   else
                   {
                       this->addTestLog(1,"容器版本不一致");
                       addTestItem("版本对比", "容器版本", "不合格");
                   }
        }
    }
    else if(data.contains("cat /var/log/urdebug.log"))
    {
        if (data.contains("+++"))
        {
            QStringList slzk=data.split("+++");
            QString Rev =slzk[1].remove(" ").remove("\n").remove("\r");
            QString set = queryCfg(CheckItemIni,"compare","gj").remove(" ");
            this->addTestLog(1,"当前版本:"+Rev);
            this->addTestLog(1,"配置的版本号:"+set);
            if(Rev == set)
            {
                this->addTestLog(1,"关键系统文件版本一致");
                addTestItem("版本对比", "关键系统文件版本", "合格");
            }
            else
            {
                this->addTestLog(1,"关键系统文件版本不一致");
                addTestItem("版本对比", "关键系统文件版本", "不合格");
            }
        }
        else
        {
            this->addTestLog(1,"未检测到关键系统文件版本");
            addTestItem("版本对比", "关键系统文件版本", "不合格");
        }
    }
    else if(data.contains("cat /etc/os-version.yaml"))
    {
        if (data.contains("OS name"))
        {
            QStringList slzk=data.split(":");
            QString Rev =slzk[2].remove(" ").remove("\n").remove("\r");
            QString set = queryCfg(CheckItemIni,"compare","cz").remove(" ");
            this->addTestLog(1,"当前版本:"+Rev);
            this->addTestLog(1,"配置的版本号:"+set);
             if(Rev == set)
             {
                 this->addTestLog(1,"操作系统版本一致");
                 addTestItem("版本对比", "操作系统版本", "合格");
             }
             else
             {
                 this->addTestLog(1,"操作系统版本不一致");
                 addTestItem("版本对比", "操作系统版本", "不合格");
             }
        }
        else
        {
            this->addTestLog(1,"操作版本未查询到");
            addTestItem("版本对比", "操作系统版本", "不合格");
        }
    }
    else if(data.contains("container uninstall"))
    {
        this->uninstallcontainer(data);
    }
    else if(data.contains("container install")|| data.contains("container config c_master_pd")) {
        this->installcontainer(data);
    }
    else if(data.contains("container monitor"))
    {
        this->checkcontainer(data);
    }
    else if(data.contains("appm -i -c")){
        this->installapp(data);
    }
    else if(data.contains("appm -I -c")){
        emit signCurrentTestItem("APP运行状态");
        this->apprunning(data);
    }
    else if(data.contains("softwarectl install-component"))
    {
        if(data.contains("IotAgent") && data.contains("failed"))
        {
            this->addTestLog(1,"IotAgent安装失败");
            //m_allRes<<"不合格";
            addTestItem("容器安装", "IotAgent", "不合格");
            //m_containerMap["IotAgent安装"]=0;
        }
        else if(data.contains("IotAgent") && data.contains("success"))
        {
            this->addTestLog(1,"IotAgent安装成功");
            //m_allRes<<"合格";
            //m_containerMap["IotAgent安装"]=1;
            addTestItem("容器安装", "IotAgent", "合格");
        }
        else if(data.contains("ExecShell") && data.contains("failed"))
        {
            this->addTestLog(1,"ExecShell安装失败");
            //m_allRes<<"不合格";
            //m_containerMap["ExecShell安装"]=0;
            addTestItem("容器安装", "ExecShell", "不合格");
        }
        else if(data.contains("ExecShell") && data.contains("success"))
        {
            this->addTestLog(1,"ExecShell安装成功");
            //m_allRes<<"合格";
            //m_containerMap["ExecShell安装"]=1;
            addTestItem("容器安装", "ExecShell", "合格");
        }
    }
    else if(data.contains("speed 1152"))
    {

        if(data.count("1152") == 2)
        {
            this->addTestLog(1,"RECEVIED：115200");
            QString command = "echo -e -n '\\xFE\\xFE\\xFE\\xFE\\x68\\x09\\x00\\x02\\x00\\x00\\x0B\\xF2\\xFF\\xFF\\xFF\\xFF\\xFF\\xFF\\x00\\x00\\x00\\x00\\x68\\x0F\\x01\\x00\\x01\\x00\\x01\\x00\\x01\\x00\\xE5\\x16' > /dev/ttySS7\r\n";
            m_shell->write(command.toUtf8());
            signTestItemRes("清除蓝牙密码","合格");
            //m_AllResMap["清除密码"]=1;
            addTestItem("清除蓝牙密码", "清除蓝牙密码", "合格");
        }
//       QStringList sl = data.split("Password:");
//       //this->addTestLog(1,sl[1]);
//       if(sl[1].remove(" ").remove("\r\n")== "115200")
//       {

//       }
//       else if(sl[1]=="\r\n")
//       {

//       }
//       else if(sl[1].remove(" ").remove("\r\n") != "115200")
        else if(data.count("1152") == 1)
        {
            m_ClearBTcount --;
            this->addTestLog(1,"未收到115200");
            if(m_ClearBTcount > 0)
            {
                 m_shell->write("echo 'Zgdky@guest123' | sudo -S stty -F /dev/ttySS7 raw speed 115200 cs8 -cstopb parenb -parodd\r\n");
            }
            else if(m_ClearBTcount == 0){
                //signTestItemRes("清除蓝牙密码","不合格");
                //m_AllResMap["清除密码"] = 0;
                //m_allRes << "不合格";
            }
        }
    }
}
void cTestLog::uninstallcontainer(QString data)
{
    emit signCurrentTestItem("清除多余文件");
    if(data.contains("success"))
    {
        for (auto item :m_containList)
        {
            if(data.contains(item))
            {
                this->addTestLog(1,item+"卸载容器成功");
                addTestItem("清除文件", item+"卸载", "合格");
            }
        }
    }
    else if(data.contains("failed"))
    {
        for (auto item :m_containList)
        {
            if(data.contains(item))
            {
                this->addTestLog(2,item+"卸载容器失败");
                addTestItem("清除文件", item+"卸载", "不合格");
            }
        }
    }
    else if(data.contains("not exist"))
    {
        for (auto item :m_containList)
        {
            if(data.contains(item))
            {
                this->addTestLog(2,item+"容器未找到");
                addTestItem("清除文件", item+"卸载", "合格");
            }
        }
    }
    else if(data.contains("error"))
    {
        for (auto item :m_containList)
        {
            if(data.contains(item))
            {
                this->addTestLog(2,item+"卸载容器错误");
                addTestItem("清除文件", item+"卸载", "不合格");
            }
        }
    }
}

void cTestLog::installcontainer(QString data)
{
    emit signCurrentTestItem("安装容器");
    if (data.indexOf("success") != -1)
    {
        for (auto item:m_containList) {
            if(data.contains(item))
            {
                addTestItem("容器安装", item+"安装", "合格");
                this->addTestLog(1,item+"安装容器成功");
            }
        }
    }
    else if(data.contains("already exists"))
    {
        for (auto item:m_containList) {
            if(data.contains(item))
            {
                addTestItem("容器安装", item+"安装", "不合格");
                this->addTestLog(2,item+"容器已经存在，请重新安装");
            }
        }
    }
    else if(data.contains("failed"))
    {
        for (auto item:m_containList) {
            if(data.contains(item))
            {
                addTestItem("容器安装", item+"安装", "不合格");
                this->addTestLog(2,item+"安装容器失败");
            }
        }
    }
    else if(data.contains("error"))
    {
        for (auto item:m_containList) {
            if(data.contains(item))
            {
                addTestItem("容器安装", item+"安装", "不合格");
                this->addTestLog(2,item+"安装容器错误");
            }
        }
    }
}
void cTestLog::checkcontainer(QString data)
{
    emit signCurrentTestItem("安装容器");
    if(data.contains("-t memory 85") && data.contains("threshold:memory threshold set success"))
    {
        this->addTestLog(1,"设置成功memory");
    }
    else if(data.contains("-t cpu 85") && data.contains("threshold:cpu threshold set success"))
    {
        this->addTestLog(1,"设置成功cpu");
    }
    else if(data.contains("-t storage 85") && data.contains("threshold:storage threshold set success"))
    {
        this->addTestLog(1,"设置成功storage");
    }
    else if (data.contains("container name")) {
        QStringList s = data.split("\n");
        QStringList ss = s[2].split(":");
        if(s[3].contains("85%") && s[4].contains("85%") && s[5].contains("85%"))
        {
             QString st = ss[1].remove(",").remove("\n")+"阈值";
             addTestItem("容器安装", st, "合格");
             addTestLog(1,ss[1].remove(",").remove("\r").remove("\n") + "阈值合格"+","+s[3].remove("\n").remove("\r").remove(" ")+"  " + s[4].remove("\n").remove("\r").remove(" ")+"  " + s[5].remove("\n").remove("\r").remove(" ")+"\n");
        }
        else
        {
            QString st = ss[1].remove(",").remove("\n")+"阈值";
             addTestItem("容器安装", st, "不合格");
             addTestLog(2,ss[2].remove(",").remove("\n") + "阈值不合格，存在阈值不符合的项" );
             addTestLog(2,s[3].remove("\n") + s[4].remove("\n") + s[5].remove("\n"));
        }
    }
}
void cTestLog::installapp(QString data)
{
    emit signCurrentTestItem("安装app");
    if (data.contains("success"))
    {
        auto item = m_AppMap.begin();
        for (;item != m_AppMap.end() ; ++item)
        {
            if(data.contains(item.key()))
            {
                addTestItem("APP安装", item.key(), "合格");
                this->addTestLog(1,item.key()+" 安装APP成功");
            }
        }
    }
    else if(data.contains("failed"))
    {
        auto item = m_AppMap.begin();
        for (;item != m_AppMap.end() ; ++item)
        {
            if(data.contains(item.key()))
            {
                addTestItem("APP安装", item.key(), "不合格");
                this->addTestLog(2,item.key()+" 安装APP失败");
            }
        }
    }
    else if(data.contains("already exists"))
    {
        auto item = m_AppMap.begin();
        for (;item != m_AppMap.end() ; ++item)
        {
            if(data.contains(item.key()))
            {
                addTestItem("APP安装", item.key(), "不合格");
                this->addTestLog(2,item.key()+" APP已存在");
            }
        }
    }
    else if(data.contains("error"))
    {
        auto item = m_AppMap.begin();
        for (;item != m_AppMap.end() ; ++item)
        {
            if(data.contains(item.key()))
            {
                addTestItem("APP安装", item.key(), "不合格");
                this->addTestLog(2,item.key()+" 安装APP错误");
            }
        }
    }
}
void cTestLog::apprunning(QString data)
{
    if(data.contains("File is not exist.")){
         for (auto item :m_containList)
         {
             if(data.contains(item))
             {
                 this->addTestLog(2,item+"未运行,文件不存在");
                 addTestItem("APP运行", item, "不合格");
             }

         }
     }
     else if (data.indexOf("Total app number")!=-1)
     {
        QStringList list = data.split("App index");
        for(auto item :list)
        {
            if(item.contains("App name"))
            {
                QStringList list1 = item.split("\n");
                if(list1[7].contains("running"))
                {
                    QString s = list1[1].remove(" ").remove("\n").remove("\r");
                    QStringList sl = s.split(":");
                    addTestItem("APP运行", sl[1] , "合格");
                    this->addTestLog(1,list1[1].remove(" ").remove("\n").remove("\r")+" APP已运行，"+list1[2].remove(" ").remove("\n").remove("\r"));
                }
                else if (list1[7].contains("stop"))
                {
                    QString s = list1[1].remove(" ").remove("\n").remove("\r");
                     QStringList sl = s.split(":");
                    addTestItem("APP运行", sl[1] , "不合格");
                    this->addTestLog(2,list1[1].remove(" ").remove("\n").remove("\r")+" APP未运行，"+list1[2].remove(" ").remove("\n").remove("\r"));
                }
            }
        }
     }
}
void cTestLog::executeNextStep1(int n)  //检测项
{

   QSettings settings(CheckItemIni, QSettings::IniFormat);
   settings.setIniCodec(QTextCodec::codecForName("UTF-8"));
   settings.beginGroup("testitem");
   QStringList clist = settings.allKeys();
   m_testItemStep = n;
   switch (m_testItemStep) {
   case 0:
       m_testItemStep=1;
       this->executeNextStep1(1);
       break;
   case 1:
       if (queryCfg(CheckItemIni, "testitem","txzFile") == "1")
       {
           this->addTestLog(1,"---------------通行证检测---------------");
           m_shell->write("echo \"Zgdky@guest123\" | sudo -S mkdir /data/back_up/\r\n");
           m_shell->write("echo \"Zgdky@guest123\" | sudo -S chmod 777 /data/back_up/\r\n");
           m_shell->write(QString("sudo mv /tmp/74* /data/back_up/\r\n").toUtf8());
           m_shell->write("echo 'Zgdky@guest123' | sudo -S mkdir /data/app/common/\r\n");
           m_shell->write("sudo mv /tmp/23* /data/app/common/\r\n");
           m_shell->write("ls /data/back_up\r\n");
           m_shell->write("ls /data/app/common\r\n");
           m_testItemStep = 2;
       }
        QTimer::singleShot(10000, [this](){
             this->executeNextStep1(2);
        });
       break;

   case 2:
       if (queryCfg(CheckItemIni, "testitem", "licenseFile") == "1" && m_liceseFile==true)
       {
           this->addTestLog(1,"---------------License检测---------------");
           //     免验签
           m_shell->write("cd /data/\r");
           m_shell->write("sudo touch /data/license_not_verify\r");

           m_shell->write("sudo touch sign_verify_lic\r");


           // 验签
//           m_shell->write("echo 'Zgdky@guest123' | sudo -S mkdir -p /data/devinfo\r\n");
//           m_shell->write("cd /data/devinfo\r\n");
//           m_shell->write("sudo chmod 777 /data/devinfo/\r\n");
//           m_shell->write("echo 'Zgdky@guest123' | sudo -S rm -rf license\r\n");
//           m_shell->write("echo \"$(cat /tmp/"+m_esamNUM.toUtf8()+".txt)\" > license\r\n");
//           m_shell->write("sed -i 's/\\r$//' license\r\n");
//           m_shell->write("ls /data/devinfo \r\n");
       }
       QTimer::singleShot(10000, [this](){
            m_testItemStep = 3;
            this->executeNextStep1(3);
       });
       break;

   case 3:
       if (queryCfg(CheckItemIni, "testitem", "compareversion") == "1")
       {
           this->addTestLog(1,"---------------版本对比---------------");
           this->compareVersion();
       }
       else
       {
            m_testItemStep = 4;
            this->executeNextStep1(4);
       }
       break;

   case 4:
       if (queryCfg(CheckItemIni, "testitem", "clearFile") == "1" ) {
           this->addTestLog(1,"---------------清除文件检测---------------");
           this->readXml("clearFile");
           QTimer::singleShot(180000, [this](){
               this->executeNextStep1(5);
               
               // 检查清除文件测试结果
               bool clearFileAllPassed = true;
               for(const auto &item : m_currentTestResult.subItems)
               {
                   if(item.mainItem == "清除文件" && item.result == "不合格")
                   {
                       clearFileAllPassed = false;
                       break;
                   }
               }
               
               if(clearFileAllPassed)
               {
                   signTestItemRes("清除文件","合格");
                   addTestItem("清除多余文件", "清除文件检测", "合格");
               }
               else
               {
                   signTestItemRes("清除文件","不合格");
                   addTestItem("清除多余文件", "清除文件检测", "不合格");
               }
           });
       }
       else {
            m_testItemStep = 5;
            this->executeNextStep1(5);
       }
       break;
   case 5:
       if(queryCfg(CheckItemIni,"testitem","clearBTcode") =="1")
       {
           m_ClearBTcount = 2;
           this->addTestLog(1,"---------------清除蓝牙密码---------------");
           qint64 l = m_shell->write("echo 'Zgdky@guest123' | sudo -S stty -F /dev/ttySS7 raw speed 115200 cs8 -cstopb parenb -parodd\r\n");
           if(l != 0 )
           {
               this->addTestLog(1,"发送完成 ");
           }
           else {
               this->addTestLog(1,"发送失败");
           }
       }
       if (queryCfg(CheckItemIni, "testitem", "installcontianer") == "1") {
           QTimer::singleShot(10000, [this](){
               this->addTestLog(1,"---------------安装容器检测---------------");
               this->readXml("installcontianer");
           });


           QTimer::singleShot(180000, [this](){
               this->executeNextStep1(6);
               // 检查安装容器测试结果
               bool containerAllPassed = true;
               for(const auto &item : m_currentTestResult.subItems)
               {
                   if(item.mainItem == "容器安装" && item.result == "不合格")
                   {
                       containerAllPassed = false;
                       break;
                   }
               }
               
               if(containerAllPassed)
               {
                   signTestItemRes("安装容器","合格");
                   addTestItem("安装容器检测", "安装容器", "合格");
               }
               else
               {
                   signTestItemRes("安装容器","不合格");
                   addTestItem("安装容器检测", "安装容器", "不合格");
               }
           });
       }
       else {
           QTimer::singleShot(10000, [this](){
            m_testItemStep = 6;
            this->executeNextStep1(6);
           });
       }
       break;

   case 6:
       if (queryCfg(CheckItemIni, "testitem", "installapp") == "1") {
           this->addTestLog(1,"---------------安装APP检测---------------");
           this->readXml("installapp");
           QTimer::singleShot(180000, [this](){
               // 检查安装APP测试结果
               bool appInstallAllPassed = true;
               for(const auto &item : m_currentTestResult.subItems)
               {
                   if(item.mainItem == "APP安装" && item.result == "不合格")
                   {
                       appInstallAllPassed = false;
                       break;
                   }
               }
               
               if(appInstallAllPassed)
               {
                   signTestItemRes("安装APP","合格");
                   addTestItem("安装APP检测", "安装APP", "合格");
               }
               else
               {
                   signTestItemRes("安装APP","不合格");
                   addTestItem("安装APP检测", "安装APP", "不合格");
               }
               this->executeNextStep1(7);
           });
       }
       else
       {
            m_testItemStep = 7;
            this->executeNextStep1(7);
       }
       break;

   case 7:
       // 重置状态标志
       m_timeSyncCompleted = false;
       m_appRunningCompleted = false;
       m_timeSyncEnabled = (queryCfg(CheckItemIni,"testitem","SetDevTim") == "1");
       m_appRunningEnabled = (queryCfg(CheckItemIni, "testitem","apprunning") == "1");
       
       if(m_timeSyncEnabled)
       {
           this->addTestLog(1,"---------------时间同步---------------");
           m_shell->write("echo 'Zgdky@guest123' | sudo -S iptables -t nat -A PREROUTING -p tcp --dport 1883 -j DNAT --to-destination **********:1883\r\n");
           this->initMqttCon();
       }
       else
       {
           m_timeSyncCompleted = true; // 如果不需要时间同步，标记为已完成
       }
       
       if(m_appRunningEnabled) 
       {
           QTimer::singleShot(3000,[this]() {
               this->addTestLog(1,"---------------运行状态检测---------------");
               this->readXml("apprunning");
           });
           
           QTimer::singleShot(60000, [this](){
               // 检查APP运行状态测试结果
               bool appRunningAllPassed = true;
               for(const auto &item : m_currentTestResult.subItems)
               {
                   if(item.mainItem == "APP运行" && item.result == "不合格")
                   {
                       appRunningAllPassed = false;
                   }
               }
               
               if(appRunningAllPassed)
               {
                   signTestItemRes("APP运行状态","合格");
                   addTestItem("APP运行状态检测", "APP运行状态", "合格");
               }
               else
               {
                   signTestItemRes("APP运行状态","不合格");
                   addTestItem("APP运行状态检测", "APP运行状态", "不合格");
               }
               
               m_appRunningCompleted = true;
               
               // 检查是否可以进入下一步
               if(m_timeSyncCompleted)
               {
                   this->executeNextStep1(8);
               }
           });
       }
       else
       {
           m_appRunningCompleted = true; // 如果不需要APP运行状态检测，标记为已完成
       }
       
       // 如果两个测试都不需要执行，直接进入下一步
       if(!m_timeSyncEnabled && !m_appRunningEnabled)
       {
           m_testItemStep = 8;
           this->executeNextStep1(8);
       }
       break;

   case 8:
       if (queryCfg(CheckItemIni, "testitem", "jiaobiao") == "1") {
           this->addTestLog(1,"---------------校表检测---------------");
           this->JbTest();
       }
       else {
           m_testItemStep = 9;
           this->executeNextStep1(9);
       }
       break;
   default:
       QTimer::singleShot(10000,[this]() {
           //this->CreatExcel();
           
           // 完成测试结果并发送
           m_currentTestResult.esn = m_esnNum;
           m_currentTestResult.esam = m_esamNUM;
           
           emit signTableRes(m_currentTestResult);
           emit signTestResultData(m_currentTestResult);
           
           emit signCurrentTestItem("已完成");
           
           // 判断整体测试结果
           bool hasFailure = false;
           for(const auto &item : m_currentTestResult.subItems)
           {
               if(item.result == "不合格")
               {
                   hasFailure = true;
                   break;
               }
           }
           
           if(hasFailure)
           {
               emit signTestResult("不合格");
           }
           else
           {
               emit signTestResult("合格");
           }
           
           this->addTestLog(1,"---------------测试完成---------------------");
           if(m_serialport != nullptr)
           {
               m_serialport->close();
               m_serialport->disconnect();
               delete m_serialport;
               m_serialport = nullptr;
           }
           if (m_sshSocket != nullptr) {
               m_sshSocket->disconnectFromHost();
               delete m_sshSocket;
               m_sshSocket = nullptr;
           }
           if (m_channel != nullptr) {
               m_channel->closeChannel();
               m_channel = nullptr;
           }

       });
       break;
   }
}

void cTestLog::startNextUpload() {
    m_currentUploadIndex++;
    if (m_currentUploadIndex >= m_uploadLocalPaths.size()) {
        this->addTestLog(1,"所有文件上传完成");
        if(UpdateDbVersion == false)
        {
            this->SendMsg("");
        }
        return;
    }

    QString localPath = m_uploadLocalPaths.at(m_currentUploadIndex);
    QString remotePath = m_uploadRemotePaths.at(m_currentUploadIndex);

    m_channel = m_sshSocket->createSftpChannel();
         if (!m_channel) {
             this->addTestLog(1,"上传通道创建失败: " + localPath);
             startNextUpload();
             return;
         }
         emit signCurrentTestItem("正在进行文件上传");
         QThread::msleep(100);
         connect(m_channel.data(), &QSsh::SftpChannel::initialized, this, &cTestLog::onSftpChannelInitialized);
         connect(m_channel.data(), &QSsh::SftpChannel::finished, this, &cTestLog::onSftpJobFinished);
         this->addTestLog(1,"正在初始化上传通道: " + localPath);
         m_channel->initialize();

}

void cTestLog::initMqttCon()
{
    m_pclient = new QMqttClient();
    connect(m_pclient, &QMqttClient::stateChanged, this, &cTestLog::on_mqtt_StateChange);
    connect(m_pclient, &QMqttClient::messageReceived, this, &cTestLog::on_mqtt_mesReceived);
    if(m_pclient->state() != QMqttClient::Connected)
    {
        m_pclient->setHostname(this->m_IP);
        m_pclient->setPort(1883);
        m_pclient->setUsername("admin");
        m_pclient->setPassword("123456");
        m_pclient->connectToHost();
        int nLen = 0;
        while(nLen < 40)
        {
            QApplication::processEvents();
            QThread::msleep(300);
            ++nLen;
            if(m_pclient->state() == QMqttClient::Connected)
            {
                QApplication::processEvents();
                break;
            }
        }
    }
    if(m_pclient->state() != QMqttClient::Connected)
    {
        this->addTestLog(1,"连接MQTT失败");
    }

}

void cTestLog::on_mqtt_StateChange()
{
    if(m_pclient->state() == QMqttClient::Connected)
    {
        this->addTestLog(1,"MQTT连接成功");
        QDateTime curTime = QDateTime::currentDateTime();
        QString s= curTime.toString("yyyy-MM-ddTHH:mm:ss");
        QJsonObject json;
        json["token"] = 1;
        json["timestamp"] = "1";

        QJsonObject ob;
        ob["setTime"] = s;
        json["body"] = ob;

        QJsonDocument doc(json);
        QMqttTopicName topic = QString("puAmr/OS-system/JSON/request/setTime");

        m_pclient->publish(topic, doc.toJson());
        m_pclient->subscribe(QString("OS-system/puAmr/JSON/response/setTime"));
    }
    else if(m_pclient->state() == QMqttClient::Disconnected)
    {

    }
    else if(m_pclient->state() == QMqttClient::Connecting)
    {

    }
}

void cTestLog::on_mqtt_mesReceived(const QByteArray &message, const QMqttTopicName &topic)
{
    QByteArray msg = message.trimmed();
    this->addTestLog(1,msg);
    if(topic.name() == "OS-system/puAmr/JSON/response/setTime")
    {
        QJsonParseError error;
        QJsonDocument doc = QJsonDocument::fromJson(msg,&error);
        if(doc.isEmpty() || error.error != QJsonParseError::NoError)
        {
            return;
        }
        if(!doc.isObject())
        {
            return;
        }

        QJsonObject obj = doc.object();
        if(obj["statusCode"].toInt() == 0)
        {
            this->addTestLog(1,"时间同步成功");
            signTestItemRes("时间同步","合格");
            addTestItem("时间同步", "时间同步", "合格");
        }
        else
        {
            this->addTestLog(1,"时间同步失败");
            signTestItemRes("时间同步","不合格");
            addTestItem("时间同步", "时间同步", "不合格");
        }
        
        m_timeSyncCompleted = true;
        
        // 检查是否可以进入下一步（APP运行状态检测也完成了）
        if(m_appRunningCompleted)
        {
            this->executeNextStep1(8);
        }
    }
}

void cTestLog::onSftpChannelInitialized() {
    QString localPath = m_uploadLocalPaths.at(m_currentUploadIndex);
    QString remotePath = m_uploadRemotePaths.at(m_currentUploadIndex);
    QFile file(localPath);
    if (!file.exists()) {
        this->addTestLog(1,"本地文件不存在: " + localPath);

        startNextUpload();
        return;
    }
    QSsh::SftpJobId job = m_channel->uploadFile(localPath, remotePath, QSsh::SftpOverwriteExisting);
    if (job != QSsh::SftpInvalidJob) {
        this->addTestLog(1,"开始上传文件: " + localPath);
    } else {
        this->addTestLog(1,"上传任务创建失败: " + localPath);
        startNextUpload();
    }
}

void cTestLog::onSftpJobFinished(QSsh::SftpJobId job, const QString &error) {
    QString localPath = m_uploadLocalPaths.at(m_currentUploadIndex);
    if (error.isEmpty()) {
        this->addTestLog(1,"上传成功: " + localPath);
    }
    else
    {
        this->addTestLog(1,"上传失败: " + localPath + " - " + error);
    }
    m_channel->closeChannel();
    m_channel = nullptr;
    startNextUpload();
}


void cTestLog::createFile(QString Name) //创建通行证文件
{
   QString snNum = this->creatCodeEs(Name);
   m_esnCodeNum = this->creatCodeEs(m_esnNum);
   m_esamCodeNum = this->creatCodeEs(m_esamNUM);
   QFile file1("E:/scu/txzFile/"+snNum);
   if(file1.open(QIODevice::ReadWrite|QIODevice::Text))
   {
       if(file1.exists())
       {
           QString s = file1.fileName();
           this->addTestLog(1, file1.fileName()+" 文件创建成功");
       }
       file1.close();
   }
   else if(Name.isEmpty())
   {
        this->addTestLog(1,snNum+"文件创建失败");
   }
}
QString cTestLog::creatCodeEs(QString s) //通行证加密
{
   QMap<int,QString> infoMap;
   for (int i = 0x1;i <= 0xff; i++)
   {
       int n = (i >> 4) & 0xF; // 十六进制的高位
       int m = i & 0xF;        // 十六进制的低位

       if (i == 0x10)
       {
            infoMap[i] = "FFFF";
            continue;
       }
       switch (i % 0xf)
       {
              case 0x0:
              case 0x1:
              case 0x4:
              case 0x6:
              case 0x7:
              case 0x9:
              case 0xa:
              case 0xc:
              case 0xf:
                  if (m - 1 == -1)
                  {
                      infoMap[i] = QString::number(n - 1, 16).toUpper() + "FC$";
                  }
                  else
                  {
                      infoMap[i] = QString::number(n, 16).toUpper() + QString::number(m - 1, 16).toUpper() + "C$";
                  }
                  break;
              case 0x2:
              case 0x5:
              case 0xb:
              case 0xe:
                      if (m - 1 == -1)
                      {
                          infoMap[i] = QString::number(n - 1, 16).toUpper() + "FA#";
                      }
                      else
                      {
                          infoMap[i] = QString::number(n, 16).toUpper() + QString::number(m - 1, 16).toUpper() + "A#";
                      }
                      break;

              case 0x3:
              case 0x8:
              case 0xd:
                  if( m+2 == 17)
                  {
                      infoMap[i] = QString::number(n + 1, 16).toUpper() + "1MEG";
                  }
                  else
                  {
                      infoMap[i] = QString::number(n, 16).toUpper() + QString::number(m + 0x2, 16).toUpper() + "MEG";
                  }
                      break;

              default:
                      break;
              }
          }

          QMap<int,QString> infoMap1;
          for (int i = 0x1;i <= 0xF; i++)
          {
              int m = i % 10 ; //个位
              if(i%0xf == 0x0 || i%0xf == 0x1 || i%0xf == 0x4 || i%0xf == 0x6 || i%0xf == 0x7 || i%0xf == 0x9 || i%0xf == 0xa  || i%0xf == 0xf)
              {
                  infoMap1[i] = "0"+ QString::number(m-1) +"C$";
              }
              else if (i%0xf == 0x2 || i%0xf == 0x5)
              {
                      infoMap1[i] = "0" + QString::number(m-1) +"A#";
              }
              else if (i%0xf == 0x3 || i%0xf == 0x8) {
                  QString s = QString::number(m+2, 16).toUpper();
                  infoMap1[i] = "0" + s +"MEG";
              }
          }


          s = s.remove(" ");
          QString code ;
          for(int i = 0; i < s.size(); i = i+2 )
          {
              QRegularExpression r2("^[0-9][G-Z]$");
              QRegularExpression r1("^[G-Z][0-9A-Z]$");
              QString res = s.mid(i,2);
              QString res1 = s.mid(i,1);
              if(r1.match(res).hasMatch() || res == "00" )
              {
                  code += "0010";
              }
              else if (r2.match(res).hasMatch()) {

                  if(infoMap1.contains(res1.toInt()))
                  {
                      code += infoMap1[res1.toInt()];
                  }
              }
              else
              {
                  int key = res.toInt(nullptr, 16);
                  if (infoMap.contains(key))
                  {
                       code += infoMap[key];
                  }
              }
          }
   return  code;
}


void cTestLog::compareVersion()  //版本对比
{
   QByteArray dbVersion2="cat /etc/build_version\r\n";
   QByteArray daVersion1="version -b\r\n";
   QByteArray zkVersion="devcfg -mbn\r\n";
   QByteArray yjVersion="devctl -H\r\n";
   QByteArray nhVersion="version -k\r\n";
   QByteArray bhVersion="verssion -k\r\n";
   QByteArray rqVersion="docker -v\r\n";
   QByteArray gjVersion="cat /var/log/urdebug.log\r\n";
   QByteArray czVersion="cat /etc/os-version.yaml\r\n";
   m_shell->write(dbVersion2);
   m_shell->write(daVersion1);
   m_shell->write(zkVersion);
   m_shell->write(yjVersion);
   m_shell->write(nhVersion);
   m_shell->write(bhVersion);
   m_shell->write(rqVersion);
   m_shell->write(gjVersion);
   m_shell->write(czVersion);
   QTimer::singleShot(30000, [this](){
       bool versionPassed = true;
       for(const auto &item : m_currentTestResult.subItems)
       {
           if(item.subItem.contains("版本") && item.result == "不合格")
           {
               versionPassed = false;
               break;
           }
       }
       if(versionPassed)
       {
           emit signTestItemRes("校验系统和版本号","合格");
           addTestItem("校验系统和版本号", "校验系统和版本号", "合格");
       }
       else
       {
           emit signTestItemRes("校验系统和版本号","不合格");
           addTestItem("校验系统和版本号", "校验系统和版本号", "不合格");
       }

       this->executeNextStep1(4);
   });

}

void cTestLog::onSlotsTcpreadyRead()
{
   QByteArray RevData = tcpsocket->readAll();
   this->addTestLog(1,"收："+RevData.toHex());
   if(RevData[21] == '\x01'&& RevData[22]== '\x11'&&RevData[23]=='\x00')
   {
       if(RevData[17] == '\x90'&& RevData[18]== '\x00'&&RevData[19]=='\x02')
       {
           this->addTestLog(1,"A相、B相、C相校表完成");
           QThread::sleep(120);
           this->dyByteArray();
           addTestItem("校表检测", "ABC校表", "合格");
       }
       else if(RevData[17] == '\x90' && RevData[18] == '\x00' && RevData[19] == '\x09')
       {
           this->addTestLog(1,"N相电流校表完成");
           QThread::sleep(60);
           this->ndlByteArray();
           addTestItem("校表检测", "N相校表", "合格");
       }
       else
       {
           this->addTestLog(1,"未收到校表回复");
       }
   }
   if(RevData[21] == '\x01'&& RevData[22]== '\x11'&&RevData[23]=='\xff')
   {
       if(RevData[17] == '\x90'&& RevData[18]== '\x00'&&RevData[19]=='\x02')
       {
           this->addTestLog(1,"A相、B相、C相校表失败");
           emit signJBTestRes(m_NUM);
           m_jbTestRes = false;
           addTestItem("校表检测", "ABC校表", "不合格");
       }
       else if(RevData[17] == '\x90' && RevData[18] == '\x00' && RevData[19] == '\x09')
       {
           this->addTestLog(1,"N相电流校表失败");
           emit signTestItemRes("校表","不合格");
           m_jbTestRes = false;
           this->executeNextStep1(9);
           addTestItem("校表检测", "N相校表", "不合格");
       }
       else
       {
//           this->addTestLog(1,"未收到校表回复");
//           emit signTestItemRes("校表","不合格");
//           this->executeNextStep1(9);
       }
   }

   else if(RevData[17] == '\x20' && RevData[18] == '\x00' && RevData[19] == '\x02')
   {
       this->dyRev(RevData);
       this->dlByteArray();
   }
   else if(RevData[17] == '\x20' && RevData[18] == '\x01' && RevData[19] == '\x02')
   {
       this->dlRev(RevData);
       this->glysByteArray();
   }
   else if(RevData[17] == '\x20' && RevData[18] == '\x0a' && RevData[19] == '\x02')
   {
       //this->glysRev(RevData);
       emit signJBTestRes(m_NUM);
   }
   else if(RevData[17] == '\x20' && RevData[18] == '\x01' && RevData[19] == '\x04')
   {
       this->ndlRev(RevData);
   }
}

void cTestLog::JbTest()
{
   m_jnTestStart = true;
   QString openPort = "container config c_master_yx -p 9001:9001\r\n ";
   m_shell->write(openPort.toUtf8());
   connect(this,&cTestLog::sigRevdata,[&](QString RevData){
       if(RevData.indexOf("config container c_master_yx success")!=-1)
       {
                  emit signCurrentTestItem("校表");
                  this->addTestLog(1,"9001端口打开成功");
                  this->initTcpConnect();
       }
   });
}

void cTestLog::readXml(QString testitem)
{
   QString filepath;
   QString s = queryCfg(CheckItemIni,"currentpc","currentpc");
   QSettings  settings(CheckItemIni, QSettings::IniFormat);
   settings.setIniCodec(QTextCodec::codecForName("UTF-8"));
   settings.beginGroup("testpc");
   QStringList clist = settings.allKeys();
   for (int i =0 ;i<clist.size();i++) {
       QString m = queryCfg(CheckItemIni,"testpc",clist[i]);
       QStringList sl = m.split("@");
       if(sl[0]==s)
       {
           filepath = "E:/scu/xml/"+sl[1]+".xml";
           //filepath = "D:/2/scu/xml/"+sl[1]+".xml";
       }
   }
   QDomDocument doc;
   QFile file(filepath);
   if (!file.open(QIODevice::ReadOnly))
   {
       this->addTestLog(1,"文件未打开");
       return;
   }
   if (!doc.setContent(&file))
   {
       this->addTestLog(1,"文件错误");

     file.close();
     return;
   }
   file.close();

   QDomElement rootElement = doc.documentElement();//根节点
   QDomNode node = rootElement.firstChild();//子节点

   QDomNode installAppNode = rootElement.firstChildElement("installapp");
          if (installAppNode.isNull()) {
              qDebug() << "<installapp> node not found";
              return;
          }
          QDomNode child = installAppNode.firstChild();
          while (!child.isNull()) {
              if (child.isElement()) {
                  QDomElement element = child.toElement();
                  QString tagName = element.tagName();
                  m_AppMap[tagName]=1;
              }
              child = child.nextSibling();
          }


   while (node.isElement())
   {
     QDomElement e = node.toElement();
     QString s = queryCfg(CheckItemIni,"testitem",e.nodeName());
     if(e.nodeName()== testitem)
     {
         QDomNodeList list = e.childNodes();

         for(int i = 0 ;i < list.size();i++)
         {
           QDomNode tmpNode = list.at(i);
           QDomElement tmpEle = tmpNode.toElement();
           QString ss = queryCfg(CheckItemIni,"installcontainer" ,tmpEle.nodeName());
           QString ssapp = queryCfg(CheckItemIni,"installapp",tmpEle.nodeName());
           if(tmpEle.nodeName() == "uninstall")
           {
               m_shell->write(tmpEle.text().toUtf8()+"\r\n");
               QStringList sl = tmpEle.text().split(" ");
           }
           else if (tmpEle.nodeName() == "delete")
           {
               m_shell->waitForBytesWritten(1000);
               m_shell->write(tmpEle.text().toUtf8()+"\r\n");
           }
           else if(ss == "1")
           {
               m_shell->write(tmpEle.text().toUtf8()+"\r\n");
               if(m_testItemStep == 5 && tmpEle.nodeName()!="IotAgent" && tmpEle.nodeName()!="ExecShell")
               {
                   QByteArray memory = "container monitor -t memory 85 " + tmpEle.nodeName().toUtf8() +"\r\n";
                   QByteArray cpu ="container monitor -t cpu 85 "+tmpEle.nodeName().toUtf8() +"\r\n";
                   QByteArray storage ="container monitor -t storage 85 "+tmpEle.nodeName().toUtf8() +"\r\n";
                   QByteArray monitor ="container monitor -d "+tmpEle.nodeName().toUtf8() +"\r\n";
                   QByteArray status = "container status "+tmpEle.nodeName().toUtf8() +"\r\n";
                   m_shell->write(memory);
                   m_shell->write(cpu);
                   m_shell->write(storage);
                   m_shell->write(monitor);
               }
               //m_shell->write(status);
           }
           else if(ssapp == "1")
           {
               m_shell->write(tmpEle.text().toUtf8()+"\r\n");

           }
         }
     }
     node = node.nextSiblingElement();
   }
}



void cTestLog::addTestItem(const QString &mainItem, const QString &subItem, const QString &result)
{
    TestItemResult item;
    item.mainItem = mainItem;
    item.subItem = subItem;
    item.result = result;
    m_currentTestResult.subItems.append(item);
}


void cTestLog::initTcpConnect()
{
   this->addTestLog(1,"tcp正在连接中");
   jbTimer = new QTimer();
   connect(jbTimer,&QTimer::timeout,[this](){
       this->addTestLog(1,"tcp正在连接中");
       tcpsocket = new QTcpSocket();
       connect(tcpsocket , SIGNAL(stateChanged(QAbstractSocket::SocketState)), this, SLOT(onSlotstateChanged(QAbstractSocket::SocketState)));
       connect(tcpsocket, SIGNAL(readyRead()), this, SLOT(onSlotsTcpreadyRead()));
       tcpsocket ->connectToHost(this->m_IP, 9001);
       if(tcpConNum == 0)
       {
           jbTimer->stop();
           emit signJBTestRes(m_NUM);
           emit signTestItemRes("校表","不合格");
           //m_allRes<<"不合格";
           this->executeNextStep1(9);
       }
       else
       {
           tcpConNum--;
       }
   });
   jbTimer->start(3000);

}
void cTestLog::onSlotstateChanged(QAbstractSocket::SocketState state)
{
   if(state == QAbstractSocket::ConnectedState)
   {
       this->addTestLog(1,"tcp已连接");
       jbTimer->stop();
       this->jbByteArray();
       emit signCurrentTestItem("校表");
       this->addTestLog(1,"开始进行校表");
   }
   else if(state == QAbstractSocket::ListeningState){
       this->addTestLog(1,"tcp连接失败");
       //m_allRes<<"不合格"；
   }
}
void cTestLog::njbByteArray()  //n相校表发送
{
    if(tcpsocket == nullptr)
    {
        return;
    }
   this->addTestLog(1,"N相电流校表检测项");
   QThread::sleep(60);
   QByteArray jbn = QByteArray::fromHex("68 17 00 43 45 aa aa aa aa aa aa 00 5b 4f 05 01 01 90 00 09 00 00 ec cd 16");
   this->addTestLog(1,"开始N相电流校表");
   tcpsocket->write(jbn);
   this->addTestLog(1,"发送N相电流校表报文：68 17 00 43 45 aa aa aa aa aa aa 00 5b 4f 05 01 01 90 00 09 00 00 ec cd 16");
}

void cTestLog::jbByteArray() //ABC校表发送
{
   QByteArray jb = QByteArray::fromHex("68 17 00 43 45 AA AA AA AA AA AA 00 5B 4F 05 01 01 90 00 02 00 00 4A E4 16");
   tcpsocket->write(jb);
   this->addTestLog(1,"发送校表报文：68 17 00 43 45 AA AA AA AA AA AA 00 5B 4F 05 01 01 90 00 02 00 00 4A E4 16");
   tcpsocket->waitForReadyRead(2000);
}
void cTestLog::dyByteArray()  //发送读取电压
{
   QByteArray dy = QByteArray::fromHex("68 17 00 43 45 AA AA AA AA AA AA 00 5B 4F 05 01 00 20 00 02 00 00 E5 BE 16");
   tcpsocket->write(dy);
   this->addTestLog(1,"发送读取电压报文：68 17 00 43 45 AA AA AA AA AA AA 00 5B 4F 05 01 00 20 00 02 00 00 E5 BE 16");
}
void cTestLog::dlByteArray()  //发送读取电流
{
   QByteArray dl = QByteArray::fromHex("68 17 00 43 45 AA AA AA AA AA AA 00 5B 4F 05 01 00 20 01 02 00 00 5E A2 16 ");
   tcpsocket->write(dl);
   this->addTestLog(1,"发送读取电流报文：68 17 00 43 45 AA AA AA AA AA AA 00 5B 4F 05 01 00 20 01 02 00 00 5E A2 16");
}
void cTestLog::glysByteArray()  //发送读取功率因素
{
   QByteArray glys = QByteArray::fromHex("68 17 00 43 45 AA AA AA AA AA AA 00 5B 4F 05 01 00 20 0A 02 00 00 4B 62 16 ");
   tcpsocket->write(glys);
   //this->addTestLog(1,"发送读取功率因数报文：68 17 00 43 45 AA AA AA AA AA AA 00 5B 4F 05 01 00 20 0A 02 00 00 4B 62 16 ");
}
void cTestLog::ndlByteArray()  //发送读取N相电流
{
   QByteArray ndl = QByteArray::fromHex("68 17 00 43 45 AA AA AA AA AA AA 00 5B 4F 05 01 0B 20 01 04 00 00 A2 59 16 ");
   tcpsocket->write(ndl);
   this->addTestLog(1,"发送读取N相电流报文:68 17 00 43 45 AA AA AA AA AA AA 00 5B 4F 05 01 0B 20 01 04 00 00 A2 59 16");
}

void cTestLog::dyRev(QByteArray revdata)  //收到电压
{
    QString dy = queryCfg(CheckItemIni,"wcConfigure","dy");
    float dynum = dy.toFloat();
    bool ok;
    short nVa = revdata.mid(25, 2).toHex().toUShort(&ok, 16);
    short nVb = revdata.mid(28, 2).toHex().toUShort(&ok, 16);
    short nVc = revdata.mid(31, 2).toHex().toUShort(&ok, 16);

    float fVa = (float)qAbs(nVa) / 10;
    float fVb = (float)qAbs(nVb) / 10;
    float fVc = (float)qAbs(nVc) / 10;

    float VaPer = qAbs(fVa - VA)/VA * 100;
    float VbPer = qAbs(fVb - VB)/VB * 100;
    float VcPer = qAbs(fVc - VC)/VC * 100;
    bool dyResult = true;
    
    if(VaPer <= dynum)
    {
        this->addTestLog(1,QString("A相电压：%1,输出电压：%2，误差百分比：%3 %,校表合格").arg(fVa).arg(VA).arg(VaPer));
        addTestItem("校表检测", "A相电压", "合格");
    }
    else
    {
        this->addTestLog(1,QString("A相电压：%1,输出电压：%2,误差百分比：%3 %,校表不合格").arg(fVa).arg(VA).arg(VaPer));
        addTestItem("校表检测", "A相电压", "不合格");
        dyResult = false;
    }

    if(VbPer <= dynum)
    {
        this->addTestLog(1,QString("B相电压：%1,输出电压：%2,误差百分比：%3 %,校表合格").arg(fVb).arg(VB).arg(VbPer));
        addTestItem("校表检测", "B相电压", "合格");
    }
    else
    {
        this->addTestLog(1,QString("B相电压：%1,输出电压：%2,误差百分比：%3 %,校表不合格").arg(fVb).arg(VB).arg(VbPer));
        addTestItem("校表检测", "B相电压", "不合格");
        dyResult = false;
    }

    if(VcPer <= dynum)
    {
        this->addTestLog(1,QString("C相电压：%1,输出电压：%2,误差百分比：%3 %,校表合格").arg(fVc).arg(VC).arg(VcPer));
        addTestItem("校表检测", "C相电压", "合格");
    }
    else
    {
        this->addTestLog(1,QString("C相电压：%1,输出电压：%2,误差百分比：%3 %,校表不合格").arg(fVc).arg(VC).arg(VcPer));
        addTestItem("校表检测", "C相电压", "不合格");
        dyResult = false;
    }
    
    if(dyResult == true)
    {
        this->addTestLog(1,"电压合格");
    }
    else
    {
        m_jbTestRes = false;
        this->addTestLog(1,"电压不合格");
       // m_allRes<<"不合格";
    }
}

void cTestLog::dlRev(QByteArray revdata)  //收到电流
{
    QString dl = queryCfg(CheckItemIni,"wcConfigure","dl");
    float dlnum = dl.toFloat();
    bool ok;
    int nIa = revdata.mid(25, 4).toHex().toUInt(&ok, 16);
    int nIb = revdata.mid(30, 4).toHex().toUInt(&ok, 16);
    int nIc = revdata.mid(35, 4).toHex().toUInt(&ok, 16);

    float fIa =  (float)qAbs(nIa) / 1000;
    float fIb =  (float)qAbs(nIb) / 1000;
    float fIc =  (float)qAbs(nIc) / 1000;

    float IaPer = qAbs(fIa - IA)/IA * 100;
    float IbPer = qAbs(fIb - IB)/IB * 100;
    float IcPer = qAbs(fIc - IC)/IC * 100;

    bool dlResult = true;

    if(IaPer <= dlnum)
    {
        this->addTestLog(1,QString("A相电流：%1,输出电流：%2,误差百分比：%3 %,校表合格").arg(fIa).arg(IA).arg(IaPer));
        addTestItem("校表检测", "A相电流", "合格");
    }
    else
    {
        this->addTestLog(1,QString("A相电流：%1,输出电流：%2,误差百分比：%3 %,校表不合格").arg(fIa).arg(IA).arg(IaPer));
        addTestItem("校表检测", "A相电流", "不合格");
        dlResult = false;
    }

    if(IbPer <= dlnum)
    {
        this->addTestLog(1,QString("B相电流：%1,输出电流：%2,误差百分比：%3 %,校表合格").arg(fIb).arg(IB).arg(IbPer));
        addTestItem("校表检测", "B相电流", "合格");
    }
    else
    {
        this->addTestLog(1,QString("B相电流：%1,输出电流：%2,误差百分比：%3 %,校表不合格").arg(fIb).arg(IB).arg(IbPer));
        addTestItem("校表检测", "B相电流", "不合格");
        dlResult = false;
    }

    if(IcPer <= dlnum)
    {
        this->addTestLog(1,QString("C相电流：%1,输出电流：%2,误差百分比：%3 %,校表合格").arg(fIc).arg(IC).arg(IcPer));
        addTestItem("校表检测", "C相电流", "合格");
    }
    else
    {
        this->addTestLog(1,QString("C相电流：%1,输出电流：%2,误差百分比：%3 %,校表不合格").arg(fIc).arg(IC).arg(IcPer));
        addTestItem("校表检测", "C相电流", "不合格");
        dlResult = false;
    }
    
    if(dlResult == true)
    {
        this->addTestLog(1,"电流合格");
    }
    else
    {
        m_jbTestRes = false;
        this->addTestLog(1,"电流不合格");
        //m_allRes<<"不合格";
    }
}

void cTestLog::ndlRev(QByteArray revdata)  //收到N电流
{
    QString dl = queryCfg(CheckItemIni,"wcConfigure","dl");
    float dlnum = dl.toFloat();
    bool ok;
    int nI = revdata.mid(23, 4).toHex().toUInt(&ok, 16);
    float fI =  (float)qAbs(nI) / 1000;

    float IPer = qAbs(fI - IC)/IC * 100;

    if(IPer <= dlnum)
    {
        this->addTestLog(1,QString("N相电流：%1,输出电流：%2,误差百分比：%3 %,校表合格").arg(fI).arg(IC).arg(IPer));
        addTestItem("校表检测", "N相电流", "合格");
    }
    else
    {
        this->addTestLog(1,QString("N相电流：%1,输出电流：%2,误差百分比：%3 %,校表不合格").arg(fI).arg(IC).arg(IPer));
        addTestItem("校表检测", "N相电流", "不合格");
        m_jbTestRes = false;
        this->addTestLog(1,"N相电流不合格");
        //m_allRes<<"不合格";
    }
    bool appInstallAllPassed = true;
    for(const auto &item : m_currentTestResult.subItems)
    {
        if(item.mainItem == "校表检测" && item.result == "不合格")
        {
            appInstallAllPassed = false;
            break;
        }
    }

    if(appInstallAllPassed)
    {
        signTestItemRes("校表检测","合格");
        //addTestItem("校表检测", "校表检测", "合格");
    }
    else
    {
        signTestItemRes("校表检测","不合格");
        //addTestItem("校表检测", "校表检测", "不合格");
    }
    this->executeNextStep1(9);
}
void cTestLog::glysRev(QByteArray revdata)
{
    QString glys = queryCfg(CheckItemIni,"wcConfigure","glys");
    float glysnum = glys.toFloat();
    bool ok;
   short p = revdata.mid(25, 2).toHex().toUShort(&ok, 16);
   short pa = revdata.mid(28, 2).toHex().toUShort(&ok, 16);
   short pb = revdata.mid(31, 2).toHex().toUShort(&ok, 16);
   short pc = revdata.mid(34, 2).toHex().toUShort(&ok, 16);

   float fp = (float)qAbs(p)/1000;
   float fpa = (float)qAbs(pa) / 1000;
   float fpb = (float)qAbs(pb) / 1000;
   float fpc = (float)qAbs(pc) / 1000;

   float pPer = qAbs(fp - 0.5)/0.5 * 100;
   float paPer = qAbs(fpa - 0.5)/0.5 * 100;
   float pbPer = qAbs(fpb - 0.5)/0.5 * 100;
   float pcPer = qAbs(fpc - 0.5)/0.5 * 100;
   bool dyResult = true;

   if(pPer <= glysnum)
   {
       this->addTestLog(1,QString("总功率因数：%1,误差百分比：%2 %,校表合格").arg(fp).arg(pPer));
       //this->addTestItem(ROW++,"校表",QString("A相电压：%1").arg(fVa),1,"合格");
   }
   else
   {
       this->addTestLog(1,QString("总功率因数：%1,误差百分比：%2 %,校表不合格").arg(fp).arg(pPer));
       //this->addTestItem(ROW++,"校表",QString("A相电压：%1").arg(fVa),1,"不合格");
       dyResult = false;
   }

   if(paPer <= glysnum)
   {
       this->addTestLog(1,QString("A相功率因数：%1,误差百分比：%2 %,校表合格").arg(fpa).arg(paPer));
       //this->addTestItem(ROW++,"校表",QString("A相电压：%1").arg(fVa),1,"合格");
   }
   else
   {
       this->addTestLog(1,QString("A相功率因数：%1,误差百分比：%2 %,校表不合格").arg(fpa).arg(paPer));
       //this->addTestItem(ROW++,"校表",QString("A相电压：%1").arg(fVa),1,"不合格");
       dyResult = false;
   }

   if(pbPer <= glysnum)
   {
       this->addTestLog(1,QString("B相功率因数：%1,误差百分比：%2 %,校表合格").arg(fpb).arg(pbPer));
       //this->addTestItem(ROW++,"校表",QString("B相电压：%1").arg(fVb),1,"合格");
   }
   else
   {
       this->addTestLog(1,QString("B相功率因数：%1,误差百分比：%2 %,校表不合格").arg(fpb).arg(pbPer));
       //this->addTestItem(ROW++,"校表",QString("B相电压：%1").arg(fVb),1,"不合格");
       dyResult = false;
   }

   if(pcPer <= glysnum)
   {
       this->addTestLog(1,QString("C相功率因数：%1,误差百分比：%2 %,校表合格").arg(fpc).arg(pcPer));
       //this->addTestLog(1,QString("C相功率因数：%1,误差百分比：%2 %,校表合格").arg(fpc).arg(pcPer));
       //this->addTestItem(ROW++,"校表",QString("C相电压：%1").arg(fVc),1,"合格");
   }
   else
   {
       this->addTestLog(1,QString("C相功率因数：%1,误差百分比：%2 %,校表不合格").arg(fpc).arg(pcPer));
       //this->addTestItem(ROW++,"校表",QString("C相电压：%1").arg(fVc),1,"不合格");
       dyResult = false;
   }
   if(dyResult == true)
   {
       this->addTestLog(1,"功率因数合格");
   }
   else
   {
       m_jbTestRes = false;
       this->addTestLog(1,"功率因数不合格");
       //m_allRes<<"不合格";
   }
}

void cTestLog::CreatExcel() {

//    QString spc = queryCfg(CheckItemIni,"currentpc","currentpc");

//    QString filePath = "E:/scu/log/"+spc+"/"+m_esnNum+"/测试结果.xlsx";// 检查文件是否存在
//    //QString filePath = "C:/scu/测试结果.xlsx";
//    bool fileExists = QFile::exists(filePath);

//    // 创建Excel应用程序对象
//    QAxObject *excel = new QAxObject("Excel.Application");
//    excel->setProperty("Visible", false); // 不可见
//    excel->setProperty("DisplayAlerts", false);

//    QAxObject *workbooks = excel->querySubObject("Workbooks");
//    QAxObject *workbook;

//    try {
//        if (fileExists) {
//            workbook = workbooks->querySubObject("Open(const QString&)", filePath);

//        } else {
//            workbook = workbooks->querySubObject("Add");
//            this->addTestLog(1,filePath);
//        }

//        QAxObject *sheets = workbook->querySubObject("Sheets");
//        QAxObject *sheet = sheets->querySubObject("Item(int)", 1);

//        QAxObject *usedRange = sheet->querySubObject("UsedRange");
//        QAxObject *rows = usedRange->querySubObject("Rows");
//        int lastRow = rows->property("Count").toInt();

//        int row = lastRow + 1;

//        row = 2;
//        int i = 1;
//        for ( auto iter =m_AllResMap.begin(); iter != m_AllResMap.end();++iter) {
//            QAxObject *cell2 = sheet->querySubObject("Cells(int,int)", 1, 1);
//            cell2->setProperty("Value2", "测试大项");
//            delete cell2;

//            QAxObject *cell3 = sheet->querySubObject("Cells(int,int)", 1, 2);
//            cell3->setProperty("Value2", "测试结果");
//            delete cell3;

//            QAxObject *cell = sheet->querySubObject("Cells(int,int)", row, 1);
//            cell->setProperty("Value2", iter.key());
//            delete cell;
//            QAxObject *cell1 = sheet->querySubObject("Cells(int,int)", row++, 2);
//            if(iter.value()==0)
//            {
//                cell1->setProperty("Value2", "不合格");
//            }
//            else
//            {
//                cell1->setProperty("Value2", "合格");
//            }
//            delete cell1;
//            qDebug()<<iter.key()<<iter.value();
//        }
//        row=2;
//        i= i+2;
//        for(auto iter =m_VersionMap.begin(); iter != m_VersionMap.end();++iter) {
//                QAxObject *cell2 = sheet->querySubObject("Cells(int,int)", 1, i);
//                cell2->setProperty("Value2", "对比版本测试项");
//                delete cell2;
//                QAxObject *cell3 = sheet->querySubObject("Cells(int,int)", 1, i+1);
//                cell3->setProperty("Value2", "测试结果");
//                delete cell3;

//                QAxObject *cell = sheet->querySubObject("Cells(int,int)", row, i);
//                cell->setProperty("Value2", iter.key());
//                delete cell;
//                QAxObject *cell1 = sheet->querySubObject("Cells(int,int)", row++, i+1);
//                if(iter.value()==0)
//                {
//                    cell1->setProperty("Value2", "不合格");
//                }
//                else
//                {
//                    cell1->setProperty("Value2", "合格");
//                }
//                delete cell1;
//                qDebug()<<iter.key()<<iter.value();
//            }
//             row = 2;
//             i=i+2;

//             for ( auto iter =m_clearResMap.begin(); iter != m_clearResMap.end();++iter) {

//                 QAxObject *cell2 = sheet->querySubObject("Cells(int,int)", 1, i);
//                 cell2->setProperty("Value2", "清除文件检测项");
//                 delete cell2;
//                 QAxObject *cell3 = sheet->querySubObject("Cells(int,int)", 1, i+1);
//                 cell3->setProperty("Value2", "测试结果");
//                 delete cell3;
//                 QAxObject *cell = sheet->querySubObject("Cells(int,int)", row, i);
//                 cell->setProperty("Value2", iter.key());
//                 delete cell;
//                 QAxObject *cell1 = sheet->querySubObject("Cells(int,int)", row++, i+1);
//                 if(iter.value()==0)
//                 {
//                     cell1->setProperty("Value2", "不合格");
//                 }
//                 else
//                 {
//                     cell1->setProperty("Value2", "合格");
//                 }
//                 delete cell1;
//             }

//             i=i+2;
//             row=2;

//             for ( auto iter =m_containerMap.begin(); iter != m_containerMap.end();++iter) {
//                 QAxObject *cell2 = sheet->querySubObject("Cells(int,int)", 1 ,i);
//                 cell2->setProperty("Value2", "安装容器检测项");
//                 delete cell2;

//                 QAxObject *cell3 = sheet->querySubObject("Cells(int,int)", 1, i+1);
//                 cell3->setProperty("Value2", "测试结果");
//                 delete cell3;

//                 QAxObject *cell = sheet->querySubObject("Cells(int,int)", row, i);
//                 cell->setProperty("Value2", iter.key());
//                 delete cell;
//                 QAxObject *cell1 = sheet->querySubObject("Cells(int,int)", row++, i+1);
//                 if(iter.value()==0)
//                 {
//                     cell1->setProperty("Value2", "不合格");
//                 }
//                 else
//                 {
//                     cell1->setProperty("Value2", "合格");
//                 }
//                 delete cell1;
//             }



//             i=i+2;
//             row=2;

//             for ( auto iter =m_appinstallMap.begin(); iter != m_appinstallMap.end();++iter) {
//                 QAxObject *cell2 = sheet->querySubObject("Cells(int,int)", 1, i);
//                 cell2->setProperty("Value2", "安装APP检测项");
//                 delete cell2;
//                 QAxObject *cell3 = sheet->querySubObject("Cells(int,int)", 1, i+1);
//                 cell3->setProperty("Value2", "测试结果");
//                 delete cell3;

//                 QAxObject *cell = sheet->querySubObject("Cells(int,int)", row, i);
//                 cell->setProperty("Value2", iter.key());
//                 delete cell;
//                 QAxObject *cell1 = sheet->querySubObject("Cells(int,int)", row++, i+1);
//                 if(iter.value()==0)
//                 {
//                     cell1->setProperty("Value2", "不合格");
//                 }
//                 else
//                 {
//                     cell1->setProperty("Value2", "合格");
//                 }
//                 delete cell1;
//             }


//             i=i+2;
//             row=2;

//             for ( auto iter =m_appRuningMap.begin(); iter != m_appRuningMap.end();++iter) {
//                 QAxObject *cell2 = sheet->querySubObject("Cells(int,int)",1, i);
//                 cell2->setProperty("Value2", "APP运行状态检测项");
//                 delete cell2;
//                 QAxObject *cell3 = sheet->querySubObject("Cells(int,int)", 1, i+1);
//                 cell3->setProperty("Value2", "测试结果");
//                 delete cell3;

//                 QAxObject *cell = sheet->querySubObject("Cells(int,int)", row, i);
//                 cell->setProperty("Value2", iter.key());
//                 delete cell;
//                 QAxObject *cell1 = sheet->querySubObject("Cells(int,int)", row++, i+1);
//                 if(iter.value()==0)
//                 {
//                     cell1->setProperty("Value2", "不合格");
//                 }
//                 else
//                 {
//                     cell1->setProperty("Value2", "合格");
//                 }
//                 delete cell1;
//             }


//             i=i+2;
//             row=2;


//         for ( auto iter =m_jbMap.begin(); iter != m_jbMap.end();++iter) {
//             QAxObject *cell2 = sheet->querySubObject("Cells(int,int)", 1, i);
//             cell2->setProperty("Value2", "校表");
//              delete cell2;
//             QAxObject *cell3 = sheet->querySubObject("Cells(int,int)", 1, i+1);
//             cell3->setProperty("Value2", "测试结果");
//             delete cell3;

//             QAxObject *cell = sheet->querySubObject("Cells(int,int)", row, i);
//             cell->setProperty("Value2", iter.key());
//             delete cell;
//             QAxObject *cell1 = sheet->querySubObject("Cells(int,int)", row++, i+1);
//             if(iter.value()==0)
//             {
//                 cell1->setProperty("Value2", "不合格");
//             }
//             else
//             {
//                 cell1->setProperty("Value2", "合格");
//             }
//             delete cell1;
//         }
//        // 保存文件（直接覆盖）
//        workbook->dynamicCall("SaveAs(const QString&)", filePath);

//        // 释放资源
//        delete sheet;
//        delete sheets;
//        workbook->dynamicCall("Close()");
//        delete workbook;
//    } catch (...) {

//    }

//    // 彻底退出Excel
//    excel->dynamicCall("Quit()");
//    delete workbooks;
//    delete excel;
//    this->addTestLog(1,"导出测试文件");
}

#ifndef CSHISTORYINFO_H
#define CSHISTORYINFO_H

#include <QWidget>
#include "database.h"

namespace Ui {
class csHistoryInfo;
}

class csHistoryInfo : public QWidget
{
    Q_OBJECT

public:
    explicit csHistoryInfo(QWidget *parent = nullptr);
    ~csHistoryInfo();

private slots:
    void onBatchChanged(const QString &batchName);

private:
    Ui::csHistoryInfo *ui;
    Database *m_database;
    
    void loadHistoryData();
    void updateTableWithBatchData(const QString &batchName);
    void loadAllBatches();
};

#endif // CSHISTORYINFO_H

<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>CGntParamdialog</class>
 <widget class="QDialog" name="CGntParamdialog">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>1244</width>
    <height>1100</height>
   </rect>
  </property>
  <property name="font">
   <font>
    <pointsize>12</pointsize>
   </font>
  </property>
  <property name="windowTitle">
   <string>参数配置</string>
  </property>
  <layout class="QGridLayout" name="gridLayout_28">
   <item row="0" column="0">
    <widget class="QTabWidget" name="tabWidget">
     <property name="font">
      <font>
       <pointsize>12</pointsize>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">QTabBar::tab {min-width:150px;min-height:30px;color: black;border: 2px solid;border-top-left-radius: 10px;border-top-right-radius: 10px;padding:5px;font: 12pt &quot;Agency FB&quot;;}
QTabBar::tab:!selected {margin-top: 5px;color: rgb(0, 0, 0);}
QTabBar::tab:selected {background-color: rgb(0, 255, 255);}
</string>
     </property>
     <property name="currentIndex">
      <number>0</number>
     </property>
     <widget class="QWidget" name="tab_2">
      <attribute name="title">
       <string>检查项</string>
      </attribute>
      <layout class="QGridLayout" name="gridLayout_4">
       <item row="0" column="0">
        <layout class="QHBoxLayout" name="horizontalLayout_18">
         <item>
          <layout class="QVBoxLayout" name="verticalLayout_8">
           <item>
            <layout class="QHBoxLayout" name="horizontalLayout">
             <item>
              <spacer name="horizontalSpacer_14">
               <property name="orientation">
                <enum>Qt::Horizontal</enum>
               </property>
               <property name="sizeHint" stdset="0">
                <size>
                 <width>40</width>
                 <height>20</height>
                </size>
               </property>
              </spacer>
             </item>
             <item>
              <widget class="QPushButton" name="allBtn">
               <property name="styleSheet">
                <string notr="true">    QPushButton {background-color:#0089ff ;
    color: white;
    border-style: outset;
    border-width: 2px;
    border-radius: 10px;
    border-color: beige;
    font-size: 26px;
    padding: 6px;}

QPushButton:hover {background-color: #0072C6;         
                     }
</string>
               </property>
               <property name="text">
                <string>全选</string>
               </property>
              </widget>
             </item>
             <item>
              <widget class="QPushButton" name="noallBtn">
               <property name="styleSheet">
                <string notr="true">    QPushButton {background-color:#0089ff ;
    color: white;
    border-style: outset;
    border-width: 2px;
    border-radius: 10px;
    border-color: beige;
    font-size: 26px;
    padding: 6px;}

QPushButton:hover {background-color: #0072C6;         
                     }
</string>
               </property>
               <property name="text">
                <string>反选</string>
               </property>
              </widget>
             </item>
             <item>
              <widget class="QPushButton" name="chkitemOk">
               <property name="styleSheet">
                <string notr="true">    QPushButton {background-color:#0089ff ;
    color: white;
    border-style: outset;
    border-width: 2px;
    border-radius: 10px;
    border-color: beige;
    font-size: 26px;
    padding: 6px;}

QPushButton:hover {background-color: #0072C6;         
                     }
</string>
               </property>
               <property name="text">
                <string>保存</string>
               </property>
              </widget>
             </item>
            </layout>
           </item>
           <item>
            <widget class="QTableWidget" name="chkItemTW"/>
           </item>
          </layout>
         </item>
         <item>
          <layout class="QVBoxLayout" name="verticalLayout_9">
           <item>
            <widget class="QLabel" name="label_23">
             <property name="minimumSize">
              <size>
               <width>0</width>
               <height>42</height>
              </size>
             </property>
             <property name="font">
              <font>
               <pointsize>12</pointsize>
              </font>
             </property>
             <property name="text">
              <string>已选择检查项</string>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QListWidget" name="itemLW"/>
           </item>
          </layout>
         </item>
        </layout>
       </item>
      </layout>
     </widget>
     <widget class="QWidget" name="tab">
      <attribute name="title">
       <string>容器-APP</string>
      </attribute>
      <layout class="QGridLayout" name="gridLayout_17">
       <item row="0" column="0">
        <layout class="QHBoxLayout" name="horizontalLayout_3">
         <property name="topMargin">
          <number>9</number>
         </property>
         <property name="bottomMargin">
          <number>9</number>
         </property>
         <item>
          <layout class="QGridLayout" name="gridLayout_16">
           <property name="rightMargin">
            <number>0</number>
           </property>
           <property name="horizontalSpacing">
            <number>12</number>
           </property>
           <property name="verticalSpacing">
            <number>9</number>
           </property>
           <item row="0" column="0">
            <widget class="QLabel" name="label_3">
             <property name="font">
              <font>
               <pointsize>12</pointsize>
              </font>
             </property>
             <property name="text">
              <string>容器个数</string>
             </property>
            </widget>
           </item>
           <item row="0" column="1">
            <widget class="QLabel" name="rqNumLb">
             <property name="font">
              <font>
               <pointsize>12</pointsize>
              </font>
             </property>
             <property name="styleSheet">
              <string notr="true">color: rgb(255, 0, 0);</string>
             </property>
             <property name="text">
              <string>0</string>
             </property>
            </widget>
           </item>
           <item row="1" column="0">
            <widget class="QLabel" name="label_5">
             <property name="font">
              <font>
               <pointsize>12</pointsize>
              </font>
             </property>
             <property name="text">
              <string>容器外APP个数</string>
             </property>
            </widget>
           </item>
           <item row="1" column="1">
            <widget class="QLabel" name="rqwAppNumLb">
             <property name="font">
              <font>
               <pointsize>12</pointsize>
              </font>
             </property>
             <property name="styleSheet">
              <string notr="true">color: rgb(255, 0, 0);</string>
             </property>
             <property name="text">
              <string>0</string>
             </property>
            </widget>
           </item>
          </layout>
         </item>
         <item>
          <widget class="QLabel" name="label_25">
           <property name="minimumSize">
            <size>
             <width>50</width>
             <height>0</height>
            </size>
           </property>
           <property name="text">
            <string/>
           </property>
          </widget>
         </item>
         <item>
          <layout class="QGridLayout" name="gridLayout_15">
           <property name="horizontalSpacing">
            <number>12</number>
           </property>
           <property name="verticalSpacing">
            <number>9</number>
           </property>
           <item row="0" column="0">
            <widget class="QLabel" name="label_4">
             <property name="font">
              <font>
               <pointsize>12</pointsize>
              </font>
             </property>
             <property name="text">
              <string>容器内APP总个数</string>
             </property>
            </widget>
           </item>
           <item row="0" column="1">
            <widget class="QLabel" name="rqAppNumLb">
             <property name="font">
              <font>
               <pointsize>12</pointsize>
              </font>
             </property>
             <property name="styleSheet">
              <string notr="true">color: rgb(255, 0, 0);</string>
             </property>
             <property name="text">
              <string>0</string>
             </property>
            </widget>
           </item>
           <item row="1" column="0">
            <widget class="QLabel" name="label_6">
             <property name="font">
              <font>
               <pointsize>12</pointsize>
              </font>
             </property>
             <property name="text">
              <string>APP总个数</string>
             </property>
            </widget>
           </item>
           <item row="1" column="1">
            <widget class="QLabel" name="AppNumLb">
             <property name="font">
              <font>
               <pointsize>12</pointsize>
              </font>
             </property>
             <property name="styleSheet">
              <string notr="true">color: rgb(255, 0, 0);</string>
             </property>
             <property name="text">
              <string>0</string>
             </property>
            </widget>
           </item>
          </layout>
         </item>
         <item>
          <spacer name="horizontalSpacer_3">
           <property name="orientation">
            <enum>Qt::Horizontal</enum>
           </property>
           <property name="sizeHint" stdset="0">
            <size>
             <width>40</width>
             <height>20</height>
            </size>
           </property>
          </spacer>
         </item>
        </layout>
       </item>
       <item row="0" column="1" rowspan="3">
        <widget class="Line" name="line">
         <property name="orientation">
          <enum>Qt::Vertical</enum>
         </property>
        </widget>
       </item>
       <item row="0" column="2">
        <layout class="QVBoxLayout" name="verticalLayout_2"/>
       </item>
       <item row="1" column="0">
        <layout class="QHBoxLayout" name="horizontalLayout_6">
         <item>
          <layout class="QVBoxLayout" name="verticalLayout_11">
           <item>
            <layout class="QHBoxLayout" name="horizontalLayout_2">
             <item>
              <widget class="QLabel" name="label">
               <property name="font">
                <font>
                 <pointsize>12</pointsize>
                </font>
               </property>
               <property name="text">
                <string>容器及APP选择</string>
               </property>
              </widget>
             </item>
             <item>
              <spacer name="horizontalSpacer">
               <property name="orientation">
                <enum>Qt::Horizontal</enum>
               </property>
               <property name="sizeHint" stdset="0">
                <size>
                 <width>40</width>
                 <height>20</height>
                </size>
               </property>
              </spacer>
             </item>
             <item>
              <widget class="QPushButton" name="allXZBtn">
               <property name="styleSheet">
                <string notr="true">    QPushButton {background-color:#0089ff ;
    color: white;
    border-style: outset;
    border-width: 2px;
    border-radius: 10px;
    border-color: beige;
    font-size: 26px;
    padding: 6px;}

QPushButton:hover {background-color: #0072C6;         
                     }</string>
               </property>
               <property name="text">
                <string>全选</string>
               </property>
              </widget>
             </item>
             <item>
              <widget class="QPushButton" name="allnotBtn">
               <property name="styleSheet">
                <string notr="true">    QPushButton {background-color:#0089ff ;
    color: white;
    border-style: outset;
    border-width: 2px;
    border-radius: 10px;
    border-color: beige;
    font-size: 26px;
    padding: 6px;}

QPushButton:hover {background-color: #0072C6;         
                     }</string>
               </property>
               <property name="text">
                <string>反选</string>
               </property>
              </widget>
             </item>
             <item>
              <widget class="QPushButton" name="rqOk">
               <property name="styleSheet">
                <string notr="true">    QPushButton {background-color:#0089ff ;
    color: white;
    border-style: outset;
    border-width: 2px;
    border-radius: 10px;
    border-color: beige;
    font-size: 26px;
    padding: 6px;}

QPushButton:hover {background-color: #0072C6;         
                     }
</string>
               </property>
               <property name="text">
                <string>保存</string>
               </property>
              </widget>
             </item>
            </layout>
           </item>
           <item>
            <widget class="QTreeWidget" name="rqTW">
             <property name="font">
              <font>
               <pointsize>12</pointsize>
              </font>
             </property>
             <property name="columnCount">
              <number>0</number>
             </property>
            </widget>
           </item>
          </layout>
         </item>
         <item>
          <layout class="QVBoxLayout" name="verticalLayout_10">
           <item>
            <widget class="QLabel" name="label_24">
             <property name="minimumSize">
              <size>
               <width>0</width>
               <height>40</height>
              </size>
             </property>
             <property name="font">
              <font>
               <pointsize>12</pointsize>
              </font>
             </property>
             <property name="text">
              <string>已选择容器及APP</string>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QTreeWidget" name="rqTW_2">
             <property name="font">
              <font>
               <pointsize>12</pointsize>
              </font>
             </property>
             <property name="columnCount">
              <number>0</number>
             </property>
            </widget>
           </item>
          </layout>
         </item>
        </layout>
       </item>
       <item row="2" column="0">
        <layout class="QHBoxLayout" name="horizontalLayout_20">
         <item>
          <widget class="QGroupBox" name="groupBox">
           <property name="sizePolicy">
            <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
             <horstretch>0</horstretch>
             <verstretch>0</verstretch>
            </sizepolicy>
           </property>
           <property name="minimumSize">
            <size>
             <width>0</width>
             <height>300</height>
            </size>
           </property>
           <property name="maximumSize">
            <size>
             <width>16777215</width>
             <height>16777215</height>
            </size>
           </property>
           <property name="font">
            <font>
             <pointsize>12</pointsize>
            </font>
           </property>
           <property name="title">
            <string> 容器外APP选择</string>
           </property>
           <layout class="QGridLayout" name="gridLayout_2">
            <item row="0" column="0">
             <widget class="QCheckBox" name="iotCB">
              <property name="font">
               <font>
                <pointsize>12</pointsize>
                <weight>50</weight>
                <bold>false</bold>
               </font>
              </property>
              <property name="text">
               <string>iotManager</string>
              </property>
              <property name="checked">
               <bool>false</bool>
              </property>
             </widget>
            </item>
            <item row="1" column="0">
             <widget class="QCheckBox" name="wirCB">
              <property name="font">
               <font>
                <pointsize>12</pointsize>
                <weight>50</weight>
                <bold>false</bold>
               </font>
              </property>
              <property name="text">
               <string>wirelessDCM</string>
              </property>
              <property name="checked">
               <bool>false</bool>
              </property>
             </widget>
            </item>
            <item row="2" column="0">
             <widget class="QCheckBox" name="scsCB">
              <property name="font">
               <font>
                <pointsize>12</pointsize>
                <weight>50</weight>
                <bold>false</bold>
               </font>
              </property>
              <property name="text">
               <string>scsMonitor</string>
              </property>
              <property name="checked">
               <bool>false</bool>
              </property>
             </widget>
            </item>
            <item row="3" column="0">
             <widget class="QCheckBox" name="iotAgentCB">
              <property name="font">
               <font>
                <pointsize>12</pointsize>
                <weight>50</weight>
                <bold>false</bold>
               </font>
              </property>
              <property name="text">
               <string>IotAgent</string>
              </property>
              <property name="checked">
               <bool>false</bool>
              </property>
             </widget>
            </item>
            <item row="4" column="0">
             <widget class="QCheckBox" name="ExeCB">
              <property name="font">
               <font>
                <pointsize>12</pointsize>
                <weight>50</weight>
                <bold>false</bold>
               </font>
              </property>
              <property name="text">
               <string>ExecShell</string>
              </property>
              <property name="checked">
               <bool>false</bool>
              </property>
             </widget>
            </item>
           </layout>
          </widget>
         </item>
         <item>
          <widget class="QGroupBox" name="groupBox_9">
           <property name="minimumSize">
            <size>
             <width>0</width>
             <height>0</height>
            </size>
           </property>
           <property name="maximumSize">
            <size>
             <width>16777215</width>
             <height>16777215</height>
            </size>
           </property>
           <property name="font">
            <font>
             <pointsize>12</pointsize>
            </font>
           </property>
           <property name="title">
            <string>已选择容器外APP</string>
           </property>
           <layout class="QGridLayout" name="gridLayout_3">
            <item row="0" column="0">
             <widget class="QCheckBox" name="iotCB_2">
              <property name="enabled">
               <bool>false</bool>
              </property>
              <property name="font">
               <font>
                <pointsize>12</pointsize>
                <weight>50</weight>
                <bold>false</bold>
               </font>
              </property>
              <property name="text">
               <string>iotManager</string>
              </property>
              <property name="checkable">
               <bool>true</bool>
              </property>
              <property name="checked">
               <bool>false</bool>
              </property>
             </widget>
            </item>
            <item row="1" column="0">
             <widget class="QCheckBox" name="wirCB_2">
              <property name="enabled">
               <bool>false</bool>
              </property>
              <property name="font">
               <font>
                <pointsize>12</pointsize>
                <weight>50</weight>
                <bold>false</bold>
               </font>
              </property>
              <property name="text">
               <string>wirelessDCM</string>
              </property>
              <property name="checkable">
               <bool>true</bool>
              </property>
              <property name="checked">
               <bool>false</bool>
              </property>
             </widget>
            </item>
            <item row="2" column="0">
             <widget class="QCheckBox" name="scsCB_2">
              <property name="enabled">
               <bool>false</bool>
              </property>
              <property name="font">
               <font>
                <pointsize>12</pointsize>
                <weight>50</weight>
                <bold>false</bold>
               </font>
              </property>
              <property name="text">
               <string>scsMonitor</string>
              </property>
              <property name="checkable">
               <bool>true</bool>
              </property>
              <property name="checked">
               <bool>false</bool>
              </property>
             </widget>
            </item>
            <item row="3" column="0">
             <widget class="QCheckBox" name="iotAgen_2t">
              <property name="enabled">
               <bool>false</bool>
              </property>
              <property name="font">
               <font>
                <pointsize>12</pointsize>
                <weight>50</weight>
                <bold>false</bold>
               </font>
              </property>
              <property name="text">
               <string>IotAgent</string>
              </property>
              <property name="checkable">
               <bool>true</bool>
              </property>
              <property name="checked">
               <bool>false</bool>
              </property>
             </widget>
            </item>
            <item row="4" column="0">
             <widget class="QCheckBox" name="ExeCB_2">
              <property name="enabled">
               <bool>false</bool>
              </property>
              <property name="font">
               <font>
                <pointsize>12</pointsize>
                <weight>50</weight>
                <bold>false</bold>
               </font>
              </property>
              <property name="text">
               <string>ExecShell</string>
              </property>
              <property name="checked">
               <bool>false</bool>
              </property>
             </widget>
            </item>
           </layout>
          </widget>
         </item>
        </layout>
       </item>
      </layout>
     </widget>
     <widget class="QWidget" name="tab_3">
      <property name="font">
       <font>
        <pointsize>12</pointsize>
       </font>
      </property>
      <attribute name="title">
       <string>SCU系统参数</string>
      </attribute>
      <layout class="QGridLayout" name="gridLayout_23">
       <item row="0" column="0">
        <widget class="QGroupBox" name="groupBox_10">
         <property name="font">
          <font>
           <pointsize>12</pointsize>
          </font>
         </property>
         <property name="title">
          <string>设置</string>
         </property>
         <layout class="QGridLayout" name="gridLayout_18">
          <item row="0" column="0">
           <layout class="QHBoxLayout" name="horizontalLayout_4">
            <property name="spacing">
             <number>9</number>
            </property>
            <item>
             <layout class="QGridLayout" name="gridLayout_5">
              <property name="verticalSpacing">
               <number>12</number>
              </property>
              <item row="0" column="0">
               <widget class="QLabel" name="label_2">
                <property name="font">
                 <font>
                  <pointsize>12</pointsize>
                 </font>
                </property>
                <property name="text">
                 <string>大包版本</string>
                </property>
               </widget>
              </item>
              <item row="0" column="1">
               <widget class="QLineEdit" name="db1LE">
                <property name="font">
                 <font>
                  <pointsize>12</pointsize>
                 </font>
                </property>
               </widget>
              </item>
              <item row="1" column="0">
               <widget class="QLabel" name="label_8">
                <property name="font">
                 <font>
                  <pointsize>12</pointsize>
                 </font>
                </property>
                <property name="text">
                 <string>主控板版本</string>
                </property>
               </widget>
              </item>
              <item row="1" column="1">
               <widget class="QLineEdit" name="zkLE">
                <property name="font">
                 <font>
                  <pointsize>12</pointsize>
                 </font>
                </property>
               </widget>
              </item>
              <item row="2" column="0">
               <widget class="QLabel" name="label_10">
                <property name="font">
                 <font>
                  <pointsize>12</pointsize>
                 </font>
                </property>
                <property name="text">
                 <string>内核版本</string>
                </property>
               </widget>
              </item>
              <item row="2" column="1">
               <widget class="QLineEdit" name="nhLE">
                <property name="font">
                 <font>
                  <pointsize>12</pointsize>
                 </font>
                </property>
               </widget>
              </item>
              <item row="3" column="0">
               <widget class="QLabel" name="label_13">
                <property name="font">
                 <font>
                  <pointsize>12</pointsize>
                 </font>
                </property>
                <property name="text">
                 <string>容器版本</string>
                </property>
               </widget>
              </item>
              <item row="3" column="1">
               <widget class="QLineEdit" name="rqLE">
                <property name="font">
                 <font>
                  <pointsize>12</pointsize>
                 </font>
                </property>
               </widget>
              </item>
              <item row="4" column="0">
               <widget class="QLabel" name="label_27">
                <property name="font">
                 <font>
                  <pointsize>12</pointsize>
                 </font>
                </property>
                <property name="text">
                 <string>关键系统文件版本</string>
                </property>
               </widget>
              </item>
              <item row="4" column="1">
               <widget class="QLineEdit" name="gjLE">
                <property name="font">
                 <font>
                  <pointsize>12</pointsize>
                 </font>
                </property>
               </widget>
              </item>
              <item row="5" column="0">
               <widget class="QLabel" name="label_14">
                <property name="font">
                 <font>
                  <pointsize>12</pointsize>
                 </font>
                </property>
                <property name="text">
                 <string>APP-CPU最大使用率(%)</string>
                </property>
               </widget>
              </item>
              <item row="5" column="1">
               <widget class="QLineEdit" name="cpuLE">
                <property name="font">
                 <font>
                  <pointsize>12</pointsize>
                 </font>
                </property>
               </widget>
              </item>
             </layout>
            </item>
            <item>
             <widget class="QLabel" name="label_67">
              <property name="minimumSize">
               <size>
                <width>50</width>
                <height>0</height>
               </size>
              </property>
              <property name="text">
               <string/>
              </property>
             </widget>
            </item>
            <item>
             <layout class="QGridLayout" name="gridLayout_21">
              <item row="0" column="0">
               <widget class="QLabel" name="label_7">
                <property name="font">
                 <font>
                  <pointsize>12</pointsize>
                 </font>
                </property>
                <property name="text">
                 <string>大包版本</string>
                </property>
               </widget>
              </item>
              <item row="0" column="1">
               <widget class="QLineEdit" name="db2LE">
                <property name="font">
                 <font>
                  <pointsize>12</pointsize>
                 </font>
                </property>
               </widget>
              </item>
              <item row="1" column="0">
               <widget class="QLabel" name="label_9">
                <property name="font">
                 <font>
                  <pointsize>12</pointsize>
                 </font>
                </property>
                <property name="text">
                 <string>硬件版本</string>
                </property>
               </widget>
              </item>
              <item row="1" column="1">
               <widget class="QLineEdit" name="yjLE">
                <property name="font">
                 <font>
                  <pointsize>12</pointsize>
                 </font>
                </property>
               </widget>
              </item>
              <item row="2" column="0">
               <widget class="QLabel" name="label_11">
                <property name="font">
                 <font>
                  <pointsize>12</pointsize>
                 </font>
                </property>
                <property name="text">
                 <string>备核版本/出厂主核版本</string>
                </property>
               </widget>
              </item>
              <item row="2" column="1">
               <widget class="QLineEdit" name="bhLE">
                <property name="font">
                 <font>
                  <pointsize>12</pointsize>
                 </font>
                </property>
               </widget>
              </item>
              <item row="3" column="0">
               <widget class="QLabel" name="label_12">
                <property name="font">
                 <font>
                  <pointsize>12</pointsize>
                 </font>
                </property>
                <property name="text">
                 <string>交采板程序版本</string>
                </property>
               </widget>
              </item>
              <item row="3" column="1">
               <widget class="QLineEdit" name="jcLE">
                <property name="font">
                 <font>
                  <pointsize>12</pointsize>
                 </font>
                </property>
               </widget>
              </item>
              <item row="4" column="0">
               <widget class="QLabel" name="label_28">
                <property name="font">
                 <font>
                  <pointsize>12</pointsize>
                 </font>
                </property>
                <property name="text">
                 <string>操作系统版本</string>
                </property>
               </widget>
              </item>
              <item row="4" column="1">
               <widget class="QLineEdit" name="czLE">
                <property name="font">
                 <font>
                  <pointsize>12</pointsize>
                 </font>
                </property>
               </widget>
              </item>
              <item row="5" column="0">
               <widget class="QLabel" name="label_15">
                <property name="font">
                 <font>
                  <pointsize>12</pointsize>
                 </font>
                </property>
                <property name="text">
                 <string>APP-内存最大使用量(M)</string>
                </property>
               </widget>
              </item>
              <item row="5" column="1">
               <widget class="QLineEdit" name="memLE">
                <property name="font">
                 <font>
                  <pointsize>12</pointsize>
                 </font>
                </property>
               </widget>
              </item>
             </layout>
            </item>
            <item>
             <spacer name="horizontalSpacer_7">
              <property name="orientation">
               <enum>Qt::Horizontal</enum>
              </property>
              <property name="sizeHint" stdset="0">
               <size>
                <width>40</width>
                <height>20</height>
               </size>
              </property>
             </spacer>
            </item>
           </layout>
          </item>
          <item row="1" column="0">
           <layout class="QHBoxLayout" name="horizontalLayout_16">
            <item>
             <spacer name="horizontalSpacer_18">
              <property name="orientation">
               <enum>Qt::Horizontal</enum>
              </property>
              <property name="sizeHint" stdset="0">
               <size>
                <width>40</width>
                <height>20</height>
               </size>
              </property>
             </spacer>
            </item>
            <item>
             <widget class="QPushButton" name="scuOK">
              <property name="styleSheet">
               <string notr="true">    QPushButton {background-color:#0089ff ;
    color: white;
    border-style: outset;
    border-width: 2px;
    border-radius: 10px;
    border-color: beige;
    font-size: 26px;
    padding: 6px;}

QPushButton:hover {background-color: #0072C6;         
                     }
</string>
              </property>
              <property name="text">
               <string>保存</string>
              </property>
             </widget>
            </item>
           </layout>
          </item>
         </layout>
        </widget>
       </item>
       <item row="1" column="0">
        <widget class="QGroupBox" name="groupBox_11">
         <property name="enabled">
          <bool>true</bool>
         </property>
         <property name="font">
          <font>
           <pointsize>12</pointsize>
          </font>
         </property>
         <property name="title">
          <string>查看</string>
         </property>
         <layout class="QGridLayout" name="gridLayout_19">
          <item row="0" column="0" rowspan="2">
           <layout class="QGridLayout" name="gridLayout_20">
            <property name="verticalSpacing">
             <number>12</number>
            </property>
            <item row="0" column="0">
             <widget class="QLabel" name="label_55">
              <property name="font">
               <font>
                <pointsize>12</pointsize>
               </font>
              </property>
              <property name="text">
               <string>大包版本</string>
              </property>
             </widget>
            </item>
            <item row="0" column="1">
             <widget class="QLineEdit" name="db1LE_3">
              <property name="font">
               <font>
                <pointsize>12</pointsize>
               </font>
              </property>
              <property name="readOnly">
               <bool>true</bool>
              </property>
             </widget>
            </item>
            <item row="1" column="0">
             <widget class="QLabel" name="label_56">
              <property name="font">
               <font>
                <pointsize>12</pointsize>
               </font>
              </property>
              <property name="text">
               <string>主控板版本</string>
              </property>
             </widget>
            </item>
            <item row="1" column="1">
             <widget class="QLineEdit" name="zkLE_3">
              <property name="font">
               <font>
                <pointsize>12</pointsize>
               </font>
              </property>
              <property name="readOnly">
               <bool>true</bool>
              </property>
             </widget>
            </item>
            <item row="2" column="0">
             <widget class="QLabel" name="label_57">
              <property name="font">
               <font>
                <pointsize>12</pointsize>
               </font>
              </property>
              <property name="text">
               <string>内核版本</string>
              </property>
             </widget>
            </item>
            <item row="2" column="1">
             <widget class="QLineEdit" name="nhLE_3">
              <property name="font">
               <font>
                <pointsize>12</pointsize>
               </font>
              </property>
              <property name="readOnly">
               <bool>true</bool>
              </property>
             </widget>
            </item>
            <item row="3" column="0">
             <widget class="QLabel" name="label_58">
              <property name="font">
               <font>
                <pointsize>12</pointsize>
               </font>
              </property>
              <property name="text">
               <string>容器版本</string>
              </property>
             </widget>
            </item>
            <item row="3" column="1">
             <widget class="QLineEdit" name="rqLE_3">
              <property name="font">
               <font>
                <pointsize>12</pointsize>
               </font>
              </property>
              <property name="readOnly">
               <bool>true</bool>
              </property>
             </widget>
            </item>
            <item row="4" column="0">
             <widget class="QLabel" name="label_59">
              <property name="font">
               <font>
                <pointsize>12</pointsize>
               </font>
              </property>
              <property name="text">
               <string>关键系统文件版本</string>
              </property>
             </widget>
            </item>
            <item row="4" column="1">
             <widget class="QLineEdit" name="gjLE_3">
              <property name="font">
               <font>
                <pointsize>12</pointsize>
               </font>
              </property>
              <property name="readOnly">
               <bool>true</bool>
              </property>
             </widget>
            </item>
            <item row="5" column="0">
             <widget class="QLabel" name="label_60">
              <property name="font">
               <font>
                <pointsize>12</pointsize>
               </font>
              </property>
              <property name="text">
               <string>APP-CPU最大使用率(%)</string>
              </property>
             </widget>
            </item>
            <item row="5" column="1">
             <widget class="QLineEdit" name="cpuLE_3">
              <property name="font">
               <font>
                <pointsize>12</pointsize>
               </font>
              </property>
              <property name="readOnly">
               <bool>true</bool>
              </property>
             </widget>
            </item>
           </layout>
          </item>
          <item row="0" column="1">
           <widget class="QLabel" name="label_68">
            <property name="minimumSize">
             <size>
              <width>50</width>
              <height>0</height>
             </size>
            </property>
            <property name="text">
             <string/>
            </property>
           </widget>
          </item>
          <item row="0" column="2" rowspan="2">
           <layout class="QGridLayout" name="gridLayout_22">
            <item row="0" column="0">
             <widget class="QLabel" name="label_61">
              <property name="font">
               <font>
                <pointsize>12</pointsize>
               </font>
              </property>
              <property name="text">
               <string>大包版本</string>
              </property>
             </widget>
            </item>
            <item row="0" column="1">
             <widget class="QLineEdit" name="db2LE_3">
              <property name="font">
               <font>
                <pointsize>12</pointsize>
               </font>
              </property>
              <property name="readOnly">
               <bool>true</bool>
              </property>
             </widget>
            </item>
            <item row="1" column="0">
             <widget class="QLabel" name="label_62">
              <property name="font">
               <font>
                <pointsize>12</pointsize>
               </font>
              </property>
              <property name="text">
               <string>硬件版本</string>
              </property>
             </widget>
            </item>
            <item row="1" column="1">
             <widget class="QLineEdit" name="yjLE_3">
              <property name="font">
               <font>
                <pointsize>12</pointsize>
               </font>
              </property>
              <property name="readOnly">
               <bool>true</bool>
              </property>
             </widget>
            </item>
            <item row="2" column="0">
             <widget class="QLabel" name="label_63">
              <property name="font">
               <font>
                <pointsize>12</pointsize>
               </font>
              </property>
              <property name="text">
               <string>备核版本/出厂主核版本</string>
              </property>
             </widget>
            </item>
            <item row="2" column="1">
             <widget class="QLineEdit" name="bhLE_3">
              <property name="font">
               <font>
                <pointsize>12</pointsize>
               </font>
              </property>
              <property name="readOnly">
               <bool>true</bool>
              </property>
             </widget>
            </item>
            <item row="3" column="0">
             <widget class="QLabel" name="label_64">
              <property name="font">
               <font>
                <pointsize>12</pointsize>
               </font>
              </property>
              <property name="text">
               <string>交采板程序版本</string>
              </property>
             </widget>
            </item>
            <item row="3" column="1">
             <widget class="QLineEdit" name="jcLE_3">
              <property name="font">
               <font>
                <pointsize>12</pointsize>
               </font>
              </property>
              <property name="readOnly">
               <bool>true</bool>
              </property>
             </widget>
            </item>
            <item row="4" column="0">
             <widget class="QLabel" name="label_65">
              <property name="font">
               <font>
                <pointsize>12</pointsize>
               </font>
              </property>
              <property name="text">
               <string>操作系统版本</string>
              </property>
             </widget>
            </item>
            <item row="4" column="1">
             <widget class="QLineEdit" name="czLE_3">
              <property name="font">
               <font>
                <pointsize>12</pointsize>
               </font>
              </property>
              <property name="readOnly">
               <bool>true</bool>
              </property>
             </widget>
            </item>
            <item row="5" column="0">
             <widget class="QLabel" name="label_66">
              <property name="font">
               <font>
                <pointsize>12</pointsize>
               </font>
              </property>
              <property name="text">
               <string>APP-内存最大使用量(M)</string>
              </property>
             </widget>
            </item>
            <item row="5" column="1">
             <widget class="QLineEdit" name="memLE_3">
              <property name="font">
               <font>
                <pointsize>12</pointsize>
               </font>
              </property>
              <property name="readOnly">
               <bool>true</bool>
              </property>
             </widget>
            </item>
           </layout>
          </item>
          <item row="1" column="3">
           <spacer name="horizontalSpacer_8">
            <property name="orientation">
             <enum>Qt::Horizontal</enum>
            </property>
            <property name="sizeHint" stdset="0">
             <size>
              <width>195</width>
              <height>20</height>
             </size>
            </property>
           </spacer>
          </item>
         </layout>
        </widget>
       </item>
       <item row="2" column="0">
        <spacer name="verticalSpacer">
         <property name="orientation">
          <enum>Qt::Vertical</enum>
         </property>
         <property name="sizeHint" stdset="0">
          <size>
           <width>20</width>
           <height>247</height>
          </size>
         </property>
        </spacer>
       </item>
      </layout>
     </widget>
     <widget class="QWidget" name="tab_4">
      <attribute name="title">
       <string>698参数</string>
      </attribute>
      <layout class="QGridLayout" name="gridLayout">
       <item row="0" column="0">
        <layout class="QHBoxLayout" name="horizontalLayout_5">
         <item>
          <widget class="QLabel" name="label_26">
           <property name="font">
            <font>
             <pointsize>12</pointsize>
            </font>
           </property>
           <property name="text">
            <string>参数查看</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QComboBox" name="comboBox">
           <property name="sizePolicy">
            <sizepolicy hsizetype="Expanding" vsizetype="Preferred">
             <horstretch>0</horstretch>
             <verstretch>0</verstretch>
            </sizepolicy>
           </property>
           <property name="font">
            <font>
             <pointsize>12</pointsize>
            </font>
           </property>
           <item>
            <property name="text">
             <string>公网通信模块1-通信配置</string>
            </property>
           </item>
           <item>
            <property name="text">
             <string>公网通信模块1-主站通信参数表</string>
            </property>
           </item>
           <item>
            <property name="text">
             <string>以太网通信模块1-通信配置</string>
            </property>
           </item>
           <item>
            <property name="text">
             <string>以太网通信模块1-主站通信参数表</string>
            </property>
           </item>
           <item>
            <property name="text">
             <string>终端停上电事件-参数配置</string>
            </property>
           </item>
           <item>
            <property name="text">
             <string>安全模式参数</string>
            </property>
           </item>
          </widget>
         </item>
         <item>
          <widget class="QPushButton" name="query698Btn">
           <property name="styleSheet">
            <string notr="true">    QPushButton {background-color:#0089ff ;
    color: white;
    border-style: outset;
    border-width: 2px;
    border-radius: 10px;
    border-color: beige;
    font-size: 26px;
    padding: 6px;}

QPushButton:hover {background-color: #0072C6;         
                     }
</string>
           </property>
           <property name="text">
            <string>查看</string>
           </property>
          </widget>
         </item>
         <item>
          <spacer name="horizontalSpacer_2">
           <property name="orientation">
            <enum>Qt::Horizontal</enum>
           </property>
           <property name="sizeHint" stdset="0">
            <size>
             <width>40</width>
             <height>20</height>
            </size>
           </property>
          </spacer>
         </item>
        </layout>
       </item>
       <item row="1" column="0">
        <layout class="QVBoxLayout" name="verticalLayout">
         <item>
          <widget class="QGroupBox" name="groupBox_2">
           <property name="font">
            <font>
             <pointsize>12</pointsize>
            </font>
           </property>
           <property name="title">
            <string>公网通信模块1</string>
           </property>
           <layout class="QGridLayout" name="gridLayout_6">
            <item row="0" column="0">
             <layout class="QVBoxLayout" name="verticalLayout_4">
              <item>
               <widget class="QCheckBox" name="gwtxpzCB">
                <property name="font">
                 <font>
                  <pointsize>12</pointsize>
                 </font>
                </property>
                <property name="text">
                 <string>通信配置</string>
                </property>
               </widget>
              </item>
              <item>
               <widget class="QCheckBox" name="gwzztxCB">
                <property name="font">
                 <font>
                  <pointsize>12</pointsize>
                 </font>
                </property>
                <property name="text">
                 <string>主站通信参数表</string>
                </property>
               </widget>
              </item>
             </layout>
            </item>
           </layout>
          </widget>
         </item>
         <item>
          <widget class="QGroupBox" name="groupBox_3">
           <property name="font">
            <font>
             <pointsize>12</pointsize>
            </font>
           </property>
           <property name="title">
            <string>以太网通信模块1</string>
           </property>
           <layout class="QGridLayout" name="gridLayout_9">
            <item row="0" column="0">
             <layout class="QVBoxLayout" name="verticalLayout_5">
              <item>
               <widget class="QCheckBox" name="ytwtxCB">
                <property name="font">
                 <font>
                  <pointsize>12</pointsize>
                 </font>
                </property>
                <property name="text">
                 <string>通信配置</string>
                </property>
               </widget>
              </item>
              <item>
               <widget class="QCheckBox" name="ytwzztxCB">
                <property name="font">
                 <font>
                  <pointsize>12</pointsize>
                 </font>
                </property>
                <property name="text">
                 <string>主站通信参数表</string>
                </property>
               </widget>
              </item>
             </layout>
            </item>
           </layout>
          </widget>
         </item>
         <item>
          <widget class="QGroupBox" name="groupBox_4">
           <property name="font">
            <font>
             <pointsize>12</pointsize>
            </font>
           </property>
           <property name="title">
            <string>终端停上电事件</string>
           </property>
           <layout class="QGridLayout" name="gridLayout_8">
            <item row="0" column="0">
             <widget class="QCheckBox" name="zdtsdcsCB">
              <property name="font">
               <font>
                <pointsize>12</pointsize>
               </font>
              </property>
              <property name="text">
               <string>参数配置</string>
              </property>
             </widget>
            </item>
           </layout>
          </widget>
         </item>
         <item>
          <widget class="QGroupBox" name="groupBox_14">
           <property name="font">
            <font>
             <pointsize>12</pointsize>
            </font>
           </property>
           <property name="title">
            <string>ESAM接口</string>
           </property>
           <layout class="QGridLayout" name="gridLayout_31">
            <item row="0" column="0">
             <widget class="QCheckBox" name="aqmscsCB">
              <property name="font">
               <font>
                <pointsize>12</pointsize>
               </font>
              </property>
              <property name="text">
               <string>安全模式参数</string>
              </property>
             </widget>
            </item>
           </layout>
          </widget>
         </item>
         <item>
          <spacer name="verticalSpacer_2">
           <property name="orientation">
            <enum>Qt::Vertical</enum>
           </property>
           <property name="sizeHint" stdset="0">
            <size>
             <width>20</width>
             <height>178</height>
            </size>
           </property>
          </spacer>
         </item>
        </layout>
       </item>
      </layout>
     </widget>
     <widget class="QWidget" name="tab_5">
      <attribute name="title">
       <string>其他参数</string>
      </attribute>
      <layout class="QGridLayout" name="gridLayout_34">
       <item row="0" column="0">
        <widget class="QGroupBox" name="groupBox_23">
         <property name="font">
          <font>
           <pointsize>12</pointsize>
          </font>
         </property>
         <property name="title">
          <string>设置</string>
         </property>
         <layout class="QGridLayout" name="gridLayout_32">
          <item row="0" column="0">
           <layout class="QHBoxLayout" name="horizontalLayout_12">
            <item>
             <widget class="QGroupBox" name="groupBox_7">
              <property name="font">
               <font>
                <pointsize>12</pointsize>
               </font>
              </property>
              <property name="title">
               <string>SCU</string>
              </property>
              <layout class="QGridLayout" name="gridLayout_12">
               <item row="0" column="0">
                <widget class="QLabel" name="label_19">
                 <property name="font">
                  <font>
                   <pointsize>12</pointsize>
                  </font>
                 </property>
                 <property name="text">
                  <string>SCU系统启动时间（s）</string>
                 </property>
                </widget>
               </item>
               <item row="0" column="1">
                <widget class="QLineEdit" name="scuQDLE">
                 <property name="sizePolicy">
                  <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
                   <horstretch>0</horstretch>
                   <verstretch>0</verstretch>
                  </sizepolicy>
                 </property>
                 <property name="font">
                  <font>
                   <pointsize>12</pointsize>
                  </font>
                 </property>
                </widget>
               </item>
               <item row="0" column="2">
                <spacer name="horizontalSpacer_13">
                 <property name="orientation">
                  <enum>Qt::Horizontal</enum>
                 </property>
                 <property name="sizeHint" stdset="0">
                  <size>
                   <width>245</width>
                   <height>20</height>
                  </size>
                 </property>
                </spacer>
               </item>
              </layout>
             </widget>
            </item>
            <item>
             <widget class="QGroupBox" name="groupBox_12">
              <property name="font">
               <font>
                <pointsize>12</pointsize>
               </font>
              </property>
              <property name="title">
               <string>MQTTIoT配置</string>
              </property>
              <layout class="QGridLayout" name="gridLayout_10">
               <item row="0" column="0">
                <layout class="QHBoxLayout" name="horizontalLayout_8">
                 <item>
                  <widget class="QLabel" name="label_29">
                   <property name="font">
                    <font>
                     <pointsize>12</pointsize>
                    </font>
                   </property>
                   <property name="text">
                    <string>IP</string>
                   </property>
                  </widget>
                 </item>
                 <item>
                  <widget class="QLineEdit" name="ipLE">
                   <property name="sizePolicy">
                    <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
                     <horstretch>0</horstretch>
                     <verstretch>0</verstretch>
                    </sizepolicy>
                   </property>
                  </widget>
                 </item>
                 <item>
                  <spacer name="horizontalSpacer_4">
                   <property name="orientation">
                    <enum>Qt::Horizontal</enum>
                   </property>
                   <property name="sizeHint" stdset="0">
                    <size>
                     <width>40</width>
                     <height>20</height>
                    </size>
                   </property>
                  </spacer>
                 </item>
                </layout>
               </item>
              </layout>
             </widget>
            </item>
            <item>
             <widget class="QGroupBox" name="groupBox_15">
              <property name="font">
               <font>
                <pointsize>12</pointsize>
               </font>
              </property>
              <property name="title">
               <string>RTC</string>
              </property>
              <layout class="QGridLayout" name="gridLayout_7">
               <item row="0" column="0">
                <layout class="QHBoxLayout" name="horizontalLayout_9">
                 <item>
                  <widget class="QLabel" name="label_16">
                   <property name="font">
                    <font>
                     <pointsize>12</pointsize>
                    </font>
                   </property>
                   <property name="text">
                    <string>RTC最大误差时间(s)：</string>
                   </property>
                  </widget>
                 </item>
                 <item>
                  <widget class="QLineEdit" name="rtcLE"/>
                 </item>
                </layout>
               </item>
              </layout>
             </widget>
            </item>
           </layout>
          </item>
          <item row="1" column="0">
           <widget class="QGroupBox" name="groupBox_6">
            <property name="font">
             <font>
              <pointsize>12</pointsize>
             </font>
            </property>
            <property name="title">
             <string>HPLC/4G</string>
            </property>
            <layout class="QGridLayout" name="gridLayout_11">
             <item row="0" column="0">
              <layout class="QHBoxLayout" name="horizontalLayout_11">
               <item>
                <layout class="QVBoxLayout" name="verticalLayout_3">
                 <item>
                  <widget class="QCheckBox" name="sm4gCB">
                   <property name="enabled">
                    <bool>true</bool>
                   </property>
                   <property name="font">
                    <font>
                     <pointsize>12</pointsize>
                    </font>
                   </property>
                   <property name="text">
                    <string>4G双模</string>
                   </property>
                  </widget>
                 </item>
                 <item>
                  <widget class="QCheckBox" name="dm4gCB">
                   <property name="enabled">
                    <bool>true</bool>
                   </property>
                   <property name="font">
                    <font>
                     <pointsize>12</pointsize>
                    </font>
                   </property>
                   <property name="text">
                    <string>4G单模</string>
                   </property>
                  </widget>
                 </item>
                </layout>
               </item>
               <item>
                <widget class="QLabel" name="label_17">
                 <property name="font">
                  <font>
                   <pointsize>12</pointsize>
                  </font>
                 </property>
                 <property name="text">
                  <string>    FirmWare Version</string>
                 </property>
                </widget>
               </item>
               <item>
                <widget class="QLineEdit" name="firvLE">
                 <property name="sizePolicy">
                  <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
                   <horstretch>0</horstretch>
                   <verstretch>0</verstretch>
                  </sizepolicy>
                 </property>
                 <property name="font">
                  <font>
                   <pointsize>12</pointsize>
                  </font>
                 </property>
                </widget>
               </item>
               <item>
                <widget class="QLabel" name="label_18">
                 <property name="font">
                  <font>
                   <pointsize>12</pointsize>
                  </font>
                 </property>
                 <property name="text">
                  <string>    Hardware Version</string>
                 </property>
                </widget>
               </item>
               <item>
                <widget class="QLineEdit" name="harvLE">
                 <property name="sizePolicy">
                  <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
                   <horstretch>0</horstretch>
                   <verstretch>0</verstretch>
                  </sizepolicy>
                 </property>
                 <property name="font">
                  <font>
                   <pointsize>12</pointsize>
                  </font>
                 </property>
                </widget>
               </item>
               <item>
                <widget class="QLabel" name="label_31">
                 <property name="font">
                  <font>
                   <pointsize>12</pointsize>
                  </font>
                 </property>
                 <property name="text">
                  <string>      本地通讯模块</string>
                 </property>
                </widget>
               </item>
               <item>
                <widget class="QLineEdit" name="txmkLE">
                 <property name="sizePolicy">
                  <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
                   <horstretch>0</horstretch>
                   <verstretch>0</verstretch>
                  </sizepolicy>
                 </property>
                 <property name="font">
                  <font>
                   <pointsize>12</pointsize>
                  </font>
                 </property>
                </widget>
               </item>
               <item>
                <spacer name="horizontalSpacer_9">
                 <property name="orientation">
                  <enum>Qt::Horizontal</enum>
                 </property>
                 <property name="sizeHint" stdset="0">
                  <size>
                   <width>68</width>
                   <height>17</height>
                  </size>
                 </property>
                </spacer>
               </item>
              </layout>
             </item>
            </layout>
           </widget>
          </item>
          <item row="2" column="0">
           <widget class="QGroupBox" name="groupBox_8">
            <property name="font">
             <font>
              <pointsize>12</pointsize>
             </font>
            </property>
            <property name="title">
             <string>交采计量精度</string>
            </property>
            <layout class="QGridLayout" name="gridLayout_13">
             <item row="0" column="0">
              <layout class="QHBoxLayout" name="horizontalLayout_15">
               <item>
                <widget class="QLabel" name="label_20">
                 <property name="font">
                  <font>
                   <pointsize>12</pointsize>
                  </font>
                 </property>
                 <property name="text">
                  <string>电压精度误差百分比（%）</string>
                 </property>
                </widget>
               </item>
               <item>
                <widget class="QLineEdit" name="dywcLE">
                 <property name="sizePolicy">
                  <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
                   <horstretch>0</horstretch>
                   <verstretch>0</verstretch>
                  </sizepolicy>
                 </property>
                 <property name="font">
                  <font>
                   <pointsize>12</pointsize>
                  </font>
                 </property>
                </widget>
               </item>
               <item>
                <widget class="QLabel" name="label_21">
                 <property name="font">
                  <font>
                   <pointsize>12</pointsize>
                  </font>
                 </property>
                 <property name="text">
                  <string>    电流精度误差百分比（%）</string>
                 </property>
                </widget>
               </item>
               <item>
                <widget class="QLineEdit" name="dlwcLE">
                 <property name="sizePolicy">
                  <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
                   <horstretch>0</horstretch>
                   <verstretch>0</verstretch>
                  </sizepolicy>
                 </property>
                 <property name="font">
                  <font>
                   <pointsize>12</pointsize>
                  </font>
                 </property>
                </widget>
               </item>
               <item>
                <widget class="QLabel" name="label_22">
                 <property name="font">
                  <font>
                   <pointsize>12</pointsize>
                  </font>
                 </property>
                 <property name="text">
                  <string>    功率因数精度误差百分比（%）</string>
                 </property>
                </widget>
               </item>
               <item>
                <widget class="QLineEdit" name="glyswcLE">
                 <property name="sizePolicy">
                  <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
                   <horstretch>0</horstretch>
                   <verstretch>0</verstretch>
                  </sizepolicy>
                 </property>
                 <property name="font">
                  <font>
                   <pointsize>12</pointsize>
                  </font>
                 </property>
                </widget>
               </item>
               <item>
                <spacer name="horizontalSpacer_22">
                 <property name="orientation">
                  <enum>Qt::Horizontal</enum>
                 </property>
                 <property name="sizeHint" stdset="0">
                  <size>
                   <width>40</width>
                   <height>20</height>
                  </size>
                 </property>
                </spacer>
               </item>
              </layout>
             </item>
             <item row="1" column="0">
              <layout class="QHBoxLayout" name="horizontalLayout_14">
               <item>
                <widget class="QLabel" name="label_32">
                 <property name="font">
                  <font>
                   <pointsize>12</pointsize>
                  </font>
                 </property>
                 <property name="text">
                  <string>有功功率精度误差百分比（%）</string>
                 </property>
                </widget>
               </item>
               <item>
                <widget class="QLineEdit" name="ygglwcLE">
                 <property name="sizePolicy">
                  <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
                   <horstretch>0</horstretch>
                   <verstretch>0</verstretch>
                  </sizepolicy>
                 </property>
                 <property name="font">
                  <font>
                   <pointsize>12</pointsize>
                  </font>
                 </property>
                </widget>
               </item>
               <item>
                <widget class="QLabel" name="label_33">
                 <property name="font">
                  <font>
                   <pointsize>12</pointsize>
                  </font>
                 </property>
                 <property name="text">
                  <string>      无功功率精度误差百分比（%）</string>
                 </property>
                </widget>
               </item>
               <item>
                <widget class="QLineEdit" name="wgglwcLE">
                 <property name="sizePolicy">
                  <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
                   <horstretch>0</horstretch>
                   <verstretch>0</verstretch>
                  </sizepolicy>
                 </property>
                 <property name="font">
                  <font>
                   <pointsize>12</pointsize>
                  </font>
                 </property>
                </widget>
               </item>
               <item>
                <spacer name="horizontalSpacer_27">
                 <property name="orientation">
                  <enum>Qt::Horizontal</enum>
                 </property>
                 <property name="sizeHint" stdset="0">
                  <size>
                   <width>40</width>
                   <height>20</height>
                  </size>
                 </property>
                </spacer>
               </item>
              </layout>
             </item>
            </layout>
           </widget>
          </item>
          <item row="3" column="0">
           <widget class="QGroupBox" name="groupBox_5">
            <property name="font">
             <font>
              <pointsize>12</pointsize>
             </font>
            </property>
            <property name="title">
             <string> 蓝牙</string>
            </property>
            <layout class="QGridLayout" name="gridLayout_25">
             <item row="0" column="0">
              <layout class="QHBoxLayout" name="horizontalLayout_22">
               <item>
                <widget class="QLabel" name="label_38">
                 <property name="font">
                  <font>
                   <pointsize>12</pointsize>
                  </font>
                 </property>
                 <property name="text">
                  <string>蓝牙驱动Hash</string>
                 </property>
                </widget>
               </item>
               <item>
                <widget class="QLineEdit" name="lyqdLE">
                 <property name="font">
                  <font>
                   <pointsize>12</pointsize>
                  </font>
                 </property>
                </widget>
               </item>
               <item>
                <spacer name="horizontalSpacer_6">
                 <property name="orientation">
                  <enum>Qt::Horizontal</enum>
                 </property>
                 <property name="sizeHint" stdset="0">
                  <size>
                   <width>40</width>
                   <height>20</height>
                  </size>
                 </property>
                </spacer>
               </item>
              </layout>
             </item>
            </layout>
           </widget>
          </item>
          <item row="4" column="0">
           <layout class="QHBoxLayout" name="horizontalLayout_7">
            <item>
             <spacer name="horizontalSpacer_12">
              <property name="orientation">
               <enum>Qt::Horizontal</enum>
              </property>
              <property name="sizeHint" stdset="0">
               <size>
                <width>40</width>
                <height>20</height>
               </size>
              </property>
             </spacer>
            </item>
            <item>
             <widget class="QPushButton" name="otherok">
              <property name="styleSheet">
               <string notr="true">    QPushButton {background-color:#0089ff ;
    color: white;
    border-style: outset;
    border-width: 2px;
    border-radius: 10px;
    border-color: beige;
    font-size: 26px;
    padding: 6px;}

QPushButton:hover {background-color: #0072C6;         
                     }
</string>
              </property>
              <property name="text">
               <string>保存</string>
              </property>
             </widget>
            </item>
           </layout>
          </item>
         </layout>
        </widget>
       </item>
       <item row="1" column="0">
        <widget class="QGroupBox" name="groupBox_24">
         <property name="enabled">
          <bool>true</bool>
         </property>
         <property name="font">
          <font>
           <pointsize>12</pointsize>
          </font>
         </property>
         <property name="title">
          <string>查看</string>
         </property>
         <property name="flat">
          <bool>false</bool>
         </property>
         <layout class="QGridLayout" name="gridLayout_33">
          <item row="0" column="0">
           <layout class="QHBoxLayout" name="horizontalLayout_21">
            <item>
             <widget class="QGroupBox" name="groupBox_27">
              <property name="font">
               <font>
                <pointsize>12</pointsize>
               </font>
              </property>
              <property name="title">
               <string>SCU</string>
              </property>
              <layout class="QGridLayout" name="gridLayout_29">
               <item row="0" column="0">
                <layout class="QHBoxLayout" name="horizontalLayout_33">
                 <item>
                  <widget class="QLabel" name="label_72">
                   <property name="font">
                    <font>
                     <pointsize>12</pointsize>
                    </font>
                   </property>
                   <property name="text">
                    <string>SCU系统启动时间（s）</string>
                   </property>
                  </widget>
                 </item>
                 <item>
                  <widget class="QLineEdit" name="scuQDLE_3">
                   <property name="sizePolicy">
                    <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
                     <horstretch>0</horstretch>
                     <verstretch>0</verstretch>
                    </sizepolicy>
                   </property>
                   <property name="font">
                    <font>
                     <pointsize>12</pointsize>
                    </font>
                   </property>
                   <property name="readOnly">
                    <bool>true</bool>
                   </property>
                  </widget>
                 </item>
                 <item>
                  <spacer name="horizontalSpacer_25">
                   <property name="orientation">
                    <enum>Qt::Horizontal</enum>
                   </property>
                   <property name="sizeHint" stdset="0">
                    <size>
                     <width>40</width>
                     <height>20</height>
                    </size>
                   </property>
                  </spacer>
                 </item>
                </layout>
               </item>
              </layout>
             </widget>
            </item>
            <item>
             <widget class="QGroupBox" name="groupBox_13">
              <property name="font">
               <font>
                <pointsize>12</pointsize>
               </font>
              </property>
              <property name="title">
               <string>MQTTIoT配置</string>
              </property>
              <layout class="QGridLayout" name="gridLayout_27">
               <item row="0" column="0">
                <widget class="QLabel" name="label_30">
                 <property name="font">
                  <font>
                   <pointsize>12</pointsize>
                  </font>
                 </property>
                 <property name="text">
                  <string>IP</string>
                 </property>
                </widget>
               </item>
               <item row="0" column="1">
                <widget class="QLineEdit" name="ipLE_2">
                 <property name="sizePolicy">
                  <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
                   <horstretch>0</horstretch>
                   <verstretch>0</verstretch>
                  </sizepolicy>
                 </property>
                 <property name="font">
                  <font>
                   <pointsize>12</pointsize>
                  </font>
                 </property>
                </widget>
               </item>
               <item row="0" column="2">
                <spacer name="horizontalSpacer_5">
                 <property name="orientation">
                  <enum>Qt::Horizontal</enum>
                 </property>
                 <property name="sizeHint" stdset="0">
                  <size>
                   <width>191</width>
                   <height>20</height>
                  </size>
                 </property>
                </spacer>
               </item>
              </layout>
             </widget>
            </item>
            <item>
             <widget class="QGroupBox" name="groupBox_16">
              <property name="font">
               <font>
                <pointsize>12</pointsize>
               </font>
              </property>
              <property name="title">
               <string>RTC</string>
              </property>
              <layout class="QGridLayout" name="gridLayout_24">
               <item row="0" column="0">
                <layout class="QHBoxLayout" name="horizontalLayout_10">
                 <item>
                  <widget class="QLabel" name="label_37">
                   <property name="font">
                    <font>
                     <pointsize>12</pointsize>
                    </font>
                   </property>
                   <property name="text">
                    <string>RTC最大误差时间(s)：</string>
                   </property>
                  </widget>
                 </item>
                 <item>
                  <widget class="QLineEdit" name="rtcLE_2">
                   <property name="readOnly">
                    <bool>true</bool>
                   </property>
                  </widget>
                 </item>
                </layout>
               </item>
              </layout>
             </widget>
            </item>
           </layout>
          </item>
          <item row="1" column="0">
           <widget class="QGroupBox" name="groupBox_26">
            <property name="font">
             <font>
              <pointsize>12</pointsize>
             </font>
            </property>
            <property name="title">
             <string>HPLC/4G</string>
            </property>
            <layout class="QGridLayout" name="gridLayout_26">
             <item row="0" column="0">
              <layout class="QHBoxLayout" name="horizontalLayout_13">
               <item>
                <layout class="QVBoxLayout" name="verticalLayout_6">
                 <item>
                  <widget class="QCheckBox" name="sm4gCB_2">
                   <property name="enabled">
                    <bool>false</bool>
                   </property>
                   <property name="font">
                    <font>
                     <pointsize>12</pointsize>
                    </font>
                   </property>
                   <property name="text">
                    <string>4G双模</string>
                   </property>
                  </widget>
                 </item>
                 <item>
                  <widget class="QCheckBox" name="dm4gCB_2">
                   <property name="enabled">
                    <bool>false</bool>
                   </property>
                   <property name="font">
                    <font>
                     <pointsize>12</pointsize>
                    </font>
                   </property>
                   <property name="text">
                    <string>4G单模</string>
                   </property>
                  </widget>
                 </item>
                </layout>
               </item>
               <item>
                <widget class="QLabel" name="label_70">
                 <property name="font">
                  <font>
                   <pointsize>12</pointsize>
                  </font>
                 </property>
                 <property name="text">
                  <string>    FirmWare Version</string>
                 </property>
                </widget>
               </item>
               <item>
                <widget class="QLineEdit" name="firvLE_3">
                 <property name="sizePolicy">
                  <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
                   <horstretch>0</horstretch>
                   <verstretch>0</verstretch>
                  </sizepolicy>
                 </property>
                 <property name="font">
                  <font>
                   <pointsize>12</pointsize>
                  </font>
                 </property>
                 <property name="readOnly">
                  <bool>true</bool>
                 </property>
                </widget>
               </item>
               <item>
                <widget class="QLabel" name="label_71">
                 <property name="font">
                  <font>
                   <pointsize>12</pointsize>
                  </font>
                 </property>
                 <property name="text">
                  <string>    Hardware Version</string>
                 </property>
                </widget>
               </item>
               <item>
                <widget class="QLineEdit" name="harvLE_3">
                 <property name="sizePolicy">
                  <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
                   <horstretch>0</horstretch>
                   <verstretch>0</verstretch>
                  </sizepolicy>
                 </property>
                 <property name="font">
                  <font>
                   <pointsize>12</pointsize>
                  </font>
                 </property>
                 <property name="readOnly">
                  <bool>true</bool>
                 </property>
                </widget>
               </item>
               <item>
                <widget class="QLabel" name="label_77">
                 <property name="font">
                  <font>
                   <pointsize>12</pointsize>
                  </font>
                 </property>
                 <property name="text">
                  <string>    本地通讯模块</string>
                 </property>
                </widget>
               </item>
               <item>
                <widget class="QLineEdit" name="txmkLE_2">
                 <property name="sizePolicy">
                  <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
                   <horstretch>0</horstretch>
                   <verstretch>0</verstretch>
                  </sizepolicy>
                 </property>
                 <property name="font">
                  <font>
                   <pointsize>12</pointsize>
                  </font>
                 </property>
                 <property name="readOnly">
                  <bool>true</bool>
                 </property>
                </widget>
               </item>
               <item>
                <spacer name="horizontalSpacer_24">
                 <property name="orientation">
                  <enum>Qt::Horizontal</enum>
                 </property>
                 <property name="sizeHint" stdset="0">
                  <size>
                   <width>40</width>
                   <height>20</height>
                  </size>
                 </property>
                </spacer>
               </item>
              </layout>
             </item>
            </layout>
           </widget>
          </item>
          <item row="2" column="0">
           <widget class="QGroupBox" name="groupBox_28">
            <property name="font">
             <font>
              <pointsize>12</pointsize>
             </font>
            </property>
            <property name="title">
             <string>交采计量精度</string>
            </property>
            <layout class="QGridLayout" name="gridLayout_14">
             <item row="0" column="0">
              <layout class="QHBoxLayout" name="horizontalLayout_19">
               <item>
                <widget class="QLabel" name="label_73">
                 <property name="font">
                  <font>
                   <pointsize>12</pointsize>
                  </font>
                 </property>
                 <property name="text">
                  <string>电压精度误差百分比（%）</string>
                 </property>
                </widget>
               </item>
               <item>
                <widget class="QLineEdit" name="dywcLE_3">
                 <property name="sizePolicy">
                  <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
                   <horstretch>0</horstretch>
                   <verstretch>0</verstretch>
                  </sizepolicy>
                 </property>
                 <property name="font">
                  <font>
                   <pointsize>12</pointsize>
                  </font>
                 </property>
                 <property name="readOnly">
                  <bool>true</bool>
                 </property>
                </widget>
               </item>
               <item>
                <widget class="QLabel" name="label_74">
                 <property name="font">
                  <font>
                   <pointsize>12</pointsize>
                  </font>
                 </property>
                 <property name="text">
                  <string>    电流精度误差百分比（%）</string>
                 </property>
                </widget>
               </item>
               <item>
                <widget class="QLineEdit" name="dlwcLE_3">
                 <property name="sizePolicy">
                  <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
                   <horstretch>0</horstretch>
                   <verstretch>0</verstretch>
                  </sizepolicy>
                 </property>
                 <property name="font">
                  <font>
                   <pointsize>12</pointsize>
                  </font>
                 </property>
                 <property name="readOnly">
                  <bool>true</bool>
                 </property>
                </widget>
               </item>
               <item>
                <widget class="QLabel" name="label_75">
                 <property name="font">
                  <font>
                   <pointsize>12</pointsize>
                  </font>
                 </property>
                 <property name="text">
                  <string>    功率因数精度误差百分比（%）</string>
                 </property>
                </widget>
               </item>
               <item>
                <widget class="QLineEdit" name="glyswcLE_3">
                 <property name="sizePolicy">
                  <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
                   <horstretch>0</horstretch>
                   <verstretch>0</verstretch>
                  </sizepolicy>
                 </property>
                 <property name="font">
                  <font>
                   <pointsize>12</pointsize>
                  </font>
                 </property>
                 <property name="readOnly">
                  <bool>true</bool>
                 </property>
                </widget>
               </item>
               <item>
                <spacer name="horizontalSpacer_26">
                 <property name="orientation">
                  <enum>Qt::Horizontal</enum>
                 </property>
                 <property name="sizeHint" stdset="0">
                  <size>
                   <width>40</width>
                   <height>20</height>
                  </size>
                 </property>
                </spacer>
               </item>
              </layout>
             </item>
             <item row="1" column="0">
              <layout class="QHBoxLayout" name="horizontalLayout_17">
               <item>
                <widget class="QLabel" name="label_35">
                 <property name="font">
                  <font>
                   <pointsize>12</pointsize>
                  </font>
                 </property>
                 <property name="text">
                  <string>有功功率精度误差百分比（%）</string>
                 </property>
                </widget>
               </item>
               <item>
                <widget class="QLineEdit" name="ygglwcLE_2">
                 <property name="sizePolicy">
                  <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
                   <horstretch>0</horstretch>
                   <verstretch>0</verstretch>
                  </sizepolicy>
                 </property>
                 <property name="font">
                  <font>
                   <pointsize>12</pointsize>
                  </font>
                 </property>
                </widget>
               </item>
               <item>
                <widget class="QLabel" name="label_34">
                 <property name="font">
                  <font>
                   <pointsize>12</pointsize>
                  </font>
                 </property>
                 <property name="text">
                  <string>      无功功率精度误差百分比（%）</string>
                 </property>
                </widget>
               </item>
               <item>
                <widget class="QLineEdit" name="wgglwcLE_2">
                 <property name="sizePolicy">
                  <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
                   <horstretch>0</horstretch>
                   <verstretch>0</verstretch>
                  </sizepolicy>
                 </property>
                 <property name="font">
                  <font>
                   <pointsize>12</pointsize>
                  </font>
                 </property>
                </widget>
               </item>
               <item>
                <spacer name="horizontalSpacer_28">
                 <property name="orientation">
                  <enum>Qt::Horizontal</enum>
                 </property>
                 <property name="sizeHint" stdset="0">
                  <size>
                   <width>40</width>
                   <height>20</height>
                  </size>
                 </property>
                </spacer>
               </item>
              </layout>
             </item>
            </layout>
           </widget>
          </item>
          <item row="3" column="0">
           <widget class="QGroupBox" name="groupBox_17">
            <property name="font">
             <font>
              <pointsize>12</pointsize>
             </font>
            </property>
            <property name="title">
             <string> 蓝牙</string>
            </property>
            <layout class="QGridLayout" name="gridLayout_30">
             <item row="0" column="0">
              <layout class="QHBoxLayout" name="horizontalLayout_23">
               <item>
                <widget class="QLabel" name="label_39">
                 <property name="font">
                  <font>
                   <pointsize>12</pointsize>
                  </font>
                 </property>
                 <property name="text">
                  <string>蓝牙驱动Hash</string>
                 </property>
                </widget>
               </item>
               <item>
                <widget class="QLineEdit" name="lyqdLE_2">
                 <property name="font">
                  <font>
                   <pointsize>12</pointsize>
                  </font>
                 </property>
                </widget>
               </item>
               <item>
                <spacer name="horizontalSpacer_10">
                 <property name="orientation">
                  <enum>Qt::Horizontal</enum>
                 </property>
                 <property name="sizeHint" stdset="0">
                  <size>
                   <width>40</width>
                   <height>20</height>
                  </size>
                 </property>
                </spacer>
               </item>
              </layout>
             </item>
            </layout>
           </widget>
          </item>
         </layout>
        </widget>
       </item>
       <item row="2" column="0">
        <spacer name="verticalSpacer_3">
         <property name="orientation">
          <enum>Qt::Vertical</enum>
         </property>
         <property name="sizeHint" stdset="0">
          <size>
           <width>17</width>
           <height>282</height>
          </size>
         </property>
        </spacer>
       </item>
      </layout>
      <widget class="QLabel" name="label_36">
       <property name="geometry">
        <rect>
         <x>9</x>
         <y>387</y>
         <width>16</width>
         <height>16</height>
        </rect>
       </property>
       <property name="text">
        <string/>
       </property>
      </widget>
     </widget>
    </widget>
   </item>
  </layout>
 </widget>
 <resources/>
 <connections/>
</ui>

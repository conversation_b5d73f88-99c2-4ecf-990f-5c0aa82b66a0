#include "ctestbatchinfo.h"
#include "ui_ctestbatchinfo.h"
#include"msk_global.h"
#include<QMessageBox>
#include<QAxObject>
#include<QDir>
#include<QFileDialog>
#include<QDebug>

cTestBatchInfo::cTestBatchInfo(QWidget *parent) :
    QWidget(parent),
    ui(new Ui::cTestBatchInfo)
{
    ui->setupUi(this);
    this->setWindowTitle("批次信息");

    ui->tableWidget->setColumnCount(3);
    QTableWidgetItem *pitem = new QTableWidgetItem();
    pitem->setText("ESN");
    ui->tableWidget->setHorizontalHeaderItem(0, pitem);

    pitem = new QTableWidgetItem();
    pitem->setText("ESAM");
    ui->tableWidget->setHorizontalHeaderItem(1, pitem);

    pitem = new QTableWidgetItem();
    pitem->setText("检测批次");
    ui->tableWidget->setHorizontalHeaderItem(2, pitem);

     ui->tableWidget->setColumnWidth(0,300);
     ui->tableWidget->setColumnWidth(1,300);
     ui->tableWidget->setColumnWidth(2,200);
     ui->tableWidget->setEditTriggers(QAbstractItemView::NoEditTriggers);

     QString s = queryCfg(CheckItemIni,"currentpc","currentpc");
     QSettings  settings(CheckItemIni, QSettings::IniFormat);
     settings.setIniCodec(QTextCodec::codecForName("UTF-8"));
     settings.beginGroup("testpc");
     QStringList clist = settings.allKeys();
     for (int i =0 ;i<clist.size();i++) {
         QString m = queryCfg(CheckItemIni,"testpc",clist[i]);
         QStringList sl = m.split("@");
         qDebug()<<sl[0];
         ui->comboBox->addItem(sl[0]);
     }
      m_batch = queryCfg(CheckItemIni,"currentpc","currentpc");
      ui->comboBox->setCurrentText(m_batch);
      m_database = new Database();
      QString str1 = QString("testinfo");
      if(m_database->isTableExist(str1))
      {
          QStringList  sl = m_database->queryTable(m_batch);
          this->UpdateTable(sl);
      }
     if(m_database->openDb())
     {
         m_database->createTable();
     }
//     if(m_database->isTableExist(str1))
//     {
//         w2dba w2dbaTest1 = {"111234111666", "11111133444333333333333", "1111"};
//         w2dba w2dbaTest2 = {"14567811", "1111113299999", "0331test"};
//         //m_database->singleInsertData(w2dbaTest1);
//         //m_database->singleInsertData(w2dbaTest2);
//     }

}

cTestBatchInfo::~cTestBatchInfo()
{
    delete ui;
}

void cTestBatchInfo::on_comboBox_currentIndexChanged(const QString &arg1)
{
    QString str1 = QString("testinfo");
    if(m_database->isTableExist(str1))
    {
        QStringList  sl = m_database->queryTable(arg1);
        this->UpdateTable(sl);
    }
}
void cTestBatchInfo::UpdateTable(QStringList sl)
{
    ui->tableWidget->setRowCount(sl.size());
    for(int i = 0; i < sl.size();i++)
    {
        QStringList iList = sl[i].split(",");
        ui->tableWidget->setItem(i,0,new QTableWidgetItem(iList[1]));
        ui->tableWidget->setItem(i,1,new QTableWidgetItem(iList[2]));
        ui->tableWidget->setItem(i,2,new QTableWidgetItem(iList[3]));
    }
}

void cTestBatchInfo::on_exportBtn_clicked()
{
    QString s = ui->comboBox->currentText();
    QString fileName = QFileDialog::getSaveFileName(
            nullptr,
            tr("保存文件"),
            QDir::homePath() + "/" + s,
            tr("Excel文件 (*.xlsx)")
        );
        if (fileName.isEmpty()) return;

        if (!fileName.endsWith(".xlsx", Qt::CaseInsensitive)) {
            fileName += ".xlsx";
        }
        this->exportFile(fileName);

}
void cTestBatchInfo::exportFile(QString s)
{
    QString filePath = s;

    // 检查文件是否存在
    bool fileExists = QFile::exists(filePath);

    //创建Excel应用程序对象
    QAxObject *excel = new QAxObject("Excel.Application");
    excel->setProperty("Visible", false);
    excel->setProperty("DisplayAlerts", false);

    QAxObject *workbooks = excel->querySubObject("Workbooks");
    QAxObject *workbook;

    try {
        if (fileExists) {
            workbook = workbooks->querySubObject("Open(const QString&)", filePath);
        } else {
            workbook = workbooks->querySubObject("Add");
        }

        QAxObject *sheets = workbook->querySubObject("Sheets");
        QAxObject *sheet = sheets->querySubObject("Item(int)", 1);

        // 写入表头（仅新文件）
        if (!fileExists) {

            QAxObject *cell0 = sheet->querySubObject("Cells(int,int)", 1, 1);
            cell0->setProperty("Value2", "序号");
            delete cell0;
            QAxObject *cell = sheet->querySubObject("Cells(int,int)", 1, 2);
            cell->setProperty("Value2", "ESN号");
            delete cell;
            QAxObject *cell1 = sheet->querySubObject("Cells(int,int)", 1, 3);
            cell1->setProperty("Value2", "ESAM号");
            delete cell1;
        }
        QAxObject* column = sheet->querySubObject("Columns(const QVariant&)", QVariant("C"));
        column->setProperty("NumberFormat", "@");


        for (int col = 0; col < ui->tableWidget->rowCount(); col++)
        {
             QAxObject *cell2 = sheet->querySubObject("Cells(int,int)", col+2, 1);
             cell2->setProperty("Value2", col+1);
             delete cell2;

             QAxObject *cell = sheet->querySubObject("Cells(int,int)", col+2, 2);
             QTableWidgetItem *item = ui->tableWidget->item(col, 0);
             cell->setProperty("Value2", item->text());
             delete cell;

             QAxObject *cell1 = sheet->querySubObject("Cells(int,int)", col+2, 3);
             QTableWidgetItem *item1 = ui->tableWidget->item(col, 1);
             cell1->setProperty("Value2", item1->text());
             delete cell1;
        }

        // 保存文件（直接覆盖）
        workbook->dynamicCall("SaveAs(const QString&)", filePath);

        // 释放资源
        delete sheet;
        delete sheets;
        workbook->dynamicCall("Close()");
        delete workbook;
        QMessageBox::information(this, "提示", "保存成功！");
    } catch (...) {
        QMessageBox::critical(this, "错误", "保存Excel文件失败！");
    }

    // 彻底退出Excel
    excel->dynamicCall("Quit()");
    delete workbooks;
    delete excel;
}

#include "cscancode.h"
#include "ui_cscancode.h"
#include<QMessageBox>
#include"msk_global.h"
#include<QDebug>
#include<QComboBox>
#include<QDir>

cScanCode::cScanCode(QWidget *parent) :
    QWidget(parent),
    ui(new Ui::cScanCode)
{
    ui->setupUi(this);
    this->setWindowTitle("检测批次");
    QString s = queryCfg(CheckItemIni,"currentpc","currentpc");
    for (int i = 0;i<ui->tableWidget->rowCount();i++) {
        if(ui->tableWidget->item(i,1)->text()==s)
        {
            m_set->onslotsTestPc(ui->tableWidget->item(i,0)->text());
            qDebug()<<ui->tableWidget->item(i,0)->text();
        }
    }
    ui->lineEdit->setText(s);
    m_set =new  cSetConfigure();

    ui->tableWidget->verticalHeader()->setVisible(false);
    ui->tableWidget->setColumnCount(3);
    ui->tableWidget->setEditTriggers(QAbstractItemView::NoEditTriggers);

    QTableWidgetItem *pitem = new QTableWidgetItem();
    pitem->setText("批次编号");
    pitem->setSelected(true);
    ui->tableWidget->setHorizontalHeaderItem(0, pitem);

    pitem = new QTableWidgetItem();
    pitem->setText("批次名");
    ui->tableWidget->setHorizontalHeaderItem(1, pitem);

    pitem = new QTableWidgetItem();
    pitem->setText("检测方案");
    ui->tableWidget->setHorizontalHeaderItem(2, pitem);

    QSettings  settings(CheckItemIni, QSettings::IniFormat);
    settings.setIniCodec(QTextCodec::codecForName("UTF-8"));
    settings.beginGroup("testpc");
    QStringList clist = settings.allKeys();
    ui->tableWidget->setRowCount(clist.size());
    for(int i = 0;i<clist.size();i++)
    {
        int n = clist[i].toInt()-1;
        ui->tableWidget->setItem(n,0,new QTableWidgetItem(clist[i]));
        QString s = queryCfg(CheckItemIni,"testpc",clist[i]);
        QStringList sl = s.split("@");
        ui->tableWidget->setItem(n,1,new QTableWidgetItem(sl[0]));
        ui->tableWidget->setItem(n,2,new QTableWidgetItem(sl[1]));
    }

}
cScanCode::~cScanCode()
{
    delete ui;
}
void cScanCode::on_CreatePcBt_clicked()
{
    ui->tableWidget->setEditTriggers(QAbstractItemView::CurrentChanged);

    ui->tableWidget->setRowCount(ui->tableWidget->rowCount() + 1);

    int num = ui->tableWidget->rowCount();

    QTableWidgetItem *item = new QTableWidgetItem();
    item->setText(QString("%1").arg(num));
    ui->tableWidget->setItem(num - 1, 0, item);

    QComboBox *comboBox = new QComboBox();
    QSettings  settings(CheckItemIni, QSettings::IniFormat);
    settings.setIniCodec(QTextCodec::codecForName("UTF-8"));

    settings.beginGroup("compareversion");
    QStringList clist = settings.allKeys();
    for(auto item:clist)
    {
        comboBox->addItem(item);
    }


    ui->tableWidget->setCellWidget(num - 1, 2, comboBox);

    connect(comboBox, QOverload<int>::of(&QComboBox::currentIndexChanged), [=](int index) {
        qDebug() << "Selected option:" << comboBox->itemText(index);
    });
}
void cScanCode::on_okBt_clicked()
{
      setCfg(CheckItemIni,"currentpc","currentpc",ui->lineEdit->text());
      QComboBox *comboBox = new QComboBox();
      QSettings  settings(CheckItemIni, QSettings::IniFormat);
      settings.setIniCodec(QTextCodec::codecForName("UTF-8"));

      settings.beginGroup("compareversion");
      QStringList clist = settings.allKeys();
      for(auto item:clist)
      {
          comboBox->addItem(item);
      }
     ui->tableWidget->setEditTriggers(QAbstractItemView::NoEditTriggers);
     int num = ui->tableWidget->rowCount();

        QString strKey = ui->tableWidget->item(num-1,0)->text();
        QString pc = ui->tableWidget->item(num-1,1)->text();
        QComboBox *comboBox1 = qobject_cast<QComboBox*>(ui->tableWidget->cellWidget(num-1, 2));

        if (comboBox1) {
            QString fangan  = comboBox1->currentText();
            QString s = pc+"@"+fangan;
            setCfg(CheckItemIni,"testpc",QString::number(num),s);
        }
     QString s = ui->lineEdit->text();
     for (int i = 0;i<ui->tableWidget->rowCount();i++) {
         if(ui->tableWidget->item(i,1)->text() == s)
         {
             m_set->onslotsTestPc(ui->tableWidget->item(i,0)->text());
             qDebug()<<ui->tableWidget->item(i,0)->text();
         }
     }
     this->close();
     m_set->show();
}

void cScanCode::on_tableWidget_itemDoubleClicked(QTableWidgetItem *item)
{
    QString s = ui->tableWidget->item(item->row(),1)->text();
    ui->lineEdit->setText(s);
}

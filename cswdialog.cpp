﻿#include "cswdialog.h"
#include "ui_cswdialog.h"
#include <QComboBox>

CSWDialog::CSWDialog(QWidget *parent) :
    QDialog(parent),
    ui(new Ui::CSWDialog)
{
    ui->setupUi(this);
    ui->tableWidget->verticalHeader()->setVisible(false);;

    ui->tableWidget->setColumnCount(3);
    QTableWidgetItem *pitem = new QTableWidgetItem();
    pitem->setText("表位");
    ui->tableWidget->setHorizontalHeaderItem(0, pitem);

    pitem = new QTableWidgetItem();
    pitem->setText("SW1");
    ui->tableWidget->setHorizontalHeaderItem(1, pitem);

    pitem = new QTableWidgetItem();
    pitem->setText("SW2");
    ui->tableWidget->setHorizontalHeaderItem(2, pitem);

    setWindowFlags(windowFlags() & ~Qt::WindowCloseButtonHint);
}

CSWDialog::~CSWDialog()
{
    delete ui;
}

void CSWDialog::on_ok_clicked()
{
    SGntJyXlGc  gntJyXl;
    gntJyXl.oid = 0;
    SGntJyXlJg  gntJyJg;
    gntJyJg.oid = 0;
    gntJyXl.strCsDl = gntJyJg.strCsDl = "SW1/2按键";
    gntJyJg.strCsXl = gntJyXl.strCsXl = "SW1/2检查";
    for (int m = 0; m < ui->tableWidget->rowCount(); ++m)
    {
        int nBw = ui->tableWidget->item(m, 0)->text().toInt();
        gntJyXl.nBw = nBw;
        gntJyJg.nBw = nBw;
        QComboBox *pcb = (QComboBox *)ui->tableWidget->cellWidget(m, 1);
        if(pcb->currentText() == "正常")
        {
            gntJyXl.strCsGc = "SW1正常";
        }
        else
        {
            gntJyXl.strCsGc = "SW1异常";

        }
        QApplication::sendEvent((QObject*)g_pFuncWindow, new CCsXlGcMsg(gntJyXl) );

        QComboBox *pcb2 = (QComboBox *)ui->tableWidget->cellWidget(m, 2);
        if(pcb2->currentText() == "正常")
        {
            gntJyXl.strCsGc = "SW2正常";
        }
        else
        {
            gntJyXl.strCsGc = "SW2异常";
        }
        QApplication::sendEvent((QObject*)g_pFuncWindow, new CCsXlGcMsg(gntJyXl) );
        if(pcb->currentText() == "正常" && pcb2->currentText() == "正常")
        {
            gntJyJg.bCsXlJg = true;
        }
        else
        {
            gntJyJg.bCsXlJg = false;
        }
        QApplication::sendEvent((QObject*)g_pFuncWindow, new CCsXlJgMsg(gntJyJg) );

    }
    hide();
    emit(okSig());
}

void CSWDialog::setSW(std::vector<int> &vtSw)
{
    ui->tableWidget->setRowCount(vtSw.size());

    QStringList strList;
    strList << "正常" << "异常";

    for (int m = 0; m < vtSw.size(); ++m)
    {
        QTableWidgetItem *pitem = new QTableWidgetItem();
        pitem->setText(QString::number(vtSw[m]));
        ui->tableWidget->setItem(m, 0, pitem);

        QComboBox * pCB = new QComboBox();
        pCB->addItems(strList);
        ui->tableWidget->setCellWidget(m, 1, pCB);

        pCB = new QComboBox();
        pCB->addItems(strList);
        ui->tableWidget->setCellWidget(m, 2, pCB);
    }
}

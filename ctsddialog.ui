<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>CTsdDialog</class>
 <widget class="QDialog" name="CTsdDialog">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>750</width>
    <height>500</height>
   </rect>
  </property>
  <property name="minimumSize">
   <size>
    <width>750</width>
    <height>500</height>
   </size>
  </property>
  <property name="maximumSize">
   <size>
    <width>750</width>
    <height>500</height>
   </size>
  </property>
  <property name="windowTitle">
   <string>终端停上电参数配置</string>
  </property>
  <layout class="QGridLayout" name="gridLayout_4">
   <item row="0" column="0">
    <widget class="QGroupBox" name="groupBox">
     <property name="title">
      <string>停电数据采集配置参数</string>
     </property>
     <layout class="QGridLayout" name="gridLayout_2">
      <item row="0" column="0">
       <layout class="QHBoxLayout" name="horizontalLayout_9">
        <item>
         <layout class="QVBoxLayout" name="verticalLayout">
          <item>
           <layout class="QHBoxLayout" name="horizontalLayout">
            <item>
             <widget class="QLabel" name="label">
              <property name="text">
               <string>采集标志</string>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QRadioButton" name="yxCB">
              <property name="text">
               <string>有效</string>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QRadioButton" name="wxCB">
              <property name="text">
               <string>无效</string>
              </property>
             </widget>
            </item>
           </layout>
          </item>
          <item>
           <layout class="QHBoxLayout" name="horizontalLayout_2">
            <item>
             <spacer name="horizontalSpacer">
              <property name="orientation">
               <enum>Qt::Horizontal</enum>
              </property>
              <property name="sizeHint" stdset="0">
               <size>
                <width>40</width>
                <height>20</height>
               </size>
              </property>
             </spacer>
            </item>
            <item>
             <widget class="QComboBox" name="cldCB">
              <property name="sizePolicy">
               <sizepolicy hsizetype="Expanding" vsizetype="Fixed">
                <horstretch>0</horstretch>
                <verstretch>0</verstretch>
               </sizepolicy>
              </property>
              <item>
               <property name="text">
                <string>采集设置测量点</string>
               </property>
              </item>
              <item>
               <property name="text">
                <string>随机选择测量点</string>
               </property>
              </item>
             </widget>
            </item>
           </layout>
          </item>
          <item>
           <layout class="QHBoxLayout" name="horizontalLayout_3">
            <item>
             <widget class="QLabel" name="label_2">
              <property name="text">
               <string>抄读时间间隔</string>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QLineEdit" name="cdsjjgLE"/>
            </item>
            <item>
             <widget class="QLabel" name="label_5">
              <property name="text">
               <string>(小时)</string>
              </property>
             </widget>
            </item>
           </layout>
          </item>
          <item>
           <layout class="QHBoxLayout" name="horizontalLayout_4">
            <item>
             <widget class="QLabel" name="label_3">
              <property name="text">
               <string>抄读时间限值</string>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QLineEdit" name="cdsjxzLE"/>
            </item>
            <item>
             <widget class="QLabel" name="label_4">
              <property name="text">
               <string>(分钟)</string>
              </property>
             </widget>
            </item>
           </layout>
          </item>
         </layout>
        </item>
        <item>
         <widget class="QGroupBox" name="groupBox_2">
          <property name="title">
           <string>需要读取停电时间电能表</string>
          </property>
          <layout class="QGridLayout" name="gridLayout">
           <item row="0" column="0">
            <layout class="QHBoxLayout" name="horizontalLayout_8">
             <item>
              <widget class="QTableWidget" name="tableWidget">
               <property name="sizePolicy">
                <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
                 <horstretch>0</horstretch>
                 <verstretch>0</verstretch>
                </sizepolicy>
               </property>
               <column>
                <property name="text">
                 <string>序号</string>
                </property>
               </column>
               <column>
                <property name="text">
                 <string>电表地址</string>
                </property>
               </column>
              </widget>
             </item>
             <item>
              <layout class="QVBoxLayout" name="verticalLayout_2">
               <item>
                <layout class="QHBoxLayout" name="horizontalLayout_5">
                 <item>
                  <widget class="QLabel" name="label_6">
                   <property name="text">
                    <string>字节数</string>
                   </property>
                  </widget>
                 </item>
                 <item>
                  <widget class="QComboBox" name="zjsCB">
                   <property name="sizePolicy">
                    <sizepolicy hsizetype="Expanding" vsizetype="Fixed">
                     <horstretch>0</horstretch>
                     <verstretch>0</verstretch>
                    </sizepolicy>
                   </property>
                  </widget>
                 </item>
                </layout>
               </item>
               <item>
                <layout class="QHBoxLayout" name="horizontalLayout_6">
                 <item>
                  <widget class="QLabel" name="label_7">
                   <property name="text">
                    <string>地址：</string>
                   </property>
                  </widget>
                 </item>
                 <item>
                  <spacer name="horizontalSpacer_2">
                   <property name="orientation">
                    <enum>Qt::Horizontal</enum>
                   </property>
                   <property name="sizeHint" stdset="0">
                    <size>
                     <width>40</width>
                     <height>20</height>
                    </size>
                   </property>
                  </spacer>
                 </item>
                </layout>
               </item>
               <item>
                <widget class="QLineEdit" name="dzLE"/>
               </item>
               <item>
                <layout class="QHBoxLayout" name="horizontalLayout_7">
                 <item>
                  <widget class="QPushButton" name="addBtn">
                   <property name="styleSheet">
                    <string notr="true">    QPushButton {background-color:#0089ff ;
    color: white;
    border-style: outset;
    border-width: 2px;
    border-radius: 10px;
    border-color: beige;
    font-size: 16px;
    padding: 6px;}

QPushButton:hover {background-color: #0072C6;         
                     }
</string>
                   </property>
                   <property name="text">
                    <string>添加</string>
                   </property>
                  </widget>
                 </item>
                 <item>
                  <widget class="QPushButton" name="delBtn">
                   <property name="styleSheet">
                    <string notr="true">    QPushButton {background-color:#0089ff ;
    color: white;
    border-style: outset;
    border-width: 2px;
    border-radius: 10px;
    border-color: beige;
    font-size: 16px;
    padding: 6px;}

QPushButton:hover {background-color: #0072C6;         
                     }
</string>
                   </property>
                   <property name="text">
                    <string>删除</string>
                   </property>
                  </widget>
                 </item>
                 <item>
                  <widget class="QPushButton" name="clearBtn">
                   <property name="styleSheet">
                    <string notr="true">    QPushButton {background-color:#0089ff ;
    color: white;
    border-style: outset;
    border-width: 2px;
    border-radius: 10px;
    border-color: beige;
    font-size: 16px;
    padding: 6px;}

QPushButton:hover {background-color: #0072C6;         
                     }

</string>
                   </property>
                   <property name="text">
                    <string>清空</string>
                   </property>
                  </widget>
                 </item>
                </layout>
               </item>
              </layout>
             </item>
            </layout>
           </item>
          </layout>
         </widget>
        </item>
       </layout>
      </item>
     </layout>
    </widget>
   </item>
   <item row="1" column="0">
    <widget class="QGroupBox" name="groupBox_3">
     <property name="title">
      <string>停电事件甄别限值参数</string>
     </property>
     <layout class="QGridLayout" name="gridLayout_3">
      <item row="0" column="0">
       <layout class="QHBoxLayout" name="horizontalLayout_17">
        <item>
         <layout class="QVBoxLayout" name="verticalLayout_3">
          <item>
           <layout class="QHBoxLayout" name="horizontalLayout_14">
            <item>
             <widget class="QLabel" name="label_8">
              <property name="text">
               <string>停电时间最小有效间隔</string>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QLineEdit" name="tdzxyxjgLE"/>
            </item>
            <item>
             <widget class="QLabel" name="label_12">
              <property name="text">
               <string>（分钟）</string>
              </property>
             </widget>
            </item>
           </layout>
          </item>
          <item>
           <layout class="QHBoxLayout" name="horizontalLayout_13">
            <item>
             <widget class="QLabel" name="label_9">
              <property name="text">
               <string>停电时间最大有效间隔</string>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QLineEdit" name="tdzdyxjgLE"/>
            </item>
            <item>
             <widget class="QLabel" name="label_13">
              <property name="text">
               <string>（分钟）</string>
              </property>
             </widget>
            </item>
           </layout>
          </item>
          <item>
           <layout class="QHBoxLayout" name="horizontalLayout_12">
            <item>
             <widget class="QLabel" name="label_10">
              <property name="text">
               <string>停电起止时间偏差限值</string>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QLineEdit" name="tdqzpcxzLE"/>
            </item>
            <item>
             <widget class="QLabel" name="label_14">
              <property name="text">
               <string>（分钟）</string>
              </property>
             </widget>
            </item>
           </layout>
          </item>
          <item>
           <layout class="QHBoxLayout" name="horizontalLayout_11">
            <item>
             <widget class="QLabel" name="label_11">
              <property name="text">
               <string>停电时间区段偏差限值</string>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QLineEdit" name="tdqdpcxzLE"/>
            </item>
            <item>
             <widget class="QLabel" name="label_15">
              <property name="text">
               <string>（分钟）</string>
              </property>
             </widget>
            </item>
           </layout>
          </item>
         </layout>
        </item>
        <item>
         <widget class="QLabel" name="label_16">
          <property name="minimumSize">
           <size>
            <width>30</width>
            <height>0</height>
           </size>
          </property>
          <property name="maximumSize">
           <size>
            <width>30</width>
            <height>16777215</height>
           </size>
          </property>
          <property name="text">
           <string/>
          </property>
         </widget>
        </item>
        <item>
         <layout class="QVBoxLayout" name="verticalLayout_4">
          <item>
           <layout class="QHBoxLayout" name="horizontalLayout_15">
            <item>
             <widget class="QLabel" name="label_17">
              <property name="text">
               <string>停电发生电压限值</string>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QLineEdit" name="tddydzLE"/>
            </item>
            <item>
             <widget class="QLabel" name="label_19">
              <property name="text">
               <string>（V）</string>
              </property>
             </widget>
            </item>
           </layout>
          </item>
          <item>
           <layout class="QHBoxLayout" name="horizontalLayout_16">
            <item>
             <widget class="QLabel" name="label_18">
              <property name="text">
               <string>停电恢复电压限值</string>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QLineEdit" name="tdhfdyszLE"/>
            </item>
            <item>
             <widget class="QLabel" name="label_20">
              <property name="text">
               <string>（V）</string>
              </property>
             </widget>
            </item>
           </layout>
          </item>
         </layout>
        </item>
       </layout>
      </item>
     </layout>
    </widget>
   </item>
   <item row="2" column="0">
    <layout class="QHBoxLayout" name="horizontalLayout_10">
     <item>
      <spacer name="horizontalSpacer_3">
       <property name="orientation">
        <enum>Qt::Horizontal</enum>
       </property>
       <property name="sizeHint" stdset="0">
        <size>
         <width>40</width>
         <height>20</height>
        </size>
       </property>
      </spacer>
     </item>
     <item>
      <widget class="QPushButton" name="okBtn">
       <property name="styleSheet">
        <string notr="true">    QPushButton {background-color:#0089ff ;
    color: white;
    border-style: outset;
    border-width: 2px;
    border-radius: 10px;
    border-color: beige;
    font-size: 26px;
    padding: 6px;}

QPushButton:hover {background-color: #0072C6;         
                     }
</string>
       </property>
       <property name="text">
        <string>保存</string>
       </property>
      </widget>
     </item>
    </layout>
   </item>
  </layout>
 </widget>
 <resources/>
 <connections/>
</ui>

﻿#include "cfunctionwindow.h"
#include "chomedialog.h"
#include"ccheckwindow.h"


#include <QApplication>

int main(int argc, char *argv[])
{
    QApplication a(argc, argv);

    QIcon icon("./mskscuoutautocheck.ico");

    CHomeDialog home;
    if(home.exec() == QDialog::Accepted)
    {
        if(home.getTtType() == 1)
        {
            // 功能台
            CFunctionWindow *cfunc = new CFunctionWindow();
            qDebug() << "func:" << cfunc;
            g_pFuncWindow = cfunc;
            cfunc->setWindowIcon(icon);
            cfunc->showMaximized();
        }
        else if(home.getTtType() == 2)
        {
            // 校表台
            CCheckWindow *check = new CCheckWindow();
            check->showMaximized();
        }

    }
    else
    {
        return 0;
    }

    return a.exec();
}

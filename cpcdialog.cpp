﻿#include "cpcdialog.h"
#include "ui_cpcdialog.h"
#include <QDebug>
#include <QMessageBox>
#include <QThread>
CPcDialog::CPcDialog(QWidget *parent) :
    QDialog(parent),
    ui(new Ui::CPcDialog)
{
    ui->setupUi(this);
    tableInit();
}

CPcDialog::~CPcDialog()
{
    delete ui;
}

void CPcDialog::initPc(quint64 nfaID, std::vector<SPC> &vtpc)
{
    m_nFAID = nfaID;

    ui->tableWidget->clearContents();
    ui->tableWidget->setRowCount(0);
    ui->tableWidget->setRowCount(vtpc.size());

    for (int m = 0; m < vtpc.size(); ++m)
    {
        SPC &pc = vtpc[m];
        QTableWidgetItem *pitem = new QTableWidgetItem();
        pitem->setFlags(pitem->flags() & ~Qt::ItemIsEnabled);
        pitem->setText(pc.strID);
        ui->tableWidget->setItem(m, EPC_ID, pitem);

        pitem = new QTableWidgetItem();
        pitem->setText(pc.strName);
        ui->tableWidget->setItem(m, EPC_NAME, pitem);
    }
}

void CPcDialog::tableInit()
{
    ui->tableWidget->setSelectionBehavior(QAbstractItemView::SelectRows);
    ui->tableWidget->setSelectionMode(QAbstractItemView::SingleSelection);
    ui->tableWidget->setAlternatingRowColors(true);


    ui->tableWidget->setColumnCount(EPC_MAX);

    QTableWidgetItem *pitem = new QTableWidgetItem();
    pitem->setText("ID");
    ui->tableWidget->setHorizontalHeaderItem(EPC_ID, pitem);

    pitem = new QTableWidgetItem();
    pitem->setText("名称");
    ui->tableWidget->setHorizontalHeaderItem(EPC_NAME,pitem);

    ui->tableWidget->setAlternatingRowColors(true);
    ui->tableWidget->setColumnWidth(EPC_NAME, 600);

    ui->tableWidget->hideColumn(EPC_ID);

   // connect(ui->tableWidget, SIGNAL(itemChanged(QTableWidgetItem *)), this, SLOT(on_tableWidget_itemChged(QTableWidgetItem *)));
}

// 添加
void CPcDialog::on_addBtn_clicked()
{

    CPcAddDialog pcadd;
    pcadd.setFaId(m_nFAID);
    QStringList strPcList;
    for (int m = 0; m < ui->tableWidget->rowCount(); ++m)
    {
        strPcList << ui->tableWidget->item(m, EPC_NAME)->text();
    }
    pcadd.setPcList(strPcList);
    if(pcadd.exec() != QDialog::Accepted)
    {
        return;
    }

    int nRow = ui->tableWidget->rowCount();
    ui->tableWidget->setRowCount(nRow+1);

    int pcID = queryCfg(strScuOutAutoCheckIni, "PCID", "id").toInt();
    QString strID = QString::number(++pcID);
    setCfg(strScuOutAutoCheckIni, "PCID", "id", strID);

    QTableWidgetItem *pitem = new QTableWidgetItem();
    pitem->setText(strID);
    pitem->setFlags(pitem->flags() & ~Qt::ItemIsEnabled);
    ui->tableWidget->setItem(nRow, EPC_ID, pitem);


    pitem = new QTableWidgetItem();
    pitem->setText(pcadd.getPcName());
    ui->tableWidget->setItem(nRow, EPC_NAME, pitem);
}

void CPcDialog::on_okBtn_clicked()
{

    std::map<QString, QString> mapCfg;

    for (int m = 0; m < ui->tableWidget->rowCount(); ++m)
    {
        QString s1 = ui->tableWidget->item(m, EPC_ID)->text();
        QString s2;
        QTableWidgetItem *pitem = ui->tableWidget->item(m, EPC_NAME);
        if(pitem != nullptr)
        {
            s2 = pitem->text();
            mapCfg[s1] = s2;
        }
    }





    emit(refreshCfg(mapCfg));
}

// 删除批次
void CPcDialog::on_delBtn_clicked()
{
    int nRow = ui->tableWidget->currentRow();
    if(nRow == -1)
    {
        QMessageBox::about(this, "删除", "请先选择批次");
        return;
    }

    QString ss = "删除批次：" ;
    if( ui->tableWidget->item(nRow, EPC_NAME) != nullptr)
    {
        ss += ui->tableWidget->item(nRow, EPC_NAME)->text();
    }
    ss += ", 是否继续？";
    if(QMessageBox::information(this, "批次删除", ss, "是", "否") != 0)
        return;

    ui->tableWidget->removeRow(nRow);
}

void CPcDialog::on_tableWidget_itemChged(QTableWidgetItem *pitem)
{
    if(pitem == nullptr)
        return;
    int col = pitem->column();
    if(col != EPC_NAME)
        return;
    if(pitem->text().isEmpty())
        return;
    int nRow = pitem->row();

    for (int m = 0; m < ui->tableWidget->rowCount(); ++m)
    {
        if(m == nRow)
            continue;
        QTableWidgetItem *item = ui->tableWidget->item(m, EPC_NAME);
        if(item== nullptr || item->text().isEmpty())
            continue;
        if(item->text() == pitem->text())
        {
            QMessageBox::about(this, "批次", "存在相同的批次名称，请重新设置");
            pitem->setText("");
            break;
        }
    }
}

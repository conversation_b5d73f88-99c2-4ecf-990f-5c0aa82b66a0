<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>CComPortDialog</class>
 <widget class="QDialog" name="CComPortDialog">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>400</width>
    <height>460</height>
   </rect>
  </property>
  <property name="minimumSize">
   <size>
    <width>400</width>
    <height>460</height>
   </size>
  </property>
  <property name="maximumSize">
   <size>
    <width>400</width>
    <height>460</height>
   </size>
  </property>
  <property name="font">
   <font>
    <pointsize>12</pointsize>
   </font>
  </property>
  <property name="windowTitle">
   <string>Dialog</string>
  </property>
  <layout class="QGridLayout" name="gridLayout">
   <item row="0" column="0">
    <layout class="QHBoxLayout" name="horizontalLayout_10">
     <item>
      <spacer name="horizontalSpacer_19">
       <property name="orientation">
        <enum>Qt::Horizontal</enum>
       </property>
       <property name="sizeHint" stdset="0">
        <size>
         <width>40</width>
         <height>20</height>
        </size>
       </property>
      </spacer>
     </item>
     <item>
      <widget class="QCheckBox" name="checkBox">
       <property name="text">
        <string>自动连接</string>
       </property>
      </widget>
     </item>
     <item>
      <spacer name="horizontalSpacer_20">
       <property name="orientation">
        <enum>Qt::Horizontal</enum>
       </property>
       <property name="sizeHint" stdset="0">
        <size>
         <width>40</width>
         <height>20</height>
        </size>
       </property>
      </spacer>
     </item>
    </layout>
   </item>
   <item row="1" column="0">
    <layout class="QHBoxLayout" name="horizontalLayout">
     <item>
      <spacer name="horizontalSpacer">
       <property name="orientation">
        <enum>Qt::Horizontal</enum>
       </property>
       <property name="sizeHint" stdset="0">
        <size>
         <width>40</width>
         <height>20</height>
        </size>
       </property>
      </spacer>
     </item>
     <item>
      <widget class="QLabel" name="label">
       <property name="text">
        <string>连接状态：</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QLabel" name="label_2">
       <property name="text">
        <string>未连接</string>
       </property>
      </widget>
     </item>
     <item>
      <spacer name="horizontalSpacer_2">
       <property name="orientation">
        <enum>Qt::Horizontal</enum>
       </property>
       <property name="sizeHint" stdset="0">
        <size>
         <width>40</width>
         <height>20</height>
        </size>
       </property>
      </spacer>
     </item>
    </layout>
   </item>
   <item row="2" column="0">
    <layout class="QHBoxLayout" name="horizontalLayout_2">
     <item>
      <spacer name="horizontalSpacer_18">
       <property name="orientation">
        <enum>Qt::Horizontal</enum>
       </property>
       <property name="sizeHint" stdset="0">
        <size>
         <width>40</width>
         <height>20</height>
        </size>
       </property>
      </spacer>
     </item>
     <item>
      <widget class="QPushButton" name="queryComBtn">
       <property name="styleSheet">
        <string notr="true">    QPushButton {background-color:#0089ff ;
    color: white;
    border-style: outset;
    border-width: 2px;
    border-radius: 10px;
    border-color: beige;
    font-size: 26px;
    padding: 6px;}

QPushButton:hover {background-color: #0072C6;         
                     }
</string>
       </property>
       <property name="text">
        <string>刷新</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QComboBox" name="comNameCB">
       <property name="sizePolicy">
        <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
         <horstretch>0</horstretch>
         <verstretch>0</verstretch>
        </sizepolicy>
       </property>
       <property name="minimumSize">
        <size>
         <width>200</width>
         <height>0</height>
        </size>
       </property>
       <property name="maximumSize">
        <size>
         <width>200</width>
         <height>16777215</height>
        </size>
       </property>
      </widget>
     </item>
     <item>
      <spacer name="horizontalSpacer_3">
       <property name="orientation">
        <enum>Qt::Horizontal</enum>
       </property>
       <property name="sizeHint" stdset="0">
        <size>
         <width>40</width>
         <height>20</height>
        </size>
       </property>
      </spacer>
     </item>
    </layout>
   </item>
   <item row="3" column="0">
    <layout class="QHBoxLayout" name="horizontalLayout_3">
     <item>
      <spacer name="horizontalSpacer_17">
       <property name="orientation">
        <enum>Qt::Horizontal</enum>
       </property>
       <property name="sizeHint" stdset="0">
        <size>
         <width>40</width>
         <height>20</height>
        </size>
       </property>
      </spacer>
     </item>
     <item>
      <widget class="QLabel" name="label_3">
       <property name="text">
        <string>波特率</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QComboBox" name="comBtlCB">
       <property name="minimumSize">
        <size>
         <width>200</width>
         <height>0</height>
        </size>
       </property>
       <property name="maximumSize">
        <size>
         <width>200</width>
         <height>16777215</height>
        </size>
       </property>
       <property name="currentText">
        <string>9600</string>
       </property>
       <item>
        <property name="text">
         <string>9600</string>
        </property>
       </item>
       <item>
        <property name="text">
         <string>115200</string>
        </property>
       </item>
      </widget>
     </item>
     <item>
      <spacer name="horizontalSpacer_4">
       <property name="orientation">
        <enum>Qt::Horizontal</enum>
       </property>
       <property name="sizeHint" stdset="0">
        <size>
         <width>40</width>
         <height>20</height>
        </size>
       </property>
      </spacer>
     </item>
    </layout>
   </item>
   <item row="4" column="0">
    <layout class="QHBoxLayout" name="horizontalLayout_4">
     <item>
      <spacer name="horizontalSpacer_16">
       <property name="orientation">
        <enum>Qt::Horizontal</enum>
       </property>
       <property name="sizeHint" stdset="0">
        <size>
         <width>40</width>
         <height>20</height>
        </size>
       </property>
      </spacer>
     </item>
     <item>
      <widget class="QLabel" name="label_4">
       <property name="text">
        <string>校验位</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QComboBox" name="comJywCB">
       <property name="minimumSize">
        <size>
         <width>200</width>
         <height>0</height>
        </size>
       </property>
       <property name="maximumSize">
        <size>
         <width>200</width>
         <height>16777215</height>
        </size>
       </property>
       <item>
        <property name="text">
         <string>NONE</string>
        </property>
       </item>
       <item>
        <property name="text">
         <string>ODD</string>
        </property>
       </item>
       <item>
        <property name="text">
         <string>EVEN</string>
        </property>
       </item>
       <item>
        <property name="text">
         <string>MARK</string>
        </property>
       </item>
       <item>
        <property name="text">
         <string>SPACE</string>
        </property>
       </item>
      </widget>
     </item>
     <item>
      <spacer name="horizontalSpacer_5">
       <property name="orientation">
        <enum>Qt::Horizontal</enum>
       </property>
       <property name="sizeHint" stdset="0">
        <size>
         <width>40</width>
         <height>20</height>
        </size>
       </property>
      </spacer>
     </item>
    </layout>
   </item>
   <item row="5" column="0">
    <layout class="QHBoxLayout" name="horizontalLayout_5">
     <item>
      <spacer name="horizontalSpacer_15">
       <property name="orientation">
        <enum>Qt::Horizontal</enum>
       </property>
       <property name="sizeHint" stdset="0">
        <size>
         <width>40</width>
         <height>20</height>
        </size>
       </property>
      </spacer>
     </item>
     <item>
      <widget class="QLabel" name="label_5">
       <property name="text">
        <string>数据位</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QComboBox" name="comSjwCB">
       <property name="minimumSize">
        <size>
         <width>200</width>
         <height>0</height>
        </size>
       </property>
       <property name="maximumSize">
        <size>
         <width>200</width>
         <height>16777215</height>
        </size>
       </property>
       <property name="currentText">
        <string>5</string>
       </property>
       <item>
        <property name="text">
         <string>5</string>
        </property>
       </item>
       <item>
        <property name="text">
         <string>6</string>
        </property>
       </item>
       <item>
        <property name="text">
         <string>7</string>
        </property>
       </item>
       <item>
        <property name="text">
         <string>8</string>
        </property>
       </item>
      </widget>
     </item>
     <item>
      <spacer name="horizontalSpacer_6">
       <property name="orientation">
        <enum>Qt::Horizontal</enum>
       </property>
       <property name="sizeHint" stdset="0">
        <size>
         <width>40</width>
         <height>20</height>
        </size>
       </property>
      </spacer>
     </item>
    </layout>
   </item>
   <item row="6" column="0">
    <layout class="QHBoxLayout" name="horizontalLayout_6">
     <item>
      <spacer name="horizontalSpacer_14">
       <property name="orientation">
        <enum>Qt::Horizontal</enum>
       </property>
       <property name="sizeHint" stdset="0">
        <size>
         <width>40</width>
         <height>20</height>
        </size>
       </property>
      </spacer>
     </item>
     <item>
      <widget class="QLabel" name="label_6">
       <property name="text">
        <string>停止位</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QComboBox" name="comTzwCB">
       <property name="sizePolicy">
        <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
         <horstretch>0</horstretch>
         <verstretch>0</verstretch>
        </sizepolicy>
       </property>
       <property name="minimumSize">
        <size>
         <width>200</width>
         <height>0</height>
        </size>
       </property>
       <property name="maximumSize">
        <size>
         <width>200</width>
         <height>16777215</height>
        </size>
       </property>
       <item>
        <property name="text">
         <string>1</string>
        </property>
       </item>
       <item>
        <property name="text">
         <string>1.5</string>
        </property>
       </item>
       <item>
        <property name="text">
         <string>2</string>
        </property>
       </item>
      </widget>
     </item>
     <item>
      <spacer name="horizontalSpacer_7">
       <property name="orientation">
        <enum>Qt::Horizontal</enum>
       </property>
       <property name="sizeHint" stdset="0">
        <size>
         <width>40</width>
         <height>20</height>
        </size>
       </property>
      </spacer>
     </item>
    </layout>
   </item>
   <item row="7" column="0">
    <layout class="QHBoxLayout" name="horizontalLayout_7">
     <item>
      <spacer name="horizontalSpacer_13">
       <property name="orientation">
        <enum>Qt::Horizontal</enum>
       </property>
       <property name="sizeHint" stdset="0">
        <size>
         <width>40</width>
         <height>20</height>
        </size>
       </property>
      </spacer>
     </item>
     <item>
      <widget class="QLabel" name="label_7">
       <property name="text">
        <string>流控制</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QComboBox" name="comLkzCB">
       <property name="minimumSize">
        <size>
         <width>200</width>
         <height>0</height>
        </size>
       </property>
       <property name="maximumSize">
        <size>
         <width>200</width>
         <height>16777215</height>
        </size>
       </property>
       <item>
        <property name="text">
         <string>NONE</string>
        </property>
       </item>
       <item>
        <property name="text">
         <string>XON/XOFF</string>
        </property>
       </item>
       <item>
        <property name="text">
         <string>RTS/CTS</string>
        </property>
       </item>
      </widget>
     </item>
     <item>
      <spacer name="horizontalSpacer_8">
       <property name="orientation">
        <enum>Qt::Horizontal</enum>
       </property>
       <property name="sizeHint" stdset="0">
        <size>
         <width>40</width>
         <height>20</height>
        </size>
       </property>
      </spacer>
     </item>
    </layout>
   </item>
   <item row="8" column="0">
    <layout class="QHBoxLayout" name="horizontalLayout_8">
     <item>
      <spacer name="horizontalSpacer_12">
       <property name="orientation">
        <enum>Qt::Horizontal</enum>
       </property>
       <property name="sizeHint" stdset="0">
        <size>
         <width>40</width>
         <height>20</height>
        </size>
       </property>
      </spacer>
     </item>
     <item>
      <widget class="QPushButton" name="linkBtn">
       <property name="minimumSize">
        <size>
         <width>200</width>
         <height>0</height>
        </size>
       </property>
       <property name="maximumSize">
        <size>
         <width>200</width>
         <height>16777215</height>
        </size>
       </property>
       <property name="styleSheet">
        <string notr="true">    QPushButton {background-color:#0089ff ;
    color: white;
    border-style: outset;
    border-width: 2px;
    border-radius: 10px;
    border-color: beige;
    font-size: 26px;
    padding: 6px;}

QPushButton:hover {background-color: #0072C6;         
                     }
</string>
       </property>
       <property name="text">
        <string>连接</string>
       </property>
      </widget>
     </item>
     <item>
      <spacer name="horizontalSpacer_9">
       <property name="orientation">
        <enum>Qt::Horizontal</enum>
       </property>
       <property name="sizeHint" stdset="0">
        <size>
         <width>40</width>
         <height>20</height>
        </size>
       </property>
      </spacer>
     </item>
    </layout>
   </item>
   <item row="9" column="0">
    <layout class="QHBoxLayout" name="horizontalLayout_9">
     <item>
      <spacer name="horizontalSpacer_11">
       <property name="orientation">
        <enum>Qt::Horizontal</enum>
       </property>
       <property name="sizeHint" stdset="0">
        <size>
         <width>40</width>
         <height>20</height>
        </size>
       </property>
      </spacer>
     </item>
     <item>
      <widget class="QPushButton" name="offBtn">
       <property name="minimumSize">
        <size>
         <width>200</width>
         <height>0</height>
        </size>
       </property>
       <property name="maximumSize">
        <size>
         <width>200</width>
         <height>16777215</height>
        </size>
       </property>
       <property name="styleSheet">
        <string notr="true">    QPushButton {background-color:#0089ff ;
    color: white;
    border-style: outset;
    border-width: 2px;
    border-radius: 10px;
    border-color: beige;
    font-size: 26px;
    padding: 6px;}

QPushButton:hover {background-color: #0072C6;         
                     }
</string>
       </property>
       <property name="text">
        <string>断开</string>
       </property>
      </widget>
     </item>
     <item>
      <spacer name="horizontalSpacer_10">
       <property name="orientation">
        <enum>Qt::Horizontal</enum>
       </property>
       <property name="sizeHint" stdset="0">
        <size>
         <width>40</width>
         <height>20</height>
        </size>
       </property>
      </spacer>
     </item>
    </layout>
   </item>
  </layout>
 </widget>
 <resources/>
 <connections/>
</ui>

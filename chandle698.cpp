﻿#include "chandle698.h"
#include <QDebug>
CHandle698::CHandle698()
{

}


quint16 CHandle698::pppfcs16(quint16 fcs, unsigned char *cp, int len)
{
    assert(sizeof (quint16) == 2);
    assert(((quint16) -1) > 0);
    while (len--)
        fcs = (fcs >> 8) ^ fcstab[(fcs ^ *cp++) & 0xff];
    return (~fcs);
}

void CHandle698::setIpApdu(char *pcFrame, OAD &oad, int &j, QString strData)
{
    switch (oad.attr_ID)
    {
    case 0x04:
        setIpApduByOad(pcFrame, j, strData);
        break;
    default:
        break;
    }
}

void CHandle698::setIpApduByOad(char *pcFrame, int &j, QString strData)
{
    qDebug() << "setIpApduByOad:" << strData;
    pcFrame[j++] = 0x02;
    pcFrame[j++] = 0x06;
    pcFrame[j++] = 0x16;
    pcFrame[j++] = 0x01;
    pcFrame[j++] = 0x09;
    pcFrame[j++] = 0x04;
    QStringList strList = strData.split(".");
    if(strList.size() < 4)
        return ;
    pcFrame[j++] = strList[0].toInt();
    pcFrame[j++] = strList[1].toInt();
    pcFrame[j++] = strList[2].toInt();
    pcFrame[j++] = strList[3].toInt();

    pcFrame[j++] = 0x09;
    pcFrame[j++] = 0x04;
    pcFrame[j++] = 0xff;
    pcFrame[j++] = 0xff;
    pcFrame[j++] = 0xff;
    pcFrame[j++] = 0x00;

    pcFrame[j++] = 0x09;
    pcFrame[j++] = 0x04;
    pcFrame[j++] = strList[0].toInt();
    pcFrame[j++] = strList[1].toInt();
    pcFrame[j++] = strList[2].toInt();
    pcFrame[j++] = 0x01;

    pcFrame[j++] = 0x0a;
    pcFrame[j++] = 0x01;
    pcFrame[j++] = 0x20;

    pcFrame[j++] = 0x0a;
    pcFrame[j++] = 0x01;
    pcFrame[j++] = 0x20;
}

void CHandle698::setTxdz(char *pcFrame, OAD &oad, int &j, QString strData)
{
    switch (oad.attr_ID)
    {
    case 0x02:
        setTxdzByOad(pcFrame, j, strData);
        break;
    default:
        break;
    }
}

void CHandle698::setTxdzByOad(char *pcFrame, int &j, QString strData)
{
    pcFrame[j++] = 0x09;

    QByteArray data = QByteArray::fromHex(strData.toUtf8());
    pcFrame[j++] = data.size();
    memcpy(pcFrame + j, data.data(), data.size());
    j += data.size();

}

bool CHandle698::CheckFrame698(char *pcFrame, int nDir)
{
    if(pcFrame == nullptr)
    {
        return false;
    }
    quint32 uwLength = 0;
    quint16 uwCRC = 0;
    FRAME_LINK_OBJ_ORIENTED stFrame;
    memset(&stFrame, 0, sizeof (stFrame));
    char *pctemp = pcFrame;
    memcpy((quint8 *)&stFrame, pctemp, 3);
    stFrame.ucBegin = pctemp[0];
    stFrame.ucLengthLow = pctemp[1];
    stFrame.ucLengthHigh = pctemp[2];
    pctemp += 3;
    stFrame.ucFunctionCode = pctemp[0] & 0x07;
    stFrame.ucScrambleCode = (pctemp[0] >> 3) & 0x01;
    stFrame.ucSplitSign = (pctemp[0] >> 5) & 0x01;
    stFrame.ucPRM = (pctemp[0] >> 6) &0x01;
    stFrame.ucDIR = (pctemp[0] >> 7) &0x01;
    ++pctemp;
    stFrame.ucServerAddrLen = pctemp[0] & 0xF;
    stFrame.ucLogicAddr = (pctemp[0] >> 4) & 0x3;
    stFrame.ucServerAddrType = (pctemp[0] >> 6) &0x3;
    ++pctemp;


    quint8 ucSA_Len = stFrame.ucServerAddrLen + 1;
    pctemp += ucSA_Len;

    if((stFrame.ucLengthHigh & 0x40) == 0x40)
    {
        uwLength = ((MAKE_WORD(stFrame.ucLengthHigh, stFrame.ucLengthLow)) & 0x2FFF) * 1024;
    }
    else
    {
        uwLength = MAKE_WORD(stFrame.ucLengthHigh, stFrame.ucLengthLow);//获取L值
    }
    stFrame.ucClientAddr = pctemp[0];
    stFrame.ucHCRCLow = pctemp[1];
    stFrame.ucHCRCHigh = pctemp[2];


    // 启动字符错误
    if (stFrame.ucBegin != 0x68)
    {
        return false;
    }

    // 传输方向判断
    if (stFrame.ucDIR != nDir)
    {
        return false;
    }

    // 功能码判断
    if ((stFrame.ucFunctionCode != 0x01) && (stFrame.ucFunctionCode != 0x03))
    {
        return false;
    }

    // 逻辑地址错误
    if (((*(pcFrame + 4) & 0x30) != 0x00) && ((*(pcFrame + 4) & 0x30) != 0x10) && ((*(pcFrame + 4) & 0x30) != 0x20))
    {
        return false;
    }

    // HCS帧校验
    uwCRC = 0xFFFF;
    uwCRC = pppfcs16(uwCRC, (unsigned char *)(pcFrame+1), ucSA_Len+5);
    if (uwCRC != MAKE_WORD(*(pcFrame+7+ucSA_Len), *(pcFrame+6+ucSA_Len)))
    {
        return false;
    }

    //FCS帧校验
    uwCRC = 0xFFFF;
    uwCRC = pppfcs16(uwCRC, (unsigned char *)(pcFrame+1), uwLength-2);
    if (uwCRC != MAKE_WORD(*(pcFrame+uwLength), *(pcFrame+uwLength-1)))
    {
        return false;
    }

    // 结束字符错误
    if (pcFrame[uwLength+1] != 0x16)
    {
        return false;
    }

    quint32 pwLen = uwLength - ucSA_Len - 9;
    memcpy(pcFrame, pcFrame+8+ucSA_Len, pwLen);


    if (stFrame.ucPRM == 0)
    {
        if ((pcFrame[0] != 8) && (pcFrame[0] != 129))
        {
            return false;
        }
    }
    else
    {
        if ((pcFrame[0] == 8) || (pcFrame[0] == 129))
        {
            return false;
        }
    }
    return true;
}

bool CHandle698::packagingFrame698(char *pcFrame, int &nLen, char *pcsaFrame, int saLen)
{
   if(pcFrame == nullptr || nLen == 0)
   {
       return false;
   }
   quint8 tempFrame[263268] = {0};
   quint16 uwCRC = 0;
   quint32 j = 0;
   quint32 uwLengthBak = nLen;
   tempFrame[j++] = 0x68;

   //长度
   tempFrame[j++] = LSB(9 + saLen + 1 + uwLengthBak);
   tempFrame[j++] = MSB(9 + saLen + 1 + uwLengthBak);

   //控制域
   tempFrame[j++] =  0x43;

   //SA标志
   tempFrame[j++] = (0x40|saLen);

   //SA
   memcpy(tempFrame+j, pcsaFrame, saLen+1);
   j += saLen+1;

   //CA
   tempFrame[j++] = 0x00;

   //HCS
   uwCRC = 0xFFFF;
   uwCRC = pppfcs16(uwCRC, tempFrame+1, j-1);
   tempFrame[j++] = LSB(uwCRC);
   tempFrame[j++] = MSB(uwCRC);

   //APDU
   memcpy(tempFrame+j, pcFrame, uwLengthBak);
   j += uwLengthBak;

   // FCS
   uwCRC = 0xFFFF;
   uwCRC = pppfcs16(uwCRC, tempFrame+1, j-1);
   tempFrame[j++] = LSB(uwCRC);
   tempFrame[j++] = MSB(uwCRC);
   //End
   tempFrame[j++] = 0x16;
   nLen = j;

   memcpy(pcFrame, tempFrame, j);
   return true;
}

int CHandle698::buildGetRequestApduByOAD(char *pcFrame, OAD & oad, int piid)
{
    char temp[8192] = {0};
    int j = 0;
    temp[j++] = 0x05;
    temp[j++] = 0x01;
    temp[j++] = piid;
    temp[j++] = MSB(oad.attr_OI);
    temp[j++] = LSB(oad.attr_OI);
    temp[j++] = oad.attr_ID;
    temp[j++] = oad.attr_index;
    temp[j++] = 0x00;
    memcpy(pcFrame, temp, j);
    return j;
}

int CHandle698::buildSetRequestApduByOAD(char *pcFrame, OAD &oad, int piid, QString strData)
{
    char temp[8192] = {0};
    int j = 0;
    temp[j++] = 0x06;
    temp[j++] = 0x01;
    temp[j++] = piid;
    temp[j++] = MSB(oad.attr_OI);
    temp[j++] = LSB(oad.attr_OI);
    temp[j++] = oad.attr_ID;
    temp[j++] = oad.attr_index;

    switch(oad.attr_OI)
    {
    case 0x4510:
        setIpApdu(temp, oad, j, strData);
        break;
    case 0x4001:
        setTxdz(temp, oad, j, strData);
        break;
    default:
        break;
    }

    temp[j++] = 0x00;
    memcpy(pcFrame, temp, j);

    QByteArray cc(pcFrame, j);
    qDebug() << "pcFrame:" << cc.toHex();
    return j ;
}

int CHandle698::buildSetRequestGwTxpz(char *pcFrame, SGwtxpz &gwtxpz)
{
    char temp[8192] = {0};
    int j = 0;
    temp[j++] = 0x06;
    temp[j++] = 0x01;
    temp[j++] = 1;
    temp[j++] = MSB(0x4500);
    temp[j++] = LSB(0x4500);
    temp[j++] = 0x02;
    temp[j++] = 0x0;
    temp[j++] = 0x02;
    temp[j++] = 0x0c;
    temp[j++] = 0x16;
    temp[j++] = gwtxpz.ngzms;
    temp[j++] = 0x16;
    temp[j++] = gwtxpz.nzxfs;
    temp[j++] = 0x16;
    temp[j++] = gwtxpz.nljfs;
    temp[j++] = 0x16;
    temp[j++] = gwtxpz.nljyyfs;
    temp[j++] = 0x01;
    temp[j++] = gwtxpz.vtztdklb.size();
    for(int m = 0; m < gwtxpz.vtztdklb.size(); ++m)
    {
        temp[j++] = 0x12;
        int cc = SHORTSWAP(gwtxpz.vtztdklb[m]);
        memcpy(temp +j, &cc, 2);
        j += 2;
    }
    temp[j++] = 0x0a;
    QByteArray ss = convertQStringToAsciiCodes(gwtxpz.strapn);

    temp[j++] = ss.size();
    memcpy(temp+j, ss.data(), ss.size());
    j += ss.size();

    temp[j++] = 0x0a;
    ss = convertQStringToAsciiCodes(gwtxpz.stryhm);
    temp[j++] = ss.size();
    memcpy(temp+j, ss.data(), ss.size());
    j += ss.size();

    temp[j++] = 0x0a;
    ss = convertQStringToAsciiCodes(gwtxpz.strmm);
    temp[j++] = ss.size();
    memcpy(temp+j, ss.data(), ss.size());
    j += ss.size();

    temp[j++] = 0x09;
    if(gwtxpz.strdlfwq.isEmpty())
        temp[j++] = 0x00;
    else
    {
        temp[j++] = 0x04;
        QStringList sList = gwtxpz.strdlfwq.split(".");
        if(sList.size() == 4)
        {
            temp[j++] =sList[0].toInt();
            temp[j++] =sList[1].toInt();
            temp[j++] =sList[2].toInt();
            temp[j++] =sList[3].toInt();
        }

    }

    temp[j++] = 0x12;
    int ndk = SHORTSWAP(gwtxpz.ndldk);
    memcpy(temp+j, &ndk, 2);
    j += 2;

    temp[j++] = 0x11;
    temp[j++] = gwtxpz.ncssjcs;
    temp[j++] = 0x12;
    int ns = SHORTSWAP(gwtxpz.nxtzq);
    memcpy(temp + j, &ns, 2);
    j += 2;

    temp[j++] = 0x00;
    memcpy(pcFrame, temp, j);

    return j ;
}

int CHandle698::buildSetRequestTxcs(char *pcFrame, SZztxdz &txcs)
{
    int j = 0;
    char temp[8192] = {0};
    temp[j++] = 0x06;
    temp[j++] = 0x01;
    temp[j++] = 1;
    temp[j++] = MSB(0x4500);
    temp[j++] = LSB(0x4500);
    temp[j++] = 0x03;
    temp[j++] = 0x00;
    temp[j++] = 0x01;
    int ncount = 1;
    if(txcs.by)
        ncount ++;
    temp[j++] = ncount;
    temp[j++] = 0x02;
    temp[j++] = 0x02;
    temp[j++] = 0x09;
    temp[j++] = 0x04;
    QStringList sList = txcs.strzyIp.split(".");

    if(sList.size() == 4)
    {
        temp[j++] = sList[0].toInt();
        temp[j++] = sList[1].toInt();
        temp[j++] = sList[2].toInt();
        temp[j++] = sList[3].toInt();
    }
    temp[j++] = 0x12;
    int ndk = SHORTSWAP( txcs.nzydk);
    memcpy(temp+j, &ndk, 2);
    j += 2;
    if(txcs.by)
    {
        temp[j++] = 0x02;
        temp[j++] = 0x02;
        temp[j++] = 0x09;
        temp[j++] = 0x04;
        QStringList sList = txcs.strbyIp.split(".");

        if(sList.size() == 4)
        {
            temp[j++] = sList[0].toInt();
            temp[j++] = sList[1].toInt();
            temp[j++] = sList[2].toInt();
            temp[j++] = sList[3].toInt();
        }
        temp[j++] = 0x12;
        int ndk = SHORTSWAP( txcs.nbydk);
        memcpy(temp+j, &ndk, 2);
        j += 2;
    }

    temp[j++] = 0x00;
    memcpy(pcFrame, temp, j);

    return j;
}

int CHandle698::buildSetRequestTxcsytw(char *pcFrame, SZztxdz &txcs)
{
    int j = 0;
    char temp[8192] = {0};
    temp[j++] = 0x06;
    temp[j++] = 0x01;
    temp[j++] = 1;
    temp[j++] = MSB(0x4510);
    temp[j++] = LSB(0x4510);
    temp[j++] = 0x03;
    temp[j++] = 0x00;
    temp[j++] = 0x01;
    int ncount = 1;
    if(txcs.by)
        ncount ++;
    temp[j++] = ncount;
    temp[j++] = 0x02;
    temp[j++] = 0x02;
    temp[j++] = 0x09;
    temp[j++] = 0x04;
    QStringList sList = txcs.strzyIp.split(".");

    if(sList.size() == 4)
    {
        temp[j++] = sList[0].toInt();
        temp[j++] = sList[1].toInt();
        temp[j++] = sList[2].toInt();
        temp[j++] = sList[3].toInt();
    }
    temp[j++] = 0x12;
    int ndk = SHORTSWAP( txcs.nzydk);
    memcpy(temp+j, &ndk, 2);
    j += 2;
    if(txcs.by)
    {
        temp[j++] = 0x02;
        temp[j++] = 0x02;
        temp[j++] = 0x09;
        temp[j++] = 0x04;
        QStringList sList = txcs.strbyIp.split(".");

        if(sList.size() == 4)
        {
            temp[j++] = sList[0].toInt();
            temp[j++] = sList[1].toInt();
            temp[j++] = sList[2].toInt();
            temp[j++] = sList[3].toInt();
        }
        temp[j++] = 0x12;
        int ndk = SHORTSWAP( txcs.nbydk);
        memcpy(temp+j, &ndk, 2);
        j += 2;
    }

    temp[j++] = 0x00;
    memcpy(pcFrame, temp, j);

    return j;
}

int CHandle698::buildSetRequestYtwTxpz(char *pcFrame, SYtwtxpz &ytwtxpz)
{
    char temp[8192] = {0};
    int j = 0;
    temp[j++] = 0x06;
    temp[j++] = 0x01;
    temp[j++] = 1;
    temp[j++] = MSB(0x4510);
    temp[j++] = LSB(0x4510);
    temp[j++] = 0x02;
    temp[j++] = 0x0;
    temp[j++] = 0x02;
    temp[j++] = 0x08;
    temp[j++] = 0x16;
    temp[j++] = ytwtxpz.ngzms;
    temp[j++] = 0x16;
    temp[j++] = ytwtxpz.nljfs;
    temp[j++] = 0x16;
    temp[j++] = ytwtxpz.nljyyfs;
    temp[j++] = 0x01;
    temp[j++] = ytwtxpz.vtztdklb.size();
    for(int m = 0; m < ytwtxpz.vtztdklb.size(); ++m)
    {
        temp[j++] = 0x12;
        int cc = SHORTSWAP(ytwtxpz.vtztdklb[m]);
        memcpy(temp +j, &cc, 2);
        j += 2;
    }

    temp[j++] = 0x09;
    if(ytwtxpz.strdlfwq.isEmpty())
        temp[j++] = 0x00;
    else
    {
        temp[j++] = 0x04;
        QStringList sList = ytwtxpz.strdlfwq.split(".");
        if(sList.size() == 4)
        {
            temp[j++] =sList[0].toInt();
            temp[j++] =sList[1].toInt();
            temp[j++] =sList[2].toInt();
            temp[j++] =sList[3].toInt();
        }

    }

    temp[j++] = 0x12;
    int ndk = SHORTSWAP(ytwtxpz.ndldk);
    memcpy(temp+j, &ndk, 2);
    j += 2;

    temp[j++] = 0x11;
    temp[j++] = ytwtxpz.ncssjcs;
    temp[j++] = 0x12;
    int ns = SHORTSWAP(ytwtxpz.nxtzq);
    memcpy(temp + j, &ns, 2);
    j += 2;

    temp[j++] = 0x00;
    memcpy(pcFrame, temp, j);

    return j ;
}

int CHandle698::buildSetRequestTsdpzcs(char *pcFrame, STsdpzcs &tsdcs)
{
    char temp[8192] = {0};
    int j = 0;
    temp[j++] = 0x06;
    temp[j++] = 0x01;
    temp[j++] = 1;
    temp[j++] = MSB(0x3106);
    temp[j++] = LSB(0x3106);
    temp[j++] = 0x06;
    temp[j++] = 0x0;

    temp[j++] = 0x02;
    temp[j++] = 0x02;

    temp[j++] = 0x02;
    temp[j++] = 0x04;

    temp[j++] = 0x04;
    temp[j++] = 0x08;
    temp[j++] = tsdcs.ncjbz;

    temp[j++] = 0x11;
    temp[j++] = tsdcs.ntdsjcdjg;

    temp[j++] = 0x11;
    temp[j++] = tsdcs.ntdsjcdxz;

    //逻辑地址处理
    temp[j++] = 0x01;
    temp[j++] = tsdcs.vtdnb.size();
    for(int m = 0; m < tsdcs.vtdnb.size(); ++m)
    {
        QByteArray data = QByteArray::fromHex(tsdcs.vtdnb[m].toUtf8());
        int nsize = data.size();
        temp[j++] = 0x55;
        temp[j++] = nsize +1;
        temp[j++] = nsize -1;
        memcpy(temp + j, data.data(), nsize);
        j += nsize;
    }

    temp[j++] = 0x02;
    temp[j++] = 0x06;

    temp[j++] = 0x12;
    int c = SHORTSWAP(tsdcs.ntdsjzxyxjg);
    memcpy(temp+j, &c, 2);
    j += 2;

    temp[j++] = 0x12;
    c = SHORTSWAP(tsdcs.ntdsjzdyxjg);
    memcpy(temp+j, &c, 2);
    j += 2;

    temp[j++] = 0x12;
    c = SHORTSWAP(tsdcs.ntdsjqzsjpzxz);
    memcpy(temp+j, &c, 2);
    j += 2;

    temp[j++] = 0x12;
    c = SHORTSWAP(tsdcs.ntdsjsjqdpcxz);
    memcpy(temp+j, &c, 2);
    j += 2;

    temp[j++] = 0x12;
    c = SHORTSWAP(tsdcs.ntdfsdzxz);
    memcpy(temp+j, &c, 2);
    j += 2;

    temp[j++] = 0x12;
    c = SHORTSWAP(tsdcs.ntdhfdyxz);
    memcpy(temp+j, &c, 2);
    j += 2;

    temp[j++] = 0x00;

    memcpy(pcFrame, temp, j);
    return j;
}

int CHandle698::buildSetRequestaqms(char *pcFrame, SAqjmpz &aqms)
{
    char temp[8192] = {0};
    int j = 0;
    temp[j++] = 0x06;
    temp[j++] = 0x01;
    temp[j++] = 0x01;
    temp[j++] = 0xf1;
    temp[j++] = 0x01;
    temp[j++] = 0x02;
    temp[j++] = 0x00;
    temp[j++] = 0x16;
    temp[j++] = aqms.njmms;
    temp[j++] = 00;
    memcpy(pcFrame, temp, j);
    return j;
}



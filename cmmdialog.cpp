﻿#include "cmmdialog.h"
#include "ui_cmmdialog.h"
#include <QMessageBox>

CMMDialog::CMMDialog(QWidget *parent) :
    QDialog(parent),
    ui(new Ui::CMMDialog)
{
    ui->setupUi(this);
    setWindowTitle("密码校验");
}

CMMDialog::~CMMDialog()
{
    delete ui;
}

void CMMDialog::on_pushButton_clicked()
{
    QString s = ui->lineEdit->text();
    if(s != "megsky@1234")
    {
        QMessageBox::about(this, "密码", "密码错误");
        return;
    }
    accept();
}

#ifndef CCHECKWINDOW_H
#define CCHECKWINDOW_H

#include <QMainWindow>
#include"caboutdialog.h"
#include"cserialwidget.h"
#include"ctestpcwidget.h"
#include"ctestlog.h"
#include"csetconfigure.h"
#include"cportset.h"
#include"ctestbatchinfo.h"
#include"cshistoryinfo.h"


namespace Ui {
class CCheckWindow;
}

class CCheckWindow : public QMainWindow
{
    Q_OBJECT

public:
    explicit CCheckWindow(QWidget *parent = nullptr);
    ~CCheckWindow();

    void ToolBarinit();
private:
    Ui::CCheckWindow *ui;
    CAboutDialog *m_pAbout;
    cserialWidget *m_serial;
    cTestLog *m_log;
    cSetConfigure *m_set;
    cPortset *m_portset;
    cTestBatchInfo *m_bInfo;
    csHistoryInfo *m_hisInfo;

public slots:
    void  on_actGy_trigger();
    void  on_actCk_trigger();
    void  on_actJl_trigger();
    void  on_actTest_trigger();
    void  on_actbInfo_trigger();
    void  on_actHisInfo_trigger();
private slots:

};

#endif // CCHECKWINDOW_H

#include "database.h"
#include<QDebug>

Database::Database()
{
    if (QSqlDatabase::contains("qt_sql_default_connection"))
    {
        database = QSqlDatabase::database("qt_sql_default_connection");
    }
    else
    {
        database = QSqlDatabase::addDatabase("QSQLITE");
        database.setDatabaseName("C:/msk_scuoutautocheckIni/JBDataBase.db");
    }
    
    // 打开数据库并创建表
    if(openDb())
    {
        createTable();
        createTestItemTable(); // 创建测试项详情表
    }
}
//void Database::onSlotsRevNum(QString curbathch,QString esnNum,QString esamNum)
//{
//    w2dba w2dbaTest1 = {esnNum, esamNum,curbathch};
//    singleInsertData(w2dbaTest1);
//}
bool Database::openDb()
{

    if (!database.open())
    {
        qDebug() << "Error: Failed to connect database." << database.lastError();
    }
    else
    {
        // do something
    }

    return true;
}
void Database::createTable()
{
    QSqlQuery sqlQuery;
    QString createSql = QString("CREATE TABLE IF NOT EXISTS testinfo (\
                         id INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL,\
                         esn TEXT UNIQUE NOT NULL,\
                         esam TEXT NOT NULL,\
                         curbatch TEXT NOT NULL)");

    sqlQuery.prepare(createSql);
    // 执行sql语句
    if(!sqlQuery.exec())
    {
        qDebug() << "Error: Fail to create table. " << sqlQuery.lastError();
    }
    else
    {
        qDebug() << "Table created!";
    }
}

void Database::createTable(QString tablename)
{
    QString createResSql = QString("CREATE TABLE IF NOT EXISTS %1(\
                                 id INTEGER PRIMARY KEY AUTOINCRMENT NOT NULL,\
                                 testitem1 TEXT NOT NULL ,\
                                 testitem2 TEXT NOT NULL ,\
                                 testres TEXT NOT NULL,\
                                   )").arg(tablename);
    QSqlQuery sqlQuery;
    sqlQuery.prepare(createResSql);
    if(!sqlQuery.exec())
    {
        qDebug() << "Error:Fail to create table." << sqlQuery.lastError();
    }
    else
    {
        qDebug()<<"Table created!";
    }
}

bool Database::isTableExist(QString& tableName)
{
    QSqlDatabase database = QSqlDatabase::database();
    if(database.tables().contains(tableName))
    {
        return true;
    }

    return false;
}

QStringList queryTestRes(QString tableName,QString TestItem)
{
    QStringList ResList;
    QSqlQuery sqlQuery;

    QString s = QString("SELECT * FROM %1").arg(tableName);
    sqlQuery.prepare(s);

    if(!sqlQuery.exec())
    {
        qDebug() << "Error:Failed to query table." << sqlQuery.lastError();
        return  ResList;
    }
    while (sqlQuery.next()) {
        QStringList sl;
        if(TestItem == "clearfile")
        {
            sl << sqlQuery.value(0).toString();
            sl << sqlQuery.value(1).toString();
            sl << sqlQuery.value(2).toString();
            sl << sqlQuery.value(3).toString();
            ResList.append(sl.join(","));
        }
    }
}

QStringList Database::queryTable(QString s)
{
    QStringList result;
    QSqlQuery sqlQuery;

    // 使用参数化查询提高安全性和性能
    sqlQuery.prepare("SELECT * FROM testinfo WHERE esn = ? OR esam = ? OR curbatch = ?");
    sqlQuery.addBindValue(s);
    sqlQuery.addBindValue(s);
    sqlQuery.addBindValue(s);

    if(!sqlQuery.exec())
    {
        qDebug() << "Error: Failed to query table." << sqlQuery.lastError();
        return result; // 返回空列表
    }

    while(sqlQuery.next())
    {
        QStringList rowData;
        rowData << sqlQuery.value(0).toString(); // id
        rowData << sqlQuery.value(1).toString(); // esn
        rowData << sqlQuery.value(2).toString(); // esam
        rowData << sqlQuery.value(3).toString(); // curbatch

        qDebug() << "Found matching record:" << rowData;
        result.append(rowData.join(",")); // 将每行数据合并为字符串加入结果
    }

    if(result.isEmpty()) {
        qDebug() << "No records found matching:" << s;
    }

    qDebug()<<result;
    return result;
}

void Database::singleInsertData(w2dba &singledb)
{
        QSqlQuery sqlQuery(database);

        if(!sqlQuery.prepare("INSERT INTO testinfo (esn, esam, curbatch) "
                            "VALUES (:esn, :esam, :curbatch)")) {
            qDebug() << "Prepare failed:" << sqlQuery.lastError();
        }

        sqlQuery.bindValue(":esn", singledb.esnNum);
        sqlQuery.bindValue(":esam", singledb.esamNum);
        sqlQuery.bindValue(":curbatch", singledb.CurBatch);

        if(!sqlQuery.isValid() || !sqlQuery.isActive()) {
            qDebug() << "Invalid query state before execution";
        }

        if(!sqlQuery.exec()) {
            qDebug() << "Error: Failed to insert data." << sqlQuery.lastError();
            qDebug() << "SQL:" << sqlQuery.lastQuery();
            qDebug() << "Bound values:"
                     << singledb.esnNum << singledb.esamNum << singledb.CurBatch;
        }

        QVariant id = sqlQuery.lastInsertId();
        if(!id.isValid() || id.toInt() == 0) {
            qDebug() << "Warning: Insert may not have succeeded, ID is invalid";
        }

        qDebug() << "Insert successful! ID:" << id.toInt();
}

void Database::createTestItemTable()
{
    QSqlQuery sqlQuery;
    QString createSql = QString("CREATE TABLE IF NOT EXISTS testitemdetail (\
                         id INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL,\
                         esn TEXT NOT NULL,\
                         esam TEXT NOT NULL,\
                         test_batch TEXT NOT NULL,\
                         test_scheme TEXT NOT NULL,\
                         test_time TEXT NOT NULL,\
                         test_main_item TEXT NOT NULL,\
                         test_sub_item TEXT NOT NULL,\
                         test_result TEXT NOT NULL,\
                         station_id INTEGER NOT NULL)");

    sqlQuery.prepare(createSql);
    if(!sqlQuery.exec())
    {
        qDebug() << "Error: Fail to create testitemdetail table. " << sqlQuery.lastError();
    }
    else
    {
        qDebug() << "TestItemDetail table created!";
    }
}

void Database::insertTestItemData(const TestItemDetail &itemData)
{
    QSqlQuery sqlQuery(database);
    
    if(!sqlQuery.prepare("INSERT INTO testitemdetail (esn, esam, test_batch, test_scheme, test_time, test_main_item, test_sub_item, test_result, station_id) "
                        "VALUES (:esn, :esam, :test_batch, :test_scheme, :test_time, :test_main_item, :test_sub_item, :test_result, :station_id)")) {
        qDebug() << "Prepare failed:" << sqlQuery.lastError();
        return;
    }

    sqlQuery.bindValue(":esn", itemData.esnNum);
    sqlQuery.bindValue(":esam", itemData.esamNum);
    sqlQuery.bindValue(":test_batch", itemData.testBatch);
    sqlQuery.bindValue(":test_scheme", itemData.testScheme);
    sqlQuery.bindValue(":test_time", itemData.testTime);
    sqlQuery.bindValue(":test_main_item", itemData.testMainItem);
    sqlQuery.bindValue(":test_sub_item", itemData.testSubItem);
    sqlQuery.bindValue(":test_result", itemData.testResult);
    sqlQuery.bindValue(":station_id", itemData.stationId);

    if(!sqlQuery.exec()) {
        qDebug() << "Error: Failed to insert test item data." << sqlQuery.lastError();
    } else {
        qDebug() << "Test item data inserted successfully!";
    }
}

QStringList Database::queryTestItemDataByBatch(QString batchName)
{
    QStringList result;
    QSqlQuery sqlQuery(database);
    
    // 首先获取该批次所有唯一的测试项，排除特定项目
    QString getItemsSql = "SELECT DISTINCT test_main_item, test_sub_item FROM testitemdetail WHERE test_batch = ? AND test_main_item NOT IN ('清除文件', '容器安装', 'APP安装', 'APP运行', '版本对比') ORDER BY test_main_item, test_sub_item";
    
    if(!sqlQuery.prepare(getItemsSql))
    {
        qDebug() << "Prepare failed:" << sqlQuery.lastError();
        return result;
    }
    
    sqlQuery.bindValue(0, batchName);
    
    if(!sqlQuery.exec())
    {
        qDebug() << "Error: Failed to get test items." << sqlQuery.lastError();
        return result;
    }
    
    QStringList testItems;
    while(sqlQuery.next())
    {
        QString itemKey = sqlQuery.value(1).toString(); // 使用子项
        testItems.append(itemKey);
    }
    
    // 构建表头
    QString header = "ESN | ESAM | 批次 | 方案 | 时间";
    for(const QString &item : testItems)
    {
        header += " | " + item;
    }
    qDebug() << "=== 批次 [" << batchName << "] 的测试项详情 ===";
    qDebug() << header;
    qDebug() << QString("-").repeated(header.length());
    
    // 获取所有记录，按ESN分组，排除特定测试项
    QString sql = "SELECT esn, esam, test_batch, test_scheme, test_time, test_main_item, test_sub_item, test_result FROM testitemdetail WHERE test_batch = ? AND test_main_item NOT IN ('清除文件', '容器安装', 'APP安装', 'APP运行', '版本对比') ORDER BY esn, test_time";
    
    if(!sqlQuery.prepare(sql))
    {
        qDebug() << "Prepare failed:" << sqlQuery.lastError();
        return result;
    }
    
    sqlQuery.bindValue(0, batchName);
    
    if(!sqlQuery.exec())
    {
        qDebug() << "Error: Failed to query test item data by batch." << sqlQuery.lastError();
        return result;
    }
    
    QMap<QString, QMap<QString, QString>> groupedData; // ESN -> {测试项 -> 结果}
    QMap<QString, QStringList> basicInfo; // ESN -> [ESAM, 批次, 方案, 时间]
    
    while(sqlQuery.next())
    {
        QString esn = sqlQuery.value(0).toString();
        QString esam = sqlQuery.value(1).toString();
        QString batch = sqlQuery.value(2).toString();
        QString scheme = sqlQuery.value(3).toString();
        QString time = sqlQuery.value(4).toString();
        QString mainItem = sqlQuery.value(5).toString();
        QString subItem = sqlQuery.value(6).toString();
        QString result = sqlQuery.value(7).toString();
        
        QString itemKey = subItem; // 使用子项作为键
        
        if(!basicInfo.contains(esn))
        {
            basicInfo[esn] = QStringList() << esam << batch << scheme << time;
        }
        
        groupedData[esn][itemKey] = result;
    }
    
    // 输出每行数据
    for(auto it = basicInfo.begin(); it != basicInfo.end(); ++it)
    {
        QString esn = it.key();
        QStringList info = it.value();
        
        QString line = esn + " | " + info.join(" | ");
        
        // 添加每个测试项的结果
        for(const QString &item : testItems)
        {
            QString result = groupedData[esn].value(item, "未测试");
            line += " | " + result;
        }
        
        qDebug() << line;
        result.append(line);
    }
    
    qDebug() << "批次 [" << batchName << "] 共找到记录:" << result.size();
    return result;
}

﻿#ifndef CTSDDIALOG_H
#define CTSDDIALOG_H

#include <QDialog>
#include "msk_global.h"

namespace Ui {
class CTsdDialog;
}

class CTsdDialog : public QDialog
{
    Q_OBJECT

public:
    explicit CTsdDialog(QWidget *parent = nullptr);
    ~CTsdDialog();

    void setKeyName(QString strKeyName);

private slots:
    void on_addBtn_clicked();

    void on_delBtn_clicked();

    void on_clearBtn_clicked();

    void on_okBtn_clicked();

private:
    Ui::CTsdDialog *ui;

    QString m_strKeyName;
};

#endif // CTSDDIALOG_H

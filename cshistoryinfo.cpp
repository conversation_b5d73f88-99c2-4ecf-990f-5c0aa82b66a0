#include "cshistoryinfo.h"
#include "ui_cshistoryinfo.h"
#include<QDebug>
#include"msk_global.h"

csHistoryInfo::csHistoryInfo(QWidget *parent) :
    QWidget(parent),
    ui(new Ui::csHistoryInfo)
{
    ui->setupUi(this);

    ui->tableWidget->verticalHeader()->setVisible(false);
    ui->tableWidget->setColumnCount(16);

    // 设置表头
    QStringList headers;
    headers << "Esn" << "Esam" << "测试批次" << "测试方案" << "测试时间" 
            << "大包版本" << "第一次升级大包" << "第二次升级大包" << "esam通行证" 
            << "esn通行证" << "校验系统和版本号" << "清除文件检测" << "安装容器" 
            << "安装APP" << "时间同步" << "APP运行状态";
    
    for(int i = 0; i < headers.size(); i++)
    {
        QTableWidgetItem *pitem = new QTableWidgetItem();
        pitem->setText(headers[i]);
        ui->tableWidget->setHorizontalHeaderItem(i, pitem);
    }

    ui->tableWidget->setRowCount(40);
    ui->tableWidget->setEditTriggers(QAbstractItemView::NoEditTriggers);
    ui->tableWidget->setSelectionBehavior(QAbstractItemView::SelectRows);

    // 初始化数据库
    m_database = new Database();
    
    // 连接下拉框信号
    connect(ui->comboBox, QOverload<const QString &>::of(&QComboBox::currentTextChanged),
            this, &csHistoryInfo::onBatchChanged);
    
    // 加载所有批次
    loadAllBatches();
    
    // 加载历史数据
    loadHistoryData();
}

csHistoryInfo::~csHistoryInfo()
{
    delete ui;
    delete m_database;
}

void csHistoryInfo::loadAllBatches()
{
    QSqlQuery sqlQuery(m_database->getDatabase());
    
    // 查询所有不同的测试批次
    QString sql = "SELECT DISTINCT test_batch FROM testitemdetail ORDER BY test_batch";
    
    if(!sqlQuery.exec(sql))
    {
        qDebug() << "Error: Failed to query batches." << sqlQuery.lastError();
        return;
    }
    
    
    ui->comboBox->clear();
    
    while(sqlQuery.next())
    {
        QString batch = sqlQuery.value(0).toString();
        ui->comboBox->addItem(batch);
    }
    
    // 设置当前批次为默认选择
    QString currentBatch = queryCfg(CheckItemIni,"currentpc","currentpc");
    int index = ui->comboBox->findText(currentBatch);
    if(index >= 0)
    {
        ui->comboBox->setCurrentIndex(index);
    }
}

void csHistoryInfo::onBatchChanged(const QString &batchName)
{
    if(!batchName.isEmpty())
    {
        updateTableWithBatchData(batchName);
    }
}

void csHistoryInfo::loadHistoryData()
{
    // 获取当前选择的批次
    QString currentBatch = ui->comboBox->currentText();
    if(currentBatch.isEmpty())
    {
        currentBatch = queryCfg(CheckItemIni,"currentpc","currentpc");
    }
    updateTableWithBatchData(currentBatch);
}

void csHistoryInfo::updateTableWithBatchData(const QString &batchName)
{
    QSqlQuery sqlQuery(m_database->getDatabase());
    
    // 查询该批次的所有数据，按ESN分组
    QString sql = "SELECT esn, esam, test_batch, test_scheme, test_time, test_sub_item, test_result "
                  "FROM testitemdetail WHERE test_batch = ? "
                  "AND test_main_item NOT IN ('清除文件', '容器安装', 'APP安装', 'APP运行', '版本对比') "
                  "ORDER BY esn, test_time";
    
    if(!sqlQuery.prepare(sql))
    {
        qDebug() << "Prepare failed:" << sqlQuery.lastError();
        return;
    }
    
    sqlQuery.bindValue(0, batchName);
    
    if(!sqlQuery.exec())
    {
        qDebug() << "Error: Failed to query history data." << sqlQuery.lastError();
        return;
    }
    
    // 按ESN分组数据
    QMap<QString, QMap<QString, QString>> groupedData;
    QMap<QString, QStringList> basicInfo;
    
    while(sqlQuery.next())
    {
        QString esn = sqlQuery.value(0).toString();
        QString esam = sqlQuery.value(1).toString();
        QString batch = sqlQuery.value(2).toString();
        QString scheme = sqlQuery.value(3).toString();
        QString time = sqlQuery.value(4).toString();
        QString subItem = sqlQuery.value(5).toString();
        QString result = sqlQuery.value(6).toString();
        
        if(!basicInfo.contains(esn))
        {
            basicInfo[esn] = QStringList() << esam << batch << scheme << time;
        }
        
        groupedData[esn][subItem] = result;
    }
    
    // 清空表格并填充数据...
    ui->tableWidget->setRowCount(0);
    
    int row = 0;
    for(auto it = basicInfo.begin(); it != basicInfo.end(); ++it)
    {
        QString esn = it.key();
        QStringList info = it.value();
        
        ui->tableWidget->setRowCount(row + 1);
        
        // 基本信息
        ui->tableWidget->setItem(row, 0, new QTableWidgetItem(esn));
        ui->tableWidget->setItem(row, 1, new QTableWidgetItem(info[0]));
        ui->tableWidget->setItem(row, 2, new QTableWidgetItem(info[1]));
        ui->tableWidget->setItem(row, 3, new QTableWidgetItem(info[2]));
        ui->tableWidget->setItem(row, 4, new QTableWidgetItem(info[3]));
        
        // 测试项结果映射
        QMap<QString, int> columnMap;
        columnMap["大包版本"] = 5;
        columnMap["第一次升级大包"] = 6;
        columnMap["第二次升级大包"] = 7;
        columnMap["esam通行证"] = 8;
        columnMap["esn通行证"] = 9;
        columnMap["校验系统和版本号"] = 10;
        columnMap["清除文件检测"] = 11;
        columnMap["安装容器"] = 12;
        columnMap["安装APP"] = 13;
        columnMap["时间同步"] = 14;
        columnMap["APP运行状态"] = 15;
        
        // 填充测试项结果
        for(auto mapIt = columnMap.begin(); mapIt != columnMap.end(); ++mapIt)
        {
            QString testItem = mapIt.key();
            int col = mapIt.value();
            QString result = groupedData[esn].value(testItem, "未检测");
            
            QTableWidgetItem *item = new QTableWidgetItem(result);
            if(result == "不合格")
            {
                item->setForeground(QBrush(Qt::red));
            }
            else if(result == "合格")
            {
                item->setForeground(QBrush(Qt::green));
            }
            else if(result == "未检测")
            {
                item->setForeground(QBrush(Qt::gray));
            }
            
            ui->tableWidget->setItem(row, col, item);
        }
        
        row++;
    }
    
    qDebug() << "History data loaded for batch:" << batchName << "Records:" << row;
}

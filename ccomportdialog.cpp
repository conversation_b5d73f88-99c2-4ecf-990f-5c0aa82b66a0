﻿#include "ccomportdialog.h"
#include "ui_ccomportdialog.h"
#include <QMessageBox>
#include <QDebug>
#include <QThread>

CComPortDialog::CComPortDialog(QWidget *parent) :
    QDialog(parent),
    ui(new Ui::CComPortDialog)
{
    ui->setupUi(this);
    m_pSerialPort = new QSerialPort(this);
    connect(ui->checkBox, SIGNAL(toggled(bool)), this, SLOT(on_checkBox_toggl(bool)));

    m_nLyZt = 2;

    connect(&m_lyzttimer, SIGNAL(timeout()), this, SLOT(on_lyzttimer()));

    m_lyzttimer.start(2000);

    connect(&m_timer, SIGNAL(timeout()), this, SLOT(on_timer()));
}

CComPortDialog::~CComPortDialog()
{
    delete ui;
}


void CComPortDialog::querySerialPort()
{
    ui->comNameCB->clear();
    QList<QSerialPortInfo> list = QSerialPortInfo::availablePorts();
    for (int i = 0; i < list.size(); i++)
    {
        ui->comNameCB->addItem( list.at(i).portName()) ;
    }
    ui->comNameCB->setCurrentText(m_scomParam.strComName);
}


bool CComPortDialog::queryLy(int num)
{
    m_nlychecknum = num;
    if(!m_pSerialPort->isOpen())
    {
        QMessageBox::about(this, "蓝牙", "蓝牙模组连接失败");
        return false;
    }

    if(m_strFuncName == "蓝牙模组")
    {
        g_mtx.lock();
        g_mapLyName.clear();
        g_mtx.unlock();

        if(!m_pSerialPort->isOpen())
        {
            return false;
        }
        m_lyzttimer.stop();
        m_pSerialPort->write("AT+TYPE=4\r\n");
        m_nLyZt = 1;
        m_timer.start(20000);

    }

    return  true;

}

bool CComPortDialog::lymzZt()
{
    return  m_pSerialPort->isOpen();;
}

void CComPortDialog::init(QString strTitle, SComParam &comParam)
{
    m_scomParam = comParam;
    m_strFuncName = strTitle;

    if(strTitle == "扫码枪")
    {
        m_strKeyName = "smqconnect";
    }
    else if (strTitle == "蓝牙模组")
    {
        m_strKeyName = "lymzconnect";
    }

    QString s = strTitle + "连接";
    setWindowTitle(s);

    querySerialPort();

    if(!comParam.strComName.isEmpty())
        ui->comNameCB->setCurrentText(comParam.strComName);

    if(!comParam.strBtl.isEmpty())
        ui->comBtlCB->setCurrentText(comParam.strBtl);

    if(!comParam.strJyw.isEmpty())
        ui->comJywCB->setCurrentText(comParam.strJyw);

    if(!comParam.strLkz.isEmpty())
        ui->comLkzCB->setCurrentText(comParam.strLkz);

    if(!comParam.strSjw.isEmpty())
        ui->comSjwCB->setCurrentText(comParam.strSjw);

    if(!comParam.strTzw.isEmpty())
        ui->comTzwCB->setCurrentText(comParam.strTzw);

    if(comParam.bAutoConnect)
    {
        ui->checkBox->setChecked(true);
        bool bFind = false;
        for (int m = 0; m < ui->comNameCB->count(); ++m)
        {
            if(ui->comNameCB->itemText(m) == comParam.strComName)
            {
                bFind = true;
                break;
            }
        }
        if(!bFind)
        {
            QMessageBox::about(this, s, s + "失败");
            return;
        }
        comLink();
    }
}


// 串口连接
void CComPortDialog::comLink()
{
    on_offBtn_clicked();        // 断开串口

    m_pSerialPort->setPortName(ui->comNameCB->currentText());

    m_pSerialPort->setBaudRate(ui->comBtlCB->currentText().toInt());
    int nDataBits = ui->comSjwCB->currentText().toInt();

    switch(nDataBits)
    {
    case 5:
        m_pSerialPort->setDataBits(QSerialPort::Data5);
        break;
    case 6:
        m_pSerialPort->setDataBits(QSerialPort::Data6);
        break;
    case 7:
        m_pSerialPort->setDataBits(QSerialPort::Data7);
        break;
    case 8:
         m_pSerialPort->setDataBits(QSerialPort::Data8);
        break;
    default:
        break;
    }

    QString sStopBits = ui->comTzwCB->currentText();
    if(sStopBits == QString("1"))
    {
        m_pSerialPort->setStopBits(QSerialPort::OneStop);
    }
    else if(sStopBits == QString ("1.5"))
    {
         m_pSerialPort->setStopBits(QSerialPort::OneAndHalfStop);
    }
    else if(sStopBits == QString("2"))
    {
        m_pSerialPort->setStopBits(QSerialPort::TwoStop);
    }
    else
    {
        m_pSerialPort->setStopBits(QSerialPort::UnknownStopBits);
    }

    QString sParty = ui->comJywCB->currentText();
    if(sParty == QString("NONE"))
    {
        m_pSerialPort->setParity(QSerialPort::NoParity);
    }
    else if(sParty == QString("ODD"))
    {
        m_pSerialPort->setParity(QSerialPort::OddParity);
    }
    else if(sParty == QString("EVEN"))
    {
        m_pSerialPort->setParity(QSerialPort::EvenParity);
    }
    else if(sParty == QString("MARK"))
    {
         m_pSerialPort->setParity(QSerialPort::MarkParity);
    }
    else if(sParty == QString("SPACE"))
    {
        m_pSerialPort->setParity(QSerialPort::SpaceParity);
    }
    else
    {
        m_pSerialPort->setParity(QSerialPort::UnknownParity);
    }

    QString sFlowControl = ui->comLkzCB->currentText();
    if(sFlowControl == QString("NONE"))
    {
        m_pSerialPort->setFlowControl(QSerialPort::NoFlowControl);
    }
    else if(sFlowControl == QString("RTS/CTS"))
    {
        m_pSerialPort->setFlowControl(QSerialPort::HardwareControl);
    }
    else if(sFlowControl == QString("XON/XOFF"))
    {
        m_pSerialPort->setFlowControl(QSerialPort::SoftwareControl);
    }
    else
    {
        m_pSerialPort->setFlowControl(QSerialPort::UnknownFlowControl);
    }
    m_pSerialPort->setReadBufferSize(0);

    if (!m_pSerialPort->open(QIODevice::ReadWrite))
    {
        QString stitle = m_strFuncName + "连接失败";
        QMessageBox::information(this, "串口连接", stitle);
        ui->label_2->setText("未连接");
    }
    else
    {
        m_scomParam.strComName = ui->comNameCB->currentText();
        connect(m_pSerialPort, SIGNAL(readyRead()), this, SLOT(on_serialPort_readyRead()));
        ui->label_2->setText("已连接");
        setCfg(strScuOutAutoCheckIni, m_strKeyName, "portName", ui->comNameCB->currentText());
        setCfg(strScuOutAutoCheckIni, m_strKeyName, "baudRate", ui->comBtlCB->currentText());
        setCfg(strScuOutAutoCheckIni, m_strKeyName, "stopBits", ui->comTzwCB->currentText());
        setCfg(strScuOutAutoCheckIni, m_strKeyName, "dataBits", ui->comSjwCB->currentText());
        setCfg(strScuOutAutoCheckIni, m_strKeyName, "flowCtrl", ui->comLkzCB->currentText());
        setCfg(strScuOutAutoCheckIni, m_strKeyName, "jyBits", ui->comJywCB->currentText());

        if(m_strFuncName == "蓝牙模组")
        {
            m_nLyZt = 1;
            m_pSerialPort->write("AT+SCANSTOP\r\n"); 
        }

    }
}
// 连接串口
void CComPortDialog::on_linkBtn_clicked()
{
    comLink();
}

// 断开
void CComPortDialog::on_offBtn_clicked()
{
    if(!m_pSerialPort->isOpen())
    {
        return;
    }
    m_pSerialPort->disconnect();
    m_pSerialPort->close();
    ui->label_2->setText("未连接");
}

// 接收数据
void CComPortDialog::on_serialPort_readyRead()
{
    if(m_strFuncName == "扫码枪")
    {
        scan_readyRead();
    }
    else if (m_strFuncName == "蓝牙模组")
    {
        lymz_readyRead();
    }
}

void CComPortDialog::on_queryComBtn_clicked()
{
    querySerialPort();
}

void CComPortDialog::on_checkBox_toggl(bool on)
{
    if(on)
    {
        setCfg(strScuOutAutoCheckIni, m_strKeyName, "autoConnect", "1");
    }
    else
    {
        setCfg(strScuOutAutoCheckIni, m_strKeyName, "autoConnect", "0");
    }
}

void CComPortDialog::on_lyzttimer()
{
    if(m_strFuncName != "蓝牙模组")
        return;

    if(m_nLyZt == 2)
        return;

    if(!m_pSerialPort->isOpen())
        return;

    m_pSerialPort->write("AT+SCANSTOP\r\n");
}

void CComPortDialog::on_timer()
{
    m_timer.stop();

    m_lyzttimer.start(2000);

    g_bLyQuery[m_nlychecknum] = true;
}

void CComPortDialog::scan_readyRead()
{
    m_sComRead.init();
    QByteArray byte = m_pSerialPort->readAll();
    QByteArray bytedata;
    if(byte[byte.size()-1] == '\r')
    {
        bytedata = byte.left(byte.size()-1);
    }
    QString strByte = QString::fromUtf8(bytedata);

    if(strByte.startsWith("台区智能融合终端"))
    {
        m_sComRead.nType = 3;
        QStringList sList = strByte.split("，");
        if(sList.size() < 9)
            return;
        for (int m = 0; m < sList.size(); ++m)
        {
            QStringList strList = sList[m].split("：");
            if(strList.size() != 2)
                continue;
            QString s1 = strList[0];
            QString s2 = strList[1];
            if(s1 == "类型")
                m_sComRead.strZdLx = s2;
            else if(s1 == "厂商")
                m_sComRead.strCs = s2;
            else if(s1 == "型号")
                m_sComRead.strXh = s2;
            else if(s1 == "ID")
                m_sComRead.strID = s2;
            else if(s1 == "ESN")
                m_sComRead.strEsn = s2;
            else if(s1 == "硬件版本")
                m_sComRead.strYjBb = s2;
            else if(s1 == "生产日期")
            {
                int nYear = s2.left(4).toInt();

                int npos = s2.indexOf("月");
                int nMon = s2.mid(5, npos-5).toInt();
                int nDay = s2.mid(npos+1, s2.size()-2-npos).toInt();

                m_sComRead.strScrq.sprintf("%04d%02d%02d", nYear, nMon, nDay);
            }
        }
    }
    else if(strByte.indexOf(".") != -1)
    {
        m_sComRead.nType = 1;
        m_sComRead.strIp = strByte;
    }
    else if(strByte.size() < 4)
    {
        m_sComRead.nType = 4;
        m_sComRead.strBw = strByte;
    }
    else
    {
        m_sComRead.nType = 2;
        m_sComRead.strLjdz = strByte.mid(9, 12);
    }

    emit(comPortSig(m_sComRead));
}

void CComPortDialog::lymz_readyRead()
{
    QByteArray data =  m_pSerialPort->readAll();
    m_zdata += QString::fromUtf8(data);
    QStringList sList = m_zdata.split("\r\n");
    for (int m = 0; m < sList.size(); ++m)
    {
        int nIndex = sList[m].indexOf("FFF0409");
        if(nIndex == -1 )
        {
            continue;;
        }

        if(sList[m].size() != (nIndex+13))
        {
            continue;
        }

        QString sLyName = sList[m].right(6);

        QByteArray hex = QByteArray::fromHex(sLyName.toUtf8());
        QString decodedStr = QString::fromLatin1(hex);

        g_mtx.lock();
        g_mapLyName[decodedStr] = 1;
        g_mtx.unlock();

        m_zdata.clear();

    }

    if(m_zdata.indexOf("AT+SCANSTOP=OK") != -1)
    {
        m_nLyZt = 2;
        m_zdata.clear();
    }

    if(m_zdata.indexOf("AT+TYPE=OK") != -1)
    {
        m_pSerialPort->write("AT+SCANSTART\r\n");
        m_zdata.clear();
    }

}

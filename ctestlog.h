﻿#ifndef CTESTLOG_H
#define CTESTLOG_H

#include <QObject>
#include <QMetaType>
#include"cscancode.h"
#include<QTcpSocket>
#include<QThread>
#include<QSerialPort>
#include<ssh/sftpchannel.h>
#include<ssh/sshconnection.h>
#include<ssh/sshremoteprocess.h>
#include <QtMqtt/qmqttclient.h>
#include<QQueue>
#include<QTimer>
#include <QJsonObject>
#include <QJsonDocument>
#include <QJsonArray>

namespace Ui {
class cTestLog;
}

struct TestItemResult {
    QString mainItem; // 大项名称
    QString subItem;  // 小项名称
    QString result;   // 测试结果
};

struct TestResult {
    int stationId;    // 测试表位
    QString esn;      // ESN
    QString esam;
    QList<TestItemResult> subItems; // 所有项结果
};

// 注册自定义类型
Q_DECLARE_METATYPE(TestResult)
Q_DECLARE_METATYPE(TestItemResult)

class cTestLog : public QObject
{
    Q_OBJECT

public:
    explicit cTestLog(QWidget *parent = nullptr);
    ~cTestLog();



signals:
    void readyReadStandardOutput();
    void sigSend(QString cmd);//发送ssh指令
    void sigDisconnected();//断开连接
    void sigUploadFile(QString localPath,QString remotePath);
    void sigRevdata(QString RevData);
    void signState(bool res);
    void signEsn(QString num);
    void signEsam(QString num);
    void signLicense(bool suc);
    void signCurrentTestItem(QString itemName);
    void signTestResult(QString s);
    void signTestShell(QString s);
    void signtestlog(QString s);
    void signTestItemRes(QString itemName,QString res);
    void signJBTestRes(int res);
    void stepFinished();
    void signTestSendNum(QString curBatch,QString esnNum,QString esamNum);

    void signTestResultData(const TestResult &result);
    void signTableRes(const TestResult &result);

public slots:

    void addTestLog(int type, QString s);
    void onSlotNUM(int num);
    void EndTest();
    void onSlotsValues(float Ia,float Ib,float Ic,float Va,float Vb,float Vc);
    void njbByteArray();


private slots:

    void onSshConnected();
    void onSshError(QSsh::SshError sshError);
    void OnReadyReadStandardOutput();
    void onSlotstateChanged(QAbstractSocket::SocketState);
    void onSlotsTcpreadyRead();
    void onSftpJobFinished(QSsh::SftpJobId job, const QString &error);
    void onSftpChannelInitialized();
    void dbvisionFalse();
    void executeNextStep();

    void on_mqtt_mesReceived(const QByteArray &message, const QMqttTopicName &topic);
    void on_mqtt_StateChange();

private:

    QSerialPort *m_serialport;
    QTimer *m_timer;
    QTimer *timer;
    int m_currentStep;

    QMqttClient  *m_pclient;
    void initMqttCon();

    bool m_NetConnect = false;
    bool m_ComConnect = false;
    bool m_jnTestStart = false;

    bool UpdateDbVersion ;
    int tcpConNum = 5;
    bool m_dbTestRes = false;
    bool m_liceseFile = true;


    float IA;
    float IB;
    float IC;
    float VA;
    float VB;
    float VC;


    //ssh连接和ssh上传文件
    void changeIP();
    QSsh::SshConnection  *m_sshSocket;
    QSsh::SshRemoteProcess::Ptr m_shell;
    QSsh::SftpChannel::Ptr m_channel;
    void SendMsg(QByteArray s);
    void netConnect(QString ip);
    void creatchannel(const QString &localpath, const QString &remotePath);
    void uploadFilesInParallel(const QStringList &localPaths, const QStringList &remotePaths);
    void startNextUpload();
    QStringList m_uploadLocalPaths;
    QStringList m_uploadRemotePaths;
    int m_currentUploadIndex = -1;

    QString m_esnNum;
    QString m_esamNUM;
    QString m_esnCodeNum;
    QString m_esamCodeNum;

    int m_ClearBTcount;
    int m_SshConCount;


    int m_NUM;      //当前表位号
    QString m_IP ;  //当前IP
    int m_nLx = 0;
    int m_portNum = 0;

    QMap<QString,QString> m_AppMap ;
    QStringList m_containList;


    //检测项
    void executeNextStep1(int n);
    void updataDb();
    int  m_testItemStep = 0;
    void readXml(QString testitem);
    void compareVersion();
    void JbTest();

    //接收数据
    QByteArray m_buffer;
    void processBuffer();
    void handleLine(const QByteArray &line);
    void uninstallcontainer(QString s);
    void installcontainer(QString s);
    void checkcontainer(QString s);
    void installapp(QString s);
    void apprunning(QString s);

    //大包升级
    bool dbVersion;
    int m_dbFalseStep;
    QTimer *m_dbtimer = nullptr;

    //校表
    QTimer *jbTimer;
    QTcpSocket *tcpsocket ;
    void initTcpConnect();
    void jbByteArray();
    void dyByteArray();
    void dlByteArray();
    void glysByteArray();
    void ndlByteArray();
    void dyRev(QByteArray revdata);
    void dlRev(QByteArray revdata);
    void ndlRev(QByteArray revdata);
    void glysRev(QByteArray revdata);
    bool m_jbTestRes = true;


    //通行证加密
    QString creatCodeEs(QString s);
    void createFile(QString filePath);
    QMap<int,QString> InfoMap;

    //结果
    TestResult m_currentTestResult;
    
    // 辅助函数
    void initTestResult();
    void addTestItem(const QString &mainItem, const QString &subItem, const QString &result);
    void finalizeTestResult();
    
    // 删除所有原有的QMap成员
    // QMap<QString,int> m_VersionMap;
    // QMap<QString,int> m_clearResMap;
    // QMap<QString,int> m_containerMap;
    // QMap<QString,int> m_appinstallMap;
    // QMap<QString,int> m_appRuningMap;
    // QMap<QString,int> m_jbMap;
    // QMap<QString,int> m_AllResMap;
    // QStringList m_allRes;
    void SaveTestFile(QByteArray s);
    void SaveShellFile(QByteArray s);
    void CreatExcel();

    bool m_timeSyncCompleted = false;    // 时间同步完成标志
    bool m_appRunningCompleted = false;  // APP运行状态检测完成标志
    bool m_timeSyncEnabled = false;      // 时间同步是否启用
    bool m_appRunningEnabled = false;    // APP运行状态检测是否启用


};


#endif // CTESTLOG_H

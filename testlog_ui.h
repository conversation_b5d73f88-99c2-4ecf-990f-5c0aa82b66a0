#ifndef TESTLOG_UI_H
#define TESTLOG_UI_H

#include <QWidget>
#include"ctestlog.h"
#include"mythread.h"

namespace Ui {
class Testlog_ui;
}

class Testlog_ui : public QWidget
{
    Q_OBJECT

public:
    explicit Testlog_ui(QWidget *parent = nullptr);
    ~Testlog_ui();

    QVector<QStringList> GetTableData();

private:
    Ui::Testlog_ui *ui;

    cTestLog *m_test;
    MyThread *m_thread;

    int Row = 0;

public slots:
    void onslotsRevtest(QString s);
    void onslotsRevShell(QString s);

    void onslotsTestItem(QString testitem,QString TestResult);

};

#endif // TESTLOG_UI_H

﻿#include "cfunctionwindow.h"
#include "ui_cfunctionwindow.h"
#include <QMessageBox>
#include <QDebug>
#include <QDir>
#include "cmmdialog.h"
CFunctionWindow::CFunctionWindow(QWidget *parent)
    : QMainWindow(parent)
    , ui(new Ui::CFunctionWindow)
{
    ui->setupUi(this);

    m_nFAID = 0;

    inicfgInit();
    toolBarInit();
    readCfg();

    m_pGorge = nullptr;
    m_pAbout = nullptr;
    m_pClasses = nullptr;
    m_pScanVerify = nullptr;
    m_pGntTest = nullptr;
    m_pVdQuery = nullptr;

    m_pSmqCom = nullptr;
    /*m_pSmqCom = new CComPortDialog(this);
    connect(m_pSmqCom, SIGNAL(comPortSig(SComRead &)), this, SLOT(on_smq_comPortSig(SComRead &)));
    m_pSmqCom->init("扫码枪", m_sSmqcomParam);*/

    m_pLyCom = new CComPortDialog(this);
    m_pLyCom->init("蓝牙模组", m_sLycomParam);
}

CFunctionWindow::~CFunctionWindow()
{
    delete ui;
}

bool CFunctionWindow::event(QEvent *e)
{

    if(e->type() == CMmiMsg::MM_CSGC)
    {
        CCsXlGcMsg* pMsg = dynamic_cast<CCsXlGcMsg*>(e);
        if(pMsg != nullptr)
        {
            m_pGntTest->pushGc(pMsg);
        }
    }
    else if (e->type() == CMmiMsg::MM_CSXL)
    {
        CCsXlJgMsg* pMsg = dynamic_cast<CCsXlJgMsg*>(e);
        if(pMsg != nullptr)
        {
            m_pGntTest->pushJg(pMsg);
        }
    }
    else if(e->type() == CMmiMsg::MM_CSJS)
    {
        CCsJsMsg* pMsg = dynamic_cast<CCsJsMsg*>(e);
        if(pMsg != nullptr)
        {
            m_pGntTest->pushJs(pMsg);
        }
    }
    else if(e->type() == CMmiMsg::MM_LYCX)
    {
        CCsLyMsg* pMsg = dynamic_cast<CCsLyMsg*>(e);
        if(pMsg != nullptr)
        {
            m_pGntTest->setYlmz(*pMsg);
        }
    }
    else if(e->type() == CMmiMsg::MM_HBDY)
    {
        m_pGntTest->setHbdy();
    }
    else if(e->type() == CMmiMsg::MM_SW)
    {
        m_pGntTest->setSW();
    }
    else if(e->type() == CMmiMsg::MM_JCJL)
    {
        CCsJcjlMsg* pMsg = dynamic_cast<CCsJcjlMsg*>(e);
        if(pMsg != nullptr)
        {
            m_pGntTest->setJcjl(pMsg->getLx());
        }
    }
    else if(e->type() == CMmiMsg::MM_JLCX)
    {
        CCsJlcxMsg* pMsg = dynamic_cast<CCsJlcxMsg*>(e);
        if(pMsg != nullptr)
        {
            m_pVdQuery->setJlcx(pMsg->getVtdl());
        }
    }
    else if(e->type() == CMmiMsg::MM_XLCX)
    {
        CCsXlCxMsg* pMsg = dynamic_cast<CCsXlCxMsg*>(e);
        if(pMsg != nullptr)
        {
            m_pVdQuery->setXlcx(pMsg->getXl());
        }
    }
    else if(e->type() == CMmiMsg::MM_GC)
    {
        CCsGcMsg* pMsg = dynamic_cast<CCsGcMsg*>(e);
        if(pMsg != nullptr)
        {
            m_pVdQuery->setGc(pMsg->getGc());
        }
    }
    else if(e->type() == CMmiMsg::MM_TTKZ)
    {
        CCsTtKzMsg* pMsg = dynamic_cast<CCsTtKzMsg*>(e);
        if(pMsg != nullptr)
        {
            m_pGntTest->setTtKz(pMsg->getTtkz());
        }
    }
    else if(e->type() == CMmiMsg::MM_YGMC)
    {
        CCsYgmcMsg * pMsg = dynamic_cast<CCsYgmcMsg*>(e);
        if(pMsg != nullptr)
        {
            m_pGntTest->setYgmc(pMsg);
        }
    }
    else if(e->type() == CMmiMsg::MM_WGMC)
    {
        CCsWgmcMsg * pMsg = dynamic_cast<CCsWgmcMsg*>(e);
        if(pMsg != nullptr)
        {
            m_pGntTest->setWgmc(pMsg);
        }
    }
    else if(e->type() == CMmiMsg::MM_MMC)
    {
        CCsmmcMsg * pMsg = dynamic_cast<CCsmmcMsg*>(e);
        if(pMsg != nullptr)
        {
            m_pGntTest->setMmc(pMsg);
        }
    }
    else if(e->type() == CMmiMsg::MM_YX)
    {
        CCsYxMsg * pMsg = dynamic_cast<CCsYxMsg*>(e);
        if(pMsg != nullptr)
        {
            m_pGntTest->setYx(pMsg->getNum());
        }
    }
    else if(e->type() == CMmiMsg::MM_RTC)
    {
        CRtcMsg * pMsg = dynamic_cast<CRtcMsg*>(e);
        if(pMsg != nullptr)
        {
            m_pGntTest->setRtc(pMsg->getNum());
        }
    }
    else if(e->type() == CMmiMsg::MM_CERTEXPORT)
    {
        CErtExportMsg *pMsg = dynamic_cast<CErtExportMsg *>(e);
        if(pMsg != nullptr)
        {
            m_pGntTest->setExport();
        }
    }
    else if(e->type() == CMmiMsg::MM_NORMAL)
    {
        CNormalMsg *pMsg = dynamic_cast<CNormalMsg *>(e);
        if(pMsg != nullptr)
        {
            m_pGntTest->setNormal(pMsg->getLx(), pMsg->getBw());
        }
    }
    else if(e->type() == CMmiMsg::MM_SSHOFF)
    {
        CSSHOFF *pMsg =  dynamic_cast<CSSHOFF *>(e);
        if(pMsg != nullptr)
        {
            m_pGntTest->setSshOff(pMsg);
        }
    }

    return QMainWindow::event(e);
}

void CFunctionWindow::on_actCk_trigger()
{
    if(m_pGorge == nullptr)
    {
        m_pGorge = new CGorgeWidget(this);
        ui->stackedWidget->addWidget(m_pGorge);
    }
    m_pGorge->initGorge();
    QString styleSheet ;
    ui->stackedWidget->setStyleSheet(styleSheet);
    ui->stackedWidget->setCurrentWidget(m_pGorge);
}

void CFunctionWindow::on_actGy_trigger()
{
    if(m_pAbout == nullptr)
    {
        m_pAbout = new CAboutDialog(this);
    }
    m_pAbout->show();
}

void CFunctionWindow::on_actFa_trigger()
{
    if(m_pClasses == nullptr)
    {
        m_pClasses = new CClassesWidget(this);
        connect(m_pClasses, SIGNAL(scanFlag()), this, SLOT(on_scanFlag()));
        ui->stackedWidget->addWidget(m_pClasses);
    }
    readFaCfg();
    m_pClasses->initClasses(m_nFAID, m_vtClasses);
    QString styleSheet ;
    ui->stackedWidget->setStyleSheet(styleSheet);
    ui->stackedWidget->setCurrentWidget(m_pClasses);
}

void CFunctionWindow::on_actgncs_trigger()
{
    if(m_pGntTest == nullptr)
    {
        m_pGntTest = new CGntTestWidget(this);
        m_pGntTest->setComPort(m_pLyCom);
        ui->stackedWidget->addWidget(m_pGntTest);
    }
    m_pGntTest->setQuery(m_pVdQuery);
    QString styleSheet ;
    ui->stackedWidget->setStyleSheet(styleSheet);
    ui->stackedWidget->setCurrentWidget(m_pGntTest);
}

void CFunctionWindow::on_actJl_trigger()
{
    if(m_pVdQuery == nullptr)
    {
        m_pVdQuery = new CVerdictQueryWg(this);
        ui->stackedWidget->addWidget(m_pVdQuery);
    }
    m_pVdQuery->init(m_pGntTest);
    QString styleSheet ;
    ui->stackedWidget->setStyleSheet(styleSheet);
    ui->stackedWidget->setCurrentWidget(m_pVdQuery);
}

// 扫码枪连接
void CFunctionWindow::on_actSm_trigger()
{
    m_pSmqCom->querySerialPort();
    m_pSmqCom->show();
}

void CFunctionWindow::on_actly_trigger()
{
    m_pLyCom->querySerialPort();
    m_pLyCom->show();
}

// 扫码枪触发数据
void CFunctionWindow::on_smq_comPortSig(SComRead &comRead)
{
    if(m_pScanVerify == nullptr)
    {
        m_pScanVerify = new CScanVerifyDialog(this);
        connect(m_pScanVerify, SIGNAL(scanInfoSig(std::map<int, SSCUParam>&)), this, SLOT(on_scan_scanInfoSig(std::map<int, SSCUParam>&)));
    }
    m_nFAID = queryCfg(strScuOutAutoCheckIni, "FA", "FAID").toInt();
    QString strName = queryCfg(strScuOutAutoCheckIni, "FA", QString::number(m_nFAID));
    m_pScanVerify->init(m_nFAID, strName);
    if(m_pScanVerify->isHidden())
        m_pScanVerify->show();

    m_pScanVerify->scanInfo(comRead);
}

void CFunctionWindow::on_scan_scanInfoSig(std::map<int, SSCUParam> &mapScuParam)
{
    m_mapScuParam = mapScuParam;

    if(m_pGntTest == nullptr)
    {
        m_pGntTest = new CGntTestWidget(this);
        m_pGntTest->setComPort(m_pLyCom);
        ui->stackedWidget->addWidget(m_pGntTest);
    }
    m_pGntTest->setQuery(m_pVdQuery);
    m_pGntTest->init(mapScuParam);
    ui->stackedWidget->setCurrentWidget(m_pGntTest);

}

void CFunctionWindow::on_scanFlag()
{
    if(m_pScanVerify == nullptr)
    {
        m_pScanVerify = new CScanVerifyDialog(this);
        connect(m_pScanVerify, SIGNAL(scanInfoSig(std::map<int, SSCUParam>&)), this, SLOT(on_scan_scanInfoSig(std::map<int, SSCUParam>&)));
    }
    m_nFAID = queryCfg(strScuOutAutoCheckIni, "FA", "FAID").toInt();
    QString strName = queryCfg(strScuOutAutoCheckIni, "FA", QString::number(m_nFAID));
    m_pScanVerify->init(m_nFAID, strName);
    if(m_pScanVerify->isHidden())
        m_pScanVerify->show();

    if(m_pScanVerify->isHidden())
        m_pScanVerify->show();
}

void CFunctionWindow::inicfgInit()
{
    g_vtChkItem.clear();
    g_vtChkItem.push_back("用户名密码");
    g_vtChkItem.push_back("清除多余文件");
    g_vtChkItem.push_back("导入license");
    g_vtChkItem.push_back("容器");
    g_vtChkItem.push_back("APP运行");
    g_vtChkItem.push_back("系统和补丁版本");
    g_vtChkItem.push_back("终端参数");
    g_vtChkItem.push_back("698参数配置");
    g_vtChkItem.push_back("证书导出");
    g_vtChkItem.push_back("证书导入");
    g_vtChkItem.push_back("参数设置");
    g_vtChkItem.push_back("设备对时");
    g_vtChkItem.push_back("遥信分辨率");
    g_vtChkItem.push_back("有功、无功、秒脉冲接口");
    g_vtChkItem.push_back("零序检查");
    g_vtChkItem.push_back("交采计量精度");
    g_vtChkItem.push_back("MQTTIOT配置");
    g_vtChkItem.push_back("蓝牙通信");
    g_vtChkItem.push_back("HPLC/4G模块");
    g_vtChkItem.push_back("回路巡检");
    g_vtChkItem.push_back("磁场状态");
    g_vtChkItem.push_back("数据清零");
    g_vtChkItem.push_back("SW1/2按键");
    g_vtChkItem.push_back("串口通信");
    g_vtChkItem.push_back("端口检查");
    g_vtChkItem.push_back("后备电源");


    QString sPath = "C:/msk_scuoutautocheckIni/scucheckrqapp.ini";
    QDir dir;
    if(!dir.exists(sPath))
    {
        QFile file("scucheckrqapp.ini");
        file.copy(sPath);
    }
}

// 工具栏初始化
void CFunctionWindow::toolBarInit()
{
    ui->toolBar->setMovable(false);     // 禁止移动
    ui->toolBar->setIconSize(QSize(60, 60));        // 图标大小
    ui->toolBar->setToolButtonStyle(Qt::ToolButtonTextUnderIcon);       // 文字显示在图标下方


    QPixmap pix("./icon/ckpz.ico");
    pix.scaled(50, 50);
    QAction *actCk = new QAction(QIcon(pix), "参数配置");
    connect(actCk, SIGNAL(triggered()), this, SLOT(on_actCk_trigger()));
    ui->toolBar->addAction(actCk);

   /* pix.load("./icon/yl.png");
    pix.scaled(50, 50);
    QAction *actSm = new QAction(QIcon(pix),"扫码枪连接");
    connect(actSm, SIGNAL(triggered()), this, SLOT(on_actSm_trigger()));
    ui->toolBar->addAction(actSm);*/

    pix.load("./icon/jlcx.jpg");
    pix.scaled(50, 50);
    QAction *actLy = new QAction(QIcon(pix),"蓝牙模组连接");
    connect(actLy, SIGNAL(triggered()), this, SLOT(on_actly_trigger()));
    ui->toolBar->addAction(actLy);

    ui->toolBar->addSeparator();

    pix.load("./icon/gncs.ico");
    pix.scaled(50,50);

    QAction *actLb = new QAction(QIcon(pix), "测试方案");
    connect(actLb, SIGNAL(triggered()), this, SLOT(on_actFa_trigger()));
    ui->toolBar->addAction(actLb);

    pix.load("./icon/gncs.png");
    pix.scaled(50,50);

    QAction *actgncs = new QAction(QIcon(pix), "功能测试");
    connect(actgncs, SIGNAL(triggered()), this, SLOT(on_actgncs_trigger()));
    ui->toolBar->addAction(actgncs);

    pix.load("./icon/jlcx.jpg");
    pix.scaled(50,50);
    QAction *actJl = new QAction(QIcon(pix), "结论查询");
    connect(actJl, SIGNAL(triggered()), this, SLOT(on_actJl_trigger()));
    ui->toolBar->addAction(actJl);

    ui->toolBar->addSeparator();

    pix.load("./icon/gy.png");
    pix.scaled(50,50);
    QAction *actGy = new QAction(QIcon(pix), "关于");
    ui->toolBar->addAction(actGy);
    connect(actGy, SIGNAL(triggered()), this, SLOT(on_actGy_trigger()));
}

void CFunctionWindow::readCfg()
{
    readGorgeCfg();
    readFaCfg();
    readComCfg();
}

void CFunctionWindow::readFaCfg()
{
    QSettings  settings(strScuOutAutoCheckIni, QSettings::IniFormat);
    settings.setIniCodec(QTextCodec::codecForName("UTF-8"));

    // 读取测试类别
    m_vtClasses.clear();
    settings.beginGroup("FA");
    QStringList sList = settings.allKeys();

    for (int m =0; m < sList.size(); ++m)
    {
        if(sList[m] == "FAID" || sList[m] == "FAXH")
            continue;
        sClasses   sla;
        sla.strXh = sList[m];
        sla.strJclb = settings.value(sList[m]).toString();
        m_vtClasses.push_back(sla);
    }
    m_nFAID = settings.value("FAID").toULongLong();
    settings.endGroup();
}

void CFunctionWindow::readGorgeCfg()
{
    QSettings  settings(strScuOutAutoCheckIni, QSettings::IniFormat);
    settings.setIniCodec(QTextCodec::codecForName("UTF-8"));

    m_mapGorge.clear();
    settings.beginGroup("gorge");
    QStringList sList =  settings.allKeys();

    for (int m = 0; m < sList.size(); ++m)
    {
       QString s = settings.value(sList[m]).toString();
       QStringList strList = s.split("@");
       if(strList.size() < 4)
           continue;
        sGorge sg;
        sg.strName = strList[1];
        sg.strIp = strList[2];
        sg.strPort = strList[3];
        m_mapGorge[strList[0]].push_back(sg);
    }
    settings.endGroup();
}

void CFunctionWindow::readComCfg()
{
    QSettings  settings(strScuOutAutoCheckIni, QSettings::IniFormat);
    settings.setIniCodec(QTextCodec::codecForName("UTF-8"));

    // 扫码枪参数
    settings.beginGroup("smqconnect");
    m_sSmqcomParam.bAutoConnect = settings.value("autoConnect").toString().toInt() == 1? true:false;
    m_sSmqcomParam.strBtl = settings.value("baudRate").toString();
    m_sSmqcomParam.strComName = settings.value("portName").toString();
    m_sSmqcomParam.strJyw = settings.value("jyBits").toString();
    m_sSmqcomParam.strLkz = settings.value("flowCtrl").toString();
    m_sSmqcomParam.strSjw = settings.value("dataBits").toString();
    m_sSmqcomParam.strTzw = settings.value("stopBits").toString();
    settings.endGroup();

    // 读取蓝牙模组
    settings.beginGroup("lymzconnect");
    m_sLycomParam.bAutoConnect = settings.value("autoConnect").toString().toInt() == 1? true:false;
    m_sLycomParam.strBtl = settings.value("baudRate").toString();
    m_sLycomParam.strComName = settings.value("portName").toString();
    m_sLycomParam.strJyw = settings.value("jyBits").toString();
    m_sLycomParam.strLkz = settings.value("flowCtrl").toString();
    m_sLycomParam.strSjw = settings.value("dataBits").toString();
    m_sLycomParam.strTzw = settings.value("stopBits").toString();
    settings.endGroup();
}






#include "ctestpcwidget.h"
#include "ui_ctestpcwidget.h"
#include<QSqlTableModel>
#include<QDebug>

cTestpcWidget::cTestpcWidget(QWidget *parent) :
    QWidget(parent),
    ui(new Ui::cTestpcWidget)
{
    ui->setupUi(this);
    ui->tableView->setEditTriggers(QAbstractItemView::NoEditTriggers);
    //QStringList horizontalHeaders;
    //horizontalHeaders << "表位号" << "ID" << "测试时间" << "测试结果";
    //ui->tableWidget->setHorizontalHeaderLabels(horizontalHeaders);

//    m_db = Database::instance();
//    m_db = new Database(this);
//    m_db->connectToDatabase("127.0.0.1","checktable","root","123456");
//    QList<QVariantMap> configs = m_db->queryData("select * from testpc");
//    qDebug()<< configs;
//    for (auto item : configs) {
//        QString testpc = item.value("TestPc").toString();
//        ui->comboBox->addItem(testpc);
//    }

//    QSqlTableModel *portModel;
//    portModel = new QSqlTableModel(this);
//    portModel->setTable("log");
//    portModel->select();
//    ui->tableView->setModel(portModel);

}

cTestpcWidget::~cTestpcWidget()
{
    delete ui;
}

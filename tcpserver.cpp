﻿#include "tcpserver.h"
#include <QTcpSocket>
#include <QAbstractSocket>
#include <winsock2.h>
#include <ws2tcpip.h>
TcpServer::TcpServer(QObject *parent) : QTcpServer(parent)
{
    m_psocket = nullptr;
}

void TcpServer::startServer(quint16 port)
{

    if(!this->listen(QHostAddress("127.0.0.1"), port)) {
        qDebug() << "Could not start server:" << this->errorString();
    } else {
        qDebug() << "Listening to port" << port << "...";
    }

    int fd = socketDescriptor();
    if (fd != -1)
    {
        int optval = 1;
        if (setsockopt(fd, SOL_SOCKET, SO_REUSEADDR, (char*)&optval, sizeof(optval)) == -1)
        {
            qDebug() << "Failed to set SO_REUSEADDR:" << strerror(errno);
        }
    }
}

void TcpServer::writeDate(QByteArray data)
{
    m_psocket->write(data);
    m_psocket->flush();
}

bool TcpServer::getState()
{
    if(m_psocket == nullptr)
        return false;
    return m_psocket->state() == QTcpSocket::ConnectedState ? true: false;
}

void TcpServer::closesocket()
{
    if(m_psocket != nullptr && getState())
    {
         m_psocket->close();
    }
}

void TcpServer::incomingConnection(qintptr socketDescriptor)
{
    m_psocket = new QTcpSocket(this);
    if(!m_psocket->setSocketDescriptor(socketDescriptor)) {
        qDebug() << "Socket error:" << m_psocket->errorString();
        delete m_psocket;
        return;
    }

    connect(m_psocket, &QTcpSocket::readyRead, this, &TcpServer::onReadyRead);
    connect(m_psocket, &QTcpSocket::disconnected, this, &TcpServer::onDisconnected);
}

void TcpServer::onReadyRead()
{
    QByteArray data = m_psocket->readAll();

    emit(readData(data));
}

void TcpServer::onDisconnected()
{
    QTcpSocket *socket = qobject_cast<QTcpSocket*>(sender());
    socket->deleteLater();
}

﻿#include "caqmsdialog.h"
#include "ui_caqmsdialog.h"
#include "msk_global.h"
CAqmsDialog::CAqmsDialog(QWidget *parent) :
    QDialog(parent),
    ui(new Ui::CAqmsDialog)
{
    ui->setupUi(this);
}

CAqmsDialog::~CAqmsDialog()
{
    delete ui;
}

void CAqmsDialog::setKeyName(QString s)
{
    m_strKeyName = s;
    QString s2 = queryCfg(strScuOutAutoCheckIni, m_strKeyName, "jmms");
    if(s2 == "1")
    {
        ui->radioButton->setChecked(true);
    }
    else
    {
        ui->radioButton_2->setChecked(true);
    }
}

void CAqmsDialog::on_anjmOk_clicked()
{
    setCfg(strScuOutAutoCheckIni, m_strKeyName, "jmms", ui->radioButton->isChecked() ? "1" :"0");
}

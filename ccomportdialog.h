﻿#ifndef CCOMPORTDIALOG_H
#define CCOMPORTDIALOG_H

#include <QTimer>
#include <QDialog>
#include <QtSerialPort/QSerialPortInfo>
#include <QtSerialPort/QSerialPort>
#include "msk_global.h"
namespace Ui {
class CComPortDialog;
}

class CComPortDialog : public QDialog
{
    Q_OBJECT

public:
    explicit CComPortDialog(QWidget *parent = nullptr);
    ~CComPortDialog();
    void init(QString strTitle, SComParam  &);

    void comLink();
    void querySerialPort();
    bool queryLy(int num);
    bool lymzZt();

Q_SIGNALS:
    void comPortSig(SComRead &);

private slots:
    void on_linkBtn_clicked();      // 连接串口

    void on_offBtn_clicked();       // 断开串口

    void on_serialPort_readyRead();     // 接收数据

    void on_queryComBtn_clicked();

    void on_checkBox_toggl(bool);

    void on_lyzttimer();
    void on_timer();

private:
    void scan_readyRead();          // 扫码枪数据
    void lymz_readyRead();          // 蓝牙模组

private:
    QString m_strFuncName;
    SComParam  m_scomParam;
    QSerialPort *m_pSerialPort;
    SComRead     m_sComRead;            // 读取参数

    QString m_strKeyName;

    int m_nLyZt;        // 蓝牙状态 1是 2是停止扫描

    QTimer  m_lyzttimer;
    QTimer  m_timer;

    QString m_zdata;

    int m_nlychecknum;
private:
    Ui::CComPortDialog *ui;
};

#endif // CCOMPORTDIALOG_H

﻿#ifndef CFUNCTIONWINDOW_H
#define CFUNCTIONWINDOW_H
#include "msk_global.h"
#include <QMainWindow>
#include <QCloseEvent>
#include "cgorgewidget.h"
#include "caboutdialog.h"
#include "cclasseswidget.h"
#include "ccomportdialog.h"
#include "cscanverifydialog.h"
#include "cgnttestwidget.h"
#include "cverdictquerywg.h"
QT_BEGIN_NAMESPACE
namespace Ui { class CFunctionWindow; }
QT_END_NAMESPACE

class CFunctionWindow : public QMainWindow
{
    Q_OBJECT

public:
    CFunctionWindow(QWidget *parent = nullptr);
    ~CFunctionWindow();
    void init();
protected:
    virtual bool event(QEvent* e);				// 处理 内部消息
private slots:
    void on_actCk_trigger();        // 串口配置
    void on_actGy_trigger();        // 关于
    void on_actFa_trigger();        // 检测方案
    void on_actgncs_trigger();        // 功能测试
    void on_actJl_trigger();        // 结论查询
    void on_actSm_trigger();        // 扫码枪连接
    void on_actly_trigger();        // 蓝牙模组连接
    void on_smq_comPortSig(SComRead &);     // 扫码枪
    void on_scan_scanInfoSig(std::map<int, SSCUParam>&);

    void on_scanFlag();         // 扫码窗口

private:
    void inicfgInit();              // 配置文件初始化
    void toolBarInit();         // 工具栏初始化
    void readCfg();             // 读配置文件
    void readFaCfg();               //查询方案参数
    void readGorgeCfg();            // 查询端口映射的IP
    void readComCfg();          // 查询串口类参数

    void initChkItem();             // 初始化校验项

private:
    std::map<QString, std::vector<sGorge> > m_mapGorge;
    std::vector<sClasses> m_vtClasses;
    quint64 m_nFAID;            // 方案ID
    SComParam m_sSmqcomParam;      // 串口参数
    SComParam m_sLycomParam;      // 串口参数

    std::map<int, SSCUParam> m_mapScuParam;

    SGntParam  m_sGntParam;          // 功能台参数
private:
    Ui::CFunctionWindow *ui;

    CGorgeWidget  *m_pGorge;  // 串口配置

    CAboutDialog  *m_pAbout;        // 关于

    CClassesWidget  *m_pClasses;        // 检测

    CComPortDialog  *m_pSmqCom;         // 扫码枪连接
    CComPortDialog  *m_pLyCom;          // 蓝牙连接

    CScanVerifyDialog   *m_pScanVerify;     // 扫码确认

    CGntTestWidget      *m_pGntTest;            // 功能台测试

    CVerdictQueryWg     *m_pVdQuery;            // 结论查询

};
#endif // CFUNCTIONWINDOW_H

﻿#include "cgnttestprocess.h"
#include <QDebug>
#include <exception>
CGntTestProcess::CGntTestProcess()
{
    m_pGntTest = nullptr;
}

void CGntTestProcess::run()
{
    if(m_pGntTest == nullptr)
    {
        m_pGntTest = new CGntTest();
        connect(m_pGntTest, SIGNAL(endSig()), this, SLOT(on_endSig()));
    }

    m_pGntTest->init(m_sGntParam);
    m_pGntTest->check();
    exec();
}

void CGntTestProcess::setGntParam(SGntParam &gntParam)
{
    m_sGntParam = gntParam;
}


void CGntTestProcess::on_endSig()
{
    this->quit();
    this->wait();
    QApplication::postEvent((QObject*)g_pFuncWindow, new CCsJsMsg(m_sGntParam.nBw));
}

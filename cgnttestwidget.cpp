﻿#include "cgnttestwidget.h"
#include "ui_cgnttestwidget.h"
#include "cverdictquerywg.h"
#include "cgntreportdialog.h"
#include <QtDebug>
#include <QDate>
#include <QMenu>
#include <QScrollBar>
#include "cfinishaccorddialog.h"

CGntTestWidget::CGntTestWidget(QWidget *parent) :
    QWidget(parent),
    ui(new Ui::CGntTestWidget)
{
    ui->setupUi(this);

    tableInit();
    csDLResultTWinit();
    csXlResultTWinit();

    m_pSW = new CSWDialog(this);
    connect(m_pSW, SIGNAL(okSig()), this, SLOT(on_SWOK()));

    for (int m = 0; m < 41; ++m)
    {
        CGntTestProcess *pGntTestPro = new CGntTestProcess();
        m_vtTestProcess.push_back(pGntTestPro);
    }

    m_dbThread.start();

    m_nCurBW = -1;
    m_nCurDl = -1;
    m_bFirstSd = true;
    m_nBwCount = 40;
    m_pEquipmentsjj = nullptr;
    m_pEquipmentwc = nullptr;
    m_ptcpServer = nullptr;
    m_pOpenCloseCap = nullptr;
    m_pGntScuInfo = nullptr;
    m_pFinishDialog = new CFinishDialog(this);
    m_bHidden = true;
    m_bYx = false;

    ui->createReportBtn->hide();

    connect(&m_timer, SIGNAL(timeout()), this, SLOT(on_timer()));
    m_timer.start(500);

    connect(&m_monitor, SIGNAL(timeout()), this, SLOT(on_monitor()));
    m_monitor.start(5000);

    connect(&m_jctimer, SIGNAL(timeout()), this, SLOT(on_jctimer()));

    connect(&m_tcpServertimer,  SIGNAL(timeout()), this, SLOT(on_tcpservertimer()));
}

CGntTestWidget::~CGntTestWidget()
{
    for (int m = 0; m < m_vtTestProcess.size(); ++m)
    {
        delete  m_vtTestProcess[m];
        m_vtTestProcess[m] = nullptr;
    }
    m_vtTestProcess.clear();
    delete ui;
}

void CGntTestWidget::init(std::map<int, SSCUParam> &mapScuParam)
{
    m_mapScuParam = mapScuParam;

    for (int m = 0; m < ui->tableWidget->rowCount(); ++m)
    {

        QTableWidgetItem *pitem = ui->tableWidget->item(m, EGntTest_BW);
        auto iter = mapScuParam.find(pitem->text().toInt());
        if(iter == mapScuParam.end())
        {
            pitem->setCheckState(Qt::Unchecked);
        }
        else
        {          
            pitem->setCheckState(Qt::Checked);
            ui->tableWidget->item(m, EGntTest_ID)->setText(iter->second.strID);
            ui->tableWidget->item(m, EGntTest_FA)->setText(iter->second.strFA);
            ui->tableWidget->item(m, EGntTest_PC)->setText(iter->second.strPC);
            ui->tableWidget->item(m, EGntTest_Zddz)->setText(iter->second.strLjdz);
            ui->tableWidget->item(m, EGntTest_FAID)->setText(QString::number(iter->second.nFAID));
            ui->tableWidget->item(m, EGntTest_PCID)->setText(QString::number(iter->second.nPCID));
            ui->tableWidget->item(m, EGntTest_ESN)->setText(iter->second.strEsn);
            ui->tableWidget->item(m, EGntTest_CS)->setText(iter->second.strCs);
            ui->tableWidget->item(m, EGntTest_XH)->setText(iter->second.strXh);
            ui->tableWidget->item(m, EGntTest_IP)->setText(iter->second.strIp);
            ui->tableWidget->item(m, EGntTest_YJBB)->setText(iter->second.strYjBb);
            ui->tableWidget->item(m, EGntTest_SCRQ)->setText(iter->second.strScrq);
        }
    }

    ui->tableWidget->resizeColumnsToContents();
}

void CGntTestWidget::pushGc(CCsXlGcMsg *gc)
{
    m_queueGc.push(*gc);
}

void CGntTestWidget::pushJg(CCsXlJgMsg *jg)
{
    m_queueJg.push(*jg);
}

void CGntTestWidget::pushJs(CCsJsMsg *js)
{
    m_queueJs.push(*js);
    m_queueJsMsg.push(*js);
}

void CGntTestWidget::pushYx(CCsYxMsg *yx)
{
    m_queueYx.push(*yx);
}

void CGntTestWidget::setExport()
{
    if(m_ptcpServer != nullptr)
    {
        m_ptcpServer->close();
        delete  m_ptcpServer;
        m_ptcpServer = nullptr;
    }
    QThread::msleep(600);
    m_ptcpServer = new TcpServer();
    connect(m_ptcpServer, SIGNAL(readData(QByteArray)), this, SLOT(on_readData(QByteArray)));
    m_ptcpServer->startServer(49165);

    if(isProcessRunning("msk_usbkey_plugin.exe"))
    {
        killProcess("msk_usbkey_plugin.exe");
    }
    QThread::msleep(1000);
    // 拉起msk_usbkey_plugin 程序
    QString program = "msk_usbkey_plugin.exe"; // 应用程序的路径
    QProcess::startDetached(program);

    QThread::msleep(1000);
    qApp->processEvents();

    g_bCertExport = true;
    m_tcpServertimer.start(3000);
    g_bqueryLj = true;
}

void CGntTestWidget::setNormal(int nLx, int nBw)
{
    m_nBw = nBw;
    switch(nLx)
    {
    case 1:
        m_strCheckItem = "证书导出";
        m_ptcpServer->writeDate("export");
        m_tcpServertimer.stop();
        break;
    case 2:
        ++m_nCertExportFinishNum;
        if(isProcessRunning("msk_usbkey_plugin.exe"))
        {
            killProcess("msk_usbkey_plugin.exe");
        }
        if(m_nCertExportFinishNum == (m_nXcNum - m_nQuitXcNum))
        {
            QThread::msleep(500);
            m_ptcpServer->close();
            delete  m_ptcpServer;
            m_ptcpServer = nullptr;
        }
        break;
    case 3:
        ++m_nKhgNum;
        if(m_nKhgNum == (m_nXcNum - m_nQuitXcNum))
        {
            // 弹框
            if(m_pOpenCloseCap == nullptr)
            {
                m_pOpenCloseCap = new COpenCloseCapDialog(this);
                connect(m_pOpenCloseCap, SIGNAL(on_allFinish()), this, SLOT(on_openclosecap_allFinish()));
            }
            m_pOpenCloseCap->show();
        }
        break;
    case 5:
        ++m_nScuIpModify;
        if(m_nScuIpModify == (m_nXcNum - m_nQuitXcNum))
        {
            g_bScuModifyip = true;
        }
        break;
    case 6:
        m_strCheckItem = "证书导入";
        m_ptcpServer->writeDate("import");
        m_tcpServertimer.stop();
        break;
    case 7:
    {
        ++m_nstartExport;
        int num = 0;
        if(m_mapSshOff.find("证书导出") != m_mapSshOff.end())
        {
            num = m_mapSshOff["证书导出"];
        }
        if(m_nstartExport == (m_nXcNum - m_nQuitXcNum - num))
        {
            g_bstartExport = true;
        }
        break;
    }
    case 8:
    {
        ++m_nstartImport;
        int num = 0;
        if(m_mapSshOff.find("证书导入") != m_mapSshOff.end())
        {
            num = m_mapSshOff["证书导入"];
        }
        if(m_nstartImport == (m_nXcNum - m_nQuitXcNum - num))
        {
            g_bstartImport = true;
        }
        break;
    }
    case 9:
        ++m_nlxdydl;

        if(m_nlxdydl == (m_nXcNum - m_nQuitXcNum))
        {

            comeEquipmentInit();
            m_pEquipmentsjj->SendCommIabc(60,80,100);
            QThread::msleep(20);
            m_pEquipmentsjj->SendCommUabc(90.909, 100, 109.09);
            QThread::msleep(20);
            m_pEquipmentsjj->SendCommPabc(60,60,60);
            QThread::msleep(20);
            m_pEquipmentsjj->SendCommF(50);
            QThread::msleep(20);
            g_blxdldy[0] = true;
        }
        break;
    case 10:
        ++m_nlxdydl_2;

        if(m_nlxdydl_2 == (m_nXcNum - m_nQuitXcNum))
        {
            m_nlxdydl_2 = 0;
            comeEquipmentInit();
            m_pEquipmentsjj->SendCommIabc(60,80,100);
            QThread::msleep(20);
            m_pEquipmentsjj->SendCommUabc(80, 100, 109.09);
            QThread::msleep(20);
            m_pEquipmentsjj->SendCommPabc(60,60,60);
            QThread::msleep(20);
            m_pEquipmentsjj->SendCommF(50);
            QThread::msleep(20);
            g_blxdldy[1] = true;
        }
        break;
    default:
        break;
    }
}

void CGntTestWidget::setSshOff(CSSHOFF *pMsg)
{
    qint16 num = 1;
    QString strItem = pMsg->getCheckItem();
    auto iter = m_mapSshOff.find(strItem);
    if(iter == m_mapSshOff.end())
    {
        m_mapSshOff[strItem] = num;
    }
    else
    {
        num = iter->second;
        ++num;
        m_mapSshOff[strItem] = num;
    }

    if(strItem == "蓝牙通信")
    {
        if(m_nLymz == (m_nXcNum - m_nQuitXcNum - num))
        {
            m_nLymz = 0;
            m_pComPort->queryLy(m_nLytxchkNum);
        }
    }
    else if(strItem == "设备对时")
    {
        if(m_nRtcNum == ((m_nXcNum - m_nQuitXcNum - num)))
        {
            m_nRtcNum = 0;
            comeEquipmentInit();

            m_pEquipmentwc->wSendMPulsef(99, 0, 1280, 2);
            qApp->processEvents();

            QThread::msleep(30);
            g_bRtcYx[m_nsbdschkNum] = true;
        }
    }
    else if(strItem == "后备电源")
    {
        if(m_nHbdyNum == (m_nXcNum - m_nQuitXcNum - num))
        {
            on_ddBtn_clicked();
            g_bHbdy = true;
        }
    }
    else if(strItem == "证书导出")
    {
        if(m_nstartExport == (m_nXcNum - m_nQuitXcNum - num))
        {
            g_bstartExport = true;
        }
    }
    else if(strItem == "证书导入")
    {
        if(m_nstartImport == (m_nXcNum - m_nQuitXcNum - num))
        {
            g_bstartImport = true;
        }
    }
}

void CGntTestWidget::setYlmz(CCsLyMsg &lyMsg)
{
    m_nLytxchkNum = lyMsg.getNum();
    ++m_nLymz;
    int num = 0;
    if(m_mapSshOff.find("蓝牙通信") != m_mapSshOff.end())
    {
        num = m_mapSshOff["蓝牙通信"];
    }
    if(m_nLymz == (m_nXcNum - m_nQuitXcNum - num))
    {
        m_nLymz = 0;
        m_pComPort->queryLy(lyMsg.getNum());
    }
}

void CGntTestWidget::gntJyXlGc(CCsXlGcMsg *pMsg)
{
    SGntJyXlGc gntXlGc = pMsg->getCsXlGc();

    QString strGc = "[" + QDateTime::currentDateTime().toString("yyyy-MM-dd_HH:mm:ss") + "] " + gntXlGc.strCsGc;
    gntXlGc.strCsGc = strGc;

    ui->tableWidget->item(gntXlGc.nBw-1, EGntTest_CHECKING)->setText(gntXlGc.strCsDl);
    if(gntXlGc.oid == 0)
    {
        gntXlGc.oid = ui->tableWidget->item(gntXlGc.nBw-1, EGntTest_OID)->text().toULongLong();
    }
    m_dbThread.gcItemPust(gntXlGc);

    auto iter = m_mapGntJyParam.find(gntXlGc.nBw);
    if(iter == m_mapGntJyParam.end())
    {
        SGntJyParam gntJyParam;
        gntJyParam.strCsDl = gntXlGc.strCsDl;
        gntJyParam.bCsJg = true;
        SGntJyXlParam gntJyXl;
        gntJyXl.strCsXl = gntXlGc.strCsXl;
        gntJyXl.vtCsXlGc.push_back(gntXlGc.strCsGc);
        gntJyParam.gntCsParam.push_back(gntJyXl);
        m_mapGntJyParam[gntXlGc.nBw][gntJyParam.strCsDl] = gntJyParam;
    }
    else
    {
        std::map<QString, SGntJyParam>&mapgntJyParam = iter->second;
        auto it = mapgntJyParam.find(gntXlGc.strCsDl);
        if(it == mapgntJyParam.end())
        {
            SGntJyParam gntJyParam;
            gntJyParam.strCsDl = gntXlGc.strCsDl;
            gntJyParam.bCsJg = true;
            SGntJyXlParam gntJyXl;
            gntJyXl.strCsXl = gntXlGc.strCsXl;
            gntJyXl.vtCsXlGc.push_back(gntXlGc.strCsGc);
            gntJyParam.gntCsParam.push_back(gntJyXl);
            mapgntJyParam[gntXlGc.strCsDl] = gntJyParam;
        }
        else
        {
            bool bFindXl = false;
            std::vector<SGntJyXlParam>& vtGntJxXl = it->second.gntCsParam;
            for (int k = 0; k < vtGntJxXl.size(); ++k )
            {
                SGntJyXlParam &xlParam = vtGntJxXl[k];
                if(xlParam.strCsXl == gntXlGc.strCsXl)
                {
                    bFindXl = true;
                    xlParam.vtCsXlGc.push_back(gntXlGc.strCsGc);
                    break;
                }
            }
            if(!bFindXl)
            {
                SGntJyXlParam xl;
                xl.bCsXlJg = true;
                xl.strCsXl = gntXlGc.strCsXl;
                xl.vtCsXlGc.push_back(gntXlGc.strCsGc);
                vtGntJxXl.push_back(xl);
            }
        }
    }

    if(gntXlGc.nBw != m_nCurBW)
        return;

    iter = m_mapGntJyParam.find(gntXlGc.nBw);
    std::map<QString, SGntJyParam> & mapGntJy = iter->second;

    QString ss;

    for (int m = 0; m < ui->csDLResultTW->rowCount(); ++m)
    {
        QTableWidgetItem *pitem = ui->csDLResultTW->item(m, 0);
        auto iter = mapGntJy.find(pitem->text());
        if(iter == mapGntJy.end())
            continue;
        pitem = new QTableWidgetItem();
        pitem->setText((int)iter->second.bCsJg == 1 ? "合格":"不合格");
        pitem->setFlags(pitem->flags() & ~Qt::ItemIsEnabled);
        ui->csDLResultTW->setItem(m, 1, pitem);

        std::vector<SGntJyXlParam> &vtdl = iter->second.gntCsParam;
        for (int n = 0; n < vtdl.size(); ++n)
        {
            SGntJyXlParam &dlParam = vtdl[n];
            ss += "【" + iter->second.strCsDl + "】-【" + dlParam.strCsXl + "】\n";
            std::vector<QString>&vtxl = dlParam.vtCsXlGc;
            for(int k = 0; k < vtxl.size(); ++k)
            {
                ss += vtxl[k];
                ss += "\n";
            }
        }
    }
    int pos = ui->textEdit->verticalScrollBar()->value();
    ui->textEdit->setText(ss);
    ui->textEdit->verticalScrollBar()->setValue(pos);
}

bool CGntTestWidget::gntJxXlJg(CCsXlJgMsg *pMsg)
{
    SGntJyXlJg jxXlJg = pMsg->getJyXlJg();
    if(jxXlJg.oid == 0)
    {
        jxXlJg.oid = ui->tableWidget->item(jxXlJg.nBw-1, EGntTest_OID)->text().toULongLong();
    }

    if(m_mapGntJyParam.find(jxXlJg.nBw) == m_mapGntJyParam.end())
    {
        return false;
    }

    std::map<QString ,SGntJyParam> & mapGntJy = m_mapGntJyParam[jxXlJg.nBw];
    auto iter = mapGntJy.find(jxXlJg.strCsDl);
    if(iter != mapGntJy.end())
    {
        bool bFind = false;
        std::vector<SGntJyXlParam> &vtJyXl= iter->second.gntCsParam;
        for (int k = 0; k < vtJyXl.size(); ++k)
        {
            SGntJyXlParam &JyXl = vtJyXl[k];
            if(JyXl.strCsXl == jxXlJg.strCsXl)
            {
                JyXl.bCsXlJg = jxXlJg.bCsXlJg;

                SSCUXL xl;
                xl.noid = jxXlJg.oid;
                xl.bresult = JyXl.bCsXlJg;
                xl.strDl = jxXlJg.strCsDl;
                xl.strXl = jxXlJg.strCsXl;
                m_dbThread.xlItemPust(xl);
                bFind = true;
                break;
            }
        }
        if(!bFind)
        {
            return false;
        }

        bool bResult = true;
        std::vector<SGntJyXlParam> &vtGntJyXl= iter->second.gntCsParam;
        for (int k = 0;  k < vtGntJyXl.size(); ++k)
        {
            if((int)vtGntJyXl[k].bCsXlJg != 1)
            {
                bResult = false;
                break;
            }
        }
        iter->second.bCsJg = bResult;
        SSCUUpdateResult rr;
        rr.noid = jxXlJg.oid;
        rr.strDlName = jxXlJg.strCsDl;
        rr.bresult = g_bTestSign ? (bResult? 1:2): 3;
        m_dbThread.dlItemPush(rr);


        if(jxXlJg.nBw != m_nCurBW)
        {
            return true;
        }

        for (int m = 0;  m < ui->csDLResultTW->rowCount(); ++m)
        {
            QString s = ui->csDLResultTW->item(m, 0)->text();
            if(s == jxXlJg.strCsDl)
            {
                QTableWidgetItem *pitem = ui->csDLResultTW->item(m ,1);
                if(pitem == nullptr)
                {
                    pitem = new QTableWidgetItem();
                    pitem->setText(bResult ? "合格" :"不合格");
                    ui->csDLResultTW->setItem(m, 1, pitem);
                }
                else
                {
                    pitem->setText(bResult ? "合格":"不合格");
                }
            }
        }

        int ndlRow = ui->csDLResultTW->currentRow();
        if(ndlRow == -1)
        {
            return true;
        }
        QString strDl = ui->csDLResultTW->item(ndlRow, 0)->text();
        if(strDl != jxXlJg.strCsDl)
        {
            return true;
        }

        bFind = false;
        for (int m = 0; m < ui->csXLResultTW->rowCount(); ++m)
        {
            if(ui->csXLResultTW->item(m, 0)->text() == jxXlJg.strCsXl)
            {
                bFind = true;
                ui->csXLResultTW->item(m, 1)->setText(((int)jxXlJg.bCsXlJg == 1) ? "合格":"不合格");
                break;
            }
        }
        if(!bFind)
        {
            int row = ui->csXLResultTW->rowCount();
            ui->csXLResultTW->setRowCount(row+1);

            QTableWidgetItem *pitem = new QTableWidgetItem();
            pitem->setText(jxXlJg.strCsXl);
            ui->csXLResultTW->setItem(row, 0, pitem);

            pitem = new QTableWidgetItem();
            pitem->setFlags(pitem->flags() & ~Qt::ItemIsEnabled);
            pitem->setText(((int)jxXlJg.bCsXlJg == 1 )? "合格" : "不合格");
            ui->csXLResultTW->setItem(row, 1, pitem);
        }
        return true;
    }
    else
    {
        return false;
    }
}

void CGntTestWidget::setJsXc(CCsJsMsg *pMsg)
{
    quint64 noid = 0;
    int nBw = pMsg->getBw();

    std::map<QString, SGntJyParam> &mapJyParam= m_mapGntJyParam[nBw];
    QString sResult = g_bTestSign ? "合格" :"未测完";
    auto iter = mapJyParam.begin();
    for (; iter != mapJyParam.end() ;++iter )
    {
        if((int)iter->second.bCsJg != 1)
        {
            sResult = "不合格";
            break;
        }

        std::vector<SGntJyXlParam> &vtXl = iter->second.gntCsParam;
        for (int x = 0 ; x < vtXl.size(); ++x)
        {
            if((int)vtXl[x].bCsXlJg != 1)
            {
                sResult = "不合格";
                break;
            }
        }
        if(sResult == "不合格")
        {
            break;
        }

    }
    ui->tableWidget->item(nBw -1, EGntTest_ZT)->setText("完成");
    ui->tableWidget->item(nBw -1, EGntTest_CHECKING)->setText("");
    ui->tableWidget->item(nBw -1, EGntTest_JL)->setText(sResult);
    ui->tableWidget->item(nBw -1, EGntTest_JL)->setTextColor(sResult=="合格"?  QColor(45,102,28) : QColor(235, 51, 36));
    noid = ui->tableWidget->item(nBw -1, EGntTest_OID)->text().toULongLong();

    SSCUUpdateResult rr;
    rr.noid = noid;
    rr.strDlName = "最后结论";
    rr.bresult = g_bTestSign? (sResult == "合格" ? 1: 2) : 3;
    m_dbThread.dlItemPush(rr);

    ui->tableWidget->resizeColumnsToContents();

     createReport(nBw -1);

    ++m_nQuitNum;
    if(m_nQuitNum == m_nXcNum)
    {
        for (int m = 0 ; m < ui->tableWidget->rowCount(); ++m)
        {
            ui->tableWidget->item(m, EGntTest_CHECKING)->setText("");
        }


        comeEquipmentInit();
        m_pEquipmentsjj->SendCommUIDownRapid();



        ui->testBegintBtn->setStyleSheet("QPushButton {background-color:#0089ff ;color: white;border-style: outset;border-width: 2px;border-radius: 10px;border-color: beige;font-size: 26px;padding: 6px;}QPushButton:hover {background-color: #0072C6;}");
        ui->testBegintBtn->setEnabled(true);
        ui->createReportBtn->setStyleSheet("QPushButton {background-color:#0089ff ;color: white;border-style: outset;border-width: 2px;border-radius: 10px;border-color: beige;font-size: 26px;padding: 6px;}QPushButton:hover {background-color: #0072C6;}");
        ui->createReportBtn->setEnabled(true);
        ui->tableWidget->resizeColumnsToContents();

        if(!g_bTestSign)
        {
            CFinishAccordDialog finish;
            finish.exec();
        }
        else
        {
            m_pFinishDialog->init(m_mapGntJyParam);
            m_pFinishDialog->show();
        }
    }
}

void CGntTestWidget::setComPort(CComPortDialog *pComPort)
{
    m_pComPort = pComPort;
}

void CGntTestWidget::setQuery(CVerdictQueryWg *pQueryRs)
{
    m_pQueryRs = pQueryRs;
}

void CGntTestWidget::setHbdy()
{
    ++m_nHbdyNum;
    qint16 num = 0;
    if(m_mapSshOff.find("后备电源") != m_mapSshOff.end())
    {
        num = m_mapSshOff["后备电源"];
    }
    if(m_nHbdyNum == (m_nXcNum - m_nQuitXcNum - num))
    {
        on_ddBtn_clicked();
        g_bHbdy = true;
    }
}

void CGntTestWidget::setSW()
{
    ++m_nSWNum;

    if(m_nSWNum == (m_nXcNum - m_nQuitXcNum ))
    {
        std::vector<int>vtBw;
        for (int m = 0; m < ui->tableWidget->rowCount(); ++m)
        {
            if(ui->tableWidget->item(m, EGntTest_BW)->checkState() == Qt::Checked)
            {
                vtBw.push_back(ui->tableWidget->item(m, EGntTest_BW)->text().toInt());
            }
        }
        m_pSW->setSW(vtBw);
        m_pSW->show();
    }
}

void CGntTestWidget::setJcjl(int nLx, bool badd)
{
    m_nJcLx = nLx;
    if(badd)
    {
         ++m_nJcjlNum;
    }


    int nwaittime = 30000;
    if(m_nJcjlNum == (m_nXcNum - m_nQuitXcNum - m_nJcExit))
    {
        m_jctimer.stop();
        comeEquipmentInit();
        switch (nLx)
        {
        case 0:
            m_bHidden = false;

            m_monitor.stop();
            m_monitor.start(1000);

            m_pEquipmentsjj->SendCommUIDownRapid();
            QThread::sleep(1);
            for (int meterIndex = 0; meterIndex < ui->tableWidget->rowCount(); meterIndex++)
            {
                m_pEquipmentwc->wAutoShutdown(meterIndex+1, 0);
                QThread::msleep(20);

            }

            for (int meterIndex = 0; meterIndex < ui->tableWidget->rowCount(); meterIndex++)
            {
                if(ui->tableWidget->item(meterIndex, EGntTest_BW)->checkState() != Qt::Checked)
                {
                    m_pEquipmentwc->wAutoShutdown(meterIndex+1, 1);
                    QThread::msleep(20);
                }
            }

            m_pEquipmentwc->wAutoShutdown(41, 0);
            QThread::msleep(20);
            m_pEquipmentsjj->Power_ON(true, 220 , 5, "50L", 50, 220, 5, 2, 7, 4);

            // 闭环命令
            for(int m = 0; m < 16; ++m)
            {
                qApp->processEvents();
                QThread::msleep(500);
                qApp->processEvents();
            }

            for (int m = 0; m < 5; ++m)
            {
                if(m_pEquipmentsjj->SendCommOpen(false))
                {
                    break;
                }
                QThread::msleep(200);
            }
            break;
        case 1:
            m_pEquipmentsjj->SendCommU(80, 2, 7, false);
            break;
        case 2:
            m_pEquipmentsjj->SendCommU(60, 2, 7, false);
            break;
        case 3:
            m_pEquipmentsjj->SendCommUIDownRapid();
            QThread::sleep(1);
            for (int meterIndex = 0; meterIndex < ui->tableWidget->rowCount(); meterIndex++)
            {
                m_pEquipmentwc->wAutoShutdown(meterIndex+1, 0);
                QThread::msleep(20);
            }

            for (int meterIndex = 0; meterIndex < ui->tableWidget->rowCount(); meterIndex++)
            {
                if(ui->tableWidget->item(meterIndex, EGntTest_BW)->checkState() != Qt::Checked)
                {
                    m_pEquipmentwc->wAutoShutdown(meterIndex+1, 1);
                    QThread::msleep(20);
                }
            }

            m_pEquipmentwc->wAutoShutdown(41, 0);
            QThread::msleep(20);
            m_pEquipmentsjj->Power_ON(true, 220*1.2 , 5, "50L", 50, 220, 5, 2, 7, 4);
            // 闭环命令
            for(int m = 0; m < 16; ++m)
            {
                qApp->processEvents();
                QThread::msleep(500);
                qApp->processEvents();
            }

            for (int m = 0; m < 5; ++m)
            {
                if(m_pEquipmentsjj->SendCommOpen(false))
                {
                    break;
                }
                QThread::msleep(200);
            }
            break;
        case 4:
            m_pEquipmentsjj->SendCommU(100, 2, 7, false);
            QThread::msleep(20);
            m_pEquipmentsjj->SendCommI(90, 2, 7, false);
            break;
        case 5:
            m_pEquipmentsjj->SendCommI(60, 2, 7, false);
            break;
        case 6:
            m_pEquipmentsjj->SendCommI(20, 2, 7, false);
            break;
        case 7:
            m_pEquipmentsjj->SendCommI(100, 2, 7, false);
            QThread::msleep(20);
            m_pEquipmentsjj->SendCommP(45, 7);
            break;
        case 8:
            m_pEquipmentsjj->SendCommU(100, 2, 7, false);
            QThread::msleep(20);
            m_pEquipmentsjj->SendCommI(60, 2, 7, false);
            QThread::msleep(20);
            m_pEquipmentsjj->SendCommP(45, 7);
            break;
        case 9:
            m_pEquipmentsjj->SendCommU(100, 2, 7, false);
            QThread::msleep(20);
            m_pEquipmentsjj->SendCommI(90, 2, 7, false);
            QThread::msleep(20);
            m_pEquipmentsjj->SendCommP(45, 7);
            break;
        case 10:
            m_pEquipmentsjj->SendCommU(100, 2, 7, false);
            QThread::msleep(20);
            m_pEquipmentsjj->SendCommI(20, 2, 7, false);
            QThread::msleep(20);
            m_pEquipmentsjj->SendCommP(45, 7);
            break;
        case 11:
            m_pEquipmentsjj->SendCommU(120, 2, 7, false);
            QThread::msleep(20);
            m_pEquipmentsjj->SendCommI(20, 2, 7, false);
            QThread::msleep(20);
            m_pEquipmentsjj->SendCommP(45, 7);
            break;
        case 12:
            m_pEquipmentsjj->SendCommU(80, 2, 7, false);
            QThread::msleep(20);
            m_pEquipmentsjj->SendCommI(20, 2, 7, false);
            QThread::msleep(20);
            m_pEquipmentsjj->SendCommP(45, 7);
            break;
        case 13:
            m_pEquipmentsjj->SendCommU(100, 2, 7, false);
            QThread::msleep(20);
            m_pEquipmentsjj->SendCommI(100, 2, 7, false);
            QThread::msleep(20);
            m_pEquipmentsjj->SendCommP(45, 7);
            QThread::msleep(20);
            m_pEquipmentsjj->SendCommF(45);
            break;
        case 14:
            m_pEquipmentsjj->SendCommU(60, 2, 7, false);
            QThread::msleep(20);
            m_pEquipmentsjj->SendCommI(100, 2, 7, false);
            QThread::msleep(20);
            m_pEquipmentsjj->SendCommP(45, 7);
            QThread::msleep(20);
            m_pEquipmentsjj->SendCommF(45);
            break;
        case 15:
            m_pEquipmentsjj->SendCommU(60, 2, 7, false);
            QThread::msleep(20);
            m_pEquipmentsjj->SendCommI(100, 2, 7, false);
            QThread::msleep(20);
            m_pEquipmentsjj->SendCommP(30, 7);
            QThread::msleep(20);
            m_pEquipmentsjj->SendCommF(45);
            break;
        case 16:
            m_pEquipmentsjj->SendCommU(80, 2, 7, false);
            QThread::msleep(20);
            m_pEquipmentsjj->SendCommI(100, 2, 7, false);
            QThread::msleep(20);
            m_pEquipmentsjj->SendCommP(30, 7);
            QThread::msleep(20);
            m_pEquipmentsjj->SendCommF(45);
            break;
        case 17:
            m_pEquipmentsjj->SendCommU(60, 2, 7, false);
            QThread::msleep(20);
            m_pEquipmentsjj->SendCommI(60, 2, 7, false);
            QThread::msleep(20);
            m_pEquipmentsjj->SendCommP(30, 7);
            QThread::msleep(20);
            m_pEquipmentsjj->SendCommF(45);
            break;
        case 18:
            m_pEquipmentsjj->SendCommU(80, 2, 7, false);
            QThread::msleep(20);
            m_pEquipmentsjj->SendCommI(60, 2, 7, false);
            QThread::msleep(20);
            m_pEquipmentsjj->SendCommP(30, 7);
            QThread::msleep(20);
            m_pEquipmentsjj->SendCommF(45);
            break;
        case 19:
            m_pEquipmentsjj->SendCommU(100, 2, 7, false);
            QThread::msleep(20);
            m_pEquipmentsjj->SendCommI(1.5, 2, 7, false);
            QThread::msleep(20);
            m_pEquipmentsjj->SendCommP(60, 7);
            QThread::msleep(20);
            m_pEquipmentsjj->SendCommF(50);
            break;
        case 20:
            m_pEquipmentsjj->SendCommUIDownRapid();
            QThread::sleep(1);
            for (int meterIndex = 0; meterIndex < ui->tableWidget->rowCount(); meterIndex++)
            {
                m_pEquipmentwc->wAutoShutdown(meterIndex+1, 2);
                QThread::msleep(20);

            }

            for (int meterIndex = 0; meterIndex < ui->tableWidget->rowCount(); meterIndex++)
            {
                if(ui->tableWidget->item(meterIndex, EGntTest_BW)->checkState() != Qt::Checked)
                {
                    m_pEquipmentwc->wAutoShutdown(meterIndex+1, 3);
                    QThread::msleep(20);
                }
            }

            m_pEquipmentwc->wAutoShutdown(41, 2);
            QThread::msleep(20);
            m_pEquipmentsjj->Power_ON(true, 220, 5, "50L", 50, 220, 5, 2, 7, 4);

            // 闭环命令
            for(int m = 0; m < 16; ++m)
            {
                qApp->processEvents();
                QThread::msleep(500);
                qApp->processEvents();
            }

            for (int m = 0; m < 5; ++m)
            {
                if(m_pEquipmentsjj->SendCommOpen(false))
                {
                    break;
                }
                QThread::msleep(200);
            }
            break;
        case 21:
            m_pEquipmentsjj->SendCommI(60, 2, 7, false);
            break;
        case 22:
            m_pEquipmentsjj->SendCommU(80, 2, 7, false);
            QThread::msleep(20);
            m_pEquipmentsjj->SendCommI(100, 2, 7, false);
            break;
        case 99:
            m_bHidden = true;
            m_monitor.stop();
            m_monitor.start(5000);

            m_pEquipmentsjj->SendCommUIDownRapid();
            QThread::sleep(1);
            for (int meterIndex = 0; meterIndex < ui->tableWidget->rowCount(); meterIndex++)
            {
                m_pEquipmentwc->wAutoShutdown(meterIndex + 1, 0);
                QThread::msleep(20);
            }
            for (int meterIndex = 0; meterIndex < ui->tableWidget->rowCount(); meterIndex++)
            {
                if(ui->tableWidget->item(meterIndex, EGntTest_BW)->checkState() != Qt::Checked)
                {
                    m_pEquipmentwc->wAutoShutdown(meterIndex + 1, 1);
                    QThread::msleep(20);
                }
            }
            m_pEquipmentwc->wAutoShutdown(41, 0);
            QThread::msleep(20);
            m_pEquipmentsjj->Power_ON(true, 220, 0, "50L", 50, 220, 5, 2, 7, 4);

            // 闭环命令
            for(int m = 0; m < 16; ++m)
            {
                qApp->processEvents();
                QThread::msleep(500);
                qApp->processEvents();
            }

            for (int m = 0; m < 5; ++m)
            {
                if(m_pEquipmentsjj->SendCommOpen(false))
                {
                    break;
                }
                QThread::msleep(200);
            }
            break;
        default:
            break;
        }
        m_nJcjlNum = 0;
        m_jctimer.start(nwaittime);
    }
}

void CGntTestWidget::setTtKz(int nTtkz)
{
    switch (nTtkz)
    {
    case 0:
        ++m_nJcExit;
         if(m_nJcjlNum == (m_nXcNum - m_nQuitXcNum - m_nJcExit))
         {
            setJcjl(m_nJcLx, false);
         }
        break;
    }
}

void CGntTestWidget::setYgmc(CCsYgmcMsg *pMsg)
{
    ++m_nYgmcNum;
    if( m_nYgmcNum == (m_nXcNum - m_nQuitXcNum))
    {
        m_nYgmcNum = 0;
        g_mapMcNum.clear();
        m_pEquipmentwc->wSendCommChkType(10);
        QThread::msleep(200);
        bool b = m_pEquipmentwc->wSendOpenPulseChanel(99, 0);  // 0 是有功+  3是无功+  4 是秒脉冲
        QThread::msleep(200);
        m_pEquipmentwc->wSendCommChkPBegin();

        int num = 0;
        while(num < 4000)
        {
            QThread::msleep(500);
            qApp->processEvents();
            qApp->processEvents();
            num += 500;
        }
        for (int m = 0; m < ui->tableWidget->rowCount(); ++m)
        {
            QTableWidgetItem *pitem = ui->tableWidget->item(m, EGntTest_BW);
            if(pitem->checkState() != Qt::Checked)
                continue;
            QString s = m_pEquipmentwc->wSendCommarr_ZZ(pitem->text().toInt());
            QThread::msleep(50);
            QStringList sList = s.split(",");
            if(sList.size() < 2)
            {
                s = m_pEquipmentwc->wSendCommarr_ZZ(pitem->text().toInt());
                sList = s.split(",");
                if(sList.size() < 2)
                {
                    continue;
                }

            }
            if(sList[1].toInt() == 0)
            {
                s = m_pEquipmentwc->wSendCommarr_ZZ(pitem->text().toInt());
                sList = s.split(",");
                if(sList.size() < 2 )
                {
                    continue;
                }
                if(sList[1].toInt() == 0)
                {
                     continue;
                }
            }
            g_mapMcNum [pitem->text().toInt()] = sList[1].toInt();
        }
        m_pEquipmentwc->wSendCommChkPEnd();

        g_bygMc[pMsg->getNum()] = true;
    }
}

void CGntTestWidget::setWgmc(CCsWgmcMsg *pMsg)
{
    ++m_nWgmcNum;
    if( m_nWgmcNum == (m_nXcNum - m_nQuitXcNum))
    {
        m_nWgmcNum = 0;
        m_pEquipmentsjj->SendCommChkSet(true, 5);
        QThread::msleep(200);
        g_mapMcNum.clear();
        m_pEquipmentwc->wSendCommChkType(10);
        QThread::msleep(200);
        m_pEquipmentwc->wSendOpenPulseChanel(99, 3);  // 0 是有功+  3是无功+  4 是秒脉冲
        QThread::msleep(200);
        m_pEquipmentwc->wSendCommChkPBegin();

        int num = 0;
        while(num < 4000)
        {
            QThread::msleep(500);
            qApp->processEvents();
            qApp->processEvents();
            num += 500;
        }

        for (int m = 0; m < ui->tableWidget->rowCount(); ++m)
        {
            QTableWidgetItem *pitem = ui->tableWidget->item(m, EGntTest_BW);
            if(pitem->checkState() != Qt::Checked)
                continue;
            QString s = m_pEquipmentwc->wSendCommarr_ZZ(pitem->text().toInt());
            QStringList sList = s.split(",");
            if(sList.size() < 2)
            {
                s = m_pEquipmentwc->wSendCommarr_ZZ(pitem->text().toInt());
                sList = s.split(",");
                if(sList.size() < 2)
                {
                    continue;
                }
            }

            if(sList[1].toInt() == 0)
            {
                s = m_pEquipmentwc->wSendCommarr_ZZ(pitem->text().toInt());
                sList = s.split(",");
                if(sList.size() < 2)
                {
                    continue;
                }
                if(sList[1].toInt() == 0)
                {
                    continue;
                }
            }
            g_mapMcNum [pitem->text().toInt()] = sList[1].toInt();
        }
        m_pEquipmentwc->wSendCommChkPEnd();
        g_bwgMc[pMsg->getNum()] = true;
    }
}

void CGntTestWidget::setMmc(CCsmmcMsg *pMsg)
{
    ++m_nmmcNum;
    if( m_nmmcNum == (m_nXcNum - m_nQuitXcNum))
    {
        m_nmmcNum = 0;
        comeEquipmentInit();
        m_pEquipmentsjj->SendCommChkSet(true, 4);
        QThread::msleep(200);
        g_mapMcNum.clear();
        m_pEquipmentwc->wSendCommChkType(10);
        QThread::msleep(200);
        m_pEquipmentwc->wSendOpenPulseChanel(99, 4);  // 0 是有功+  3是无功+  4 是秒脉冲
        QThread::msleep(200);
        m_pEquipmentwc->wSendCommChkPBegin();

        int num = 0;
        while(num < 4000)
        {
            QThread::msleep(500);
            qApp->processEvents();
            qApp->processEvents();
            num += 500;
        }
        for (int m = 0; m < ui->tableWidget->rowCount(); ++m)
        {
            QTableWidgetItem *pitem = ui->tableWidget->item(m, EGntTest_BW);
            if(pitem->checkState() != Qt::Checked)
                continue;
            QString s = m_pEquipmentwc->wSendCommarr_ZZ(pitem->text().toInt());
            QStringList sList = s.split(",");
            if(sList.size() < 2)
            {
                s = m_pEquipmentwc->wSendCommarr_ZZ(pitem->text().toInt());
                if(sList.size() < 2)
                {
                    continue;
                }
            }
            if(sList[1].toInt() == 0)
            {
                s = m_pEquipmentwc->wSendCommarr_ZZ(pitem->text().toInt());
                if(sList.size() < 2)
                {
                    continue;
                }
                if(sList[1].toInt() == 0)
                {
                    continue;
                }
            }
            g_mapMcNum [pitem->text().toInt()] = sList[1].toInt();
        }
        m_pEquipmentwc->wSendCommChkPEnd();

        g_bmMc[pMsg->getNum()] = true;
    }
}

void CGntTestWidget::setYx( int num)
{
    ++m_nyxNum;

    if(m_nyxNum ==  (m_nXcNum - m_nQuitXcNum))
    {
        m_nyxNum = 0;
        comeEquipmentInit();

        m_pEquipmentwc->wSendMPulsef(99, 0, 1280, 2);
        qApp->processEvents();

        QThread::msleep(200);

        m_pEquipmentwc->wSendMPulsef(99, 1, 1280, 2);
        qApp->processEvents();
        QThread::msleep(200);

        m_pEquipmentwc->wSendMPulsef(99, 2, 1280, 2);
        qApp->processEvents();
        QThread::msleep(200);

        m_pEquipmentwc->wSendMPulsef(99, 3, 1280, 2);
        qApp->processEvents();
        QThread::msleep(200);

        g_bYxFinish[num] = true;
    }
}

void CGntTestWidget::setRtc(int nchecknum)
{
    m_nsbdschkNum = nchecknum;
    ++m_nRtcNum;
    int num = 0;
    if(m_mapSshOff.find("设备对时") != m_mapSshOff.end())
    {
        num = m_mapSshOff["设备对时"];
    }
    if(m_nRtcNum == ((m_nXcNum - m_nQuitXcNum - num)))
    {
        m_nRtcNum = 0;
        comeEquipmentInit();

        m_pEquipmentwc->wSendMPulsef(99, 0, 1280, 2);
        qApp->processEvents();

        QThread::msleep(30);
        g_bRtcYx[nchecknum] = true;
    }
}

bool CGntTestWidget::threadCloseZt()
{
    for(int m = 0; m < m_vtTestProcess.size(); ++m)
    {
        if(m_vtTestProcess[m]->isRunning())
            return false;
    }
    return true;
}

void CGntTestWidget::on_table_customContextMenuRequested(const QPoint &pt)
{
    QMenu menu ;
    QAction *actXq = new QAction("SCU详情");

    connect(actXq, SIGNAL(triggered()), this, SLOT(on_scuXq_trigger()));
    menu.addAction(actXq);
    menu.exec(QCursor::pos());
    delete  actXq;
    actXq = nullptr;
}

void CGntTestWidget::on_table_itemClicked(QTableWidgetItem *pitem)
{
    m_nCurDl = -1;
    ui->csXLResultTW->clearContents();
    ui->csXLResultTW->setRowCount(0);
    ui->textEdit->clear();
    ui->csDLResultTW->clearContents();
    ui->csDLResultTW->setRowCount(0);
    int nRow = pitem->row();

    int nBw = ui->tableWidget->item(nRow, EGntTest_BW)->text().toInt();

    ui->label_2->setText("测试过程信息（表位：" + QString::number(nBw) + "）");

    m_nCurBW = nBw;

    auto iter = m_mapGntJyParam.find(nBw);
    if(iter == m_mapGntJyParam.end())
        return;

    std::vector<QString> &vtCsItem = m_gntParam.vtItem;
    ui->csDLResultTW->setRowCount(vtCsItem.size());
    for (int m = 0; m < vtCsItem.size(); ++m)
    {
        QTableWidgetItem *pitem = new QTableWidgetItem();
        pitem->setText(vtCsItem[m]);
        ui->csDLResultTW->setItem(m, 0, pitem);

        pitem = new QTableWidgetItem();
        pitem->setFlags(pitem->flags() & ~Qt::ItemIsEnabled);
        pitem->setText("");
        ui->csDLResultTW->setItem(m, 1, pitem);

    }

    std::map<QString, SGntJyParam> & mapGntJy = iter->second;

    QString ss;

    for (int m = 0; m < ui->csDLResultTW->rowCount(); ++m)
    {
        QTableWidgetItem *pitem = ui->csDLResultTW->item(m, 0);
        auto iter = mapGntJy.find(pitem->text());
        if(iter == mapGntJy.end())
            continue;

        pitem = ui->csDLResultTW->item(m, 1);
        if(pitem == nullptr)
        {
            pitem = new QTableWidgetItem();
            pitem->setFlags(pitem->flags() & ~Qt::ItemIsEnabled);
            pitem->setText((int)iter->second.bCsJg == 1? "合格":"不合格");
            ui->csDLResultTW->setItem(m, 1, pitem);
        }
        else
        {
            pitem->setFlags(pitem->flags() & ~Qt::ItemIsEnabled);
            pitem->setText((int)iter->second.bCsJg == 1 ? "合格":"不合格");
        }


        std::vector<SGntJyXlParam> &vtdl = iter->second.gntCsParam;
        for (int n = 0; n < vtdl.size(); ++n)
        {
            SGntJyXlParam &dlParam = vtdl[n];
            ss += "【" + iter->second.strCsDl + "】-【" + dlParam.strCsXl + "】\n";
            std::vector<QString>&vtxl = dlParam.vtCsXlGc;
            for(int k = 0; k < vtxl.size(); ++k)
            {
                ss += vtxl[k];
                ss += "\n";
            }
        }
    }
    ui->textEdit->setText(ss);
}

void CGntTestWidget::on_cdDLTW_itemClicked(QTableWidgetItem *pitem)
{
    int nRow = ui->tableWidget->currentRow();
    int nBw = ui->tableWidget->item(nRow, EGntTest_BW)->text().toInt();

    auto iter = m_mapGntJyParam.find(nBw);
    if(iter == m_mapGntJyParam.end())
    {
        ui->csXLResultTW->setRowCount(0);
        return;
    }

    nRow = pitem->row();
    ui->csXLResultTW->clearContents();
    ui->csXLResultTW->setRowCount(0);

    m_nCurDl = nRow;
    QString strCsDl = ui->csDLResultTW->item(nRow, 0)->text();

    std::map<QString, SGntJyParam> &mapGntJy = iter->second;
    auto it = mapGntJy.find(strCsDl);
    if(it == mapGntJy.end())
    {
        return;
    }
    std::vector<SGntJyXlParam> &vtGntJyXl= it->second.gntCsParam;
    ui->csXLResultTW->setRowCount(vtGntJyXl.size());

    for (int k = 0; k < vtGntJyXl.size(); ++k)
    {
        QTableWidgetItem *item = new QTableWidgetItem();
        item->setText(vtGntJyXl[k].strCsXl);
        ui->csXLResultTW->setItem(k, 0, item);

        item = new QTableWidgetItem();
        item->setFlags(pitem->flags() & ~Qt::ItemIsEnabled);
        bool b = ((int)vtGntJyXl[k].bCsXlJg == 1) ;
        item->setText( b? "合格": "不合格");
        ui->csXLResultTW->setItem(k, 1, item);
    }

}

void CGntTestWidget::on_readData(QByteArray data)
{
    m_tcpServertimer.stop();
    SGntJyXlJg gntCsXlJg;
    SGntJyXlGc gntCsXlGc;
    gntCsXlGc.oid = 0;
    gntCsXlJg.oid = 0;
    gntCsXlGc.nBw = gntCsXlJg.nBw = m_nBw;
    gntCsXlJg.strCsDl = gntCsXlGc.strCsDl = m_strCheckItem;
    gntCsXlJg.strCsXl = gntCsXlGc.strCsXl = m_strCheckItem;
    gntCsXlGc.strCsGc += m_strCheckItem;

    QString s = QString::fromUtf8(data);
    QStringList sList = s.split("-");
    if(sList.size() != 2)
    {
        gntCsXlGc.strCsGc = "失败";
        QApplication::sendEvent((QObject*)g_pFuncWindow, new CCsXlGcMsg(gntCsXlGc));
        gntCsXlJg.bCsXlJg = false;
        QApplication::sendEvent((QObject*)g_pFuncWindow, new CCsXlJgMsg(gntCsXlJg));
    }
    else
    {
        if(sList[1].toInt() == 0)
        {
            gntCsXlGc.strCsGc += "成功";
            QApplication::sendEvent((QObject*)g_pFuncWindow, new CCsXlGcMsg(gntCsXlGc));
            gntCsXlJg.bCsXlJg = true;
            QApplication::sendEvent((QObject*)g_pFuncWindow, new CCsXlJgMsg(gntCsXlJg));
        }
        else
        {
            gntCsXlGc.strCsGc += "失败";
            QApplication::sendEvent((QObject*)g_pFuncWindow, new CCsXlGcMsg(gntCsXlGc));
            gntCsXlJg.bCsXlJg = false;
            QApplication::sendEvent((QObject*)g_pFuncWindow, new CCsXlJgMsg(gntCsXlJg));
        }
    }
    g_bSingleExportFinish = true;

}

// scu 详情展示
void CGntTestWidget::on_scuXq_trigger()
{
    int nRow = ui->tableWidget->currentRow();
    int nBw = ui->tableWidget->item(nRow, EGntTest_BW)->text().toInt();
    auto iter = m_mapScuParam.find(nBw);
    if(iter == m_mapScuParam.end())
    {
        return;
    }
    SSCUParam scuParam = iter->second;
    if(m_pGntScuInfo == nullptr)
    {
        m_pGntScuInfo = new CGntScuInfoDialog(this);
    }
    m_pGntScuInfo->init(scuParam);

    m_pGntScuInfo->show();

}

void CGntTestWidget::tableInit()
{
    ui->tableWidget->verticalHeader()->setVisible(false);
    ui->tableWidget->setSelectionBehavior(QAbstractItemView::SelectRows);
    ui->tableWidget->setSelectionMode(QAbstractItemView::SingleSelection);
    ui->tableWidget->setAlternatingRowColors(true);

    ui->tableWidget->setContextMenuPolicy(Qt::CustomContextMenu);
    connect(ui->tableWidget, SIGNAL(itemClicked(QTableWidgetItem *)), this, SLOT(on_table_itemClicked(QTableWidgetItem *)));
    ui->tableWidget->setColumnCount(EGntTest_MAX);

    QTableWidgetItem *pitem = new QTableWidgetItem();
    pitem->setText("表位");
    ui->tableWidget->setHorizontalHeaderItem(EGntTest_BW, pitem);

    pitem = new QTableWidgetItem();
    pitem->setText("逻辑地址");
    ui->tableWidget->setHorizontalHeaderItem(EGntTest_Zddz, pitem);

    pitem = new QTableWidgetItem();
    pitem->setText("ID");
    ui->tableWidget->setHorizontalHeaderItem(EGntTest_ID, pitem);

    pitem = new QTableWidgetItem();
    pitem->setText("ESN");
    ui->tableWidget->setHorizontalHeaderItem(EGntTest_ESN, pitem);

    pitem = new QTableWidgetItem();
    pitem->setText("厂商");
    ui->tableWidget->setHorizontalHeaderItem(EGntTest_CS, pitem);

    pitem = new QTableWidgetItem();
    pitem->setText("型号");
    ui->tableWidget->setHorizontalHeaderItem(EGntTest_XH, pitem);

    pitem = new QTableWidgetItem();
    pitem->setText("IP");
    ui->tableWidget->setHorizontalHeaderItem(EGntTest_IP, pitem);

    pitem = new QTableWidgetItem();
    pitem->setText("硬件版本");
    ui->tableWidget->setHorizontalHeaderItem(EGntTest_YJBB, pitem);

    pitem = new QTableWidgetItem();
    pitem->setText("生产日期");
    ui->tableWidget->setHorizontalHeaderItem(EGntTest_SCRQ, pitem);

    pitem = new QTableWidgetItem();
    pitem->setText("状态");
    ui->tableWidget->setHorizontalHeaderItem(EGntTest_ZT, pitem);

    pitem = new QTableWidgetItem();
    pitem->setText("当前测试项");
    ui->tableWidget->setHorizontalHeaderItem(EGntTest_CHECKING, pitem);

    pitem = new QTableWidgetItem();
    pitem->setText("测试结论");
    ui->tableWidget->setHorizontalHeaderItem(EGntTest_JL, pitem);

    pitem = new QTableWidgetItem();
    pitem->setText("方案");
    ui->tableWidget->setHorizontalHeaderItem(EGntTest_FA, pitem);

    pitem = new QTableWidgetItem();
    pitem->setText("批次");
    ui->tableWidget->setHorizontalHeaderItem(EGntTest_PC, pitem);

    pitem = new QTableWidgetItem();
    pitem->setText("方案ID");
    ui->tableWidget->setHorizontalHeaderItem(EGntTest_FAID, pitem);

    pitem = new QTableWidgetItem();
    pitem->setText("批次ID");
    ui->tableWidget->setHorizontalHeaderItem(EGntTest_PCID, pitem);

    pitem = new QTableWidgetItem();
    pitem->setText("OID");
    ui->tableWidget->setHorizontalHeaderItem(EGntTest_OID, pitem);

    ui->tableWidget->hideColumn(EGntTest_FAID);
    ui->tableWidget->hideColumn(EGntTest_PCID);
    ui->tableWidget->hideColumn(EGntTest_OID);

    ui->tableWidget->setRowCount(40);
    for (int m = 0; m < 40; ++m)
    {
        pitem = new QTableWidgetItem();
        pitem->setText(QString::number(m+1));
        pitem->setCheckState(Qt::Unchecked);
        ui->tableWidget->setItem(m, EGntTest_BW, pitem);

        pitem = new QTableWidgetItem();
        ui->tableWidget->setItem(m, EGntTest_Zddz, pitem);

        pitem = new QTableWidgetItem();
        ui->tableWidget->setItem(m, EGntTest_ID, pitem);

        pitem = new QTableWidgetItem();
        ui->tableWidget->setItem(m, EGntTest_ZT, pitem);

        pitem = new QTableWidgetItem();
        ui->tableWidget->setItem(m, EGntTest_CHECKING, pitem);

        pitem = new QTableWidgetItem();
        ui->tableWidget->setItem(m, EGntTest_FA, pitem);

        pitem = new QTableWidgetItem();
        ui->tableWidget->setItem(m, EGntTest_PC, pitem);

        pitem = new QTableWidgetItem();
        ui->tableWidget->setItem(m, EGntTest_FAID, pitem);

        pitem = new QTableWidgetItem();
        ui->tableWidget->setItem(m, EGntTest_PCID, pitem);

        pitem = new QTableWidgetItem();
        ui->tableWidget->setItem(m, EGntTest_OID, pitem);

        pitem = new QTableWidgetItem();
        pitem->setFlags(pitem->flags() & ~Qt::ItemIsEnabled);
        ui->tableWidget->setItem(m, EGntTest_JL, pitem);

        pitem = new QTableWidgetItem();
        ui->tableWidget->setItem(m, EGntTest_ESN, pitem);

        pitem = new QTableWidgetItem();
        ui->tableWidget->setItem(m, EGntTest_CS, pitem);

        pitem = new QTableWidgetItem();
        ui->tableWidget->setItem(m, EGntTest_XH, pitem);

        pitem = new QTableWidgetItem();
        ui->tableWidget->setItem(m, EGntTest_IP, pitem);

        pitem = new QTableWidgetItem();
        ui->tableWidget->setItem(m, EGntTest_YJBB, pitem);

        pitem = new QTableWidgetItem();
        ui->tableWidget->setItem(m, EGntTest_SCRQ, pitem);
    }
}

void CGntTestWidget::csDLResultTWinit()
{
    ui->csDLResultTW->setSelectionBehavior(QAbstractItemView::SelectRows);
    ui->csDLResultTW->setSelectionMode(QAbstractItemView::SingleSelection);
    ui->csDLResultTW->setAlternatingRowColors(true);
    ui->csDLResultTW->setColumnCount(2);

    QTableWidgetItem *pitem = new QTableWidgetItem();
    pitem->setText("测试大项");
    ui->csDLResultTW->setHorizontalHeaderItem(0, pitem);

    pitem = new QTableWidgetItem();
    pitem->setText("结论");
    ui->csDLResultTW->setHorizontalHeaderItem(1, pitem);

    connect(ui->csDLResultTW, SIGNAL(itemClicked(QTableWidgetItem *)), this, SLOT(on_cdDLTW_itemClicked(QTableWidgetItem *)));
}

void CGntTestWidget::csXlResultTWinit()
{
    ui->csXLResultTW->setSelectionBehavior(QAbstractItemView::SelectRows);
    ui->csXLResultTW->setSelectionMode(QAbstractItemView::SingleSelection);
    ui->csXLResultTW->setAlternatingRowColors(true);
    ui->csXLResultTW->setColumnCount(2);

    QTableWidgetItem *pitem = new QTableWidgetItem();
    pitem->setText("测试小项");
    ui->csXLResultTW->setHorizontalHeaderItem(0, pitem);

    pitem = new QTableWidgetItem();
    pitem->setText("结论");
    ui->csXLResultTW->setHorizontalHeaderItem(1, pitem);
}

void CGntTestWidget::readCfgBeforeCheck()
{
    QSettings  settings(strScuOutAutoCheckIni, QSettings::IniFormat);
    settings.setIniCodec(QTextCodec::codecForName("UTF-8"));

    settings.beginGroup("FA");
    int nFAID = settings.value("FAID").toInt();
    settings.endGroup();

    m_mapRs485.clear();
    settings.beginGroup("gorge_rs485");
    QStringList strList = settings.allKeys();
    for (int m = 0; m < strList.size() ; ++m)
    {
        m_mapRs485[strList[m].toInt()] = settings.value(strList[m]).toString();
    }
    settings.endGroup();

    settings.beginGroup("jcx_" + QString::number(nFAID));
    QString sJcx = settings.value("jcx").toString();
    settings.endGroup();

    m_gntParam.vtItem.clear();
    QStringList sList = sJcx.split("@");
    for (int m = 0; m < sList.size(); ++m)
    {
        if(sList[m].isEmpty())
            continue;
        m_gntParam.vtItem.push_back(sList[m]);
    }

    settings.beginGroup("scusys_" + QString::number(nFAID));
    m_gntParam.scuXTParam.strDB1 = settings.value("db1").toString();
    m_gntParam.scuXTParam.strDB2 = settings.value("db2").toString();
    m_gntParam.scuXTParam.strZKB = settings.value("zkb").toString();
    m_gntParam.scuXTParam.strYJB = settings.value("yjb").toString();
    m_gntParam.scuXTParam.strNHB = settings.value("nhb").toString();
    m_gntParam.scuXTParam.strBHB = settings.value("bhb").toString();
    m_gntParam.scuXTParam.strRQB = settings.value("rqb").toString();
    m_gntParam.scuXTParam.strJCB = settings.value("jcb").toString();
    m_gntParam.scuXTParam.strGJXT = settings.value("gjxtb").toString();
    m_gntParam.scuXTParam.strCZXT = settings.value("czxtb").toString();
    m_gntParam.scuXTParam.strCPU = settings.value("cpumax").toString();
    m_gntParam.scuXTParam.strMem = settings.value("memmax").toString();
    settings.endGroup();

    m_gntParam.rqApp.mapScuRq.clear();
    settings.beginGroup("gntparam_rq_" + QString::number(nFAID));
    sList = settings.allKeys();
    for (int m = 0; m < sList.size(); ++m)
    {
        if(settings.value(sList[m]).toInt() != 1)
        {
            continue;
        }
        m_gntParam.rqApp.mapScuRq[sList[m]] = false;
    }
    settings.endGroup();

    m_gntParam.rqApp.mapScuApp.clear();
    settings.beginGroup("gntparam_rqApp_" + QString::number(nFAID));
    sList = settings.allKeys();
    for (int m = 0; m < sList.size(); ++m)
    {
        if(settings.value(sList[m]).toInt() != 1)
        {
            continue;
        }
        m_gntParam.rqApp.mapScuApp[sList[m]] = false;
    }
    settings.endGroup();

    m_gntParam.rqApp.mapRqwApp.clear();
    settings.beginGroup("gntparam_rqwApp_" + QString::number(nFAID));
    sList = settings.allKeys();
    for (int m = 0; m < sList.size(); ++m)
    {
        if(settings.value(sList[m]).toInt() != 1)
        {
            continue;
        }
        m_gntParam.rqApp.mapRqwApp[sList[m]] = false;
    }
    settings.endGroup();

    m_gntParam.rqApp.mapScuRqHash.clear();
    settings.beginGroup("gntparam_rqhash_" + QString::number(nFAID));
    sList = settings.allKeys();
    for (int m = 0; m < sList.size(); ++m)
    {
        m_gntParam.rqApp.mapScuRqHash[sList[m]] = settings.value(sList[m]).toString();
    }
    settings.endGroup();

    m_gntParam.rqApp.mapScuAppHash.clear();
    settings.beginGroup("gntparam_rqApphash_" + QString::number(nFAID));
    sList = settings.allKeys();
    for (int m = 0; m < sList.size(); ++m)
    {
        m_gntParam.rqApp.mapScuAppHash[sList[m]] = settings.value(sList[m]).toString();
    }
    settings.endGroup();

    settings.beginGroup("gwtxpz_" + QString::number(nFAID));
    m_gntParam.gwtxpz.bvalid = settings.value("valid").toBool();
    QString s = settings.value("gzms").toString();
    if(s == "混合模式")
        m_gntParam.gwtxpz.ngzms = 0;
    else if(s == "客户机模式")
        m_gntParam.gwtxpz.ngzms = 1;
    else if (s == "服务器模式")
        m_gntParam.gwtxpz.ngzms = 2;

    s = settings.value("zxfs").toString();
    if(s == "永久在线")
        m_gntParam.gwtxpz.nzxfs = 0;
    else if(s == "被动激活")
        m_gntParam.gwtxpz.nzxfs = 1;

    s = settings.value("ljfs").toString();
    if(s == "TCP")
        m_gntParam.gwtxpz.nljfs = 0;
    else if(s == "UDP")
        m_gntParam.gwtxpz.nljfs = 1;

    s = settings.value("ljyyfs").toString();
    if(s == "主备模式")
        m_gntParam.gwtxpz.nljyyfs = 0;
    else if(s == "多连接模式")
        m_gntParam.gwtxpz.nljyyfs =1;

    m_gntParam.gwtxpz.vtztdklb.clear();
    s = settings.value("ztdklb").toString();
    sList = s.split("@");
    for (int m = 0; m < sList.size(); ++m)
    {
        if(sList[m].isEmpty())
            continue;
        m_gntParam.gwtxpz.vtztdklb.push_back(sList[m].toInt());
    }

    m_gntParam.gwtxpz.strapn = settings.value("apn").toString();
    m_gntParam.gwtxpz.stryhm = settings.value("yhm").toString();
    m_gntParam.gwtxpz.strmm = settings.value("mm").toString();
    s = settings.value("dlfwq").toString();
    if(s == "0")
        m_gntParam.gwtxpz.strdlfwq = "0.0.0.0";
    else
        m_gntParam.gwtxpz.strdlfwq = s;
    m_gntParam.gwtxpz.ndldk = settings.value("dldk").toInt();
    m_gntParam.gwtxpz.nxtzq = settings.value("xtzq").toInt();
    int ncssj = settings.value("cssj").toInt();
    int ncfcs = settings.value("cfcs").toInt();
    m_gntParam.gwtxpz.ncssjcs = ((ncssj << 2)  | ncfcs);
    settings.endGroup();

    settings.beginGroup("aqmscspz_" + QString::number(nFAID));
    m_gntParam.aqjmpz.bvalid = settings.value("valid").toBool();
    m_gntParam.aqjmpz.njmms = settings.value("jmms").toInt();
    settings.endGroup();

    settings.beginGroup("ytwtxpz_" + QString::number(nFAID));
    m_gntParam.ytwtxpz.bvalid = settings.value("valid").toBool();
    s = settings.value("gzms").toString();
    if(s == "混合模式")
        m_gntParam.ytwtxpz.ngzms = 0;
    else if(s == "客户机模式")
        m_gntParam.ytwtxpz.ngzms = 1;
    else if(s == "服务器模式")
        m_gntParam.ytwtxpz.ngzms = 2;

    s = settings.value("ljfs").toString();
    if(s == "TCP")
        m_gntParam.ytwtxpz.nljfs = 0;
    else if(s == "UDP")
        m_gntParam.ytwtxpz.nljfs = 1;

    s = settings.value("ljyyfs").toString();
    if(s == "主备模式")
        m_gntParam.ytwtxpz.nljyyfs = 0;
    else if(s == "多连接模式")
        m_gntParam.ytwtxpz.nljyyfs = 1;

    m_gntParam.ytwtxpz.ndldk = settings.value("dldk").toInt();
    m_gntParam.ytwtxpz.nxtzq = settings.value("xtzq").toInt();
    ncssj = settings.value("cssj").toInt();
    ncfcs = settings.value("cfcs").toInt();
    m_gntParam.ytwtxpz.ncssjcs = ((ncssj << 2)  | ncfcs);
    s = settings.value("ztdklb").toString();
    m_gntParam.ytwtxpz.vtztdklb.clear();
    sList = s.split("@");
    for (int m = 0; m < sList.size(); ++m)
    {
        if(sList[m].isEmpty())
            continue;
        m_gntParam.ytwtxpz.vtztdklb.push_back(sList[m].toInt());
    }
    s = settings.value("dlfwq").toString();
    if(s == "0")
        m_gntParam.ytwtxpz.strdlfwq = "0.0.0.0";
    else
        m_gntParam.ytwtxpz.strdlfwq = s;
    settings.endGroup();

    settings.beginGroup("gwzztxdz_" + QString::number(nFAID));
    m_gntParam.gwtxdz.bvalid = settings.value("valid").toBool();
    m_gntParam.gwtxdz.bzy = settings.value("zy").toBool();
    m_gntParam.gwtxdz.by = settings.value("by").toBool();
    m_gntParam.gwtxdz.strzyIp = settings.value("zyip").toString();
    m_gntParam.gwtxdz.nzydk = settings.value("zydk").toInt();
    m_gntParam.gwtxdz.strbyIp = settings.value("byip").toString();
    m_gntParam.gwtxdz.nbydk = settings.value("bydk").toInt();
    settings.endGroup();

    settings.beginGroup("ytwzztxdz_" + QString::number(nFAID));
    m_gntParam.ytwtxdz.bvalid = settings.value("valid").toBool();
    m_gntParam.ytwtxdz.bzy = settings.value("zy").toBool();
    m_gntParam.ytwtxdz.by = settings.value("by").toBool();
    m_gntParam.ytwtxdz.strzyIp = settings.value("zyip").toString();
    m_gntParam.ytwtxdz.nzydk = settings.value("zydk").toInt();
    m_gntParam.ytwtxdz.strbyIp = settings.value("byip").toString();
    m_gntParam.ytwtxdz.nbydk = settings.value("bydk").toInt();
    settings.endGroup();

    settings.beginGroup("tsdcspz_" + QString::number(nFAID));
    m_gntParam.tsdpzcs.bvalid = settings.value("valid").toBool();
    int ncjbz = settings.value("cjbz").toInt();
    int ncjdd = 0;
    s = settings.value("cjcld").toString();
    if(s == "采集设置测量点")
        ncjdd = 0;
    else if(s == "随机选择测量点")
        ncjdd = 1;
    m_gntParam.tsdpzcs.ncjbz = ((ncjbz << 7) | ncjdd << 6);
    m_gntParam.tsdpzcs.ntdsjcdjg = settings.value("cdsjjg").toInt();
    m_gntParam.tsdpzcs.ntdsjcdxz = settings.value("cdsjxz").toInt();
    s = settings.value("dqtdsjdnb").toString();
    sList = s.split("@");
    for (int m = 0; m < sList.size(); ++m)
    {
        if(sList[m].isEmpty())
            continue;
        m_gntParam.tsdpzcs.vtdnb.push_back(sList[m]);
    }
    m_gntParam.tsdpzcs.ntdsjzxyxjg = settings.value("tdsjzxyxjg").toInt();
    m_gntParam.tsdpzcs.ntdsjzdyxjg = settings.value("tdsjzdyxjg").toInt();
    m_gntParam.tsdpzcs.ntdsjqzsjpzxz = settings.value("tdqzsjpcxz").toInt();
    m_gntParam.tsdpzcs.ntdsjsjqdpcxz = settings.value("tdsjqdpcxz").toInt();
    m_gntParam.tsdpzcs.ntdfsdzxz = settings.value("tdfsdyxz").toInt() * 10;
    m_gntParam.tsdpzcs.ntdhfdyxz = settings.value("tdhfdyxz").toInt() * 10;
    settings.endGroup();

    settings.beginGroup("other_" + QString::number(nFAID));
    m_gntParam.nJyCs = (settings.value("fccs").toInt() + 1);
    m_gntParam.other.strFirv = settings.value("firmWareV").toString();
    m_gntParam.other.strHarv = settings.value("hardwareV").toString();
    m_gntParam.other.nSCUqd = settings.value("scuqd").toString().toInt();
    m_gntParam.other.fdywc = settings.value("dywc").toFloat();
    m_gntParam.other.fdlwc = settings.value("dlwc").toFloat();
    m_gntParam.other.fglyswc = settings.value("glyswc").toFloat();
    m_gntParam.other.fygglwc = settings.value("ygglwc").toFloat();
    m_gntParam.other.fwgglwc = settings.value("wgglwc").toFloat();
    m_gntParam.other.strMqttIotIp = settings.value("mqttiotip").toString();
    m_gntParam.other.strBdtxmk = settings.value("bdtxmk").toString();
    m_gntParam.other.bsm4g = settings.value("4gsm").toBool();
    m_gntParam.other.frtcwc = settings.value("rtcwc").toFloat();
    m_gntParam.other.strLyqdHash = settings.value("lyqdhash").toString();
    settings.endGroup();

    m_mapTypeCcom.clear();
    settings.beginGroup("comdeploy");
    sList = settings.allKeys();
    for (int m = 0; m < sList.size(); ++m)
    {
        if(sList[m] == "xtcom" || sList[m] == "wccom" || sList[m] == "bzbcom")
        {
            continue;
        }
        m_mapTypeCcom[sList[m].toInt()] = settings.value(sList[m]).toString();
    }
    settings.endGroup();


    settings.beginGroup("Licenselj");
    m_gntParam.other.strLicense = settings.value("Licenselj").toString();
    settings.endGroup();

    settings.beginGroup("zdzsdclj");
    m_gntParam.other.strZdzsdclj = settings.value("zdzsdclj").toString();
    settings.endGroup();


    QSettings  sets(strScuRqAppIni, QSettings::IniFormat);
    sets.setIniCodec(QTextCodec::codecForName("UTF-8"));
    m_gntParam.rqApp.mapScuRqApp.clear();
    sets.beginGroup("rqapp");
    sList = sets.allKeys();
    for (int m = 0; m < sList.size(); ++m)
    {
        QString ss = sets.value(sList[m]).toString();
        QStringList strList = ss.split("@");
        for (int k = 0; k < strList.size(); ++k)
        {
            if(strList[k].isEmpty())
                continue;
            m_gntParam.rqApp.mapScuRqApp[strList[k]] = sList[m];
        }
    }
    sets.endGroup();

}

void CGntTestWidget::comeEquipmentInit()
{
    if(m_pEquipmentsjj == nullptr)
    {
        m_pEquipmentsjj = new COMEquipment::EquipmentNz2230();
    }

    if(m_pEquipmentwc == nullptr)
    {
        m_pEquipmentwc = new COMEquipment::EquipmentWc();
        QString swc = queryCfg(strScuOutAutoCheckIni, "comdeploy", "wccom");
        m_pEquipmentwc->ConnectEquipment(swc.toUpper());

        QString sxt = queryCfg(strScuOutAutoCheckIni, "comdeploy", "xtcom");
        QString sbzb = queryCfg(strScuOutAutoCheckIni, "comdeploy", "bzbcom");

        m_pEquipmentsjj->ConnectSet(sxt.toUpper(), "DSB301", sbzb.toUpper(), false);
    }
}


// 开始测试
void CGntTestWidget::on_testBegintBtn_clicked()
{
    if(m_pQueryRs != nullptr && m_pQueryRs->isQuery())
    {
        QMessageBox::about(this, "测试", "正在进行结论查询, 禁止校验");
        return;
    }

    m_mapSshOff.clear();
    m_nLytxchkNum = 1;
    m_nlxdydl = 0;
    m_nlxdydl_2 = 0;
    m_nstartImport = 0;
    m_nstartExport = 0;
    m_nCertExportFinishNum = 0;
    m_nCertExportNum = 0;
    m_nExportNum = 0;
    m_nJcExit = 0;
    m_nQuitNum = 0;
    m_nHbdyNum = 0;
    m_nQuitXcNum = 0;
    m_nXcNum  = 0;
    m_nSWNum = 0;
    m_nJcjlNum = 0;
    m_njcjlhfNum = 0;
    m_nLymz = 0;
    m_nYgmcNum = 0;
    m_nWgmcNum = 0;
    m_nmmcNum = 0;
    m_nyxNum = 0;
    m_nRtcNum = 0;
    m_nKhgNum = 0;
    m_nScuIpModify = 0;
    m_mapGntJyParam.clear();

    g_blxdldy[0] = false;
    g_blxdldy[1] = false;
    g_bScuModifyip = false;
    g_bSingleExportFinish = false;
    g_bCertExport = false;
    m_bHidden = true;
    g_bTestSign = true;
    g_bHbdy = false;
    g_bSW = false;
    g_bstartExport = false;
    g_bstartImport = false;

    for (int m = 0; m < 15; ++m)
    {
        g_bYxFinish[m] = false;
    }
    for (int m = 0; m < g_njcjlNum; ++m)
    {
        g_bJcjl[m] = false;
    }

    for (int m = 0; m < 41; ++m)
    {
        g_bYx[m] = false;
    }

    for(int m = 0; m < 5; ++m)
    {      
        g_bRtcYx[m] = false;
    }

    for(int m = 0; m < 10; ++m)
    {
        g_bLyQuery[m] = false;
    }
    for (int m = 0; m < 15; ++m) {
        g_bygMc[m] = false;
        g_bwgMc[m] = false;
        g_bmMc[m] = false;
    }



    g_bopenclosecapFinish = false;


    ui->csDLResultTW->clearContents();
    ui->csDLResultTW->setRowCount(0);
    ui->csXLResultTW->clearContents();
    ui->csXLResultTW->setRowCount(0);
    ui->textEdit->clear();

    readCfgBeforeCheck();

    SSCURecoreResult rr;
    rr.dtRq = QDateTime::currentDateTime();

   for (int m = 0;  m < m_gntParam.vtItem.size(); ++m)
    {
        if(m_gntParam.vtItem[m] == "蓝牙通信")
        {
            if(!m_pComPort->lymzZt())
            {
                QMessageBox::about(this, "开始检测", "蓝牙模组未连接");
                return;
            }
            else
                break;
        }
    }

    for (int m = 0; m < ui->tableWidget->rowCount(); ++m)
    {

        QTableWidgetItem *pitem = ui->tableWidget->item(m, EGntTest_BW);
        if(pitem->checkState() != Qt::Checked)
            continue;
        ui->tableWidget->item(m, EGntTest_JL)->setText("");
        int nBw = pitem->text().toInt();
        auto iter = m_mapScuParam.find(nBw);
        if(iter == m_mapScuParam.end())
            continue;
        ui->tableWidget->item(m, EGntTest_ZT)->setText("正在校验");
        rr.nfaid = ui->tableWidget->item(m, EGntTest_FAID)->text().toInt();
        rr.npcid = ui->tableWidget->item(m, EGntTest_PCID)->text().toInt();
        rr.noid = queryCfg(strScuOutAutoCheckIni, "scuoid", "scuoid").toULongLong();
        ++rr.noid;
        rr.strEsn = iter->second.strEsn;
        rr.strDevid = iter->second.strID;
        rr.strLogicaddr = iter->second.strLjdz;
        rr.strIp = iter->second.strIp;
        setCfg(strScuOutAutoCheckIni, "scuoid", "scuoid", rr.noid);
        ui->tableWidget->item(m, EGntTest_OID)->setText(QString::number(rr.noid));

        m_dbThread.dlResultPush(rr);

        ++ m_nXcNum;
        m_gntParam.noid = rr.noid;
        m_gntParam.strTypeCCom.clear();
        auto it = m_mapTypeCcom.find(nBw);
        if(it != m_mapTypeCcom.end() )
        {
            m_gntParam.strTypeCCom = it->second;
        }
        m_gntParam.strRs485.clear();
        auto tor = m_mapRs485.find(nBw);
        if(tor != m_mapRs485.end())
        {
            m_gntParam.strRs485 = tor->second;
        }
        m_gntParam.nBw = nBw;
        m_gntParam.scuParam = iter->second;
        m_vtTestProcess[nBw]->setGntParam(m_gntParam);
        if(!m_vtTestProcess[nBw]->isRunning())
        {
            m_vtTestProcess[nBw]->start();
        }
    }

    if(m_nXcNum != 0)
    {
        ui->testBegintBtn->setEnabled(false);
        ui->testBegintBtn->setStyleSheet("background-color: #ff0000; color: white;border-style: outset;border-width: 2px;border-radius: 10px;border-color: beige;font-size: 26px;padding: 6px;");
        ui->createReportBtn->setEnabled(false);
        ui->createReportBtn->setStyleSheet("background-color: #ff0000; color: white;border-style: outset;border-width: 2px;border-radius: 10px;border-color: beige;font-size: 26px;padding: 6px;");
    }
}

// 结束测试
void CGntTestWidget::on_testEndBtn_clicked()
{
    comeEquipmentInit();
    m_pEquipmentsjj->SendCommUIDownRapid();

    g_bTestSign = false;
}


// 上电
void CGntTestWidget::on_sdBtn_clicked()
{
    ui->testEndBtn->setEnabled(false);
    ui->ddBtn->setEnabled(false);
    ui->sdBtn->setEnabled(false);
    ui->testBegintBtn->setEnabled(false);
    ui->createReportBtn->setEnabled(false);

    ui->testEndBtn->setStyleSheet("background-color: #ff0000; color: white;border-style: outset;border-width: 2px;border-radius: 10px;border-color: beige;font-size: 26px;padding: 6px;");
    ui->ddBtn->setStyleSheet("background-color: #ff0000; color: white;border-style: outset;border-width: 2px;border-radius: 10px;border-color: beige;font-size: 26px;padding: 6px;");
    ui->sdBtn->setStyleSheet("background-color: #ff0000; color: white;border-style: outset;border-width: 2px;border-radius: 10px;border-color: beige;font-size: 26px;padding: 6px;");
    ui->testBegintBtn->setStyleSheet("background-color: #ff0000; color: white;border-style: outset;border-width: 2px;border-radius: 10px;border-color: beige;font-size: 26px;padding: 6px;");
    ui->createReportBtn->setStyleSheet("background-color: #ff0000; color: white;border-style: outset;border-width: 2px;border-radius: 10px;border-color: beige;font-size: 26px;padding: 6px;");

    qApp->processEvents();
    comeEquipmentInit();
    m_pEquipmentsjj->SendCommUIDownRapid();
    QThread::sleep(1);
    for (int meterIndex = 0; meterIndex < ui->tableWidget->rowCount(); meterIndex++)
    {
        m_pEquipmentwc->wAutoShutdown(meterIndex+1, 0);
        QThread::msleep(20);

    }

    for (int meterIndex = 0; meterIndex < ui->tableWidget->rowCount(); meterIndex++)
    {
        if(ui->tableWidget->item(meterIndex, EGntTest_BW)->checkState() != Qt::Checked)
        {
            m_pEquipmentwc->wAutoShutdown(meterIndex+1, 1);
            QThread::msleep(20);
        }
    }

    // 上电命令
    m_pEquipmentwc->wAutoShutdown(41, 0);
    QThread::msleep(20);
    m_pEquipmentsjj->SendCommOpen(false);
    QThread::msleep(20);
    m_pEquipmentsjj->Power_ON(true, 220 , 5, "50L", 50, 220, 5, 2, 7, 4);

    // 闭环命令
    for(int m = 0; m < 16; ++m)
    {
        qApp->processEvents();
        QThread::msleep(500);
        qApp->processEvents();
    }

    for (int m = 0; m < 5; ++m)
    {
        if(m_pEquipmentsjj->SendCommOpen(false))
        {
            break;
        }
        QThread::msleep(200);
    }

    if(m_bFirstSd)
    {
        m_bFirstSd = false;
        for (int m = 1; m < 41; ++m)
        {
            qApp->processEvents();
            m_pEquipmentwc->wSendSwitchWeakTmnlChannel(m, 11);
        }
    }
    ui->testBegintBtn->setEnabled(true);
    ui->testEndBtn->setEnabled(true);
    ui->ddBtn->setEnabled(true);
    ui->sdBtn->setEnabled(true);
    ui->createReportBtn->setEnabled(true);


    ui->testBegintBtn->setStyleSheet("QPushButton {background-color:#0089ff ;color: white;border-style: outset;border-width: 2px;border-radius: 10px;border-color: beige;font-size: 26px;padding: 6px;}QPushButton:hover {background-color: #0072C6;}");
    ui->testEndBtn->setStyleSheet("QPushButton {background-color:#0089ff ;color: white;border-style: outset;border-width: 2px;border-radius: 10px;border-color: beige;font-size: 26px;padding: 6px;}QPushButton:hover {background-color: #0072C6;}");
    ui->ddBtn->setStyleSheet("QPushButton {background-color:#0089ff ;color: white;border-style: outset;border-width: 2px;border-radius: 10px;border-color: beige;font-size: 26px;padding: 6px;}QPushButton:hover {background-color: #0072C6;}");
    ui->sdBtn->setStyleSheet("QPushButton {background-color:#0089ff ;color: white;border-style: outset;border-width: 2px;border-radius: 10px;border-color: beige;font-size: 26px;padding: 6px;}QPushButton:hover {background-color: #0072C6;}");
    ui->createReportBtn->setStyleSheet("QPushButton {background-color:#0089ff ;color: white;border-style: outset;border-width: 2px;border-radius: 10px;border-color: beige;font-size: 26px;padding: 6px;}QPushButton:hover {background-color: #0072C6;}");
}

// 断电
void CGntTestWidget::on_ddBtn_clicked()
{
    comeEquipmentInit();
    m_pEquipmentsjj->SendCommUIDownRapid();
}

void CGntTestWidget::on_SWOK()
{
    g_bSW = true;
}

void CGntTestWidget::on_timer()
{
    int num = 0;
    while(m_queueGc.size() != 0  && num < 50)
    {
        CCsXlGcMsg gc= m_queueGc.front();
        gntJyXlGc(&gc);
        m_queueGc.pop();
        ++num;
    }

    num = 0;
    while(m_queueJg.size() != 0 && num < 20)
    {
        CCsXlJgMsg jg= m_queueJg.front();
        if(gntJxXlJg(&jg))
        {
            m_queueJg.pop();
        }
        else
        {
            break;
        }
        ++num;
    }

    if(m_queueJsMsg.size() != 0)
    {
        ++m_nQuitXcNum;
        m_queueJsMsg.pop();
    }

    if(m_queueGc.size() == 0 && m_queueJg.size() == 0  && m_queueJs.size() != 0)
    {
        CCsJsMsg js = m_queueJs.front();
        m_queueJs.pop();
        setJsXc(&js);
    }
}


void CGntTestWidget::on_monitor()
{
    if(isHidden() && m_bHidden)
    {
        return;
    }
    comeEquipmentInit();

    QString smonitor= m_pEquipmentsjj->GetMonitor();

    QStringList sList = smonitor.split(",");

    if(sList.size() < 19)
        return;

    g_mtxMonitor.lock();

    g_SMonitor.sU1 = leaveThreeDecPlace(sList[0]);
    g_SMonitor.sU2 = leaveThreeDecPlace(sList[7]);
    g_SMonitor.sU3 = leaveThreeDecPlace(sList[14]);

    g_SMonitor.sI1 = leaveThreeDecPlace(sList[1]);
    g_SMonitor.sI2 = leaveThreeDecPlace(sList[8]);
    g_SMonitor.sI3 = leaveThreeDecPlace(sList[15]);

    g_SMonitor.sP1 = leaveThreeDecPlace(sList[2]);
    g_SMonitor.sP2 = leaveThreeDecPlace(sList[9]);
    g_SMonitor.sP3 = leaveThreeDecPlace(sList[16]);

    g_SMonitor.sQ1 = leaveThreeDecPlace(sList[3]);
    g_SMonitor.sQ2 = leaveThreeDecPlace(sList[10]);
    g_SMonitor.sQ3 = leaveThreeDecPlace(sList[17]);

    g_SMonitor.sX1 = leaveThreeDecPlace(sList[4]);
    g_SMonitor.sX2 = leaveThreeDecPlace(sList[11]);
    g_SMonitor.sX3 = leaveThreeDecPlace(sList[18]);

    g_mtxMonitor.unlock();

    ui->uaLB->setText("Ua："+ sList[0] + "V");
    ui->ubLB->setText("Ub："+ sList[7] + "V");
    ui->ucLB->setText("Uc："+ sList[14] +"V");

    ui->iaLB->setText("Ia："+ sList[1] + "A");
    ui->ibLB->setText("Ib："+ sList[8] + "A");
    ui->icLB->setText("Ic："+ sList[15] + "A");
}

void CGntTestWidget::on_jctimer()
{
    m_jctimer.stop();

    g_bJcjl[m_nJcLx] = true;

}

void CGntTestWidget::on_tcpservertimer()
{
    if(m_ptcpServer == nullptr || !g_bqueryLj)
        return;
    FILE* pipe = _popen("netstat -ano | findstr \"*************:2404\"", "r");

    // 读取命令输出
    char buffer[128];
    std::string result = "";
    while (fgets(buffer, sizeof(buffer), pipe) != nullptr) {
        result += buffer;
    }

    // 关闭管道
    _pclose(pipe);

    bool b = m_ptcpServer->getState();
    if(result.find("ESTABLISHED") != -1 && !result.empty() && m_ptcpServer->getState())
    {
        g_btcpserver = true;
    }
    else
    {
        g_btcpserver = false;
    }
}

// 生成报告
void CGntTestWidget::createReport(int m)
{
    QString strReportFile = queryCfg(strScuOutAutoCheckIni, "baogaosclj", "baogaosclj");
    strReportFile.replace("\\\\", "/");
    if(!strReportFile.endsWith("/"))
        strReportFile += "/";

    QDate dt = QDate::currentDate();
    QString strDate = dt.toString("yyyyMMdd");

    if(ui->tableWidget->item(m, EGntTest_BW)->checkState() != Qt::Checked)
        return;;
    QString strID = ui->tableWidget->item(m, EGntTest_ID)->text();
    QString strFileName = strReportFile + strID + "_" + strDate + ".doc";

    QString html;
    saveHtmlToWord(m, html);

    QFile outFile(strFileName);
    if(!outFile.open(QIODevice::WriteOnly | QIODevice::Text))
        return;
    QTextStream ts(&outFile);
    ts<<html<<endl;
    outFile.close();

}

void CGntTestWidget::on_openclosecap_allFinish()
{
    g_bopenclosecapFinish = true;
}

bool CGntTestWidget::saveHtmlToWord(int m, QString &html)
{
    QString strID = ui->tableWidget->item(m, EGntTest_ID)->text();
    QString strRFID = ui->tableWidget->item(m, EGntTest_ESN)->text();
    QString strYjbb = ui->tableWidget->item(m, EGntTest_YJBB)->text();
    html += "<html>";
    html += "<body style=\"bgcolor:yellow\">";
    html += "<p style=\"text-align: center; font-size: 22px; font-weight: bold;\">新型台区智能融合终端 出厂检验报告</p>";
    html += "<table style=\"border: 2px solid black; border-collapse: collapse; width: 100% \">";
    html += "<tr>";
    html += "<td rowspan=\"4\" style=\"border: 2px solid black; text-align: center\"><strong>装置信息</td>";
    html += "<td style=\"border: 2px solid black; text-align: center\"><strong>产品编号</td>";
    html += "<td style=\"border: 2px solid black; text-align: center\"><strong>额定电压/电流</td>";
    html += "<td style=\"border: 2px solid black; text-align: center\"><strong>工作电源</td>";
    html += "</tr>";
    html += "<tr>";
    html += "<td style=\"border: 2px solid black; text-align: center; font-size: 14px;\">";
    html += strID;
    html += "</td>";
    html += "<td style=\"border: 2px solid black; text-align: center; font-size: 14px;\">AC 220V/5A</td>";
    html += "<td style=\"border: 2px solid black; text-align: center; font-size: 14px;\">AC 220V</td>";
    html += "</tr>";
    html += "<tr>";
    html += "<td style=\"border: 2px solid black; text-align: center\"><strong>RFID编号</td>";
    html += "<td style=\"border: 2px solid black; text-align: center\"><strong>硬件版本</td>";
    html += "<td style=\"border: 2px solid black; text-align: center\"><strong>软件版本</td>";
    html += "</tr>";
    html += "<tr>";
    html += "<td style=\"border: 2px solid black; text-align: center; font-size: 14px;\">";
    html += strRFID;
    html +=  "</td>";
    html += "<td style=\"border: 2px solid black; text-align: center; font-size: 14px;\">";
    html += strYjbb;
    html +="</td>";
    html += "<td style=\"border: 2px solid black; text-align: center; font-size: 14px;\"></td>";
    html += "</tr>";

    html += "<tr>";
    html += "<td style=\"border: 2px solid black; text-align: center\"><strong>检验项目</td>";
    html += "<td colspan=\"2\" style=\"border: 2px solid black; text-align: center;  font-size: 14px;\">检 验 标 准</td>";
    html += "<td style=\"border: 2px solid black; text-align: center; font-size: 14px;\">检验结论</td>";
    html += "</tr>";

    html += "<tr>";
    html += "<td rowspan=\"2\" style=\"border: 2px solid black; text-align: center\"><strong>外观检查</td>";
    html += "<td colspan=\"2\" style=\"border: 2px solid black; text-align: center; font-size: 14px;\">外壳无污物，无划痕；铭牌及标签印字清晰正确，粘贴牢固</td>";
    html += "<td style=\"border: 2px solid black; text-align: center; font-size: 14px;\">√</td>";
    html += "</tr>";

    html += "<tr>";
    html += "<td colspan=\"2\" style=\"border: 2px solid black; text-align: center; font-size: 14px;\">4G通讯模块安装入位；航插紧固，不松动</td>";
    html += "<td style=\"border: 2px solid black; text-align: center; font-size: 14px;\">√</td>";
    html += "</tr>";

    html += "<tr>";
    html += "<td rowspan=\"6\" style=\"border: 2px solid black; text-align: center\"><strong>绝缘检验</td>";
    html += "<td style=\"border: 2px solid black; text-align: center; font-size: 14px;\">检验部位</td>";
    html += "<td style=\"border: 2px solid black; text-align: center; font-size: 14px;\">介质强度</td>";
    html += "<td rowspan=\"6\" style=\"border: 2px solid black; text-align: center\">√</td>";
    html += "</tr>";

    html += "<tr>";
    html += "<td style=\"border: 2px solid black; text-align: center; font-size: 14px;\">电源回路对地</td>";
    html += "<td rowspan=\"5\" style=\"border: 2px solid black; text-align: center\">2500V、50Hz、1min无击穿或闪络</td>";
    html += "</tr>";

    html += "<tr>";
    html += "<td style=\"border: 2px solid black; text-align: center; font-size: 14px;\">交流工频电压输入回路对地</td>";
    html += "</tr>";

    html += "<tr>";
    html += "<td style=\"border: 2px solid black; text-align: center; font-size: 14px;\">交流工频电流输入回路对地</td>";
    html += "</tr>";

    html += "<tr>";
    html += "<td style=\"border: 2px solid black; text-align: center; font-size: 14px;\">状态输入回路对地</td>";
    html += "</tr>";

    html += "<tr>";
    html += "<td style=\"border: 2px solid black; text-align: center; font-size: 14px;\">交流工频电压输入回路与交流工频电流输入回路之间</td>";
    html += "</tr>";


    html += "<tr>";
    html += "<td style=\"border: 2px solid black; text-align: center\"><strong>连续通电稳定性</td>";
    html += "<td colspan=\"2\" style=\"border: 2px solid black; text-align: center; font-size: 14px;\">连续通电高温老化24小时，产品工作正常，模拟量精度满足要求</td>";
    html += "<td style=\"border: 2px solid black; text-align: center; font-size: 14px;\">√</td>";
    html += "</tr>";

    html += "<tr>";
    html += "<td rowspan=\"";
    html += QString::number(g_vtChkItem.size());
    html += "\" style=\"border: 2px solid black; text-align: center\"><strong>功能检验</td>";

    for (int n = 0; n < g_vtChkItem.size(); ++n)
    {
        QString strDl = g_vtChkItem[n];
        html += "<td colspan=\"2\" style=\"border: 2px solid black; text-align: center; font-size: 14px;\">";
        html += (strDl + "满足产品检验要求");
        html += "</td>";
        html += "<td style=\"border: 2px solid black; text-align: center; font-size: 14px;\">√</td>";
        html += "</tr>";
    }

    html += "<tr>";
    html += "<td style=\"border: 2px solid black; text-align: center\"><strong>检验结论</td>";
    html += "<td colspan=\"3\" style=\"border: 2px solid black; text-align: center;  font-size: 14px;\">经检验，产品符合企业标准要求，准予出厂</td>";
    html += "</tr>";

    html += "<tr>";
    html += "<td style=\"border: 2px solid black; text-align: center\"><strong>检验人</td>";
    html += "<td style=\"border: 2px solid black; text-align: center; font-size: 14px;\"></td>";
    html += "<td style=\"border: 2px solid black; text-align: center\"><strong>检验时间</td>";
    html += "<td style=\"border: 2px solid black; text-align: center; font-size: 14px;\">";
    QDate dt = QDate::currentDate();
    html += dt.toString("yyyy.MM.dd");
    html += "</td>";
    html += "</tr>";

    html += "</table>";
    html += "</body>";
    html += "</html>";

    return true;
}

bool CGntTestWidget::isProcessRunning(const QString &processName)
{
    QProcess process;
    process.start("tasklist", QStringList() << "/FI" << "IMAGENAME eq " + processName);
    process.waitForFinished();
    QString output = process.readAllStandardOutput();
    return output.contains(processName);
}

bool CGntTestWidget::killProcess(const QString &processName)
{
    QProcess process;
    process.start("taskkill", QStringList() << "/IM" << processName << "/F");
    process.waitForFinished();
    return (process.exitCode() == 0);
}

QString CGntTestWidget::leaveThreeDecPlace(QString str)
{
    int dotPos = str.indexOf('.');
    if (dotPos != -1 && str.length() > dotPos + 3) {
        str = str.left(dotPos + 4);  // 保留小数点后三位
    }
    return str;
}

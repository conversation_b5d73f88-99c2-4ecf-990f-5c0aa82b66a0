#include "cserialport.h"
#include "ui_cserialport.h"

CserialPort::CserialPort(QWidget *parent) :
    QWidget(parent),
    ui(new Ui::CserialPort)
{
    ui->setupUi(this);
    ui->ComCB->clear();
    QList<QSerialPortInfo> list = QSerialPortInfo::availablePorts();
    for (int i = 0; i < list.size(); i++)
    {
        ui->ComCB->addItem( list.at(i).portName()) ;
    }
}

CserialPort::~CserialPort()
{
    delete ui;
}

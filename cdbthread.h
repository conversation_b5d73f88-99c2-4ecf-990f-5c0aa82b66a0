﻿#ifndef CDBTHREAD_H
#define CDBTHREAD_H

#include <QObject>
#include <QThread>
#include <QSqlDatabase>
#include <QSqlQuery>
#include <QSqlError>
#include "msk_global.h"
#include <queue>
#include <QMutex>
#include <QDateTime>

class CDBThread : public QThread
{
    Q_OBJECT
public:
    CDBThread();
    void run();
    void dlResultPush(SSCURecoreResult&);
    void dlItemPush(SSCUUpdateResult&);
    void xlItemPust(SSCUXL&);
    void gcItemPust(SGntJyXlGc&);
private:
    bool existDLResult();       // 判断是否存在SCUDETECTIONRESULT表
    void createDLResult();      // 创建SCUDETECTIONRESULT表
    bool existXLResult();       // 判断是否存在SCUXLDETECTIONRESULT表
    void createXlResult();
    bool existGc();             // 判断是否存在SCUDETECTIONGC表
    void createGc();
    int recordResultNum();
    void recordDLResult();
    int updateDlNum();
    void updateDLItem();
    int dealXlNum();
    void dealXl();
    bool existXl(SSCUXL &);
    void updateXl(SSCUXL &);
    void recordXl(SSCUXL &);
    int gcJhNum();
    void recordGc();
    void dropTable();
private:
    QSqlDatabase m_db;
    std::queue<SSCURecoreResult> m_vtRecordResult;
    QMutex  m_mtxRecordResult;

    std::queue<SSCUUpdateResult> m_vtUpdateResult;
    QMutex m_mtxUpdateDl;

    std::queue<SSCUXL>m_vtSCUXL;
    QMutex m_mtxXL;

    std::queue<SGntJyXlGc>m_vtSCUGC;
    QMutex m_mtxGC;




};

#endif // CDBTHREAD_H

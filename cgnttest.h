﻿#ifndef CGNTTEST_H
#define CGNTTEST_H

#include <msk_global.h>
#include <QObject>
#include <QSerialPort>
#include <QSerialPortInfo>
#include <QTimer>
#include "sshconnection.h"
#include "sshremoteprocess.h"
#include "ssh/sftpchannel.h"
#include "chandle698.h"
#include <QTcpSocket>
#include <QtMqtt/qmqttclient.h>
#include <QDateTime>
#include <QJsonObject>
#include <QJsonDocument>
#include <QJsonArray>
#include <QDir>
class CGntTest :public QObject
{
    Q_OBJECT
public:
    CGntTest();
    ~CGntTest();
    void init(SGntParam&);
    void check();       // 开始校验
    void clear();

Q_SIGNALS:
    void endSig();

private slots:
    void on_ssh_connected();
    void on_ssh_error();
    void on_ssh_readyReadStandardOutput();

    void on_tcp_error(QAbstractSocket::SocketError);
    void on_tcp_stateChanged(QAbstractSocket::SocketState);
    void on_tcp_readyRead();

    void on_time_timeout();
    void on_time_timeout2();

    void on_mqtt_mesReceived(const QByteArray &message, const QMqttTopicName &topic);
    void on_mqtt_StateChange();

    void onChannelInitialized1();

    void onChannelInitialized2();

    void onChannelInitialized3();

    void onChannelInitialized4();

    void onChannelInitialized5();

    void onChannelError1(const QString &);

    void onOpfinished(QSsh::SftpJobId, QString);

    void onOpfinished2(QSsh::SftpJobId, QString);

    void onOpfinished3(QSsh::SftpJobId, QString);

    void onOpfinished4(QSsh::SftpJobId, QString);

    void onOpfinished5(QSsh::SftpJobId, QString);

    void on_rs485_readyRead();
private:
    void checkItem();

    void userPassWd();          // 用户名密码校验
    void rqJc();             // 容器校验
    void qcdywjJc();            // 清除多余文件检查
    void APPJc();            // APP运行检测
    void xtbdJc();           // 系统补丁校验
    void zdcsJc();           // 终端参数检查
    void cs698Jc();          // 698参数检查
    void sbdsJc();          // 设备对时
    void cktxJc();          // 串口通信
    void hplc4GJc();        // hplc/4G模块
    void sjqlJc();          // 数据清零检查
    void hlxjJc();          // 回路巡检检查
    void ccztJc();          // 磁场状态检查
    void dkJc();            // 端口检查
    void lytxJc();          // 蓝牙通信检查
    void hbdyJc();          // 后备电源检查
    void SWJc();            // SW检查
    void jcjlJc();          // 交采计量检查
    void mqttIotJc();           // MQTTIoT
    void ywmmcJc();             // 有功、无功、秒脉冲接口检查
    void yxfblJc();             // 遥信分辨率检查
    void zsdcJc();                // 证书导出检查
    void zsdrJc();                // 证书导入检查
    void zdipJc();                  // 参数设置
    void importLicense();           // 导入license
    void lxJc();                    // 零序电压电流检查
    void hplccheck();     //hplc模块检查
    void g4check();
    void hbdycheck();
    void createRemoteProcess(QByteArray);
    void dbbb1(QString);        // 大包版本1
    void dbbb2(QString);        // 大包版本2
    void zkb(QString);          // 主控板
    void yjb(QString);          // 硬件版本
    void nhb(QString);          // 内核版本
    void bhb(QString);          // 备核版本
    void rqb(QString);          // 容器版本
    void jcb(QString);          // 交采版本
    void gjxtb(QString);        // 关键系统版本
    void czxtb(QString);        // 操作系统版本

    void rqchk(QString);        // 容器检查
    void APPchk(QString);       // APP检查
    void apprealchk(QString);          // app检查
    void rqwappchk();               // 容器外App检查
    void rqwApp(QString);               // 容器外APP检查
    void zdcs(QString);                 // 终端参数
    void zdcsjg(QString);               // 终端参数结果查询
    void dk9001(QString);               // 端口开启
    void ds1(QString);
    void ds2(QString);
    void tty(QString);
    void g4tty(QString);
    void devapptime(QString);
    void apptime(QString);
    void rs4851(QString);
    void rs4851_1(QString);

    void rs485II(QString);
    void rs485II_1(QString);

    void rs485III(QString);
    void rs485III_1(QString);

    void rs485IV(QString);
    void rs485IV_1(QString);
    void devctl(QString);
    void mk4g(QString);
    void dkzk(QString);
    void hbdy(QString);
    void setSW1(QString);
    void setSW2(QString);
    void mqttIot(QString);
    void mqttIot2(QString);
    void ipVerify(QString);
    void esnQuery(QString);
    void verifyfile(QString);
    void queryEsamId(QString);
    void devInfoExist(QString);     // 判断/data/devinfo文件夹是否存在
    void licenseExist(QString);  // 判断license文件是否存在
    void fileClearVerify(QString);   // 文件清除确认
    void bakFileVerify(QString);   // 备份文件是否存在
    void newFileVerify(QString);   // 新文件存在确认
    void lymmVerify(QString);       // 蓝牙密码确认
    void lyqdVerify(QString);         // 蓝牙驱动确认
    void oldUartdrvFile(QString);       // 判断文件是否存在
    void modifyconfig(QString);
    void configVerify(QString);         // security_proxy_config 文件确认
    void secufileVerify(QString);           // 文件确认
    void scuIdQuery(QString);           // 终端ID 查询
    void md5Verify(QString);            // md5值
    void mvParam();
    void mvParam2();
    void addQx();
    void APPitem();
    void rqwAppItem();
    void cs698Item();
    void sjqltcp();
    void cczttcp();
    void zdcstcp();
    void jcjltcp();
    void rqappsjdb();           // app启动时间判断
    void sendGcEvent();
    void sendJgEvent();

    void jcjldy(QByteArray &);
    void jcjldl(QByteArray &);
    void jcjllxdl(QByteArray &);
    void jcjlglys(QByteArray &);
    void jcjlyg(QByteArray &);
    void jcjlwg(QByteArray &);
    void zdyxbs(QByteArray &);


    void psdev4(QString &);

    void createJm(QString &);
    void fileExist(QString &);
    void psdev(QString &);
    void psdev2(QString &);
    void psdev3(QString &);
    void mqttIoTFile(QString &);
    void sshConnect();
    void sshClose();

    bool serialConnect(QString sPortName);      // rs串口连接

    QString calculateSHA256(const QString &input);

private:
    int m_ndkjc;
    QTimer *m_ptimer;
    QTimer *m_ptimer2;
    SGntParam m_gntParam;
    QString m_strChkItem;           // 校验项
    int m_nJyCs;                    // 校验次数
    bool m_bsjqlcheck;   // 数据清零专项检查
    bool m_bcheck;
    bool m_btimercheck;
    bool m_bjccheck;
    int m_nLx;
    int m_nsshLj;
    int m_nchkHLXJ;             // 回路巡检检查次数
    int m_nHLXJ[6];            // 回路巡检状态 0 初始化状态 1 正常状态 2 异常状态
    int m_nReTcp;             // tcp重连次数
    int m_nReSsh;
    QSerialPort *m_pSerialPort;
    CHandle698  m_698Handle;
    QMqttClient  *m_pclient;

    int m_nTypeCType;
    int m_ndkjcLx;
    int m_nHbdylx;
    int m_nstartchecknum;
    OAD m_oad;

    QSsh::SshConnection *m_pSsh;
    QSsh::SshRemoteProcess::Ptr sshRemote;
    QTcpSocket  *m_pSocket;

    QDateTime m_dt2;


    SGntJyXlGc  m_gntJyXl;
    SGntJyXlJg  m_gntJyJg;

    std::vector<QString>m_vtRqName;
    std::vector<QString>m_vtrqwApp;
    std::vector<QString>m_vtCmd;

    bool m_bScuAppChk;
    bool m_bScuRqwAppChk;
    bool m_bFirst;
    bool m_bRecv;

    bool m_bSendSshOff;         // 是否发送ssh断开消息

    QString m_strRqwApp;
    QString m_strtty;
    QString m_strDestFile;

    QString m_strComeApp;
    QString m_strdevId;


    int m_nHbdy;
    int m_nJcLx;            // 交采类型
    int m_njcssh;
    int m_nTcp;
    int m_ntimeout;
    int m_ntimeout2;
    int m_nAppNum;                  // 查询app启动时间的次数
    int m_nJcQueryNum;              // 交采版本查询次数
    int m_nRS485Num;                // RS485的测试次数
    int m_nYgMcChkNum;                // 有功检查次数
    int m_nWgMcChkNum;                // 无功检查次数
    int m_nMmcChkNum;               // 秒脉冲检查次数
    int m_nLxChkNum;                // 零序检查次数

    QTime m_devtime;

    QSsh::SftpChannel::Ptr m_channel;
    std::vector<QString> m_vtCompareApp;  // 进行时间对比的app;
    std::map<int, int>m_mapYx;

    QSerialPort *m_pRs485;
    QByteArray m_zrsdata;
    QString m_Ck;
    std::vector<QByteArray>m_vtYx;


    QDateTime m_dtZdRtc[5];       //rtc时间
    QDateTime m_dtSys[5];          // 系统时间
    int m_nRtcChkNum;               // rtc校验次数
    QString m_strKill;

    bool m_bNodealssherr;

    QDateTime m_opencloseTime;

    QString m_strEsn;

    QString m_strEsamId;

    bool m_bLock;

    int m_nLymm;        // 蓝牙密码验证次数

    QJsonArray m_bodyary;

    bool m_blytx[10]; //  蓝牙通信检查
    int m_nlytxnum ;    // 蓝牙通信检查次数
    bool m_bYx1Chk[15];       // 遥信校验结果
    bool m_bYx2Chk[15];       // 遥信校验结果
    bool m_bYx3Chk[15];       // 遥信校验结果
    bool m_bYx4Chk[15];       // 遥信校验结果
    bool m_bYgMcChk[10];         //有功脉冲检查结果
    bool m_bWgMcChk[10];         //无功脉冲检查结果
    bool m_bMmcChk[10];          //秒脉冲检查结果
    bool m_bLxChk[10];          // 零序检查结果
    bool m_bLxChk_1[10];          // 零序检查结果
    int m_nYxChkNum ;

};


#endif // CGNTTEST_H

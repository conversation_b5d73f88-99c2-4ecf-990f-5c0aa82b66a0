/****************************************************************************
**
** Metadata for COMEquipment generated by dumpcpp v5.12.10 using
** dumpcpp.exe COMEquipment.tlb
** from the type library COMEquipment.tlb
**
****************************************************************************/

#define QAX_DUMPCPP_COMEQUIPMENT_NOINLINES
#include "comequipment.h"

using namespace COMEquipment;

struct qt_meta_stringdata_all_t {
    QByteArrayData data[203];
    char stringdata0[2482];
};
#define QT_MOC_LITERAL(idx, ofs, len, table) \
    Q_STATIC_BYTE_ARRAY_DATA_HEADER_INITIALIZER_WITH_OFFSET(len, \
    offsetof(qt_meta_stringdata_all_t, stringdata##table) + ofs \
        - idx * sizeof(QByteArrayData) \
    )
static const qt_meta_stringdata_all_t qt_meta_stringdata_all = {
    {
QT_MOC_LITERAL(0, 0, 29, 0),
QT_MOC_LITERAL(1, 30, 11, 0),
QT_MOC_LITERAL(2, 42, 13, 0),
QT_MOC_LITERAL(3, 56, 9, 0),
QT_MOC_LITERAL(4, 66, 0, 0),
QT_MOC_LITERAL(5, 67, 4, 0),
QT_MOC_LITERAL(6, 72, 6, 0),
QT_MOC_LITERAL(7, 79, 4, 0),
QT_MOC_LITERAL(8, 84, 4, 0),
QT_MOC_LITERAL(9, 89, 15, 0),
QT_MOC_LITERAL(10, 105, 4, 0),
QT_MOC_LITERAL(11, 110, 6, 0),
QT_MOC_LITERAL(12, 117, 4, 0),
QT_MOC_LITERAL(13, 122, 4, 0),
QT_MOC_LITERAL(14, 127, 12, 0),
QT_MOC_LITERAL(15, 140, 5, 0),
QT_MOC_LITERAL(16, 146, 10, 0),
QT_MOC_LITERAL(17, 157, 11, 0),
QT_MOC_LITERAL(18, 169, 7, 0),
QT_MOC_LITERAL(19, 177, 11, 0),
QT_MOC_LITERAL(20, 189, 11, 0),
QT_MOC_LITERAL(21, 201, 14, 0),
QT_MOC_LITERAL(22, 216, 4, 0),
QT_MOC_LITERAL(23, 221, 7, 0),
QT_MOC_LITERAL(24, 229, 4, 0),
QT_MOC_LITERAL(25, 234, 13, 0),
QT_MOC_LITERAL(26, 248, 6, 0),
QT_MOC_LITERAL(27, 255, 8, 0),
QT_MOC_LITERAL(28, 264, 8, 0),
QT_MOC_LITERAL(29, 273, 7, 0),
QT_MOC_LITERAL(30, 281, 8, 0),
QT_MOC_LITERAL(31, 290, 9, 0),
QT_MOC_LITERAL(32, 300, 8, 0),
QT_MOC_LITERAL(33, 309, 7, 0),
QT_MOC_LITERAL(34, 317, 5, 0),
QT_MOC_LITERAL(35, 323, 2, 0),
QT_MOC_LITERAL(36, 326, 6, 0),
QT_MOC_LITERAL(37, 333, 3, 0),
QT_MOC_LITERAL(38, 337, 11, 0),
QT_MOC_LITERAL(39, 349, 10, 0),
QT_MOC_LITERAL(40, 360, 14, 0),
QT_MOC_LITERAL(41, 375, 5, 0),
QT_MOC_LITERAL(42, 381, 10, 0),
QT_MOC_LITERAL(43, 392, 9, 0),
QT_MOC_LITERAL(44, 402, 5, 0),
QT_MOC_LITERAL(45, 408, 10, 0),
QT_MOC_LITERAL(46, 419, 5, 0),
QT_MOC_LITERAL(47, 425, 11, 0),
QT_MOC_LITERAL(48, 437, 5, 0),
QT_MOC_LITERAL(49, 443, 14, 0),
QT_MOC_LITERAL(50, 458, 1, 0),
QT_MOC_LITERAL(51, 460, 1, 0),
QT_MOC_LITERAL(52, 462, 17, 0),
QT_MOC_LITERAL(53, 480, 7, 0),
QT_MOC_LITERAL(54, 488, 16, 0),
QT_MOC_LITERAL(55, 505, 8, 0),
QT_MOC_LITERAL(56, 514, 13, 0),
QT_MOC_LITERAL(57, 528, 4, 0),
QT_MOC_LITERAL(58, 533, 1, 0),
QT_MOC_LITERAL(59, 535, 7, 0),
QT_MOC_LITERAL(60, 543, 7, 0),
QT_MOC_LITERAL(61, 551, 10, 0),
QT_MOC_LITERAL(62, 562, 9, 0),
QT_MOC_LITERAL(63, 572, 9, 0),
QT_MOC_LITERAL(64, 582, 14, 0),
QT_MOC_LITERAL(65, 597, 14, 0),
QT_MOC_LITERAL(66, 612, 9, 0),
QT_MOC_LITERAL(67, 622, 4, 0),
QT_MOC_LITERAL(68, 627, 23, 0),
QT_MOC_LITERAL(69, 651, 9, 0),
QT_MOC_LITERAL(70, 661, 9, 0),
QT_MOC_LITERAL(71, 671, 19, 0),
QT_MOC_LITERAL(72, 691, 8, 0),
QT_MOC_LITERAL(73, 700, 9, 0),
QT_MOC_LITERAL(74, 710, 10, 0),
QT_MOC_LITERAL(75, 721, 12, 0),
QT_MOC_LITERAL(76, 734, 5, 0),
QT_MOC_LITERAL(77, 740, 5, 0),
QT_MOC_LITERAL(78, 746, 5, 0),
QT_MOC_LITERAL(79, 752, 12, 0),
QT_MOC_LITERAL(80, 765, 5, 0),
QT_MOC_LITERAL(81, 771, 9, 0),
QT_MOC_LITERAL(82, 781, 4, 0),
QT_MOC_LITERAL(83, 786, 12, 0),
QT_MOC_LITERAL(84, 799, 5, 0),
QT_MOC_LITERAL(85, 805, 5, 0),
QT_MOC_LITERAL(86, 811, 5, 0),
QT_MOC_LITERAL(87, 817, 15, 0),
QT_MOC_LITERAL(88, 833, 15, 0),
QT_MOC_LITERAL(89, 849, 7, 0),
QT_MOC_LITERAL(90, 857, 17, 0),
QT_MOC_LITERAL(91, 875, 6, 0),
QT_MOC_LITERAL(92, 882, 6, 0),
QT_MOC_LITERAL(93, 889, 9, 0),
QT_MOC_LITERAL(94, 899, 19, 0),
QT_MOC_LITERAL(95, 919, 12, 0),
QT_MOC_LITERAL(96, 932, 5, 0),
QT_MOC_LITERAL(97, 938, 5, 0),
QT_MOC_LITERAL(98, 944, 5, 0),
QT_MOC_LITERAL(99, 950, 24, 0),
QT_MOC_LITERAL(100, 975, 10, 0),
QT_MOC_LITERAL(101, 986, 17, 0),
QT_MOC_LITERAL(102, 1004, 9, 0),
QT_MOC_LITERAL(103, 1014, 13, 0),
QT_MOC_LITERAL(104, 1028, 26, 0),
QT_MOC_LITERAL(105, 1055, 8, 0),
QT_MOC_LITERAL(106, 1064, 5, 0),
QT_MOC_LITERAL(107, 1070, 10, 0),
QT_MOC_LITERAL(108, 1081, 5, 0),
QT_MOC_LITERAL(109, 1087, 13, 0),
QT_MOC_LITERAL(110, 1101, 7, 0),
QT_MOC_LITERAL(111, 1109, 8, 0),
QT_MOC_LITERAL(112, 1118, 8, 0),
QT_MOC_LITERAL(113, 1127, 29, 0),
QT_MOC_LITERAL(114, 1157, 25, 0),
QT_MOC_LITERAL(115, 1183, 12, 0),
QT_MOC_LITERAL(116, 1196, 22, 0),
QT_MOC_LITERAL(117, 1219, 10, 0),
QT_MOC_LITERAL(118, 1230, 7, 0),
QT_MOC_LITERAL(119, 1238, 16, 0),
QT_MOC_LITERAL(120, 1255, 10, 0),
QT_MOC_LITERAL(121, 1266, 19, 0),
QT_MOC_LITERAL(122, 1286, 11, 0),
QT_MOC_LITERAL(123, 1298, 3, 0),
QT_MOC_LITERAL(124, 1302, 19, 0),
QT_MOC_LITERAL(125, 1322, 13, 0),
QT_MOC_LITERAL(126, 1336, 12, 0),
QT_MOC_LITERAL(127, 1349, 13, 0),
QT_MOC_LITERAL(128, 1363, 4, 0),
QT_MOC_LITERAL(129, 1368, 11, 0),
QT_MOC_LITERAL(130, 1380, 19, 0),
QT_MOC_LITERAL(131, 1400, 13, 0),
QT_MOC_LITERAL(132, 1414, 3, 0),
QT_MOC_LITERAL(133, 1418, 8, 0),
QT_MOC_LITERAL(134, 1427, 8, 0),
QT_MOC_LITERAL(135, 1436, 10, 0),
QT_MOC_LITERAL(136, 1447, 11, 0),
QT_MOC_LITERAL(137, 1459, 21, 0),
QT_MOC_LITERAL(138, 1481, 20, 0),
QT_MOC_LITERAL(139, 1502, 6, 0),
QT_MOC_LITERAL(140, 1509, 18, 0),
QT_MOC_LITERAL(141, 1528, 16, 0),
QT_MOC_LITERAL(142, 1545, 16, 0),
QT_MOC_LITERAL(143, 1562, 12, 0),
QT_MOC_LITERAL(144, 1575, 6, 0),
QT_MOC_LITERAL(145, 1582, 8, 0),
QT_MOC_LITERAL(146, 1591, 15, 0),
QT_MOC_LITERAL(147, 1607, 17, 0),
QT_MOC_LITERAL(148, 1625, 14, 0),
QT_MOC_LITERAL(149, 1640, 14, 0),
QT_MOC_LITERAL(150, 1655, 13, 0),
QT_MOC_LITERAL(151, 1669, 4, 0),
QT_MOC_LITERAL(152, 1674, 18, 0),
QT_MOC_LITERAL(153, 1693, 6, 0),
QT_MOC_LITERAL(154, 1700, 10, 0),
QT_MOC_LITERAL(155, 1711, 4, 0),
QT_MOC_LITERAL(156, 1716, 18, 0),
QT_MOC_LITERAL(157, 1735, 14, 0),
QT_MOC_LITERAL(158, 1750, 5, 0),
QT_MOC_LITERAL(159, 1756, 16, 0),
QT_MOC_LITERAL(160, 1773, 20, 0),
QT_MOC_LITERAL(161, 1794, 8, 0),
QT_MOC_LITERAL(162, 1803, 22, 0),
QT_MOC_LITERAL(163, 1826, 15, 0),
QT_MOC_LITERAL(164, 1842, 25, 0),
QT_MOC_LITERAL(165, 1868, 11, 0),
QT_MOC_LITERAL(166, 1880, 13, 0),
QT_MOC_LITERAL(167, 1894, 12, 0),
QT_MOC_LITERAL(168, 1907, 6, 0),
QT_MOC_LITERAL(169, 1914, 6, 0),
QT_MOC_LITERAL(170, 1921, 6, 0),
QT_MOC_LITERAL(171, 1928, 19, 0),
QT_MOC_LITERAL(172, 1948, 12, 0),
QT_MOC_LITERAL(173, 1961, 21, 0),
QT_MOC_LITERAL(174, 1983, 11, 0),
QT_MOC_LITERAL(175, 1995, 21, 0),
QT_MOC_LITERAL(176, 2017, 7, 0),
QT_MOC_LITERAL(177, 2025, 16, 0),
QT_MOC_LITERAL(178, 2042, 20, 0),
QT_MOC_LITERAL(179, 2063, 13, 0),
QT_MOC_LITERAL(180, 2077, 21, 0),
QT_MOC_LITERAL(181, 2099, 18, 0),
QT_MOC_LITERAL(182, 2118, 24, 0),
QT_MOC_LITERAL(183, 2143, 26, 0),
QT_MOC_LITERAL(184, 2170, 14, 0),
QT_MOC_LITERAL(185, 2185, 3, 0),
QT_MOC_LITERAL(186, 2189, 11, 0),
QT_MOC_LITERAL(187, 2201, 4, 0),
QT_MOC_LITERAL(188, 2206, 20, 0),
QT_MOC_LITERAL(189, 2227, 6, 0),
QT_MOC_LITERAL(190, 2234, 3, 0),
QT_MOC_LITERAL(191, 2238, 14, 0),
QT_MOC_LITERAL(192, 2253, 15, 0),
QT_MOC_LITERAL(193, 2269, 21, 0),
QT_MOC_LITERAL(194, 2291, 4, 0),
QT_MOC_LITERAL(195, 2296, 17, 0),
QT_MOC_LITERAL(196, 2314, 4, 0),
QT_MOC_LITERAL(197, 2319, 27, 0),
QT_MOC_LITERAL(198, 2347, 7, 0),
QT_MOC_LITERAL(199, 2355, 26, 0),
QT_MOC_LITERAL(200, 2382, 37, 0),
QT_MOC_LITERAL(201, 2420, 36, 0),
QT_MOC_LITERAL(202, 2457, 23, 0)
    },
    "COMEquipment::EquipmentNz2230\0Interface 1\0IEquipmentSJJ\0exception\0\0code\0"
    "source\0disc\0help\0propertyChanged\0name\0signal\0argc\0argv\0ClearWarning\0Close\0"
    "ConnectSet\0sysPortName\0stdName\0stdPortName\0isSignalSet\0ControlVoteage\0open\0"
    "percent\0gear\0EnVFall_Start\0upTime\0fullTime\0downTime\0lowTime\0lowValue\0testTimes\0"
    "fallType\0fallNum\0higva\0cl\0Equals\0obj\0GetHashCode\0GetMonitor\0GetPhaseDegree\0"
    "ptpNo\0capacitive\0direction\0phase\0GetRangeIb\0curri\0GetRangeUbI\0curru\0"
    "GetStemCTheory\0u\0I\0GetStemCTheoryStr\0GetType\0mscorlib::_Type*\0Power_ON\0"
    "setPhaseOrder\0phic\0f\0voltage\0current\0wiringMode\0workPhase\0SendComAA\0"
    "SendCommChkEnd\0SendCommChkSet\0SendCommF\0fltF\0SendCommHarmonicContent\0uContents\0"
    "iContents\0SendCommHarmonicSet\0harmonic\0SendCommI\0bolNotAuto\0SendCommIabc\0"
    "fltIa\0fltIb\0fltIc\0SendCommOpen\0bOpen\0SendCommP\0fltP\0SendCommPabc\0fltPa\0fltPb\0"
    "fltPc\0SendCommRangeNo\0SendCommSpeedUI\0seconds\0SendCommSpeedUI_2\0uspeed\0ispeed\0"
    "SendCommU\0SendCommUIDownRapid\0SendCommUabc\0fltUa\0fltUb\0fltUc\0SendCommVoltageDropBreak\0"
    "breakTimes\0voltagePercentage\0dropTimes\0recoveryTimes\0SendCommVoltageDropBreak_2\0"
    "dropMode\0paras\0SendCommfs\0value\0SendWantStemC\0openCOM\0commport\0ToString\0"
    "COMEquipment::EquipmentSd2000\0COMEquipment::EquipmentWc\0IEquipmentWC\0"
    "CheckRemoteKnifeSwitch\0meterCount\0inttell\0ConnectEquipment\0wcPortname\0"
    "Relay_EnVFall_Start\0meterIndexs\0abc\0intervalMillisecond\0ioMillisecond\0"
    "repeatsCount\0wAutoShutdown\0emno\0meterStatus\0wGet_Send_Base_Info\0set485Chanels\0"
    "dlt\0baudrate\0gaddress\0delaytimes\0delaytimes2\0wGet_Send_Base_Info_2\0"
    "wSendBegin_End_YaoCe\0status\0wSendCommChkPBegin\0wSendCommChkPEnd\0wSendCommChkType\0"
    "wSendCommEMC\0meterC\0currfcoe\0wSendCommEMCAll\0wSendCommEMCAll_2\0wSendCommEMC_2\0"
    "wSendCommEMC_3\0wSendCommFcoe\0fcoe\0wSendCommFcoeNeedt\0second\0wSendCommN\0chkR\0"
    "wSendCommPulseType\0wSendCommSTEMC\0stemc\0wSendCommSTEMC_2\0wSendCommarr_ReadERR\0"
    "curremno\0wSendCommarr_ReadPulse\0wSendCommarr_ZZ\0wSendCtrolVoltageOnandOff\0"
    "openOrclose\0wSendMKSignal\0wSendMPulsef\0chanel\0mcycle\0mcNums\0wSendOpen485Chanels\0"
    "setConnector\0wSendOpen485Chanels_2\0firstChanel\0wSendOpen485Chanels_3\0open485\0"
    "wSendOpenConnect\0wSendOpenPulseChanel\0wSendReUCheck\0wSendReadBaseInfoData\0"
    "wSendReadTransData\0wSendSwitchTml485Channel\0wSendSwitchWeakTmnlChannel\0"
    "wSendTransData\0arr\0wSendUCheck\0chku\0wSendVouttoUpandDown\0dOtime\0num\0"
    "wSendWorkModel\0wSend_Get_YaoCe\0wSendandReadTransData\0time\0wc_readdatereturn\0"
    "enmo\0COMEquipment::IEquipmentSJJ\0control\0COMEquipment::IEquipmentWC\0"
    "COMEquipment::IIndustrialControlPower\0COMEquipment::IndustrialControlPower\0"
    "IIndustrialControlPower\0"
};
#undef QT_MOC_LITERAL

static const uint qt_meta_data_COMEquipment__EquipmentNz2230[] = {

 // content:
    7, // revision
    0,  // classname
    1, 14, // classinfo
    41, 16, // methods
    1, 438, // properties
    0, 0, // enums/sets
    0, 0, // constructors
    0, // flags
    3, // signal count

 // classinfo: key, value
    1, 2, 

// signal: name, argc, parameters, tag, flags
    3, 4, 221, 4, 5,
    9, 1, 230, 4, 5,
    11, 3, 233, 4, 5,

// slot: name, argc, parameters, tag, flags
    14, 0, 240, 4, 9,
    15, 0, 241, 4, 9,
    16, 4, 242, 4, 9,
    21, 2, 251, 4, 11,
    21, 3, 256, 4, 9,
    25, 10, 263, 4, 9,
    36, 1, 284, 4, 9,
    38, 0, 287, 4, 9,
    39, 0, 288, 4, 9,
    40, 4, 289, 4, 9,
    45, 1, 298, 4, 9,
    47, 1, 301, 4, 9,
    49, 2, 304, 4, 9,
    52, 2, 309, 4, 9,
    53, 0, 314, 4, 9,
    55, 10, 315, 4, 9,
    63, 0, 336, 4, 9,
    64, 0, 337, 4, 9,
    65, 2, 338, 4, 9,
    66, 1, 343, 4, 9,
    68, 2, 346, 4, 9,
    71, 1, 351, 4, 9,
    73, 4, 354, 4, 9,
    75, 3, 363, 4, 9,
    79, 1, 370, 4, 9,
    81, 2, 373, 4, 9,
    83, 3, 378, 4, 9,
    87, 2, 385, 4, 9,
    88, 1, 390, 4, 9,
    90, 2, 393, 4, 9,
    93, 4, 398, 4, 9,
    94, 0, 407, 4, 9,
    95, 3, 408, 4, 9,
    99, 4, 415, 4, 9,
    104, 3, 424, 4, 9,
    107, 1, 431, 4, 9,
    109, 0, 434, 4, 9,
    110, 1, 435, 4, 9,

// signal: parameters
    QMetaType::Void, QMetaType::Int, QMetaType::QString, QMetaType::QString, QMetaType::QString, 5, 6, 7, 8,
    QMetaType::Void, QMetaType::QString, 10,
    QMetaType::Void, QMetaType::QString, QMetaType::Int, QMetaType::VoidStar, 10, 12, 13,

// slot: parameters
    QMetaType::Bool,
    QMetaType::Void,
    QMetaType::QString, QMetaType::QString, QMetaType::QString, QMetaType::QString, QMetaType::Bool, 17, 18, 19, 20,
    QMetaType::Bool, QMetaType::Bool, QMetaType::Int, 22, 23,
    QMetaType::Bool, QMetaType::Bool, QMetaType::Int, QMetaType::QString, 22, 23, 24,
    QMetaType::Bool, QMetaType::Double, QMetaType::Double, QMetaType::Double, QMetaType::Double, QMetaType::Double, QMetaType::Int, QMetaType::Int, QMetaType::Int, QMetaType::Double, QMetaType::UInt, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35,
    QMetaType::Bool, QMetaType::QVariant, 37,
    QMetaType::Int,
    QMetaType::QString,
    QMetaType::Double, QMetaType::UInt, QMetaType::QString, QMetaType::UInt, QMetaType::UInt, 41, 42, 43, 44,
    QMetaType::Double, QMetaType::Double, 46,
    QMetaType::Double, QMetaType::Double, 48,
    QMetaType::LongLong, QMetaType::Double, QMetaType::Double, 50, 51,
    QMetaType::QString, QMetaType::Double, QMetaType::Double, 50, 51,
    0x80000000 | 54,
    QMetaType::Bool, QMetaType::Bool, QMetaType::Double, QMetaType::Double, QMetaType::QString, QMetaType::Double, QMetaType::Double, QMetaType::Double, QMetaType::UInt, QMetaType::UInt, QMetaType::UInt, 56, 50, 51, 57, 58, 59, 60, 61, 62, 41,
    QMetaType::Bool,
    QMetaType::Bool,
    QMetaType::Bool, QMetaType::Bool, QMetaType::UInt, 56, 41,
    QMetaType::Bool, QMetaType::Double, 67,
    QMetaType::Bool, QMetaType::QString, QMetaType::QString, 69, 70,
    QMetaType::Bool, QMetaType::UInt, 72,
    QMetaType::Bool, QMetaType::Double, QMetaType::UInt, QMetaType::UInt, QMetaType::Bool, 60, 61, 62, 74,
    QMetaType::Bool, QMetaType::Double, QMetaType::Double, QMetaType::Double, 76, 77, 78,
    QMetaType::Bool, QMetaType::Bool, 80,
    QMetaType::Bool, QMetaType::Double, QMetaType::UInt, 82, 62,
    QMetaType::Bool, QMetaType::Double, QMetaType::Double, QMetaType::Double, 84, 85, 86,
    QMetaType::Bool, QMetaType::Double, QMetaType::Double, 59, 60,
    QMetaType::Bool, QMetaType::Int, 89,
    QMetaType::Bool, QMetaType::Int, QMetaType::Int, 91, 92,
    QMetaType::Bool, QMetaType::Double, QMetaType::UInt, QMetaType::UInt, QMetaType::Bool, 59, 61, 62, 74,
    QMetaType::Void,
    QMetaType::Bool, QMetaType::Double, QMetaType::Double, QMetaType::Double, 96, 97, 98,
    QMetaType::Bool, QMetaType::Int, QMetaType::Int, QMetaType::Double, QMetaType::Double, 100, 101, 102, 103,
    QMetaType::Bool, QMetaType::Int, QMetaType::LongLong, QMetaType::QStringList, 105, 102, 106,
    QMetaType::Bool, QMetaType::Int, 108,
    QMetaType::LongLong,
    QMetaType::Void, QMetaType::QString, 111,

 // properties: name, type, flags
    112, QMetaType::QString, 0x0a005001, 		 // QString ToString

    0 // eod
};

const QMetaObject EquipmentNz2230::staticMetaObject = {
{ &QObject::staticMetaObject,
qt_meta_stringdata_all.data,
qt_meta_data_COMEquipment__EquipmentNz2230, 0, 0, 0 }
};

void *EquipmentNz2230::qt_metacast(const char *_clname)
{
    if (!_clname) return 0;
    if (!strcmp(_clname, "COMEquipment::EquipmentNz2230"))
        return static_cast<void*>(const_cast<EquipmentNz2230*>(this));
    return QAxObject::qt_metacast(_clname);
}
static const uint qt_meta_data_COMEquipment__EquipmentSd2000[] = {

 // content:
    7, // revision
    113,  // classname
    1, 14, // classinfo
    41, 16, // methods
    1, 438, // properties
    0, 0, // enums/sets
    0, 0, // constructors
    0, // flags
    3, // signal count

 // classinfo: key, value
    1, 2, 

// signal: name, argc, parameters, tag, flags
    3, 4, 221, 4, 5,
    9, 1, 230, 4, 5,
    11, 3, 233, 4, 5,

// slot: name, argc, parameters, tag, flags
    14, 0, 240, 4, 9,
    15, 0, 241, 4, 9,
    16, 4, 242, 4, 9,
    21, 2, 251, 4, 11,
    21, 3, 256, 4, 9,
    25, 10, 263, 4, 9,
    36, 1, 284, 4, 9,
    38, 0, 287, 4, 9,
    39, 0, 288, 4, 9,
    40, 4, 289, 4, 9,
    45, 1, 298, 4, 9,
    47, 1, 301, 4, 9,
    49, 2, 304, 4, 9,
    52, 2, 309, 4, 9,
    53, 0, 314, 4, 9,
    55, 10, 315, 4, 9,
    63, 0, 336, 4, 9,
    64, 0, 337, 4, 9,
    65, 2, 338, 4, 9,
    66, 1, 343, 4, 9,
    68, 2, 346, 4, 9,
    71, 1, 351, 4, 9,
    73, 4, 354, 4, 9,
    75, 3, 363, 4, 9,
    79, 1, 370, 4, 9,
    81, 2, 373, 4, 9,
    83, 3, 378, 4, 9,
    87, 2, 385, 4, 9,
    88, 1, 390, 4, 9,
    90, 2, 393, 4, 9,
    93, 4, 398, 4, 9,
    94, 0, 407, 4, 9,
    95, 3, 408, 4, 9,
    99, 4, 415, 4, 9,
    104, 3, 424, 4, 9,
    107, 1, 431, 4, 9,
    109, 0, 434, 4, 9,
    110, 1, 435, 4, 9,

// signal: parameters
    QMetaType::Void, QMetaType::Int, QMetaType::QString, QMetaType::QString, QMetaType::QString, 5, 6, 7, 8,
    QMetaType::Void, QMetaType::QString, 10,
    QMetaType::Void, QMetaType::QString, QMetaType::Int, QMetaType::VoidStar, 10, 12, 13,

// slot: parameters
    QMetaType::Bool,
    QMetaType::Void,
    QMetaType::QString, QMetaType::QString, QMetaType::QString, QMetaType::QString, QMetaType::Bool, 17, 18, 19, 20,
    QMetaType::Bool, QMetaType::Bool, QMetaType::Int, 22, 23,
    QMetaType::Bool, QMetaType::Bool, QMetaType::Int, QMetaType::QString, 22, 23, 24,
    QMetaType::Bool, QMetaType::Double, QMetaType::Double, QMetaType::Double, QMetaType::Double, QMetaType::Double, QMetaType::Int, QMetaType::Int, QMetaType::Int, QMetaType::Double, QMetaType::UInt, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35,
    QMetaType::Bool, QMetaType::QVariant, 37,
    QMetaType::Int,
    QMetaType::QString,
    QMetaType::Double, QMetaType::UInt, QMetaType::QString, QMetaType::UInt, QMetaType::UInt, 41, 42, 43, 44,
    QMetaType::Double, QMetaType::Double, 46,
    QMetaType::Double, QMetaType::Double, 48,
    QMetaType::LongLong, QMetaType::Double, QMetaType::Double, 50, 51,
    QMetaType::QString, QMetaType::Double, QMetaType::Double, 50, 51,
    0x80000000 | 54,
    QMetaType::Bool, QMetaType::Bool, QMetaType::Double, QMetaType::Double, QMetaType::QString, QMetaType::Double, QMetaType::Double, QMetaType::Double, QMetaType::UInt, QMetaType::UInt, QMetaType::UInt, 56, 50, 51, 57, 58, 59, 60, 61, 62, 41,
    QMetaType::Bool,
    QMetaType::Bool,
    QMetaType::Bool, QMetaType::Bool, QMetaType::UInt, 56, 41,
    QMetaType::Bool, QMetaType::Double, 67,
    QMetaType::Bool, QMetaType::QString, QMetaType::QString, 69, 70,
    QMetaType::Bool, QMetaType::UInt, 72,
    QMetaType::Bool, QMetaType::Double, QMetaType::UInt, QMetaType::UInt, QMetaType::Bool, 60, 61, 62, 74,
    QMetaType::Bool, QMetaType::Double, QMetaType::Double, QMetaType::Double, 76, 77, 78,
    QMetaType::Bool, QMetaType::Bool, 80,
    QMetaType::Bool, QMetaType::Double, QMetaType::UInt, 82, 62,
    QMetaType::Bool, QMetaType::Double, QMetaType::Double, QMetaType::Double, 84, 85, 86,
    QMetaType::Bool, QMetaType::Double, QMetaType::Double, 59, 60,
    QMetaType::Bool, QMetaType::Int, 89,
    QMetaType::Bool, QMetaType::Int, QMetaType::Int, 91, 92,
    QMetaType::Bool, QMetaType::Double, QMetaType::UInt, QMetaType::UInt, QMetaType::Bool, 59, 61, 62, 74,
    QMetaType::Void,
    QMetaType::Bool, QMetaType::Double, QMetaType::Double, QMetaType::Double, 96, 97, 98,
    QMetaType::Bool, QMetaType::Int, QMetaType::Int, QMetaType::Double, QMetaType::Double, 100, 101, 102, 103,
    QMetaType::Bool, QMetaType::Int, QMetaType::LongLong, QMetaType::QStringList, 105, 102, 106,
    QMetaType::Bool, QMetaType::Int, 108,
    QMetaType::LongLong,
    QMetaType::Void, QMetaType::QString, 111,

 // properties: name, type, flags
    112, QMetaType::QString, 0x0a005001, 		 // QString ToString

    0 // eod
};

const QMetaObject EquipmentSd2000::staticMetaObject = {
{ &QObject::staticMetaObject,
qt_meta_stringdata_all.data,
qt_meta_data_COMEquipment__EquipmentSd2000, 0, 0, 0 }
};

void *EquipmentSd2000::qt_metacast(const char *_clname)
{
    if (!_clname) return 0;
    if (!strcmp(_clname, "COMEquipment::EquipmentSd2000"))
        return static_cast<void*>(const_cast<EquipmentSd2000*>(this));
    return QAxObject::qt_metacast(_clname);
}
static const uint qt_meta_data_COMEquipment__EquipmentWc[] = {

 // content:
    7, // revision
    114,  // classname
    1, 14, // classinfo
    51, 16, // methods
    1, 508, // properties
    0, 0, // enums/sets
    0, 0, // constructors
    0, // flags
    3, // signal count

 // classinfo: key, value
    1, 115, 

// signal: name, argc, parameters, tag, flags
    3, 4, 271, 4, 5,
    9, 1, 280, 4, 5,
    11, 3, 283, 4, 5,

// slot: name, argc, parameters, tag, flags
    116, 2, 290, 4, 9,
    15, 0, 295, 4, 9,
    119, 1, 296, 4, 9,
    36, 1, 299, 4, 9,
    38, 0, 302, 4, 9,
    53, 0, 303, 4, 9,
    121, 5, 304, 4, 9,
    127, 2, 315, 4, 9,
    130, 7, 320, 4, 9,
    137, 5, 335, 4, 9,
    138, 2, 346, 4, 9,
    140, 0, 351, 4, 9,
    141, 0, 352, 4, 9,
    142, 1, 353, 4, 9,
    143, 2, 356, 4, 9,
    146, 2, 361, 4, 9,
    147, 2, 366, 4, 9,
    148, 1, 371, 4, 9,
    149, 1, 374, 4, 9,
    150, 1, 377, 4, 9,
    152, 1, 380, 4, 9,
    154, 1, 383, 4, 9,
    156, 1, 386, 4, 9,
    157, 1, 389, 4, 9,
    159, 1, 392, 4, 9,
    160, 1, 395, 4, 9,
    162, 1, 398, 4, 9,
    163, 1, 401, 4, 9,
    164, 2, 404, 4, 9,
    166, 2, 409, 4, 9,
    167, 4, 414, 4, 9,
    171, 1, 423, 4, 9,
    173, 1, 426, 4, 9,
    175, 2, 429, 4, 9,
    177, 1, 434, 4, 9,
    178, 2, 437, 4, 9,
    179, 2, 442, 4, 9,
    180, 2, 447, 4, 9,
    181, 2, 452, 4, 9,
    182, 2, 457, 4, 9,
    183, 2, 462, 4, 9,
    184, 3, 467, 4, 9,
    186, 2, 474, 4, 9,
    188, 4, 479, 4, 9,
    191, 2, 488, 4, 9,
    192, 1, 493, 4, 9,
    193, 4, 496, 4, 9,
    195, 1, 505, 4, 9,

// signal: parameters
    QMetaType::Void, QMetaType::Int, QMetaType::QString, QMetaType::QString, QMetaType::QString, 5, 6, 7, 8,
    QMetaType::Void, QMetaType::QString, 10,
    QMetaType::Void, QMetaType::QString, QMetaType::Int, QMetaType::VoidStar, 10, 12, 13,

// slot: parameters
    QMetaType::QString, QMetaType::Int, QMetaType::UInt, 117, 118,
    QMetaType::Void,
    QMetaType::QString, QMetaType::QString, 120,
    QMetaType::Bool, QMetaType::QVariant, 37,
    QMetaType::Int,
    0x80000000 | 54,
    QMetaType::Bool, QMetaType::QString, QMetaType::Int, QMetaType::Int, QMetaType::Int, QMetaType::Int, 122, 123, 124, 125, 126,
    QMetaType::Void, QMetaType::Int, QMetaType::UInt, 128, 129,
    QMetaType::Bool, QMetaType::Int, QMetaType::Int, QMetaType::Int, QMetaType::Int, QMetaType::QString, QMetaType::Int, QMetaType::Int, 131, 128, 132, 133, 134, 135, 136,
    QMetaType::Bool, QMetaType::Int, QMetaType::Int, QMetaType::Int, QMetaType::QString, QMetaType::QString, 131, 128, 132, 133, 134,
    QMetaType::Void, QMetaType::UInt, QMetaType::Int, 128, 139,
    QMetaType::Void,
    QMetaType::Bool,
    QMetaType::Bool, QMetaType::Int, 108,
    QMetaType::Void, QMetaType::QVariantList, QMetaType::Int, 144, 145,
    QMetaType::Void, QMetaType::LongLong, QMetaType::Int, 144, 145,
    QMetaType::Void, QMetaType::QString, QMetaType::Int, 144, 145,
    QMetaType::Void, QMetaType::QVariantList, 144,
    QMetaType::Void, QMetaType::QStringList, 144,
    QMetaType::Void, QMetaType::Double, 151,
    QMetaType::Void, QMetaType::Int, 153,
    QMetaType::Void, QMetaType::UInt, 155,
    QMetaType::Void, QMetaType::Bool, 43,
    QMetaType::Void, QMetaType::LongLong, 158,
    QMetaType::Void, QMetaType::QString, 158,
    QMetaType::QString, QMetaType::UInt, 161,
    QMetaType::QString, QMetaType::UInt, 161,
    QMetaType::QString, QMetaType::UInt, 161,
    QMetaType::Bool, QMetaType::UInt, QMetaType::Int, 161, 165,
    QMetaType::Void, QMetaType::UInt, QMetaType::Int, 128, 139,
    QMetaType::Void, QMetaType::UInt, QMetaType::Int, QMetaType::Int, QMetaType::Int, 128, 168, 169, 170,
    QMetaType::Bool, QMetaType::Int, 172,
    QMetaType::Void, QMetaType::Bool, 174,
    QMetaType::Bool, QMetaType::UInt, QMetaType::Bool, 161, 176,
    QMetaType::Bool, QMetaType::UInt, 128,
    QMetaType::Bool, QMetaType::UInt, QMetaType::UInt, 128, 168,
    QMetaType::Bool, QMetaType::Int, QMetaType::UInt, 128, 118,
    QMetaType::QByteArray, QMetaType::Int, QMetaType::Int, 128, 131,
    QMetaType::QByteArray, QMetaType::Int, QMetaType::Int, 128, 131,
    QMetaType::Void, QMetaType::UInt, QMetaType::Int, 128, 139,
    QMetaType::Bool, QMetaType::UInt, QMetaType::Int, 128, 139,
    QMetaType::Bool, QMetaType::Int, QMetaType::QByteArray, QMetaType::Int, 128, 185, 131,
    QMetaType::Bool, QMetaType::Int, QMetaType::Int, 128, 187,
    QMetaType::Bool, QMetaType::QString, QMetaType::Double, QMetaType::Double, QMetaType::Int, 122, 26, 189, 190,
    QMetaType::Void, QMetaType::UInt, QMetaType::Int, 128, 139,
    QMetaType::QString, QMetaType::UInt, 128,
    QMetaType::QByteArray, QMetaType::Int, QMetaType::QByteArray, QMetaType::Int, QMetaType::Int, 128, 185, 131, 194,
    QMetaType::QVariantList, QMetaType::UInt, 196,

 // properties: name, type, flags
    112, QMetaType::QString, 0x0a005001, 		 // QString ToString

    0 // eod
};

const QMetaObject EquipmentWc::staticMetaObject = {
{ &QObject::staticMetaObject,
qt_meta_stringdata_all.data,
qt_meta_data_COMEquipment__EquipmentWc, 0, 0, 0 }
};

void *EquipmentWc::qt_metacast(const char *_clname)
{
    if (!_clname) return 0;
    if (!strcmp(_clname, "COMEquipment::EquipmentWc"))
        return static_cast<void*>(const_cast<EquipmentWc*>(this));
    return QAxObject::qt_metacast(_clname);
}
static const uint qt_meta_data_COMEquipment__IEquipmentSJJ[] = {

 // content:
    7, // revision
    197,  // classname
    0, 0, // classinfo
    38, 14, // methods
    1, 416, // properties
    0, 0, // enums/sets
    0, 0, // constructors
    0, // flags
    3, // signal count

// signal: name, argc, parameters, tag, flags
    3, 4, 204, 4, 5,
    9, 1, 213, 4, 5,
    11, 3, 216, 4, 5,

// slot: name, argc, parameters, tag, flags
    14, 0, 223, 4, 9,
    15, 0, 224, 4, 9,
    16, 4, 225, 4, 9,
    21, 2, 234, 4, 11,
    21, 3, 239, 4, 9,
    25, 10, 246, 4, 9,
    39, 0, 267, 4, 9,
    40, 4, 268, 4, 9,
    45, 1, 277, 4, 9,
    47, 1, 280, 4, 9,
    49, 2, 283, 4, 9,
    52, 2, 288, 4, 9,
    55, 10, 293, 4, 9,
    63, 0, 314, 4, 9,
    64, 0, 315, 4, 9,
    65, 2, 316, 4, 9,
    66, 1, 321, 4, 9,
    68, 2, 324, 4, 9,
    71, 1, 329, 4, 9,
    73, 4, 332, 4, 9,
    75, 3, 341, 4, 9,
    79, 1, 348, 4, 9,
    81, 2, 351, 4, 9,
    83, 3, 356, 4, 9,
    87, 2, 363, 4, 9,
    88, 1, 368, 4, 9,
    90, 2, 371, 4, 9,
    93, 4, 376, 4, 9,
    94, 0, 385, 4, 9,
    95, 3, 386, 4, 9,
    99, 4, 393, 4, 9,
    104, 3, 402, 4, 9,
    107, 1, 409, 4, 9,
    109, 0, 412, 4, 9,
    110, 1, 413, 4, 9,

// signal: parameters
    QMetaType::Void, QMetaType::Int, QMetaType::QString, QMetaType::QString, QMetaType::QString, 5, 6, 7, 8,
    QMetaType::Void, QMetaType::QString, 10,
    QMetaType::Void, QMetaType::QString, QMetaType::Int, QMetaType::VoidStar, 10, 12, 13,

// slot: parameters
    QMetaType::Bool,
    QMetaType::Void,
    QMetaType::QString, QMetaType::QString, QMetaType::QString, QMetaType::QString, QMetaType::Bool, 17, 18, 19, 20,
    QMetaType::Bool, QMetaType::Bool, QMetaType::Int, 22, 23,
    QMetaType::Bool, QMetaType::Bool, QMetaType::Int, QMetaType::QString, 22, 23, 24,
    QMetaType::Bool, QMetaType::Double, QMetaType::Double, QMetaType::Double, QMetaType::Double, QMetaType::Double, QMetaType::Int, QMetaType::Int, QMetaType::Int, QMetaType::Double, QMetaType::UInt, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35,
    QMetaType::QString,
    QMetaType::Double, QMetaType::UInt, QMetaType::QString, QMetaType::UInt, QMetaType::UInt, 41, 42, 43, 44,
    QMetaType::Double, QMetaType::Double, 46,
    QMetaType::Double, QMetaType::Double, 48,
    QMetaType::LongLong, QMetaType::Double, QMetaType::Double, 50, 51,
    QMetaType::QString, QMetaType::Double, QMetaType::Double, 50, 51,
    QMetaType::Bool, QMetaType::Bool, QMetaType::Double, QMetaType::Double, QMetaType::QString, QMetaType::Double, QMetaType::Double, QMetaType::Double, QMetaType::UInt, QMetaType::UInt, QMetaType::UInt, 56, 50, 51, 57, 58, 59, 60, 61, 62, 41,
    QMetaType::Bool,
    QMetaType::Bool,
    QMetaType::Bool, QMetaType::Bool, QMetaType::UInt, 56, 41,
    QMetaType::Bool, QMetaType::Double, 67,
    QMetaType::Bool, QMetaType::QString, QMetaType::QString, 69, 70,
    QMetaType::Bool, QMetaType::UInt, 72,
    QMetaType::Bool, QMetaType::Double, QMetaType::UInt, QMetaType::UInt, QMetaType::Bool, 60, 61, 62, 74,
    QMetaType::Bool, QMetaType::Double, QMetaType::Double, QMetaType::Double, 76, 77, 78,
    QMetaType::Bool, QMetaType::Bool, 80,
    QMetaType::Bool, QMetaType::Double, QMetaType::UInt, 82, 62,
    QMetaType::Bool, QMetaType::Double, QMetaType::Double, QMetaType::Double, 84, 85, 86,
    QMetaType::Bool, QMetaType::Double, QMetaType::Double, 59, 60,
    QMetaType::Bool, QMetaType::Int, 89,
    QMetaType::Bool, QMetaType::Int, QMetaType::Int, 91, 92,
    QMetaType::Bool, QMetaType::Double, QMetaType::UInt, QMetaType::UInt, QMetaType::Bool, 59, 61, 62, 74,
    QMetaType::Void,
    QMetaType::Bool, QMetaType::Double, QMetaType::Double, QMetaType::Double, 96, 97, 98,
    QMetaType::Bool, QMetaType::Int, QMetaType::Int, QMetaType::Double, QMetaType::Double, 100, 101, 102, 103,
    QMetaType::Bool, QMetaType::Int, QMetaType::LongLong, QMetaType::QStringList, 105, 102, 106,
    QMetaType::Bool, QMetaType::Int, 108,
    QMetaType::LongLong,
    QMetaType::Void, QMetaType::QString, 111,

 // properties: name, type, flags
    198, QMetaType::QString, 0x0a055003, 		 // QString control

    0 // eod
};

const QMetaObject IEquipmentSJJ::staticMetaObject = {
{ &QObject::staticMetaObject,
qt_meta_stringdata_all.data,
qt_meta_data_COMEquipment__IEquipmentSJJ, 0, 0, 0 }
};

void *IEquipmentSJJ::qt_metacast(const char *_clname)
{
    if (!_clname) return 0;
    if (!strcmp(_clname, "COMEquipment::IEquipmentSJJ"))
        return static_cast<void*>(const_cast<IEquipmentSJJ*>(this));
    return QAxObject::qt_metacast(_clname);
}
static const uint qt_meta_data_COMEquipment__IEquipmentWC[] = {

 // content:
    7, // revision
    199,  // classname
    0, 0, // classinfo
    48, 14, // methods
    1, 486, // properties
    0, 0, // enums/sets
    0, 0, // constructors
    0, // flags
    3, // signal count

// signal: name, argc, parameters, tag, flags
    3, 4, 254, 4, 5,
    9, 1, 263, 4, 5,
    11, 3, 266, 4, 5,

// slot: name, argc, parameters, tag, flags
    116, 2, 273, 4, 9,
    15, 0, 278, 4, 9,
    119, 1, 279, 4, 9,
    121, 5, 282, 4, 9,
    127, 2, 293, 4, 9,
    130, 7, 298, 4, 9,
    137, 5, 313, 4, 9,
    138, 2, 324, 4, 9,
    140, 0, 329, 4, 9,
    141, 0, 330, 4, 9,
    142, 1, 331, 4, 9,
    143, 2, 334, 4, 9,
    146, 2, 339, 4, 9,
    147, 2, 344, 4, 9,
    148, 1, 349, 4, 9,
    149, 1, 352, 4, 9,
    150, 1, 355, 4, 9,
    152, 1, 358, 4, 9,
    154, 1, 361, 4, 9,
    156, 1, 364, 4, 9,
    157, 1, 367, 4, 9,
    159, 1, 370, 4, 9,
    160, 1, 373, 4, 9,
    162, 1, 376, 4, 9,
    163, 1, 379, 4, 9,
    164, 2, 382, 4, 9,
    166, 2, 387, 4, 9,
    167, 4, 392, 4, 9,
    171, 1, 401, 4, 9,
    173, 1, 404, 4, 9,
    175, 2, 407, 4, 9,
    177, 1, 412, 4, 9,
    178, 2, 415, 4, 9,
    179, 2, 420, 4, 9,
    180, 2, 425, 4, 9,
    181, 2, 430, 4, 9,
    182, 2, 435, 4, 9,
    183, 2, 440, 4, 9,
    184, 3, 445, 4, 9,
    186, 2, 452, 4, 9,
    188, 4, 457, 4, 9,
    191, 2, 466, 4, 9,
    192, 1, 471, 4, 9,
    193, 4, 474, 4, 9,
    195, 1, 483, 4, 9,

// signal: parameters
    QMetaType::Void, QMetaType::Int, QMetaType::QString, QMetaType::QString, QMetaType::QString, 5, 6, 7, 8,
    QMetaType::Void, QMetaType::QString, 10,
    QMetaType::Void, QMetaType::QString, QMetaType::Int, QMetaType::VoidStar, 10, 12, 13,

// slot: parameters
    QMetaType::QString, QMetaType::Int, QMetaType::UInt, 117, 118,
    QMetaType::Void,
    QMetaType::QString, QMetaType::QString, 120,
    QMetaType::Bool, QMetaType::QString, QMetaType::Int, QMetaType::Int, QMetaType::Int, QMetaType::Int, 122, 123, 124, 125, 126,
    QMetaType::Void, QMetaType::Int, QMetaType::UInt, 128, 129,
    QMetaType::Bool, QMetaType::Int, QMetaType::Int, QMetaType::Int, QMetaType::Int, QMetaType::QString, QMetaType::Int, QMetaType::Int, 131, 128, 132, 133, 134, 135, 136,
    QMetaType::Bool, QMetaType::Int, QMetaType::Int, QMetaType::Int, QMetaType::QString, QMetaType::QString, 131, 128, 132, 133, 134,
    QMetaType::Void, QMetaType::UInt, QMetaType::Int, 128, 139,
    QMetaType::Void,
    QMetaType::Bool,
    QMetaType::Bool, QMetaType::Int, 108,
    QMetaType::Void, QMetaType::QVariantList, QMetaType::Int, 144, 145,
    QMetaType::Void, QMetaType::LongLong, QMetaType::Int, 144, 145,
    QMetaType::Void, QMetaType::QString, QMetaType::Int, 144, 145,
    QMetaType::Void, QMetaType::QVariantList, 144,
    QMetaType::Void, QMetaType::QStringList, 144,
    QMetaType::Void, QMetaType::Double, 151,
    QMetaType::Void, QMetaType::Int, 153,
    QMetaType::Void, QMetaType::UInt, 155,
    QMetaType::Void, QMetaType::Bool, 43,
    QMetaType::Void, QMetaType::LongLong, 158,
    QMetaType::Void, QMetaType::QString, 158,
    QMetaType::QString, QMetaType::UInt, 161,
    QMetaType::QString, QMetaType::UInt, 161,
    QMetaType::QString, QMetaType::UInt, 161,
    QMetaType::Bool, QMetaType::UInt, QMetaType::Int, 161, 165,
    QMetaType::Void, QMetaType::UInt, QMetaType::Int, 128, 139,
    QMetaType::Void, QMetaType::UInt, QMetaType::Int, QMetaType::Int, QMetaType::Int, 128, 168, 169, 170,
    QMetaType::Bool, QMetaType::Int, 172,
    QMetaType::Void, QMetaType::Bool, 174,
    QMetaType::Bool, QMetaType::UInt, QMetaType::Bool, 161, 176,
    QMetaType::Bool, QMetaType::UInt, 128,
    QMetaType::Bool, QMetaType::UInt, QMetaType::UInt, 128, 168,
    QMetaType::Bool, QMetaType::Int, QMetaType::UInt, 128, 118,
    QMetaType::QByteArray, QMetaType::Int, QMetaType::Int, 128, 131,
    QMetaType::QByteArray, QMetaType::Int, QMetaType::Int, 128, 131,
    QMetaType::Void, QMetaType::UInt, QMetaType::Int, 128, 139,
    QMetaType::Bool, QMetaType::UInt, QMetaType::Int, 128, 139,
    QMetaType::Bool, QMetaType::Int, QMetaType::QByteArray, QMetaType::Int, 128, 185, 131,
    QMetaType::Bool, QMetaType::Int, QMetaType::Int, 128, 187,
    QMetaType::Bool, QMetaType::QString, QMetaType::Double, QMetaType::Double, QMetaType::Int, 122, 26, 189, 190,
    QMetaType::Void, QMetaType::UInt, QMetaType::Int, 128, 139,
    QMetaType::QString, QMetaType::UInt, 128,
    QMetaType::QByteArray, QMetaType::Int, QMetaType::QByteArray, QMetaType::Int, QMetaType::Int, 128, 185, 131, 194,
    QMetaType::QVariantList, QMetaType::UInt, 196,

 // properties: name, type, flags
    198, QMetaType::QString, 0x0a055003, 		 // QString control

    0 // eod
};

const QMetaObject IEquipmentWC::staticMetaObject = {
{ &QObject::staticMetaObject,
qt_meta_stringdata_all.data,
qt_meta_data_COMEquipment__IEquipmentWC, 0, 0, 0 }
};

void *IEquipmentWC::qt_metacast(const char *_clname)
{
    if (!_clname) return 0;
    if (!strcmp(_clname, "COMEquipment::IEquipmentWC"))
        return static_cast<void*>(const_cast<IEquipmentWC*>(this));
    return QAxObject::qt_metacast(_clname);
}
static const uint qt_meta_data_COMEquipment__IIndustrialControlPower[] = {

 // content:
    7, // revision
    200,  // classname
    0, 0, // classinfo
    5, 14, // methods
    1, 66, // properties
    0, 0, // enums/sets
    0, 0, // constructors
    0, // flags
    3, // signal count

// signal: name, argc, parameters, tag, flags
    3, 4, 39, 4, 5,
    9, 1, 48, 4, 5,
    11, 3, 51, 4, 5,

// slot: name, argc, parameters, tag, flags
    21, 1, 58, 4, 11,
    21, 2, 61, 4, 9,

// signal: parameters
    QMetaType::Void, QMetaType::Int, QMetaType::QString, QMetaType::QString, QMetaType::QString, 5, 6, 7, 8,
    QMetaType::Void, QMetaType::QString, 10,
    QMetaType::Void, QMetaType::QString, QMetaType::Int, QMetaType::VoidStar, 10, 12, 13,

// slot: parameters
    QMetaType::Bool, QMetaType::Bool, 22,
    QMetaType::Bool, QMetaType::Bool, QMetaType::QString, 22, 24,

 // properties: name, type, flags
    198, QMetaType::QString, 0x0a055003, 		 // QString control

    0 // eod
};

const QMetaObject IIndustrialControlPower::staticMetaObject = {
{ &QObject::staticMetaObject,
qt_meta_stringdata_all.data,
qt_meta_data_COMEquipment__IIndustrialControlPower, 0, 0, 0 }
};

void *IIndustrialControlPower::qt_metacast(const char *_clname)
{
    if (!_clname) return 0;
    if (!strcmp(_clname, "COMEquipment::IIndustrialControlPower"))
        return static_cast<void*>(const_cast<IIndustrialControlPower*>(this));
    return QAxObject::qt_metacast(_clname);
}
static const uint qt_meta_data_COMEquipment__IndustrialControlPower[] = {

 // content:
    7, // revision
    201,  // classname
    1, 14, // classinfo
    8, 16, // methods
    1, 88, // properties
    0, 0, // enums/sets
    0, 0, // constructors
    0, // flags
    3, // signal count

 // classinfo: key, value
    1, 202, 

// signal: name, argc, parameters, tag, flags
    3, 4, 56, 4, 5,
    9, 1, 65, 4, 5,
    11, 3, 68, 4, 5,

// slot: name, argc, parameters, tag, flags
    21, 1, 75, 4, 11,
    21, 2, 78, 4, 9,
    36, 1, 83, 4, 9,
    38, 0, 86, 4, 9,
    53, 0, 87, 4, 9,

// signal: parameters
    QMetaType::Void, QMetaType::Int, QMetaType::QString, QMetaType::QString, QMetaType::QString, 5, 6, 7, 8,
    QMetaType::Void, QMetaType::QString, 10,
    QMetaType::Void, QMetaType::QString, QMetaType::Int, QMetaType::VoidStar, 10, 12, 13,

// slot: parameters
    QMetaType::Bool, QMetaType::Bool, 22,
    QMetaType::Bool, QMetaType::Bool, QMetaType::QString, 22, 24,
    QMetaType::Bool, QMetaType::QVariant, 37,
    QMetaType::Int,
    0x80000000 | 54,

 // properties: name, type, flags
    112, QMetaType::QString, 0x0a005001, 		 // QString ToString

    0 // eod
};

const QMetaObject IndustrialControlPower::staticMetaObject = {
{ &QObject::staticMetaObject,
qt_meta_stringdata_all.data,
qt_meta_data_COMEquipment__IndustrialControlPower, 0, 0, 0 }
};

void *IndustrialControlPower::qt_metacast(const char *_clname)
{
    if (!_clname) return 0;
    if (!strcmp(_clname, "COMEquipment::IndustrialControlPower"))
        return static_cast<void*>(const_cast<IndustrialControlPower*>(this));
    return QAxObject::qt_metacast(_clname);
}


﻿#include "copenclosecapdialog.h"
#include "ui_copenclosecapdialog.h"

COpenCloseCapDialog::COpenCloseCapDialog(QWidget *parent) :
    QDialog(parent),
    ui(new Ui::COpenCloseCapDialog)
{
    ui->setupUi(this);
    setWindowFlags(windowFlags() & ~Qt::WindowCloseButtonHint);

}

COpenCloseCapDialog::~COpenCloseCapDialog()
{
    delete ui;
}

void COpenCloseCapDialog::on_pushButton_clicked()
{
    emit(on_allFinish());
    hide();
}

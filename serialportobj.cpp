#include "serialportobj.h"

SerialPortObj::SerialPortObj(QObject *parent) : QObject(parent)
{

}

SerialPortObj::~SerialPortObj()
{
    qDebug ()<<"destroy SerialPortObj";
    if (m_pSerialPort){
        if (m_pSerialPort->isOpen ())
            m_pSerialPort->close ();
        delete m_pSerialPort;
        m_pSerialPort = NULL;
    }
}

void SerialPortObj::init()
{

    m_pSerialPort = new QSerialPort(m_serialName);

    connect (m_pSerialPort,SIGNAL(readyRead()),this,SLOT(slotReadSerialPortData()));

    m_pSerialPort->flush();
    m_pSerialPort->setPortName("COM53");
    m_pSerialPort->setBaudRate(115200);
    m_pSerialPort->setPortName(m_serialName);


    m_pSerialPort->setDataBits(QSerialPort::Data8);

    m_pSerialPort->setStopBits(QSerialPort::OneStop);

    m_pSerialPort->setParity(QSerialPort::NoParity);

    m_pSerialPort->setFlowControl(QSerialPort::NoFlowControl);
    m_pSerialPort->setReadBufferSize(0);


    if (m_pSerialPort->open (QIODevice::ReadWrite)){
        qDebug ()<<"open serial port ok!";
    }
    else
    {
        qDebug ()<<"open serial port failed!"<<m_pSerialPort->errorString ();
    }
}


void SerialPortObj::slotStartInit()
{
    qDebug ()<<"SerialPortObj thread id:"<<QThread::currentThreadId ();
    init();
}

void SerialPortObj::slotReadSerialPortData()
{
    if (m_pSerialPort->bytesAvailable () <= 0)
        return ;
    QByteArray tempBuf = m_pSerialPort->readAll ();

    emit signSendSerialPortData (tempBuf);
}

void SerialPortObj::slotWriteSerialPort(const QByteArray &buffData)
{
    if (!buffData.isEmpty ())
        m_pSerialPort->write (buffData);
}

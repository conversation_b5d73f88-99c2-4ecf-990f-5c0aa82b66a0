﻿#ifndef TCPSERVER_H
#define TCPSERVER_H

#include <QObject>
#include <QTcpServer>
#include <QTcpSocket>
class TcpServer : public QTcpServer
{
    Q_OBJECT
public:
    explicit TcpServer(QObject *parent = nullptr);
    void startServer(quint16 port);

    void writeDate(QByteArray);

    bool getState();            // 获取连接状态

    void closesocket();

protected:
    void incomingConnection(qintptr socketDescriptor) override;


Q_SIGNALS:
    void readData(QByteArray);

private slots:
    void onReadyRead();
    void onDisconnected();

private:

    QTcpSocket *m_psocket;

};
#endif // TCPSERVER_H

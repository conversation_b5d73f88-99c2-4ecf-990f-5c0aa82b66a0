<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>cTestBatchInfo</class>
 <widget class="QWidget" name="cTestBatchInfo">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>545</width>
    <height>509</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Form</string>
  </property>
  <layout class="QGridLayout" name="gridLayout">
   <item row="3" column="0">
    <widget class="QTableWidget" name="tableWidget"/>
   </item>
   <item row="2" column="0">
    <layout class="QHBoxLayout" name="horizontalLayout" stretch="0,1,1,1">
     <item>
      <widget class="QLabel" name="label">
       <property name="text">
        <string>批次选择：</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QComboBox" name="comboBox">
       <property name="styleSheet">
        <string notr="true"/>
       </property>
      </widget>
     </item>
     <item>
      <spacer name="horizontalSpacer">
       <property name="orientation">
        <enum>Qt::Horizontal</enum>
       </property>
       <property name="sizeHint" stdset="0">
        <size>
         <width>40</width>
         <height>20</height>
        </size>
       </property>
      </spacer>
     </item>
     <item>
      <widget class="QPushButton" name="exportBtn">
       <property name="styleSheet">
        <string notr="true">    background-color: #0072C6;
    color: white;
    border-style: outset;
    border-width: 2px;
    border-radius: 10px;
    border-color: beige;
    font-size: 26px;
    padding: 6px;</string>
       </property>
       <property name="text">
        <string>导出信息</string>
       </property>
      </widget>
     </item>
    </layout>
   </item>
   <item row="0" column="0">
    <spacer name="horizontalSpacer_2">
     <property name="orientation">
      <enum>Qt::Horizontal</enum>
     </property>
     <property name="sizeHint" stdset="0">
      <size>
       <width>40</width>
       <height>20</height>
      </size>
     </property>
    </spacer>
   </item>
   <item row="1" column="0">
    <spacer name="horizontalSpacer_3">
     <property name="orientation">
      <enum>Qt::Horizontal</enum>
     </property>
     <property name="sizeHint" stdset="0">
      <size>
       <width>40</width>
       <height>20</height>
      </size>
     </property>
    </spacer>
   </item>
  </layout>
 </widget>
 <resources/>
 <connections/>
</ui>

<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>CGorgeWidget</class>
 <widget class="QWidget" name="CGorgeWidget">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>1017</width>
    <height>684</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Form</string>
  </property>
  <layout class="QGridLayout" name="gridLayout_5">
   <property name="leftMargin">
    <number>0</number>
   </property>
   <property name="topMargin">
    <number>0</number>
   </property>
   <property name="rightMargin">
    <number>0</number>
   </property>
   <property name="bottomMargin">
    <number>0</number>
   </property>
   <item row="0" column="0">
    <widget class="QTabWidget" name="tabWidget">
     <property name="styleSheet">
      <string notr="true">QTabBar::tab {min-width:220px;min-hight:160px;color: black;border: 2px solid;border-top-left-radius: 10px;border-top-right-radius: 10px;padding:5px;font: 13pt;}
QTabBar::tab:!selected {margin-top: 5px;color: rgb(0, 0, 0);}
QTabBar::tab:selected {background-color: rgb(0, 255, 255);}
background-color: rgb(255, 85, 255);</string>
     </property>
     <property name="currentIndex">
      <number>2</number>
     </property>
     <widget class="QWidget" name="typeC">
      <attribute name="title">
       <string> typeC-台体串口</string>
      </attribute>
      <layout class="QGridLayout" name="gridLayout_6">
       <item row="0" column="0">
        <layout class="QVBoxLayout" name="verticalLayout_3">
         <item>
          <layout class="QHBoxLayout" name="horizontalLayout_2">
           <item>
            <widget class="QGroupBox" name="groupBox">
             <property name="font">
              <font>
               <pointsize>12</pointsize>
              </font>
             </property>
             <property name="title">
              <string>SCU type-C串口设置</string>
             </property>
             <layout class="QGridLayout" name="gridLayout_2">
              <item row="0" column="0">
               <layout class="QVBoxLayout" name="verticalLayout">
                <item>
                 <layout class="QHBoxLayout" name="horizontalLayout">
                  <item>
                   <widget class="QPushButton" name="lxckhBtn">
                    <property name="styleSheet">
                     <string notr="true">    QPushButton {background-color:#0089ff ;
    color: white;
    border-style: outset;
    border-width: 2px;
    border-radius: 10px;
    border-color: beige;
    font-size: 26px;
    padding: 6px;}

QPushButton:hover {background-color: #0072C6;         
                     }</string>
                    </property>
                    <property name="text">
                     <string>连续串口号</string>
                    </property>
                   </widget>
                  </item>
                  <item>
                   <spacer name="horizontalSpacer_2">
                    <property name="orientation">
                     <enum>Qt::Horizontal</enum>
                    </property>
                    <property name="sizeHint" stdset="0">
                     <size>
                      <width>38</width>
                      <height>20</height>
                     </size>
                    </property>
                   </spacer>
                  </item>
                  <item>
                   <widget class="QPushButton" name="pushButton">
                    <property name="styleSheet">
                     <string notr="true">    QPushButton {background-color:#0089ff ;
    color: white;
    border-style: outset;
    border-width: 2px;
    border-radius: 10px;
    border-color: beige;
    font-size: 26px;
    padding: 6px;}

QPushButton:hover {background-color: #0072C6;         
                     }
</string>
                    </property>
                    <property name="text">
                     <string>保存</string>
                    </property>
                   </widget>
                  </item>
                 </layout>
                </item>
                <item>
                 <widget class="QTableWidget" name="tableWidget"/>
                </item>
               </layout>
              </item>
             </layout>
            </widget>
           </item>
           <item>
            <widget class="QGroupBox" name="groupBox_3">
             <property name="font">
              <font>
               <pointsize>12</pointsize>
              </font>
             </property>
             <property name="title">
              <string>SCU type-C串口查看</string>
             </property>
             <layout class="QGridLayout" name="gridLayout">
              <item row="0" column="0">
               <widget class="QTableWidget" name="tableWidget_2"/>
              </item>
             </layout>
            </widget>
           </item>
          </layout>
         </item>
         <item>
          <layout class="QHBoxLayout" name="horizontalLayout_4">
           <item>
            <widget class="QGroupBox" name="groupBox_2">
             <property name="font">
              <font>
               <pointsize>12</pointsize>
              </font>
             </property>
             <property name="title">
              <string>台体串口设置</string>
             </property>
             <layout class="QGridLayout" name="gridLayout_4">
              <item row="0" column="0">
               <layout class="QVBoxLayout" name="verticalLayout_2">
                <item>
                 <layout class="QHBoxLayout" name="horizontalLayout_3">
                  <item>
                   <spacer name="horizontalSpacer">
                    <property name="orientation">
                     <enum>Qt::Horizontal</enum>
                    </property>
                    <property name="sizeHint" stdset="0">
                     <size>
                      <width>40</width>
                      <height>20</height>
                     </size>
                    </property>
                   </spacer>
                  </item>
                  <item>
                   <widget class="QPushButton" name="pushButton_2">
                    <property name="styleSheet">
                     <string notr="true">    QPushButton {background-color:#0089ff ;
    color: white;
    border-style: outset;
    border-width: 2px;
    border-radius: 10px;
    border-color: beige;
    font-size: 26px;
    padding: 6px;}

QPushButton:hover {background-color: #0072C6;         
                     }
</string>
                    </property>
                    <property name="text">
                     <string>保存</string>
                    </property>
                   </widget>
                  </item>
                 </layout>
                </item>
                <item>
                 <widget class="QTableWidget" name="systemTW">
                  <property name="sizePolicy">
                   <sizepolicy hsizetype="Expanding" vsizetype="Preferred">
                    <horstretch>0</horstretch>
                    <verstretch>0</verstretch>
                   </sizepolicy>
                  </property>
                  <property name="minimumSize">
                   <size>
                    <width>0</width>
                    <height>200</height>
                   </size>
                  </property>
                  <property name="maximumSize">
                   <size>
                    <width>16777215</width>
                    <height>200</height>
                   </size>
                  </property>
                 </widget>
                </item>
               </layout>
              </item>
             </layout>
            </widget>
           </item>
           <item>
            <widget class="QGroupBox" name="groupBox_4">
             <property name="font">
              <font>
               <pointsize>12</pointsize>
              </font>
             </property>
             <property name="title">
              <string>台体串口查看</string>
             </property>
             <layout class="QGridLayout" name="gridLayout_3">
              <item row="0" column="0">
               <widget class="QTableWidget" name="systemTW_2">
                <property name="sizePolicy">
                 <sizepolicy hsizetype="Expanding" vsizetype="Preferred">
                  <horstretch>0</horstretch>
                  <verstretch>0</verstretch>
                 </sizepolicy>
                </property>
                <property name="minimumSize">
                 <size>
                  <width>0</width>
                  <height>0</height>
                 </size>
                </property>
                <property name="maximumSize">
                 <size>
                  <width>16777215</width>
                  <height>16777215</height>
                 </size>
                </property>
               </widget>
              </item>
             </layout>
            </widget>
           </item>
          </layout>
         </item>
        </layout>
       </item>
      </layout>
     </widget>
     <widget class="QWidget" name="RS485">
      <attribute name="title">
       <string>RS485串口</string>
      </attribute>
      <layout class="QGridLayout" name="gridLayout_7">
       <item row="0" column="0">
        <layout class="QHBoxLayout" name="horizontalLayout_6">
         <item>
          <layout class="QVBoxLayout" name="verticalLayout_4">
           <item>
            <layout class="QHBoxLayout" name="horizontalLayout_5">
             <item>
              <widget class="QPushButton" name="pushButton_4">
               <property name="styleSheet">
                <string notr="true">    QPushButton {background-color:#0089ff ;
    color: white;
    border-style: outset;
    border-width: 2px;
    border-radius: 10px;
    border-color: beige;
    font-size: 26px;
    padding: 6px;}

QPushButton:hover {background-color: #0072C6;         
                     }</string>
               </property>
               <property name="text">
                <string> 连续串口号</string>
               </property>
              </widget>
             </item>
             <item>
              <spacer name="horizontalSpacer_3">
               <property name="orientation">
                <enum>Qt::Horizontal</enum>
               </property>
               <property name="sizeHint" stdset="0">
                <size>
                 <width>40</width>
                 <height>20</height>
                </size>
               </property>
              </spacer>
             </item>
             <item>
              <widget class="QPushButton" name="pushButton_3">
               <property name="styleSheet">
                <string notr="true">    QPushButton {background-color:#0089ff ;
    color: white;
    border-style: outset;
    border-width: 2px;
    border-radius: 10px;
    border-color: beige;
    font-size: 26px;
    padding: 6px;}

QPushButton:hover {background-color: #0072C6;         
                     }</string>
               </property>
               <property name="text">
                <string>保存</string>
               </property>
              </widget>
             </item>
            </layout>
           </item>
           <item>
            <widget class="QTableWidget" name="rs485TW"/>
           </item>
          </layout>
         </item>
         <item>
          <widget class="QTableWidget" name="rs485TW_2"/>
         </item>
        </layout>
       </item>
      </layout>
     </widget>
     <widget class="QWidget" name="tab">
      <attribute name="title">
       <string>其他</string>
      </attribute>
      <layout class="QGridLayout" name="gridLayout_8">
       <item row="0" column="0">
        <layout class="QHBoxLayout" name="horizontalLayout_7">
         <item>
          <widget class="QLabel" name="label">
           <property name="font">
            <font>
             <pointsize>12</pointsize>
            </font>
           </property>
           <property name="text">
            <string>终端证书存放路径   </string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QLineEdit" name="lineEdit">
           <property name="sizePolicy">
            <sizepolicy hsizetype="Expanding" vsizetype="Preferred">
             <horstretch>0</horstretch>
             <verstretch>0</verstretch>
            </sizepolicy>
           </property>
           <property name="font">
            <font>
             <pointsize>12</pointsize>
            </font>
           </property>
           <property name="readOnly">
            <bool>true</bool>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QPushButton" name="pushButton_5">
           <property name="styleSheet">
            <string notr="true">    QPushButton {background-color:#0089ff ;
    color: white;
    border-style: outset;
    border-width: 2px;
    border-radius: 10px;
    border-color: beige;
    font-size: 26px;
    padding: 6px;}

QPushButton:hover {background-color: #0072C6;         
                     }</string>
           </property>
           <property name="text">
            <string>浏览</string>
           </property>
          </widget>
         </item>
        </layout>
       </item>
       <item row="1" column="0">
        <layout class="QHBoxLayout" name="horizontalLayout_8">
         <item>
          <widget class="QLabel" name="label_2">
           <property name="font">
            <font>
             <pointsize>12</pointsize>
            </font>
           </property>
           <property name="text">
            <string>报告生成路径       </string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QLineEdit" name="lineEdit_2">
           <property name="sizePolicy">
            <sizepolicy hsizetype="Expanding" vsizetype="Preferred">
             <horstretch>0</horstretch>
             <verstretch>0</verstretch>
            </sizepolicy>
           </property>
           <property name="font">
            <font>
             <pointsize>12</pointsize>
            </font>
           </property>
           <property name="readOnly">
            <bool>true</bool>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QPushButton" name="pushButton_6">
           <property name="styleSheet">
            <string notr="true">    QPushButton {background-color:#0089ff ;
    color: white;
    border-style: outset;
    border-width: 2px;
    border-radius: 10px;
    border-color: beige;
    font-size: 26px;
    padding: 6px;}

QPushButton:hover {background-color: #0072C6;         
                     }</string>
           </property>
           <property name="text">
            <string>浏览</string>
           </property>
          </widget>
         </item>
        </layout>
       </item>
       <item row="2" column="0">
        <layout class="QHBoxLayout" name="horizontalLayout_9">
         <item>
          <widget class="QLabel" name="label_3">
           <property name="font">
            <font>
             <pointsize>12</pointsize>
            </font>
           </property>
           <property name="text">
            <string>License文件读取路径</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QLineEdit" name="lineEdit_3">
           <property name="sizePolicy">
            <sizepolicy hsizetype="Expanding" vsizetype="Preferred">
             <horstretch>0</horstretch>
             <verstretch>0</verstretch>
            </sizepolicy>
           </property>
           <property name="font">
            <font>
             <pointsize>12</pointsize>
            </font>
           </property>
           <property name="readOnly">
            <bool>true</bool>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QPushButton" name="pushButton_7">
           <property name="styleSheet">
            <string notr="true">    QPushButton {background-color:#0089ff ;
    color: white;
    border-style: outset;
    border-width: 2px;
    border-radius: 10px;
    border-color: beige;
    font-size: 26px;
    padding: 6px;}

QPushButton:hover {background-color: #0072C6;         
                     }</string>
           </property>
           <property name="text">
            <string>浏览</string>
           </property>
          </widget>
         </item>
        </layout>
       </item>
       <item row="3" column="0">
        <spacer name="verticalSpacer">
         <property name="orientation">
          <enum>Qt::Vertical</enum>
         </property>
         <property name="sizeHint" stdset="0">
          <size>
           <width>20</width>
           <height>530</height>
          </size>
         </property>
        </spacer>
       </item>
      </layout>
     </widget>
    </widget>
   </item>
  </layout>
 </widget>
 <resources/>
 <connections/>
</ui>

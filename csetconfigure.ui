<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>cSetConfigure</class>
 <widget class="QWidget" name="cSetConfigure">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>1038</width>
    <height>740</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Form</string>
  </property>
  <layout class="QGridLayout" name="gridLayout_2">
   <item row="0" column="0">
    <widget class="QTabWidget" name="tabWidget">
     <property name="currentIndex">
      <number>1</number>
     </property>
     <widget class="QWidget" name="tab">
      <attribute name="title">
       <string>检测项选择</string>
      </attribute>
      <layout class="QGridLayout" name="gridLayout">
       <item row="9" column="0">
        <widget class="QCheckBox" name="APPCheck">
         <property name="text">
          <string>安装APP </string>
         </property>
        </widget>
       </item>
       <item row="14" column="0">
        <spacer name="verticalSpacer">
         <property name="orientation">
          <enum>Qt::Vertical</enum>
         </property>
         <property name="sizeHint" stdset="0">
          <size>
           <width>20</width>
           <height>40</height>
          </size>
         </property>
        </spacer>
       </item>
       <item row="13" column="0">
        <widget class="QPushButton" name="pushButton_2">
         <property name="styleSheet">
          <string notr="true">    background-color: #0072C6;
    color: white;
    border-style: outset;
    border-width: 2px;
    border-radius: 10px;
    border-color: beige;
    font-size: 26px;
    padding: 6px;</string>
         </property>
         <property name="text">
          <string>确定</string>
         </property>
        </widget>
       </item>
       <item row="5" column="0">
        <widget class="QCheckBox" name="CompareCheck">
         <property name="text">
          <string>校验系统和版本号</string>
         </property>
        </widget>
       </item>
       <item row="1" column="0">
        <widget class="QCheckBox" name="AllCheckBox">
         <property name="text">
          <string>全选</string>
         </property>
        </widget>
       </item>
       <item row="11" column="0">
        <widget class="QCheckBox" name="RunningCheck">
         <property name="text">
          <string>APP运行状态检测</string>
         </property>
        </widget>
       </item>
       <item row="12" column="0">
        <widget class="QCheckBox" name="JBCheck">
         <property name="text">
          <string>校表</string>
         </property>
        </widget>
       </item>
       <item row="3" column="0">
        <widget class="QCheckBox" name="txzCheckBox">
         <property name="text">
          <string>通行证导入</string>
         </property>
        </widget>
       </item>
       <item row="4" column="0">
        <widget class="QCheckBox" name="LicenseCheckBox">
         <property name="text">
          <string>license文件</string>
         </property>
        </widget>
       </item>
       <item row="7" column="0">
        <widget class="QCheckBox" name="ClearCodeCheck">
         <property name="text">
          <string>清除蓝牙密码</string>
         </property>
        </widget>
       </item>
       <item row="2" column="0">
        <widget class="QCheckBox" name="updatadbCheckBox">
         <property name="text">
          <string>升级大包</string>
         </property>
        </widget>
       </item>
       <item row="8" column="0">
        <widget class="QCheckBox" name="ContainerCheck">
         <property name="text">
          <string>安装容器</string>
         </property>
        </widget>
       </item>
       <item row="6" column="0">
        <widget class="QCheckBox" name="ClearcheckBox">
         <property name="text">
          <string>清除多余文件</string>
         </property>
        </widget>
       </item>
       <item row="10" column="0">
        <widget class="QCheckBox" name="SetTimeCheck">
         <property name="text">
          <string>设备时间同步</string>
         </property>
        </widget>
       </item>
      </layout>
     </widget>
     <widget class="QWidget" name="widget">
      <attribute name="title">
       <string>检测配置</string>
      </attribute>
      <layout class="QGridLayout" name="gridLayout_3">
       <item row="3" column="1" colspan="5">
        <widget class="QLineEdit" name="lineEdit_11"/>
       </item>
       <item row="2" column="0" colspan="6">
        <widget class="Line" name="line_2">
         <property name="orientation">
          <enum>Qt::Horizontal</enum>
         </property>
        </widget>
       </item>
       <item row="0" column="1">
        <widget class="QComboBox" name="comboBox">
         <property name="editable">
          <bool>false</bool>
         </property>
        </widget>
       </item>
       <item row="4" column="1" colspan="5">
        <widget class="QLineEdit" name="lineEdit_12"/>
       </item>
       <item row="0" column="2">
        <widget class="QPushButton" name="pushButton_3">
         <property name="styleSheet">
          <string notr="true">    background-color: #0072C6;
    color: white;
    border-style: outset;
    border-width: 2px;
    border-radius: 10px;
    border-color: beige;
    font-size: 22px;
    padding: 6px;</string>
         </property>
         <property name="text">
          <string>新增</string>
         </property>
        </widget>
       </item>
       <item row="5" column="0" colspan="6">
        <widget class="Line" name="line_3">
         <property name="orientation">
          <enum>Qt::Horizontal</enum>
         </property>
        </widget>
       </item>
       <item row="0" column="3">
        <widget class="QPushButton" name="ChageBtn">
         <property name="styleSheet">
          <string notr="true">    background-color: #0072C6;
    color: white;
    border-style: outset;
    border-width: 2px;
    border-radius: 10px;
    border-color: beige;
    font-size: 22px;
    padding: 6px;</string>
         </property>
         <property name="text">
          <string>修改</string>
         </property>
        </widget>
       </item>
       <item row="3" column="0">
        <widget class="QPushButton" name="dbFileBtn">
         <property name="styleSheet">
          <string notr="true">    background-color: #0072C6;
    color: white;
    border-style: outset;
    border-width: 2px;
    border-radius: 10px;
    border-color: beige;
    font-size: 18px;
    padding: 6px;</string>
         </property>
         <property name="text">
          <string>选择大包安装包</string>
         </property>
        </widget>
       </item>
       <item row="0" column="0">
        <widget class="QLabel" name="label_3">
         <property name="text">
          <string>校验系统和版本：</string>
         </property>
        </widget>
       </item>
       <item row="0" column="4">
        <widget class="QPushButton" name="del">
         <property name="styleSheet">
          <string notr="true">    background-color: #0072C6;
    color: white;
    border-style: outset;
    border-width: 2px;
    border-radius: 10px;
    border-color: beige;
    font-size: 22px;
    padding: 6px;</string>
         </property>
         <property name="text">
          <string>删除</string>
         </property>
        </widget>
       </item>
       <item row="1" column="0" colspan="6">
        <layout class="QFormLayout" name="formLayout">
         <item row="2" column="0">
          <widget class="QLabel" name="label_4">
           <property name="text">
            <string>大包版本：</string>
           </property>
          </widget>
         </item>
         <item row="2" column="1">
          <widget class="QLineEdit" name="lineEdit">
           <property name="text">
            <string/>
           </property>
          </widget>
         </item>
         <item row="3" column="0">
          <widget class="QLabel" name="label_5">
           <property name="text">
            <string>大包版本：</string>
           </property>
          </widget>
         </item>
         <item row="3" column="1">
          <widget class="QLineEdit" name="lineEdit_2">
           <property name="text">
            <string/>
           </property>
          </widget>
         </item>
         <item row="4" column="0">
          <widget class="QLabel" name="label_6">
           <property name="text">
            <string>主控板版本</string>
           </property>
          </widget>
         </item>
         <item row="4" column="1">
          <widget class="QLineEdit" name="lineEdit_3"/>
         </item>
         <item row="5" column="0">
          <widget class="QLabel" name="label_7">
           <property name="text">
            <string>硬件版本：</string>
           </property>
          </widget>
         </item>
         <item row="5" column="1">
          <widget class="QLineEdit" name="lineEdit_4"/>
         </item>
         <item row="6" column="0">
          <widget class="QLabel" name="label_8">
           <property name="text">
            <string>内核版本：</string>
           </property>
          </widget>
         </item>
         <item row="6" column="1">
          <widget class="QLineEdit" name="lineEdit_5"/>
         </item>
         <item row="7" column="0">
          <widget class="QLabel" name="label_9">
           <property name="text">
            <string>备核版本/出厂主核版本：</string>
           </property>
          </widget>
         </item>
         <item row="7" column="1">
          <widget class="QLineEdit" name="lineEdit_6"/>
         </item>
         <item row="8" column="0">
          <widget class="QLabel" name="label_10">
           <property name="text">
            <string>容器版本：</string>
           </property>
          </widget>
         </item>
         <item row="8" column="1">
          <widget class="QLineEdit" name="lineEdit_7"/>
         </item>
         <item row="9" column="0">
          <widget class="QLabel" name="label_12">
           <property name="text">
            <string>关键系统文件版本：</string>
           </property>
          </widget>
         </item>
         <item row="9" column="1">
          <widget class="QLineEdit" name="lineEdit_9"/>
         </item>
         <item row="10" column="0">
          <widget class="QLabel" name="label_13">
           <property name="text">
            <string>操作系统版本：</string>
           </property>
          </widget>
         </item>
         <item row="10" column="1">
          <widget class="QLineEdit" name="lineEdit_10"/>
         </item>
        </layout>
       </item>
       <item row="4" column="0">
        <widget class="QPushButton" name="appFIleBtn">
         <property name="styleSheet">
          <string notr="true">    background-color: #0072C6;
    color: white;
    border-style: outset;
    border-width: 2px;
    border-radius: 10px;
    border-color: beige;
    font-size: 18px;
    padding: 6px;
selection-background-color: rgb(85, 255, 255);</string>
         </property>
         <property name="text">
          <string>选择APP安装包</string>
         </property>
        </widget>
       </item>
       <item row="0" column="5">
        <widget class="QPushButton" name="pushButton">
         <property name="styleSheet">
          <string notr="true">    background-color: #0072C6;
    color: white;
    border-style: outset;
    border-width: 2px;
    border-radius: 10px;
    border-color: beige;
    font-size: 22px;
    padding: 6px;</string>
         </property>
         <property name="text">
          <string>保存</string>
         </property>
        </widget>
       </item>
       <item row="8" column="0" colspan="2">
        <widget class="QLabel" name="label">
         <property name="text">
          <string/>
         </property>
        </widget>
       </item>
       <item row="7" column="0" colspan="6">
        <widget class="QTextEdit" name="textEdit"/>
       </item>
      </layout>
     </widget>
     <widget class="QWidget" name="tab_2">
      <attribute name="title">
       <string>容器和APP选择</string>
      </attribute>
      <layout class="QGridLayout" name="gridLayout_4">
       <item row="0" column="0">
        <widget class="QLabel" name="label_2">
         <property name="text">
          <string>容器外APP</string>
         </property>
        </widget>
       </item>
       <item row="2" column="0">
        <widget class="QTreeWidget" name="treeWidget">
         <column>
          <property name="text">
           <string>容器及APP选择</string>
          </property>
         </column>
        </widget>
       </item>
       <item row="1" column="0">
        <widget class="QCheckBox" name="IotCheckBox">
         <property name="text">
          <string>iotManager</string>
         </property>
         <property name="checked">
          <bool>true</bool>
         </property>
        </widget>
       </item>
      </layout>
     </widget>
     <widget class="QWidget" name="tab_3">
      <attribute name="title">
       <string>误差配置</string>
      </attribute>
      <layout class="QGridLayout" name="gridLayout_5">
       <item row="0" column="0">
        <layout class="QFormLayout" name="formLayout_2">
         <item row="0" column="0">
          <widget class="QLabel" name="label_14">
           <property name="text">
            <string>电流误差：%</string>
           </property>
          </widget>
         </item>
         <item row="0" column="1">
          <widget class="QLineEdit" name="dlEdit"/>
         </item>
         <item row="1" column="0">
          <widget class="QLabel" name="label_15">
           <property name="text">
            <string>电压误差：%</string>
           </property>
          </widget>
         </item>
         <item row="1" column="1">
          <widget class="QLineEdit" name="dyEdit"/>
         </item>
        </layout>
       </item>
       <item row="1" column="0">
        <widget class="QPushButton" name="wuchaBtn">
         <property name="styleSheet">
          <string notr="true">    background-color: #0072C6;
    color: white;
    border-style: outset;
    border-width: 2px;
    border-radius: 10px;
    border-color: beige;
    font-size: 26px;
    padding: 6px;</string>
         </property>
         <property name="text">
          <string>保存</string>
         </property>
        </widget>
       </item>
       <item row="2" column="0">
        <spacer name="verticalSpacer_2">
         <property name="orientation">
          <enum>Qt::Vertical</enum>
         </property>
         <property name="sizeHint" stdset="0">
          <size>
           <width>20</width>
           <height>40</height>
          </size>
         </property>
        </spacer>
       </item>
      </layout>
     </widget>
    </widget>
   </item>
  </layout>
 </widget>
 <resources/>
 <connections/>
</ui>

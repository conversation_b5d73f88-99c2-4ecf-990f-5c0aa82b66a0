#ifndef CSETCONFIGURE_H
#define CSETCONFIGURE_H

#include <QWidget>
#include<QCheckBox>
#include<QVector>
#include<QVBoxLayout>
#include<QTreeWidgetItem>



namespace Ui {
class cSetConfigure;
}

class cSetConfigure : public QWidget
{
    Q_OBJECT

public:
    explicit cSetConfigure(QWidget *parent = nullptr);
    ~cSetConfigure();


private:
    Ui::cSetConfigure *ui;
    std::vector<QCheckBox*> checkBoxs;


    std::map<QString, std::vector<QString> > m_AppMap;

    QStringList additem;

    void traverseTreeBFS(QTreeWidgetItem* rootItem);

public slots:
    void onslotsTestPc(QString s);
    void onslotsChageAPP(QString s);



private slots:
    void onCheckBoxAllStateChanged(int state);
    void on_pushButton_2_clicked();

    void on_pushButton_clicked();

    void on_comboBox_currentIndexChanged(const QString &arg1);
    void on_pushButton_3_clicked();
    void on_treeWidget_itemChanged(QTreeWidgetItem *item, int column);
    void on_dbFileBtn_clicked();

    void on_appFIleBtn_clicked();
    void on_ChageBtn_clicked();
    void on_del_clicked();
    void on_wuchaBtn_clicked();
};

#endif // CSETCONFIGURE_H

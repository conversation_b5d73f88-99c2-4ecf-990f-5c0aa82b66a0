﻿#ifndef CGWTXPZDIALOG_H
#define CGWTXPZDIALOG_H

#include <QDialog>
#include <QSettings>
#include <QTextCodec>
#include "msk_global.h"
#include <QMessageBox>
namespace Ui {
class CGwtxpzDialog;
}

class CGwtxpzDialog : public QDialog
{
    Q_OBJECT

public:
    explicit CGwtxpzDialog(QWidget *parent = nullptr);
    ~CGwtxpzDialog();
    void setFAID(int nFAID);
private:
    void queryCfg();

private slots:
    void on_ok_clicked();

    void on_del_clicked();

    void on_pushButton_clicked();
private:
    int m_nFAID;
    QString m_strName;
private:
    Ui::CGwtxpzDialog *ui;
};

#endif // CGWTXPZDIALOG_H

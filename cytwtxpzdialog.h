﻿#ifndef CYTWTXPZDIALOG_H
#define CYTWTXPZDIALOG_H

#include <QDialog>
#include "msk_global.h"

namespace Ui {
class CYtwtxpzDialog;
}

class CYtwtxpzDialog : public QDialog
{
    Q_OBJECT

public:
    explicit CYtwtxpzDialog(QWidget *parent = nullptr);
    ~CYtwtxpzDialog();
    void setKeyName(QString strName);

private slots:
    void on_addBtn_clicked();

    void on_delBtn_clicked();

    void on_okBtn_clicked();

private:
    Ui::CYtwtxpzDialog *ui;

    QString m_strKeyName;
};

#endif // CYTWTXPZDIALOG_H

﻿#include "csqlitedb.h"
#include <QDebug>

CSqLiteDB::CSqLiteDB()
{
    if(QSqlDatabase::contains("my_sql_connect"))
    {
        m_db = QSqlDatabase::database("my_sql_connect");
    }
    else
    {
        m_db = QSqlDatabase::addDatabase("QSQLITE", "my_sql_connect");
        m_db.setDatabaseName("scuoutcheck.db");
        m_db.setUserName("scu");
        m_db.setPassword("scucheck");
    }

    if(!m_db.open())
    {
        qDebug() << "Error: Failed to connect database." << m_db.lastError();
    }

}

void CSqLiteDB::closeDB()
{
    m_db.close();
}


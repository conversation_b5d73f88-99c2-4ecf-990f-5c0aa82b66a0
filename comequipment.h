/****************************************************************************
**
** Namespace COMEquipment generated by dumpcpp v5.12.10 using
** dumpcpp.exe COMEquipment.tlb
** from the type library COMEquipment.tlb
**
****************************************************************************/

#ifndef QAX_DUMPCPP_COMEQUIPMENT_H
#define QAX_DUMPCPP_COMEQUIPMENT_H

// Define this symbol to __declspec(dllexport) or __declspec(dllimport)
#ifndef COMEQUIPMENT_EXPORT
#define COMEQUIPMENT_EXPORT
#endif

#include <qaxobject.h>
#include <qaxwidget.h>
#include <qdatetime.h>
#include <qpixmap.h>

struct IDispatch;


// Referenced namespace
namespace mscorlib {
    class _Type;
}

Q_DECLARE_OPAQUE_POINTER(mscorlib::_Type*)

namespace COMEquipment {


class COMEQUIPMENT_EXPORT IEquipmentSJJ : public QAxObject
{
public:
    IEquipmentSJJ(IDispatch *subobject = 0, QAxObject *parent = 0)
    : QAxObject((IUnknown*)subobject, parent)
    {
        internalRelease();
    }

    /*
    Method ClearWarning
    */
    inline bool ClearWarning();

    /*
    Method Close
    */
    inline void Close();

    /*
    Method ConnectSet
    */
    inline QString ConnectSet(const QString& sysPortName, const QString& stdName, const QString& stdPortName, bool isSignalSet);

    /*
    Method ControlVoteage
    */
    inline bool ControlVoteage(bool open, int percent);

    /*
    Method ControlVoteage
    */
    inline bool ControlVoteage(bool open, int percent, const QString& gear);

    /*
    Method EnVFall_Start
    */
    inline bool EnVFall_Start(double upTime, double fullTime, double downTime, double lowTime, double lowValue, int testTimes, int fallType, int fallNum, double higva, uint cl);

    /*
    Method GetMonitor
    */
    inline QString GetMonitor();

    /*
    Method GetPhaseDegree
    */
    inline double GetPhaseDegree(uint ptpNo, const QString& capacitive, uint direction, uint phase);

    /*
    Method GetRangeIb
    */
    inline double GetRangeIb(double curri);

    /*
    Method GetRangeUbI
    */
    inline double GetRangeUbI(double curru);

    /*
    Method GetStemCTheory
    */
    inline qlonglong GetStemCTheory(double u, double I);

    /*
    Method GetStemCTheoryStr
    */
    inline QString GetStemCTheoryStr(double u, double I);

    /*
    Method Power_ON
    */
    inline bool Power_ON(bool setPhaseOrder, double u, double I, const QString& phic, double f, double voltage, double current, uint wiringMode, uint workPhase, uint ptpNo);

    /*
    Method SendComAA
    */
    inline bool SendComAA();

    /*
    Method SendCommChkEnd
    */
    inline bool SendCommChkEnd();

    /*
    Method SendCommChkSet
    */
    inline bool SendCommChkSet(bool setPhaseOrder, uint ptpNo);

    /*
    Method SendCommF
    */
    inline bool SendCommF(double fltF);

    /*
    Method SendCommHarmonicContent
    */
    inline bool SendCommHarmonicContent(const QString& uContents, const QString& iContents);

    /*
    Method SendCommHarmonicSet
    */
    inline bool SendCommHarmonicSet(uint harmonic);

    /*
    Method SendCommI
    */
    inline bool SendCommI(double current, uint wiringMode, uint workPhase, bool bolNotAuto);

    /*
    Method SendCommIabc
    */
    inline bool SendCommIabc(double fltIa, double fltIb, double fltIc);

    /*
    Method SendCommOpen
    */
    inline bool SendCommOpen(bool bOpen);

    /*
    Method SendCommP
    */
    inline bool SendCommP(double fltP, uint workPhase);

    /*
    Method SendCommPabc
    */
    inline bool SendCommPabc(double fltPa, double fltPb, double fltPc);

    /*
    Method SendCommRangeNo
    */
    inline bool SendCommRangeNo(double voltage, double current);

    /*
    Method SendCommSpeedUI
    */
    inline bool SendCommSpeedUI(int seconds);

    /*
    Method SendCommSpeedUI_2
    */
    inline bool SendCommSpeedUI_2(int uspeed, int ispeed);

    /*
    Method SendCommU
    */
    inline bool SendCommU(double voltage, uint wiringMode, uint workPhase, bool bolNotAuto);

    /*
    Method SendCommUIDownRapid
    */
    inline void SendCommUIDownRapid();

    /*
    Method SendCommUabc
    */
    inline bool SendCommUabc(double fltUa, double fltUb, double fltUc);

    /*
    Method SendCommVoltageDropBreak
    */
    inline bool SendCommVoltageDropBreak(int breakTimes, int voltagePercentage, double dropTimes, double recoveryTimes);

    /*
    Method SendCommVoltageDropBreak_2
    */
    inline bool SendCommVoltageDropBreak_2(int dropMode, qlonglong dropTimes, const QStringList& paras);

    /*
    Method SendCommfs
    */
    inline bool SendCommfs(int value);

    /*
    Method SendWantStemC
    */
    inline qlonglong SendWantStemC();

    /*
    Method openCOM
    */
    inline void openCOM(const QString& commport);

// meta object functions
    static const QMetaObject staticMetaObject;
    virtual const QMetaObject *metaObject() const { return &staticMetaObject; }
    virtual void *qt_metacast(const char *);
};

class COMEQUIPMENT_EXPORT IEquipmentWC : public QAxObject
{
public:
    IEquipmentWC(IDispatch *subobject = 0, QAxObject *parent = 0)
    : QAxObject((IUnknown*)subobject, parent)
    {
        internalRelease();
    }

    /*
    Method CheckRemoteKnifeSwitch
    */
    inline QString CheckRemoteKnifeSwitch(int meterCount, uint inttell);

    /*
    Method Close
    */
    inline void Close();

    /*
    Method ConnectEquipment
    */
    inline QString ConnectEquipment(const QString& wcPortname);

    /*
    Method Relay_EnVFall_Start
    */
    inline bool Relay_EnVFall_Start(const QString& meterIndexs, int abc, int intervalMillisecond, int ioMillisecond, int repeatsCount);

    /*
    Method wAutoShutdown
    */
    inline void wAutoShutdown(int emno, uint meterStatus);

    /*
    Method wGet_Send_Base_Info
    */
    inline bool wGet_Send_Base_Info(int set485Chanels, int emno, int dlt, int baudrate, const QString& gaddress, int delaytimes, int delaytimes2);

    /*
    Method wGet_Send_Base_Info_2
    */
    inline bool wGet_Send_Base_Info_2(int set485Chanels, int emno, int dlt, const QString& baudrate, const QString& gaddress);

    /*
    Method wSendBegin_End_YaoCe
    */
    inline void wSendBegin_End_YaoCe(uint emno, int status);

    /*
    Method wSendCommChkPBegin
    */
    inline void wSendCommChkPBegin();

    /*
    Method wSendCommChkPEnd
    */
    inline bool wSendCommChkPEnd();

    /*
    Method wSendCommChkType
    */
    inline bool wSendCommChkType(int value);

    /*
    Method wSendCommEMC
    */
    inline void wSendCommEMC(QVariantList meterC, int currfcoe);

    /*
    Method wSendCommEMCAll
    */
    inline void wSendCommEMCAll(qlonglong meterC, int currfcoe);

    /*
    Method wSendCommEMCAll_2
    */
    inline void wSendCommEMCAll_2(const QString& meterC, int currfcoe);

    /*
    Method wSendCommEMC_2
    */
    inline void wSendCommEMC_2(QVariantList meterC);

    /*
    Method wSendCommEMC_3
    */
    inline void wSendCommEMC_3(const QStringList& meterC);

    /*
    Method wSendCommFcoe
    */
    inline void wSendCommFcoe(double fcoe);

    /*
    Method wSendCommFcoeNeedt
    */
    inline void wSendCommFcoeNeedt(int second);

    /*
    Method wSendCommN
    */
    inline void wSendCommN(uint chkR);

    /*
    Method wSendCommPulseType
    */
    inline void wSendCommPulseType(bool direction);

    /*
    Method wSendCommSTEMC
    */
    inline void wSendCommSTEMC(qlonglong stemc);

    /*
    Method wSendCommSTEMC_2
    */
    inline void wSendCommSTEMC_2(const QString& stemc);

    /*
    Method wSendCommarr_ReadERR
    */
    inline QString wSendCommarr_ReadERR(uint curremno);

    /*
    Method wSendCommarr_ReadPulse
    */
    inline QString wSendCommarr_ReadPulse(uint curremno);

    /*
    Method wSendCommarr_ZZ
    */
    inline QString wSendCommarr_ZZ(uint curremno);

    /*
    Method wSendCtrolVoltageOnandOff
    */
    inline bool wSendCtrolVoltageOnandOff(uint curremno, int openOrclose);

    /*
    Method wSendMKSignal
    */
    inline void wSendMKSignal(uint emno, int status);

    /*
    Method wSendMPulsef
    */
    inline void wSendMPulsef(uint emno, int chanel, int mcycle, int mcNums);

    /*
    Method wSendOpen485Chanels
    */
    inline bool wSendOpen485Chanels(int setConnector);

    /*
    Method wSendOpen485Chanels_2
    */
    inline void wSendOpen485Chanels_2(bool firstChanel);

    /*
    Method wSendOpen485Chanels_3
    */
    inline bool wSendOpen485Chanels_3(uint curremno, bool open485);

    /*
    Method wSendOpenConnect
    */
    inline bool wSendOpenConnect(uint emno);

    /*
    Method wSendOpenPulseChanel
    */
    inline bool wSendOpenPulseChanel(uint emno, uint chanel);

    /*
    Method wSendReUCheck
    */
    inline bool wSendReUCheck(int emno, uint inttell);

    /*
    Method wSendReadBaseInfoData
    */
    inline QByteArray wSendReadBaseInfoData(int emno, int set485Chanels);

    /*
    Method wSendReadTransData
    */
    inline QByteArray wSendReadTransData(int emno, int set485Chanels);

    /*
    Method wSendSwitchTml485Channel
    */
    inline void wSendSwitchTml485Channel(uint emno, int status);

    /*
    Method wSendSwitchWeakTmnlChannel
    */
    inline bool wSendSwitchWeakTmnlChannel(uint emno, int status);

    /*
    Method wSendTransData
    */
    inline bool wSendTransData(int emno, const QByteArray& arr, int set485Chanels);

    /*
    Method wSendUCheck
    */
    inline bool wSendUCheck(int emno, int chku);

    /*
    Method wSendVouttoUpandDown
    */
    inline bool wSendVouttoUpandDown(const QString& meterIndexs, double upTime, double dOtime, int num);

    /*
    Method wSendWorkModel
    */
    inline void wSendWorkModel(uint emno, int status);

    /*
    Method wSend_Get_YaoCe
    */
    inline QString wSend_Get_YaoCe(uint emno);

    /*
    Method wSendandReadTransData
    */
    inline QByteArray wSendandReadTransData(int emno, const QByteArray& arr, int set485Chanels, int time);

    /*
    Method wc_readdatereturn
    */
    inline QVariantList wc_readdatereturn(uint enmo);

// meta object functions
    static const QMetaObject staticMetaObject;
    virtual const QMetaObject *metaObject() const { return &staticMetaObject; }
    virtual void *qt_metacast(const char *);
};

class COMEQUIPMENT_EXPORT IIndustrialControlPower : public QAxObject
{
public:
    IIndustrialControlPower(IDispatch *subobject = 0, QAxObject *parent = 0)
    : QAxObject((IUnknown*)subobject, parent)
    {
        internalRelease();
    }

    /*
    Method ControlVoteage
    */
    inline bool ControlVoteage(bool open);

    /*
    Method ControlVoteage
    */
    inline bool ControlVoteage(bool open, const QString& gear);

// meta object functions
    static const QMetaObject staticMetaObject;
    virtual const QMetaObject *metaObject() const { return &staticMetaObject; }
    virtual void *qt_metacast(const char *);
};

// Actual coclasses
class COMEQUIPMENT_EXPORT EquipmentNz2230 : public QAxObject
{
public:
    EquipmentNz2230(QObject *parent = 0)
    : QAxObject(parent)
    {
        setControl(QStringLiteral("{839e9de7-ecd3-4fee-8ca6-3ea2d3664552}"));
    }

    EquipmentNz2230(IEquipmentSJJ *iface)
    : QAxObject()
    {
        initializeFrom(iface);
        delete iface;
    }

    /*
    Property ToString
    */
    inline QString ToString() const; //Returns the value of ToString

    /*
    Method ClearWarning
    */
    inline bool ClearWarning();

    /*
    Method Close
    */
    inline void Close();

    /*
    Method ConnectSet
    */
    inline QString ConnectSet(const QString& sysPortName, const QString& stdName, const QString& stdPortName, bool isSignalSet);

    /*
    Method ControlVoteage
    */
    inline bool ControlVoteage(bool open, int percent);

    /*
    Method ControlVoteage
    */
    inline bool ControlVoteage(bool open, int percent, const QString& gear);

    /*
    Method EnVFall_Start
    */
    inline bool EnVFall_Start(double upTime, double fullTime, double downTime, double lowTime, double lowValue, int testTimes, int fallType, int fallNum, double higva, uint cl);

    /*
    Method Equals
    */
    inline bool Equals(const QVariant& obj);

    /*
    Method GetHashCode
    */
    inline int GetHashCode();

    /*
    Method GetMonitor
    */
    inline QString GetMonitor();

    /*
    Method GetPhaseDegree
    */
    inline double GetPhaseDegree(uint ptpNo, const QString& capacitive, uint direction, uint phase);

    /*
    Method GetRangeIb
    */
    inline double GetRangeIb(double curri);

    /*
    Method GetRangeUbI
    */
    inline double GetRangeUbI(double curru);

    /*
    Method GetStemCTheory
    */
    inline qlonglong GetStemCTheory(double u, double I);

    /*
    Method GetStemCTheoryStr
    */
    inline QString GetStemCTheoryStr(double u, double I);

    /*
    Method GetType
    */
    inline mscorlib::_Type* GetType();

    /*
    Method Power_ON
    */
    inline bool Power_ON(bool setPhaseOrder, double u, double I, const QString& phic, double f, double voltage, double current, uint wiringMode, uint workPhase, uint ptpNo);

    /*
    Method SendComAA
    */
    inline bool SendComAA();

    /*
    Method SendCommChkEnd
    */
    inline bool SendCommChkEnd();

    /*
    Method SendCommChkSet
    */
    inline bool SendCommChkSet(bool setPhaseOrder, uint ptpNo);

    /*
    Method SendCommF
    */
    inline bool SendCommF(double fltF);

    /*
    Method SendCommHarmonicContent
    */
    inline bool SendCommHarmonicContent(const QString& uContents, const QString& iContents);

    /*
    Method SendCommHarmonicSet
    */
    inline bool SendCommHarmonicSet(uint harmonic);

    /*
    Method SendCommI
    */
    inline bool SendCommI(double current, uint wiringMode, uint workPhase, bool bolNotAuto);

    /*
    Method SendCommIabc
    */
    inline bool SendCommIabc(double fltIa, double fltIb, double fltIc);

    /*
    Method SendCommOpen
    */
    inline bool SendCommOpen(bool bOpen);

    /*
    Method SendCommP
    */
    inline bool SendCommP(double fltP, uint workPhase);

    /*
    Method SendCommPabc
    */
    inline bool SendCommPabc(double fltPa, double fltPb, double fltPc);

    /*
    Method SendCommRangeNo
    */
    inline bool SendCommRangeNo(double voltage, double current);

    /*
    Method SendCommSpeedUI
    */
    inline bool SendCommSpeedUI(int seconds);

    /*
    Method SendCommSpeedUI_2
    */
    inline bool SendCommSpeedUI_2(int uspeed, int ispeed);

    /*
    Method SendCommU
    */
    inline bool SendCommU(double voltage, uint wiringMode, uint workPhase, bool bolNotAuto);

    /*
    Method SendCommUIDownRapid
    */
    inline void SendCommUIDownRapid();

    /*
    Method SendCommUabc
    */
    inline bool SendCommUabc(double fltUa, double fltUb, double fltUc);

    /*
    Method SendCommVoltageDropBreak
    */
    inline bool SendCommVoltageDropBreak(int breakTimes, int voltagePercentage, double dropTimes, double recoveryTimes);

    /*
    Method SendCommVoltageDropBreak_2
    */
    inline bool SendCommVoltageDropBreak_2(int dropMode, qlonglong dropTimes, const QStringList& paras);

    /*
    Method SendCommfs
    */
    inline bool SendCommfs(int value);

    /*
    Method SendWantStemC
    */
    inline qlonglong SendWantStemC();

    /*
    Method openCOM
    */
    inline void openCOM(const QString& commport);

// meta object functions
    static const QMetaObject staticMetaObject;
    virtual const QMetaObject *metaObject() const { return &staticMetaObject; }
    virtual void *qt_metacast(const char *);
};

class COMEQUIPMENT_EXPORT EquipmentSd2000 : public QAxObject
{
public:
    EquipmentSd2000(QObject *parent = 0)
    : QAxObject(parent)
    {
        setControl(QStringLiteral("{839e9de7-ecd3-4fee-8ca6-3ea2d3664553}"));
    }

    EquipmentSd2000(IEquipmentSJJ *iface)
    : QAxObject()
    {
        initializeFrom(iface);
        delete iface;
    }

    /*
    Property ToString
    */
    inline QString ToString() const; //Returns the value of ToString

    /*
    Method ClearWarning
    */
    inline bool ClearWarning();

    /*
    Method Close
    */
    inline void Close();

    /*
    Method ConnectSet
    */
    inline QString ConnectSet(const QString& sysPortName, const QString& stdName, const QString& stdPortName, bool isSignalSet);

    /*
    Method ControlVoteage
    */
    inline bool ControlVoteage(bool open, int percent);

    /*
    Method ControlVoteage
    */
    inline bool ControlVoteage(bool open, int percent, const QString& gear);

    /*
    Method EnVFall_Start
    */
    inline bool EnVFall_Start(double upTime, double fullTime, double downTime, double lowTime, double lowValue, int testTimes, int fallType, int fallNum, double higva, uint cl);

    /*
    Method Equals
    */
    inline bool Equals(const QVariant& obj);

    /*
    Method GetHashCode
    */
    inline int GetHashCode();

    /*
    Method GetMonitor
    */
    inline QString GetMonitor();

    /*
    Method GetPhaseDegree
    */
    inline double GetPhaseDegree(uint ptpNo, const QString& capacitive, uint direction, uint phase);

    /*
    Method GetRangeIb
    */
    inline double GetRangeIb(double curri);

    /*
    Method GetRangeUbI
    */
    inline double GetRangeUbI(double curru);

    /*
    Method GetStemCTheory
    */
    inline qlonglong GetStemCTheory(double u, double I);

    /*
    Method GetStemCTheoryStr
    */
    inline QString GetStemCTheoryStr(double u, double I);

    /*
    Method GetType
    */
    inline mscorlib::_Type* GetType();

    /*
    Method Power_ON
    */
    inline bool Power_ON(bool setPhaseOrder, double u, double I, const QString& phic, double f, double voltage, double current, uint wiringMode, uint workPhase, uint ptpNo);

    /*
    Method SendComAA
    */
    inline bool SendComAA();

    /*
    Method SendCommChkEnd
    */
    inline bool SendCommChkEnd();

    /*
    Method SendCommChkSet
    */
    inline bool SendCommChkSet(bool setPhaseOrder, uint ptpNo);

    /*
    Method SendCommF
    */
    inline bool SendCommF(double fltF);

    /*
    Method SendCommHarmonicContent
    */
    inline bool SendCommHarmonicContent(const QString& uContents, const QString& iContents);

    /*
    Method SendCommHarmonicSet
    */
    inline bool SendCommHarmonicSet(uint harmonic);

    /*
    Method SendCommI
    */
    inline bool SendCommI(double current, uint wiringMode, uint workPhase, bool bolNotAuto);

    /*
    Method SendCommIabc
    */
    inline bool SendCommIabc(double fltIa, double fltIb, double fltIc);

    /*
    Method SendCommOpen
    */
    inline bool SendCommOpen(bool bOpen);

    /*
    Method SendCommP
    */
    inline bool SendCommP(double fltP, uint workPhase);

    /*
    Method SendCommPabc
    */
    inline bool SendCommPabc(double fltPa, double fltPb, double fltPc);

    /*
    Method SendCommRangeNo
    */
    inline bool SendCommRangeNo(double voltage, double current);

    /*
    Method SendCommSpeedUI
    */
    inline bool SendCommSpeedUI(int seconds);

    /*
    Method SendCommSpeedUI_2
    */
    inline bool SendCommSpeedUI_2(int uspeed, int ispeed);

    /*
    Method SendCommU
    */
    inline bool SendCommU(double voltage, uint wiringMode, uint workPhase, bool bolNotAuto);

    /*
    Method SendCommUIDownRapid
    */
    inline void SendCommUIDownRapid();

    /*
    Method SendCommUabc
    */
    inline bool SendCommUabc(double fltUa, double fltUb, double fltUc);

    /*
    Method SendCommVoltageDropBreak
    */
    inline bool SendCommVoltageDropBreak(int breakTimes, int voltagePercentage, double dropTimes, double recoveryTimes);

    /*
    Method SendCommVoltageDropBreak_2
    */
    inline bool SendCommVoltageDropBreak_2(int dropMode, qlonglong dropTimes, const QStringList& paras);

    /*
    Method SendCommfs
    */
    inline bool SendCommfs(int value);

    /*
    Method SendWantStemC
    */
    inline qlonglong SendWantStemC();

    /*
    Method openCOM
    */
    inline void openCOM(const QString& commport);

// meta object functions
    static const QMetaObject staticMetaObject;
    virtual const QMetaObject *metaObject() const { return &staticMetaObject; }
    virtual void *qt_metacast(const char *);
};

class COMEQUIPMENT_EXPORT EquipmentWc : public QAxObject
{
public:
    EquipmentWc(QObject *parent = 0)
    : QAxObject(parent)
    {
        setControl(QStringLiteral("{fc555440-8fe7-475e-b5ea-bf8dea4b190a}"));
    }

    EquipmentWc(IEquipmentWC *iface)
    : QAxObject()
    {
        initializeFrom(iface);
        delete iface;
    }

    /*
    Property ToString
    */
    inline QString ToString() const; //Returns the value of ToString

    /*
    Method CheckRemoteKnifeSwitch
    */
    inline QString CheckRemoteKnifeSwitch(int meterCount, uint inttell);

    /*
    Method Close
    */
    inline void Close();

    /*
    Method ConnectEquipment
    */
    inline QString ConnectEquipment(const QString& wcPortname);

    /*
    Method Equals
    */
    inline bool Equals(const QVariant& obj);

    /*
    Method GetHashCode
    */
    inline int GetHashCode();

    /*
    Method GetType
    */
    inline mscorlib::_Type* GetType();

    /*
    Method Relay_EnVFall_Start
    */
    inline bool Relay_EnVFall_Start(const QString& meterIndexs, int abc, int intervalMillisecond, int ioMillisecond, int repeatsCount);

    /*
    Method wAutoShutdown
    */
    inline void wAutoShutdown(int emno, uint meterStatus);

    /*
    Method wGet_Send_Base_Info
    */
    inline bool wGet_Send_Base_Info(int set485Chanels, int emno, int dlt, int baudrate, const QString& gaddress, int delaytimes, int delaytimes2);

    /*
    Method wGet_Send_Base_Info_2
    */
    inline bool wGet_Send_Base_Info_2(int set485Chanels, int emno, int dlt, const QString& baudrate, const QString& gaddress);

    /*
    Method wSendBegin_End_YaoCe
    */
    inline void wSendBegin_End_YaoCe(uint emno, int status);

    /*
    Method wSendCommChkPBegin
    */
    inline void wSendCommChkPBegin();

    /*
    Method wSendCommChkPEnd
    */
    inline bool wSendCommChkPEnd();

    /*
    Method wSendCommChkType
    */
    inline bool wSendCommChkType(int value);

    /*
    Method wSendCommEMC
    */
    inline void wSendCommEMC(QVariantList meterC, int currfcoe);

    /*
    Method wSendCommEMCAll
    */
    inline void wSendCommEMCAll(qlonglong meterC, int currfcoe);

    /*
    Method wSendCommEMCAll_2
    */
    inline void wSendCommEMCAll_2(const QString& meterC, int currfcoe);

    /*
    Method wSendCommEMC_2
    */
    inline void wSendCommEMC_2(QVariantList meterC);

    /*
    Method wSendCommEMC_3
    */
    inline void wSendCommEMC_3(const QStringList& meterC);

    /*
    Method wSendCommFcoe
    */
    inline void wSendCommFcoe(double fcoe);

    /*
    Method wSendCommFcoeNeedt
    */
    inline void wSendCommFcoeNeedt(int second);

    /*
    Method wSendCommN
    */
    inline void wSendCommN(uint chkR);

    /*
    Method wSendCommPulseType
    */
    inline void wSendCommPulseType(bool direction);

    /*
    Method wSendCommSTEMC
    */
    inline void wSendCommSTEMC(qlonglong stemc);

    /*
    Method wSendCommSTEMC_2
    */
    inline void wSendCommSTEMC_2(const QString& stemc);

    /*
    Method wSendCommarr_ReadERR
    */
    inline QString wSendCommarr_ReadERR(uint curremno);

    /*
    Method wSendCommarr_ReadPulse
    */
    inline QString wSendCommarr_ReadPulse(uint curremno);

    /*
    Method wSendCommarr_ZZ
    */
    inline QString wSendCommarr_ZZ(uint curremno);

    /*
    Method wSendCtrolVoltageOnandOff
    */
    inline bool wSendCtrolVoltageOnandOff(uint curremno, int openOrclose);

    /*
    Method wSendMKSignal
    */
    inline void wSendMKSignal(uint emno, int status);

    /*
    Method wSendMPulsef
    */
    inline void wSendMPulsef(uint emno, int chanel, int mcycle, int mcNums);

    /*
    Method wSendOpen485Chanels
    */
    inline bool wSendOpen485Chanels(int setConnector);

    /*
    Method wSendOpen485Chanels_2
    */
    inline void wSendOpen485Chanels_2(bool firstChanel);

    /*
    Method wSendOpen485Chanels_3
    */
    inline bool wSendOpen485Chanels_3(uint curremno, bool open485);

    /*
    Method wSendOpenConnect
    */
    inline bool wSendOpenConnect(uint emno);

    /*
    Method wSendOpenPulseChanel
    */
    inline bool wSendOpenPulseChanel(uint emno, uint chanel);

    /*
    Method wSendReUCheck
    */
    inline bool wSendReUCheck(int emno, uint inttell);

    /*
    Method wSendReadBaseInfoData
    */
    inline QByteArray wSendReadBaseInfoData(int emno, int set485Chanels);

    /*
    Method wSendReadTransData
    */
    inline QByteArray wSendReadTransData(int emno, int set485Chanels);

    /*
    Method wSendSwitchTml485Channel
    */
    inline void wSendSwitchTml485Channel(uint emno, int status);

    /*
    Method wSendSwitchWeakTmnlChannel
    */
    inline bool wSendSwitchWeakTmnlChannel(uint emno, int status);

    /*
    Method wSendTransData
    */
    inline bool wSendTransData(int emno, const QByteArray& arr, int set485Chanels);

    /*
    Method wSendUCheck
    */
    inline bool wSendUCheck(int emno, int chku);

    /*
    Method wSendVouttoUpandDown
    */
    inline bool wSendVouttoUpandDown(const QString& meterIndexs, double upTime, double dOtime, int num);

    /*
    Method wSendWorkModel
    */
    inline void wSendWorkModel(uint emno, int status);

    /*
    Method wSend_Get_YaoCe
    */
    inline QString wSend_Get_YaoCe(uint emno);

    /*
    Method wSendandReadTransData
    */
    inline QByteArray wSendandReadTransData(int emno, const QByteArray& arr, int set485Chanels, int time);

    /*
    Method wc_readdatereturn
    */
    inline QVariantList wc_readdatereturn(uint enmo);

// meta object functions
    static const QMetaObject staticMetaObject;
    virtual const QMetaObject *metaObject() const { return &staticMetaObject; }
    virtual void *qt_metacast(const char *);
};

class COMEQUIPMENT_EXPORT IndustrialControlPower : public QAxObject
{
public:
    IndustrialControlPower(IDispatch *subobject = 0, QAxObject *parent = 0)
    : QAxObject((IUnknown*)subobject, parent)
    {
        internalRelease();
    }

    IndustrialControlPower(IIndustrialControlPower *iface)
    : QAxObject()
    {
        initializeFrom(iface);
        delete iface;
    }

    /*
    Property ToString
    */
    inline QString ToString() const; //Returns the value of ToString

    /*
    Method ControlVoteage
    */
    inline bool ControlVoteage(bool open);

    /*
    Method ControlVoteage
    */
    inline bool ControlVoteage(bool open, const QString& gear);

    /*
    Method Equals
    */
    inline bool Equals(const QVariant& obj);

    /*
    Method GetHashCode
    */
    inline int GetHashCode();

    /*
    Method GetType
    */
    inline mscorlib::_Type* GetType();

// meta object functions
    static const QMetaObject staticMetaObject;
    virtual const QMetaObject *metaObject() const { return &staticMetaObject; }
    virtual void *qt_metacast(const char *);
};

// member function implementation
#ifndef QAX_DUMPCPP_COMEQUIPMENT_NOINLINES
inline QString EquipmentNz2230::ToString() const
{
    QVariant qax_result = property("ToString");
    Q_ASSERT(qax_result.isValid());
    return *(QString*)qax_result.constData();
}

inline bool EquipmentNz2230::ClearWarning()
{
    bool qax_result;
    void *_a[] = {(void*)&qax_result};
    qt_metacall(QMetaObject::InvokeMetaMethod, 8, _a);
    return qax_result;
}

inline void EquipmentNz2230::Close()
{
    void *_a[] = {0};
    qt_metacall(QMetaObject::InvokeMetaMethod, 9, _a);
}

inline QString EquipmentNz2230::ConnectSet(const QString& sysPortName, const QString& stdName, const QString& stdPortName, bool isSignalSet)
{
    QString qax_result;
    void *_a[] = {(void*)&qax_result, (void*)&sysPortName, (void*)&stdName, (void*)&stdPortName, (void*)&isSignalSet};
    qt_metacall(QMetaObject::InvokeMetaMethod, 10, _a);
    return qax_result;
}

inline bool EquipmentNz2230::ControlVoteage(bool open, int percent)
{
    bool qax_result;
    void *_a[] = {(void*)&qax_result, (void*)&open, (void*)&percent};
    qt_metacall(QMetaObject::InvokeMetaMethod, 11, _a);
    return qax_result;
}

inline bool EquipmentNz2230::ControlVoteage(bool open, int percent, const QString& gear)
{
    bool qax_result;
    void *_a[] = {(void*)&qax_result, (void*)&open, (void*)&percent, (void*)&gear};
    qt_metacall(QMetaObject::InvokeMetaMethod, 12, _a);
    return qax_result;
}

inline bool EquipmentNz2230::EnVFall_Start(double upTime, double fullTime, double downTime, double lowTime, double lowValue, int testTimes, int fallType, int fallNum, double higva, uint cl)
{
    bool qax_result;
    void *_a[] = {(void*)&qax_result, (void*)&upTime, (void*)&fullTime, (void*)&downTime, (void*)&lowTime, (void*)&lowValue, (void*)&testTimes, (void*)&fallType, (void*)&fallNum, (void*)&higva, (void*)&cl};
    qt_metacall(QMetaObject::InvokeMetaMethod, 13, _a);
    return qax_result;
}

inline bool EquipmentNz2230::Equals(const QVariant& obj)
{
    bool qax_result;
    void *_a[] = {(void*)&qax_result, (void*)&obj};
    qt_metacall(QMetaObject::InvokeMetaMethod, 14, _a);
    return qax_result;
}

inline int EquipmentNz2230::GetHashCode()
{
    int qax_result;
    void *_a[] = {(void*)&qax_result};
    qt_metacall(QMetaObject::InvokeMetaMethod, 15, _a);
    return qax_result;
}

inline QString EquipmentNz2230::GetMonitor()
{
    QString qax_result;
    void *_a[] = {(void*)&qax_result};
    qt_metacall(QMetaObject::InvokeMetaMethod, 16, _a);
    return qax_result;
}

inline double EquipmentNz2230::GetPhaseDegree(uint ptpNo, const QString& capacitive, uint direction, uint phase)
{
    double qax_result;
    void *_a[] = {(void*)&qax_result, (void*)&ptpNo, (void*)&capacitive, (void*)&direction, (void*)&phase};
    qt_metacall(QMetaObject::InvokeMetaMethod, 17, _a);
    return qax_result;
}

inline double EquipmentNz2230::GetRangeIb(double curri)
{
    double qax_result;
    void *_a[] = {(void*)&qax_result, (void*)&curri};
    qt_metacall(QMetaObject::InvokeMetaMethod, 18, _a);
    return qax_result;
}

inline double EquipmentNz2230::GetRangeUbI(double curru)
{
    double qax_result;
    void *_a[] = {(void*)&qax_result, (void*)&curru};
    qt_metacall(QMetaObject::InvokeMetaMethod, 19, _a);
    return qax_result;
}

inline qlonglong EquipmentNz2230::GetStemCTheory(double u, double I)
{
    qlonglong qax_result;
    void *_a[] = {(void*)&qax_result, (void*)&u, (void*)&I};
    qt_metacall(QMetaObject::InvokeMetaMethod, 20, _a);
    return qax_result;
}

inline QString EquipmentNz2230::GetStemCTheoryStr(double u, double I)
{
    QString qax_result;
    void *_a[] = {(void*)&qax_result, (void*)&u, (void*)&I};
    qt_metacall(QMetaObject::InvokeMetaMethod, 21, _a);
    return qax_result;
}

inline mscorlib::_Type* EquipmentNz2230::GetType()
{
    mscorlib::_Type* qax_result = 0;
#ifdef QAX_DUMPCPP_MSCORLIB_H
    qRegisterMetaType<mscorlib::_Type*>("mscorlib::_Type*", &qax_result);
    qRegisterMetaType<mscorlib::_Type>("mscorlib::_Type", qax_result);
#endif
    void *_a[] = {(void*)&qax_result};
    qt_metacall(QMetaObject::InvokeMetaMethod, 22, _a);
    return qax_result;
}

inline bool EquipmentNz2230::Power_ON(bool setPhaseOrder, double u, double I, const QString& phic, double f, double voltage, double current, uint wiringMode, uint workPhase, uint ptpNo)
{
    bool qax_result;
    void *_a[] = {(void*)&qax_result, (void*)&setPhaseOrder, (void*)&u, (void*)&I, (void*)&phic, (void*)&f, (void*)&voltage, (void*)&current, (void*)&wiringMode, (void*)&workPhase, (void*)&ptpNo};
    qt_metacall(QMetaObject::InvokeMetaMethod, 23, _a);
    return qax_result;
}

inline bool EquipmentNz2230::SendComAA()
{
    bool qax_result;
    void *_a[] = {(void*)&qax_result};
    qt_metacall(QMetaObject::InvokeMetaMethod, 24, _a);
    return qax_result;
}

inline bool EquipmentNz2230::SendCommChkEnd()
{
    bool qax_result;
    void *_a[] = {(void*)&qax_result};
    qt_metacall(QMetaObject::InvokeMetaMethod, 25, _a);
    return qax_result;
}

inline bool EquipmentNz2230::SendCommChkSet(bool setPhaseOrder, uint ptpNo)
{
    bool qax_result;
    void *_a[] = {(void*)&qax_result, (void*)&setPhaseOrder, (void*)&ptpNo};
    qt_metacall(QMetaObject::InvokeMetaMethod, 26, _a);
    return qax_result;
}

inline bool EquipmentNz2230::SendCommF(double fltF)
{
    bool qax_result;
    void *_a[] = {(void*)&qax_result, (void*)&fltF};
    qt_metacall(QMetaObject::InvokeMetaMethod, 27, _a);
    return qax_result;
}

inline bool EquipmentNz2230::SendCommHarmonicContent(const QString& uContents, const QString& iContents)
{
    bool qax_result;
    void *_a[] = {(void*)&qax_result, (void*)&uContents, (void*)&iContents};
    qt_metacall(QMetaObject::InvokeMetaMethod, 28, _a);
    return qax_result;
}

inline bool EquipmentNz2230::SendCommHarmonicSet(uint harmonic)
{
    bool qax_result;
    void *_a[] = {(void*)&qax_result, (void*)&harmonic};
    qt_metacall(QMetaObject::InvokeMetaMethod, 29, _a);
    return qax_result;
}

inline bool EquipmentNz2230::SendCommI(double current, uint wiringMode, uint workPhase, bool bolNotAuto)
{
    bool qax_result;
    void *_a[] = {(void*)&qax_result, (void*)&current, (void*)&wiringMode, (void*)&workPhase, (void*)&bolNotAuto};
    qt_metacall(QMetaObject::InvokeMetaMethod, 30, _a);
    return qax_result;
}

inline bool EquipmentNz2230::SendCommIabc(double fltIa, double fltIb, double fltIc)
{
    bool qax_result;
    void *_a[] = {(void*)&qax_result, (void*)&fltIa, (void*)&fltIb, (void*)&fltIc};
    qt_metacall(QMetaObject::InvokeMetaMethod, 31, _a);
    return qax_result;
}

inline bool EquipmentNz2230::SendCommOpen(bool bOpen)
{
    bool qax_result;
    void *_a[] = {(void*)&qax_result, (void*)&bOpen};
    qt_metacall(QMetaObject::InvokeMetaMethod, 32, _a);
    return qax_result;
}

inline bool EquipmentNz2230::SendCommP(double fltP, uint workPhase)
{
    bool qax_result;
    void *_a[] = {(void*)&qax_result, (void*)&fltP, (void*)&workPhase};
    qt_metacall(QMetaObject::InvokeMetaMethod, 33, _a);
    return qax_result;
}

inline bool EquipmentNz2230::SendCommPabc(double fltPa, double fltPb, double fltPc)
{
    bool qax_result;
    void *_a[] = {(void*)&qax_result, (void*)&fltPa, (void*)&fltPb, (void*)&fltPc};
    qt_metacall(QMetaObject::InvokeMetaMethod, 34, _a);
    return qax_result;
}

inline bool EquipmentNz2230::SendCommRangeNo(double voltage, double current)
{
    bool qax_result;
    void *_a[] = {(void*)&qax_result, (void*)&voltage, (void*)&current};
    qt_metacall(QMetaObject::InvokeMetaMethod, 35, _a);
    return qax_result;
}

inline bool EquipmentNz2230::SendCommSpeedUI(int seconds)
{
    bool qax_result;
    void *_a[] = {(void*)&qax_result, (void*)&seconds};
    qt_metacall(QMetaObject::InvokeMetaMethod, 36, _a);
    return qax_result;
}

inline bool EquipmentNz2230::SendCommSpeedUI_2(int uspeed, int ispeed)
{
    bool qax_result;
    void *_a[] = {(void*)&qax_result, (void*)&uspeed, (void*)&ispeed};
    qt_metacall(QMetaObject::InvokeMetaMethod, 37, _a);
    return qax_result;
}

inline bool EquipmentNz2230::SendCommU(double voltage, uint wiringMode, uint workPhase, bool bolNotAuto)
{
    bool qax_result;
    void *_a[] = {(void*)&qax_result, (void*)&voltage, (void*)&wiringMode, (void*)&workPhase, (void*)&bolNotAuto};
    qt_metacall(QMetaObject::InvokeMetaMethod, 38, _a);
    return qax_result;
}

inline void EquipmentNz2230::SendCommUIDownRapid()
{
    void *_a[] = {0};
    qt_metacall(QMetaObject::InvokeMetaMethod, 39, _a);
}

inline bool EquipmentNz2230::SendCommUabc(double fltUa, double fltUb, double fltUc)
{
    bool qax_result;
    void *_a[] = {(void*)&qax_result, (void*)&fltUa, (void*)&fltUb, (void*)&fltUc};
    qt_metacall(QMetaObject::InvokeMetaMethod, 40, _a);
    return qax_result;
}

inline bool EquipmentNz2230::SendCommVoltageDropBreak(int breakTimes, int voltagePercentage, double dropTimes, double recoveryTimes)
{
    bool qax_result;
    void *_a[] = {(void*)&qax_result, (void*)&breakTimes, (void*)&voltagePercentage, (void*)&dropTimes, (void*)&recoveryTimes};
    qt_metacall(QMetaObject::InvokeMetaMethod, 41, _a);
    return qax_result;
}

inline bool EquipmentNz2230::SendCommVoltageDropBreak_2(int dropMode, qlonglong dropTimes, const QStringList& paras)
{
    bool qax_result;
    void *_a[] = {(void*)&qax_result, (void*)&dropMode, (void*)&dropTimes, (void*)&paras};
    qt_metacall(QMetaObject::InvokeMetaMethod, 42, _a);
    return qax_result;
}

inline bool EquipmentNz2230::SendCommfs(int value)
{
    bool qax_result;
    void *_a[] = {(void*)&qax_result, (void*)&value};
    qt_metacall(QMetaObject::InvokeMetaMethod, 43, _a);
    return qax_result;
}

inline qlonglong EquipmentNz2230::SendWantStemC()
{
    qlonglong qax_result;
    void *_a[] = {(void*)&qax_result};
    qt_metacall(QMetaObject::InvokeMetaMethod, 44, _a);
    return qax_result;
}

inline void EquipmentNz2230::openCOM(const QString& commport)
{
    void *_a[] = {0, (void*)&commport};
    qt_metacall(QMetaObject::InvokeMetaMethod, 45, _a);
}


inline QString EquipmentSd2000::ToString() const
{
    QVariant qax_result = property("ToString");
    Q_ASSERT(qax_result.isValid());
    return *(QString*)qax_result.constData();
}

inline bool EquipmentSd2000::ClearWarning()
{
    bool qax_result;
    void *_a[] = {(void*)&qax_result};
    qt_metacall(QMetaObject::InvokeMetaMethod, 8, _a);
    return qax_result;
}

inline void EquipmentSd2000::Close()
{
    void *_a[] = {0};
    qt_metacall(QMetaObject::InvokeMetaMethod, 9, _a);
}

inline QString EquipmentSd2000::ConnectSet(const QString& sysPortName, const QString& stdName, const QString& stdPortName, bool isSignalSet)
{
    QString qax_result;
    void *_a[] = {(void*)&qax_result, (void*)&sysPortName, (void*)&stdName, (void*)&stdPortName, (void*)&isSignalSet};
    qt_metacall(QMetaObject::InvokeMetaMethod, 10, _a);
    return qax_result;
}

inline bool EquipmentSd2000::ControlVoteage(bool open, int percent)
{
    bool qax_result;
    void *_a[] = {(void*)&qax_result, (void*)&open, (void*)&percent};
    qt_metacall(QMetaObject::InvokeMetaMethod, 11, _a);
    return qax_result;
}

inline bool EquipmentSd2000::ControlVoteage(bool open, int percent, const QString& gear)
{
    bool qax_result;
    void *_a[] = {(void*)&qax_result, (void*)&open, (void*)&percent, (void*)&gear};
    qt_metacall(QMetaObject::InvokeMetaMethod, 12, _a);
    return qax_result;
}

inline bool EquipmentSd2000::EnVFall_Start(double upTime, double fullTime, double downTime, double lowTime, double lowValue, int testTimes, int fallType, int fallNum, double higva, uint cl)
{
    bool qax_result;
    void *_a[] = {(void*)&qax_result, (void*)&upTime, (void*)&fullTime, (void*)&downTime, (void*)&lowTime, (void*)&lowValue, (void*)&testTimes, (void*)&fallType, (void*)&fallNum, (void*)&higva, (void*)&cl};
    qt_metacall(QMetaObject::InvokeMetaMethod, 13, _a);
    return qax_result;
}

inline bool EquipmentSd2000::Equals(const QVariant& obj)
{
    bool qax_result;
    void *_a[] = {(void*)&qax_result, (void*)&obj};
    qt_metacall(QMetaObject::InvokeMetaMethod, 14, _a);
    return qax_result;
}

inline int EquipmentSd2000::GetHashCode()
{
    int qax_result;
    void *_a[] = {(void*)&qax_result};
    qt_metacall(QMetaObject::InvokeMetaMethod, 15, _a);
    return qax_result;
}

inline QString EquipmentSd2000::GetMonitor()
{
    QString qax_result;
    void *_a[] = {(void*)&qax_result};
    qt_metacall(QMetaObject::InvokeMetaMethod, 16, _a);
    return qax_result;
}

inline double EquipmentSd2000::GetPhaseDegree(uint ptpNo, const QString& capacitive, uint direction, uint phase)
{
    double qax_result;
    void *_a[] = {(void*)&qax_result, (void*)&ptpNo, (void*)&capacitive, (void*)&direction, (void*)&phase};
    qt_metacall(QMetaObject::InvokeMetaMethod, 17, _a);
    return qax_result;
}

inline double EquipmentSd2000::GetRangeIb(double curri)
{
    double qax_result;
    void *_a[] = {(void*)&qax_result, (void*)&curri};
    qt_metacall(QMetaObject::InvokeMetaMethod, 18, _a);
    return qax_result;
}

inline double EquipmentSd2000::GetRangeUbI(double curru)
{
    double qax_result;
    void *_a[] = {(void*)&qax_result, (void*)&curru};
    qt_metacall(QMetaObject::InvokeMetaMethod, 19, _a);
    return qax_result;
}

inline qlonglong EquipmentSd2000::GetStemCTheory(double u, double I)
{
    qlonglong qax_result;
    void *_a[] = {(void*)&qax_result, (void*)&u, (void*)&I};
    qt_metacall(QMetaObject::InvokeMetaMethod, 20, _a);
    return qax_result;
}

inline QString EquipmentSd2000::GetStemCTheoryStr(double u, double I)
{
    QString qax_result;
    void *_a[] = {(void*)&qax_result, (void*)&u, (void*)&I};
    qt_metacall(QMetaObject::InvokeMetaMethod, 21, _a);
    return qax_result;
}

inline mscorlib::_Type* EquipmentSd2000::GetType()
{
    mscorlib::_Type* qax_result = 0;
#ifdef QAX_DUMPCPP_MSCORLIB_H
    qRegisterMetaType<mscorlib::_Type*>("mscorlib::_Type*", &qax_result);
    qRegisterMetaType<mscorlib::_Type>("mscorlib::_Type", qax_result);
#endif
    void *_a[] = {(void*)&qax_result};
    qt_metacall(QMetaObject::InvokeMetaMethod, 22, _a);
    return qax_result;
}

inline bool EquipmentSd2000::Power_ON(bool setPhaseOrder, double u, double I, const QString& phic, double f, double voltage, double current, uint wiringMode, uint workPhase, uint ptpNo)
{
    bool qax_result;
    void *_a[] = {(void*)&qax_result, (void*)&setPhaseOrder, (void*)&u, (void*)&I, (void*)&phic, (void*)&f, (void*)&voltage, (void*)&current, (void*)&wiringMode, (void*)&workPhase, (void*)&ptpNo};
    qt_metacall(QMetaObject::InvokeMetaMethod, 23, _a);
    return qax_result;
}

inline bool EquipmentSd2000::SendComAA()
{
    bool qax_result;
    void *_a[] = {(void*)&qax_result};
    qt_metacall(QMetaObject::InvokeMetaMethod, 24, _a);
    return qax_result;
}

inline bool EquipmentSd2000::SendCommChkEnd()
{
    bool qax_result;
    void *_a[] = {(void*)&qax_result};
    qt_metacall(QMetaObject::InvokeMetaMethod, 25, _a);
    return qax_result;
}

inline bool EquipmentSd2000::SendCommChkSet(bool setPhaseOrder, uint ptpNo)
{
    bool qax_result;
    void *_a[] = {(void*)&qax_result, (void*)&setPhaseOrder, (void*)&ptpNo};
    qt_metacall(QMetaObject::InvokeMetaMethod, 26, _a);
    return qax_result;
}

inline bool EquipmentSd2000::SendCommF(double fltF)
{
    bool qax_result;
    void *_a[] = {(void*)&qax_result, (void*)&fltF};
    qt_metacall(QMetaObject::InvokeMetaMethod, 27, _a);
    return qax_result;
}

inline bool EquipmentSd2000::SendCommHarmonicContent(const QString& uContents, const QString& iContents)
{
    bool qax_result;
    void *_a[] = {(void*)&qax_result, (void*)&uContents, (void*)&iContents};
    qt_metacall(QMetaObject::InvokeMetaMethod, 28, _a);
    return qax_result;
}

inline bool EquipmentSd2000::SendCommHarmonicSet(uint harmonic)
{
    bool qax_result;
    void *_a[] = {(void*)&qax_result, (void*)&harmonic};
    qt_metacall(QMetaObject::InvokeMetaMethod, 29, _a);
    return qax_result;
}

inline bool EquipmentSd2000::SendCommI(double current, uint wiringMode, uint workPhase, bool bolNotAuto)
{
    bool qax_result;
    void *_a[] = {(void*)&qax_result, (void*)&current, (void*)&wiringMode, (void*)&workPhase, (void*)&bolNotAuto};
    qt_metacall(QMetaObject::InvokeMetaMethod, 30, _a);
    return qax_result;
}

inline bool EquipmentSd2000::SendCommIabc(double fltIa, double fltIb, double fltIc)
{
    bool qax_result;
    void *_a[] = {(void*)&qax_result, (void*)&fltIa, (void*)&fltIb, (void*)&fltIc};
    qt_metacall(QMetaObject::InvokeMetaMethod, 31, _a);
    return qax_result;
}

inline bool EquipmentSd2000::SendCommOpen(bool bOpen)
{
    bool qax_result;
    void *_a[] = {(void*)&qax_result, (void*)&bOpen};
    qt_metacall(QMetaObject::InvokeMetaMethod, 32, _a);
    return qax_result;
}

inline bool EquipmentSd2000::SendCommP(double fltP, uint workPhase)
{
    bool qax_result;
    void *_a[] = {(void*)&qax_result, (void*)&fltP, (void*)&workPhase};
    qt_metacall(QMetaObject::InvokeMetaMethod, 33, _a);
    return qax_result;
}

inline bool EquipmentSd2000::SendCommPabc(double fltPa, double fltPb, double fltPc)
{
    bool qax_result;
    void *_a[] = {(void*)&qax_result, (void*)&fltPa, (void*)&fltPb, (void*)&fltPc};
    qt_metacall(QMetaObject::InvokeMetaMethod, 34, _a);
    return qax_result;
}

inline bool EquipmentSd2000::SendCommRangeNo(double voltage, double current)
{
    bool qax_result;
    void *_a[] = {(void*)&qax_result, (void*)&voltage, (void*)&current};
    qt_metacall(QMetaObject::InvokeMetaMethod, 35, _a);
    return qax_result;
}

inline bool EquipmentSd2000::SendCommSpeedUI(int seconds)
{
    bool qax_result;
    void *_a[] = {(void*)&qax_result, (void*)&seconds};
    qt_metacall(QMetaObject::InvokeMetaMethod, 36, _a);
    return qax_result;
}

inline bool EquipmentSd2000::SendCommSpeedUI_2(int uspeed, int ispeed)
{
    bool qax_result;
    void *_a[] = {(void*)&qax_result, (void*)&uspeed, (void*)&ispeed};
    qt_metacall(QMetaObject::InvokeMetaMethod, 37, _a);
    return qax_result;
}

inline bool EquipmentSd2000::SendCommU(double voltage, uint wiringMode, uint workPhase, bool bolNotAuto)
{
    bool qax_result;
    void *_a[] = {(void*)&qax_result, (void*)&voltage, (void*)&wiringMode, (void*)&workPhase, (void*)&bolNotAuto};
    qt_metacall(QMetaObject::InvokeMetaMethod, 38, _a);
    return qax_result;
}

inline void EquipmentSd2000::SendCommUIDownRapid()
{
    void *_a[] = {0};
    qt_metacall(QMetaObject::InvokeMetaMethod, 39, _a);
}

inline bool EquipmentSd2000::SendCommUabc(double fltUa, double fltUb, double fltUc)
{
    bool qax_result;
    void *_a[] = {(void*)&qax_result, (void*)&fltUa, (void*)&fltUb, (void*)&fltUc};
    qt_metacall(QMetaObject::InvokeMetaMethod, 40, _a);
    return qax_result;
}

inline bool EquipmentSd2000::SendCommVoltageDropBreak(int breakTimes, int voltagePercentage, double dropTimes, double recoveryTimes)
{
    bool qax_result;
    void *_a[] = {(void*)&qax_result, (void*)&breakTimes, (void*)&voltagePercentage, (void*)&dropTimes, (void*)&recoveryTimes};
    qt_metacall(QMetaObject::InvokeMetaMethod, 41, _a);
    return qax_result;
}

inline bool EquipmentSd2000::SendCommVoltageDropBreak_2(int dropMode, qlonglong dropTimes, const QStringList& paras)
{
    bool qax_result;
    void *_a[] = {(void*)&qax_result, (void*)&dropMode, (void*)&dropTimes, (void*)&paras};
    qt_metacall(QMetaObject::InvokeMetaMethod, 42, _a);
    return qax_result;
}

inline bool EquipmentSd2000::SendCommfs(int value)
{
    bool qax_result;
    void *_a[] = {(void*)&qax_result, (void*)&value};
    qt_metacall(QMetaObject::InvokeMetaMethod, 43, _a);
    return qax_result;
}

inline qlonglong EquipmentSd2000::SendWantStemC()
{
    qlonglong qax_result;
    void *_a[] = {(void*)&qax_result};
    qt_metacall(QMetaObject::InvokeMetaMethod, 44, _a);
    return qax_result;
}

inline void EquipmentSd2000::openCOM(const QString& commport)
{
    void *_a[] = {0, (void*)&commport};
    qt_metacall(QMetaObject::InvokeMetaMethod, 45, _a);
}


inline QString EquipmentWc::ToString() const
{
    QVariant qax_result = property("ToString");
    Q_ASSERT(qax_result.isValid());
    return *(QString*)qax_result.constData();
}

inline QString EquipmentWc::CheckRemoteKnifeSwitch(int meterCount, uint inttell)
{
    QString qax_result;
    void *_a[] = {(void*)&qax_result, (void*)&meterCount, (void*)&inttell};
    qt_metacall(QMetaObject::InvokeMetaMethod, 8, _a);
    return qax_result;
}

inline void EquipmentWc::Close()
{
    void *_a[] = {0};
    qt_metacall(QMetaObject::InvokeMetaMethod, 9, _a);
}

inline QString EquipmentWc::ConnectEquipment(const QString& wcPortname)
{
    QString qax_result;
    void *_a[] = {(void*)&qax_result, (void*)&wcPortname};
    qt_metacall(QMetaObject::InvokeMetaMethod, 10, _a);
    return qax_result;
}

inline bool EquipmentWc::Equals(const QVariant& obj)
{
    bool qax_result;
    void *_a[] = {(void*)&qax_result, (void*)&obj};
    qt_metacall(QMetaObject::InvokeMetaMethod, 11, _a);
    return qax_result;
}

inline int EquipmentWc::GetHashCode()
{
    int qax_result;
    void *_a[] = {(void*)&qax_result};
    qt_metacall(QMetaObject::InvokeMetaMethod, 12, _a);
    return qax_result;
}

inline mscorlib::_Type* EquipmentWc::GetType()
{
    mscorlib::_Type* qax_result = 0;
#ifdef QAX_DUMPCPP_MSCORLIB_H
    qRegisterMetaType<mscorlib::_Type*>("mscorlib::_Type*", &qax_result);
    qRegisterMetaType<mscorlib::_Type>("mscorlib::_Type", qax_result);
#endif
    void *_a[] = {(void*)&qax_result};
    qt_metacall(QMetaObject::InvokeMetaMethod, 13, _a);
    return qax_result;
}

inline bool EquipmentWc::Relay_EnVFall_Start(const QString& meterIndexs, int abc, int intervalMillisecond, int ioMillisecond, int repeatsCount)
{
    bool qax_result;
    void *_a[] = {(void*)&qax_result, (void*)&meterIndexs, (void*)&abc, (void*)&intervalMillisecond, (void*)&ioMillisecond, (void*)&repeatsCount};
    qt_metacall(QMetaObject::InvokeMetaMethod, 14, _a);
    return qax_result;
}

inline void EquipmentWc::wAutoShutdown(int emno, uint meterStatus)
{
    void *_a[] = {0, (void*)&emno, (void*)&meterStatus};
    qt_metacall(QMetaObject::InvokeMetaMethod, 15, _a);
}

inline bool EquipmentWc::wGet_Send_Base_Info(int set485Chanels, int emno, int dlt, int baudrate, const QString& gaddress, int delaytimes, int delaytimes2)
{
    bool qax_result;
    void *_a[] = {(void*)&qax_result, (void*)&set485Chanels, (void*)&emno, (void*)&dlt, (void*)&baudrate, (void*)&gaddress, (void*)&delaytimes, (void*)&delaytimes2};
    qt_metacall(QMetaObject::InvokeMetaMethod, 16, _a);
    return qax_result;
}

inline bool EquipmentWc::wGet_Send_Base_Info_2(int set485Chanels, int emno, int dlt, const QString& baudrate, const QString& gaddress)
{
    bool qax_result;
    void *_a[] = {(void*)&qax_result, (void*)&set485Chanels, (void*)&emno, (void*)&dlt, (void*)&baudrate, (void*)&gaddress};
    qt_metacall(QMetaObject::InvokeMetaMethod, 17, _a);
    return qax_result;
}

inline void EquipmentWc::wSendBegin_End_YaoCe(uint emno, int status)
{
    void *_a[] = {0, (void*)&emno, (void*)&status};
    qt_metacall(QMetaObject::InvokeMetaMethod, 18, _a);
}

inline void EquipmentWc::wSendCommChkPBegin()
{
    void *_a[] = {0};
    qt_metacall(QMetaObject::InvokeMetaMethod, 19, _a);
}

inline bool EquipmentWc::wSendCommChkPEnd()
{
    bool qax_result;
    void *_a[] = {(void*)&qax_result};
    qt_metacall(QMetaObject::InvokeMetaMethod, 20, _a);
    return qax_result;
}

inline bool EquipmentWc::wSendCommChkType(int value)
{
    bool qax_result;
    void *_a[] = {(void*)&qax_result, (void*)&value};
    qt_metacall(QMetaObject::InvokeMetaMethod, 21, _a);
    return qax_result;
}

inline void EquipmentWc::wSendCommEMC(QVariantList meterC, int currfcoe)
{
    void *_a[] = {0, (void*)&meterC, (void*)&currfcoe};
    qt_metacall(QMetaObject::InvokeMetaMethod, 22, _a);
}

inline void EquipmentWc::wSendCommEMCAll(qlonglong meterC, int currfcoe)
{
    void *_a[] = {0, (void*)&meterC, (void*)&currfcoe};
    qt_metacall(QMetaObject::InvokeMetaMethod, 23, _a);
}

inline void EquipmentWc::wSendCommEMCAll_2(const QString& meterC, int currfcoe)
{
    void *_a[] = {0, (void*)&meterC, (void*)&currfcoe};
    qt_metacall(QMetaObject::InvokeMetaMethod, 24, _a);
}

inline void EquipmentWc::wSendCommEMC_2(QVariantList meterC)
{
    void *_a[] = {0, (void*)&meterC};
    qt_metacall(QMetaObject::InvokeMetaMethod, 25, _a);
}

inline void EquipmentWc::wSendCommEMC_3(const QStringList& meterC)
{
    void *_a[] = {0, (void*)&meterC};
    qt_metacall(QMetaObject::InvokeMetaMethod, 26, _a);
}

inline void EquipmentWc::wSendCommFcoe(double fcoe)
{
    void *_a[] = {0, (void*)&fcoe};
    qt_metacall(QMetaObject::InvokeMetaMethod, 27, _a);
}

inline void EquipmentWc::wSendCommFcoeNeedt(int second)
{
    void *_a[] = {0, (void*)&second};
    qt_metacall(QMetaObject::InvokeMetaMethod, 28, _a);
}

inline void EquipmentWc::wSendCommN(uint chkR)
{
    void *_a[] = {0, (void*)&chkR};
    qt_metacall(QMetaObject::InvokeMetaMethod, 29, _a);
}

inline void EquipmentWc::wSendCommPulseType(bool direction)
{
    void *_a[] = {0, (void*)&direction};
    qt_metacall(QMetaObject::InvokeMetaMethod, 30, _a);
}

inline void EquipmentWc::wSendCommSTEMC(qlonglong stemc)
{
    void *_a[] = {0, (void*)&stemc};
    qt_metacall(QMetaObject::InvokeMetaMethod, 31, _a);
}

inline void EquipmentWc::wSendCommSTEMC_2(const QString& stemc)
{
    void *_a[] = {0, (void*)&stemc};
    qt_metacall(QMetaObject::InvokeMetaMethod, 32, _a);
}

inline QString EquipmentWc::wSendCommarr_ReadERR(uint curremno)
{
    QString qax_result;
    void *_a[] = {(void*)&qax_result, (void*)&curremno};
    qt_metacall(QMetaObject::InvokeMetaMethod, 33, _a);
    return qax_result;
}

inline QString EquipmentWc::wSendCommarr_ReadPulse(uint curremno)
{
    QString qax_result;
    void *_a[] = {(void*)&qax_result, (void*)&curremno};
    qt_metacall(QMetaObject::InvokeMetaMethod, 34, _a);
    return qax_result;
}

inline QString EquipmentWc::wSendCommarr_ZZ(uint curremno)
{
    QString qax_result;
    void *_a[] = {(void*)&qax_result, (void*)&curremno};
    qt_metacall(QMetaObject::InvokeMetaMethod, 35, _a);
    return qax_result;
}

inline bool EquipmentWc::wSendCtrolVoltageOnandOff(uint curremno, int openOrclose)
{
    bool qax_result;
    void *_a[] = {(void*)&qax_result, (void*)&curremno, (void*)&openOrclose};
    qt_metacall(QMetaObject::InvokeMetaMethod, 36, _a);
    return qax_result;
}

inline void EquipmentWc::wSendMKSignal(uint emno, int status)
{
    void *_a[] = {0, (void*)&emno, (void*)&status};
    qt_metacall(QMetaObject::InvokeMetaMethod, 37, _a);
}

inline void EquipmentWc::wSendMPulsef(uint emno, int chanel, int mcycle, int mcNums)
{
    void *_a[] = {0, (void*)&emno, (void*)&chanel, (void*)&mcycle, (void*)&mcNums};
    qt_metacall(QMetaObject::InvokeMetaMethod, 38, _a);
}

inline bool EquipmentWc::wSendOpen485Chanels(int setConnector)
{
    bool qax_result;
    void *_a[] = {(void*)&qax_result, (void*)&setConnector};
    qt_metacall(QMetaObject::InvokeMetaMethod, 39, _a);
    return qax_result;
}

inline void EquipmentWc::wSendOpen485Chanels_2(bool firstChanel)
{
    void *_a[] = {0, (void*)&firstChanel};
    qt_metacall(QMetaObject::InvokeMetaMethod, 40, _a);
}

inline bool EquipmentWc::wSendOpen485Chanels_3(uint curremno, bool open485)
{
    bool qax_result;
    void *_a[] = {(void*)&qax_result, (void*)&curremno, (void*)&open485};
    qt_metacall(QMetaObject::InvokeMetaMethod, 41, _a);
    return qax_result;
}

inline bool EquipmentWc::wSendOpenConnect(uint emno)
{
    bool qax_result;
    void *_a[] = {(void*)&qax_result, (void*)&emno};
    qt_metacall(QMetaObject::InvokeMetaMethod, 42, _a);
    return qax_result;
}

inline bool EquipmentWc::wSendOpenPulseChanel(uint emno, uint chanel)
{
    bool qax_result;
    void *_a[] = {(void*)&qax_result, (void*)&emno, (void*)&chanel};
    qt_metacall(QMetaObject::InvokeMetaMethod, 43, _a);
    return qax_result;
}

inline bool EquipmentWc::wSendReUCheck(int emno, uint inttell)
{
    bool qax_result;
    void *_a[] = {(void*)&qax_result, (void*)&emno, (void*)&inttell};
    qt_metacall(QMetaObject::InvokeMetaMethod, 44, _a);
    return qax_result;
}

inline QByteArray EquipmentWc::wSendReadBaseInfoData(int emno, int set485Chanels)
{
    QByteArray qax_result;
    void *_a[] = {(void*)&qax_result, (void*)&emno, (void*)&set485Chanels};
    qt_metacall(QMetaObject::InvokeMetaMethod, 45, _a);
    return qax_result;
}

inline QByteArray EquipmentWc::wSendReadTransData(int emno, int set485Chanels)
{
    QByteArray qax_result;
    void *_a[] = {(void*)&qax_result, (void*)&emno, (void*)&set485Chanels};
    qt_metacall(QMetaObject::InvokeMetaMethod, 46, _a);
    return qax_result;
}

inline void EquipmentWc::wSendSwitchTml485Channel(uint emno, int status)
{
    void *_a[] = {0, (void*)&emno, (void*)&status};
    qt_metacall(QMetaObject::InvokeMetaMethod, 47, _a);
}

inline bool EquipmentWc::wSendSwitchWeakTmnlChannel(uint emno, int status)
{
    bool qax_result;
    void *_a[] = {(void*)&qax_result, (void*)&emno, (void*)&status};
    qt_metacall(QMetaObject::InvokeMetaMethod, 48, _a);
    return qax_result;
}

inline bool EquipmentWc::wSendTransData(int emno, const QByteArray& arr, int set485Chanels)
{
    bool qax_result;
    void *_a[] = {(void*)&qax_result, (void*)&emno, (void*)&arr, (void*)&set485Chanels};
    qt_metacall(QMetaObject::InvokeMetaMethod, 49, _a);
    return qax_result;
}

inline bool EquipmentWc::wSendUCheck(int emno, int chku)
{
    bool qax_result;
    void *_a[] = {(void*)&qax_result, (void*)&emno, (void*)&chku};
    qt_metacall(QMetaObject::InvokeMetaMethod, 50, _a);
    return qax_result;
}

inline bool EquipmentWc::wSendVouttoUpandDown(const QString& meterIndexs, double upTime, double dOtime, int num)
{
    bool qax_result;
    void *_a[] = {(void*)&qax_result, (void*)&meterIndexs, (void*)&upTime, (void*)&dOtime, (void*)&num};
    qt_metacall(QMetaObject::InvokeMetaMethod, 51, _a);
    return qax_result;
}

inline void EquipmentWc::wSendWorkModel(uint emno, int status)
{
    void *_a[] = {0, (void*)&emno, (void*)&status};
    qt_metacall(QMetaObject::InvokeMetaMethod, 52, _a);
}

inline QString EquipmentWc::wSend_Get_YaoCe(uint emno)
{
    QString qax_result;
    void *_a[] = {(void*)&qax_result, (void*)&emno};
    qt_metacall(QMetaObject::InvokeMetaMethod, 53, _a);
    return qax_result;
}

inline QByteArray EquipmentWc::wSendandReadTransData(int emno, const QByteArray& arr, int set485Chanels, int time)
{
    QByteArray qax_result;
    void *_a[] = {(void*)&qax_result, (void*)&emno, (void*)&arr, (void*)&set485Chanels, (void*)&time};
    qt_metacall(QMetaObject::InvokeMetaMethod, 54, _a);
    return qax_result;
}

inline QVariantList EquipmentWc::wc_readdatereturn(uint enmo)
{
    QVariantList qax_result;
    void *_a[] = {(void*)&qax_result, (void*)&enmo};
    qt_metacall(QMetaObject::InvokeMetaMethod, 55, _a);
    return qax_result;
}


inline bool IEquipmentSJJ::ClearWarning()
{
    bool qax_result;
    void *_a[] = {(void*)&qax_result};
    qt_metacall(QMetaObject::InvokeMetaMethod, 8, _a);
    return qax_result;
}

inline void IEquipmentSJJ::Close()
{
    void *_a[] = {0};
    qt_metacall(QMetaObject::InvokeMetaMethod, 9, _a);
}

inline QString IEquipmentSJJ::ConnectSet(const QString& sysPortName, const QString& stdName, const QString& stdPortName, bool isSignalSet)
{
    QString qax_result;
    void *_a[] = {(void*)&qax_result, (void*)&sysPortName, (void*)&stdName, (void*)&stdPortName, (void*)&isSignalSet};
    qt_metacall(QMetaObject::InvokeMetaMethod, 10, _a);
    return qax_result;
}

inline bool IEquipmentSJJ::ControlVoteage(bool open, int percent)
{
    bool qax_result;
    void *_a[] = {(void*)&qax_result, (void*)&open, (void*)&percent};
    qt_metacall(QMetaObject::InvokeMetaMethod, 11, _a);
    return qax_result;
}

inline bool IEquipmentSJJ::ControlVoteage(bool open, int percent, const QString& gear)
{
    bool qax_result;
    void *_a[] = {(void*)&qax_result, (void*)&open, (void*)&percent, (void*)&gear};
    qt_metacall(QMetaObject::InvokeMetaMethod, 12, _a);
    return qax_result;
}

inline bool IEquipmentSJJ::EnVFall_Start(double upTime, double fullTime, double downTime, double lowTime, double lowValue, int testTimes, int fallType, int fallNum, double higva, uint cl)
{
    bool qax_result;
    void *_a[] = {(void*)&qax_result, (void*)&upTime, (void*)&fullTime, (void*)&downTime, (void*)&lowTime, (void*)&lowValue, (void*)&testTimes, (void*)&fallType, (void*)&fallNum, (void*)&higva, (void*)&cl};
    qt_metacall(QMetaObject::InvokeMetaMethod, 13, _a);
    return qax_result;
}

inline QString IEquipmentSJJ::GetMonitor()
{
    QString qax_result;
    void *_a[] = {(void*)&qax_result};
    qt_metacall(QMetaObject::InvokeMetaMethod, 14, _a);
    return qax_result;
}

inline double IEquipmentSJJ::GetPhaseDegree(uint ptpNo, const QString& capacitive, uint direction, uint phase)
{
    double qax_result;
    void *_a[] = {(void*)&qax_result, (void*)&ptpNo, (void*)&capacitive, (void*)&direction, (void*)&phase};
    qt_metacall(QMetaObject::InvokeMetaMethod, 15, _a);
    return qax_result;
}

inline double IEquipmentSJJ::GetRangeIb(double curri)
{
    double qax_result;
    void *_a[] = {(void*)&qax_result, (void*)&curri};
    qt_metacall(QMetaObject::InvokeMetaMethod, 16, _a);
    return qax_result;
}

inline double IEquipmentSJJ::GetRangeUbI(double curru)
{
    double qax_result;
    void *_a[] = {(void*)&qax_result, (void*)&curru};
    qt_metacall(QMetaObject::InvokeMetaMethod, 17, _a);
    return qax_result;
}

inline qlonglong IEquipmentSJJ::GetStemCTheory(double u, double I)
{
    qlonglong qax_result;
    void *_a[] = {(void*)&qax_result, (void*)&u, (void*)&I};
    qt_metacall(QMetaObject::InvokeMetaMethod, 18, _a);
    return qax_result;
}

inline QString IEquipmentSJJ::GetStemCTheoryStr(double u, double I)
{
    QString qax_result;
    void *_a[] = {(void*)&qax_result, (void*)&u, (void*)&I};
    qt_metacall(QMetaObject::InvokeMetaMethod, 19, _a);
    return qax_result;
}

inline bool IEquipmentSJJ::Power_ON(bool setPhaseOrder, double u, double I, const QString& phic, double f, double voltage, double current, uint wiringMode, uint workPhase, uint ptpNo)
{
    bool qax_result;
    void *_a[] = {(void*)&qax_result, (void*)&setPhaseOrder, (void*)&u, (void*)&I, (void*)&phic, (void*)&f, (void*)&voltage, (void*)&current, (void*)&wiringMode, (void*)&workPhase, (void*)&ptpNo};
    qt_metacall(QMetaObject::InvokeMetaMethod, 20, _a);
    return qax_result;
}

inline bool IEquipmentSJJ::SendComAA()
{
    bool qax_result;
    void *_a[] = {(void*)&qax_result};
    qt_metacall(QMetaObject::InvokeMetaMethod, 21, _a);
    return qax_result;
}

inline bool IEquipmentSJJ::SendCommChkEnd()
{
    bool qax_result;
    void *_a[] = {(void*)&qax_result};
    qt_metacall(QMetaObject::InvokeMetaMethod, 22, _a);
    return qax_result;
}

inline bool IEquipmentSJJ::SendCommChkSet(bool setPhaseOrder, uint ptpNo)
{
    bool qax_result;
    void *_a[] = {(void*)&qax_result, (void*)&setPhaseOrder, (void*)&ptpNo};
    qt_metacall(QMetaObject::InvokeMetaMethod, 23, _a);
    return qax_result;
}

inline bool IEquipmentSJJ::SendCommF(double fltF)
{
    bool qax_result;
    void *_a[] = {(void*)&qax_result, (void*)&fltF};
    qt_metacall(QMetaObject::InvokeMetaMethod, 24, _a);
    return qax_result;
}

inline bool IEquipmentSJJ::SendCommHarmonicContent(const QString& uContents, const QString& iContents)
{
    bool qax_result;
    void *_a[] = {(void*)&qax_result, (void*)&uContents, (void*)&iContents};
    qt_metacall(QMetaObject::InvokeMetaMethod, 25, _a);
    return qax_result;
}

inline bool IEquipmentSJJ::SendCommHarmonicSet(uint harmonic)
{
    bool qax_result;
    void *_a[] = {(void*)&qax_result, (void*)&harmonic};
    qt_metacall(QMetaObject::InvokeMetaMethod, 26, _a);
    return qax_result;
}

inline bool IEquipmentSJJ::SendCommI(double current, uint wiringMode, uint workPhase, bool bolNotAuto)
{
    bool qax_result;
    void *_a[] = {(void*)&qax_result, (void*)&current, (void*)&wiringMode, (void*)&workPhase, (void*)&bolNotAuto};
    qt_metacall(QMetaObject::InvokeMetaMethod, 27, _a);
    return qax_result;
}

inline bool IEquipmentSJJ::SendCommIabc(double fltIa, double fltIb, double fltIc)
{
    bool qax_result;
    void *_a[] = {(void*)&qax_result, (void*)&fltIa, (void*)&fltIb, (void*)&fltIc};
    qt_metacall(QMetaObject::InvokeMetaMethod, 28, _a);
    return qax_result;
}

inline bool IEquipmentSJJ::SendCommOpen(bool bOpen)
{
    bool qax_result;
    void *_a[] = {(void*)&qax_result, (void*)&bOpen};
    qt_metacall(QMetaObject::InvokeMetaMethod, 29, _a);
    return qax_result;
}

inline bool IEquipmentSJJ::SendCommP(double fltP, uint workPhase)
{
    bool qax_result;
    void *_a[] = {(void*)&qax_result, (void*)&fltP, (void*)&workPhase};
    qt_metacall(QMetaObject::InvokeMetaMethod, 30, _a);
    return qax_result;
}

inline bool IEquipmentSJJ::SendCommPabc(double fltPa, double fltPb, double fltPc)
{
    bool qax_result;
    void *_a[] = {(void*)&qax_result, (void*)&fltPa, (void*)&fltPb, (void*)&fltPc};
    qt_metacall(QMetaObject::InvokeMetaMethod, 31, _a);
    return qax_result;
}

inline bool IEquipmentSJJ::SendCommRangeNo(double voltage, double current)
{
    bool qax_result;
    void *_a[] = {(void*)&qax_result, (void*)&voltage, (void*)&current};
    qt_metacall(QMetaObject::InvokeMetaMethod, 32, _a);
    return qax_result;
}

inline bool IEquipmentSJJ::SendCommSpeedUI(int seconds)
{
    bool qax_result;
    void *_a[] = {(void*)&qax_result, (void*)&seconds};
    qt_metacall(QMetaObject::InvokeMetaMethod, 33, _a);
    return qax_result;
}

inline bool IEquipmentSJJ::SendCommSpeedUI_2(int uspeed, int ispeed)
{
    bool qax_result;
    void *_a[] = {(void*)&qax_result, (void*)&uspeed, (void*)&ispeed};
    qt_metacall(QMetaObject::InvokeMetaMethod, 34, _a);
    return qax_result;
}

inline bool IEquipmentSJJ::SendCommU(double voltage, uint wiringMode, uint workPhase, bool bolNotAuto)
{
    bool qax_result;
    void *_a[] = {(void*)&qax_result, (void*)&voltage, (void*)&wiringMode, (void*)&workPhase, (void*)&bolNotAuto};
    qt_metacall(QMetaObject::InvokeMetaMethod, 35, _a);
    return qax_result;
}

inline void IEquipmentSJJ::SendCommUIDownRapid()
{
    void *_a[] = {0};
    qt_metacall(QMetaObject::InvokeMetaMethod, 36, _a);
}

inline bool IEquipmentSJJ::SendCommUabc(double fltUa, double fltUb, double fltUc)
{
    bool qax_result;
    void *_a[] = {(void*)&qax_result, (void*)&fltUa, (void*)&fltUb, (void*)&fltUc};
    qt_metacall(QMetaObject::InvokeMetaMethod, 37, _a);
    return qax_result;
}

inline bool IEquipmentSJJ::SendCommVoltageDropBreak(int breakTimes, int voltagePercentage, double dropTimes, double recoveryTimes)
{
    bool qax_result;
    void *_a[] = {(void*)&qax_result, (void*)&breakTimes, (void*)&voltagePercentage, (void*)&dropTimes, (void*)&recoveryTimes};
    qt_metacall(QMetaObject::InvokeMetaMethod, 38, _a);
    return qax_result;
}

inline bool IEquipmentSJJ::SendCommVoltageDropBreak_2(int dropMode, qlonglong dropTimes, const QStringList& paras)
{
    bool qax_result;
    void *_a[] = {(void*)&qax_result, (void*)&dropMode, (void*)&dropTimes, (void*)&paras};
    qt_metacall(QMetaObject::InvokeMetaMethod, 39, _a);
    return qax_result;
}

inline bool IEquipmentSJJ::SendCommfs(int value)
{
    bool qax_result;
    void *_a[] = {(void*)&qax_result, (void*)&value};
    qt_metacall(QMetaObject::InvokeMetaMethod, 40, _a);
    return qax_result;
}

inline qlonglong IEquipmentSJJ::SendWantStemC()
{
    qlonglong qax_result;
    void *_a[] = {(void*)&qax_result};
    qt_metacall(QMetaObject::InvokeMetaMethod, 41, _a);
    return qax_result;
}

inline void IEquipmentSJJ::openCOM(const QString& commport)
{
    void *_a[] = {0, (void*)&commport};
    qt_metacall(QMetaObject::InvokeMetaMethod, 42, _a);
}


inline QString IEquipmentWC::CheckRemoteKnifeSwitch(int meterCount, uint inttell)
{
    QString qax_result;
    void *_a[] = {(void*)&qax_result, (void*)&meterCount, (void*)&inttell};
    qt_metacall(QMetaObject::InvokeMetaMethod, 8, _a);
    return qax_result;
}

inline void IEquipmentWC::Close()
{
    void *_a[] = {0};
    qt_metacall(QMetaObject::InvokeMetaMethod, 9, _a);
}

inline QString IEquipmentWC::ConnectEquipment(const QString& wcPortname)
{
    QString qax_result;
    void *_a[] = {(void*)&qax_result, (void*)&wcPortname};
    qt_metacall(QMetaObject::InvokeMetaMethod, 10, _a);
    return qax_result;
}

inline bool IEquipmentWC::Relay_EnVFall_Start(const QString& meterIndexs, int abc, int intervalMillisecond, int ioMillisecond, int repeatsCount)
{
    bool qax_result;
    void *_a[] = {(void*)&qax_result, (void*)&meterIndexs, (void*)&abc, (void*)&intervalMillisecond, (void*)&ioMillisecond, (void*)&repeatsCount};
    qt_metacall(QMetaObject::InvokeMetaMethod, 11, _a);
    return qax_result;
}

inline void IEquipmentWC::wAutoShutdown(int emno, uint meterStatus)
{
    void *_a[] = {0, (void*)&emno, (void*)&meterStatus};
    qt_metacall(QMetaObject::InvokeMetaMethod, 12, _a);
}

inline bool IEquipmentWC::wGet_Send_Base_Info(int set485Chanels, int emno, int dlt, int baudrate, const QString& gaddress, int delaytimes, int delaytimes2)
{
    bool qax_result;
    void *_a[] = {(void*)&qax_result, (void*)&set485Chanels, (void*)&emno, (void*)&dlt, (void*)&baudrate, (void*)&gaddress, (void*)&delaytimes, (void*)&delaytimes2};
    qt_metacall(QMetaObject::InvokeMetaMethod, 13, _a);
    return qax_result;
}

inline bool IEquipmentWC::wGet_Send_Base_Info_2(int set485Chanels, int emno, int dlt, const QString& baudrate, const QString& gaddress)
{
    bool qax_result;
    void *_a[] = {(void*)&qax_result, (void*)&set485Chanels, (void*)&emno, (void*)&dlt, (void*)&baudrate, (void*)&gaddress};
    qt_metacall(QMetaObject::InvokeMetaMethod, 14, _a);
    return qax_result;
}

inline void IEquipmentWC::wSendBegin_End_YaoCe(uint emno, int status)
{
    void *_a[] = {0, (void*)&emno, (void*)&status};
    qt_metacall(QMetaObject::InvokeMetaMethod, 15, _a);
}

inline void IEquipmentWC::wSendCommChkPBegin()
{
    void *_a[] = {0};
    qt_metacall(QMetaObject::InvokeMetaMethod, 16, _a);
}

inline bool IEquipmentWC::wSendCommChkPEnd()
{
    bool qax_result;
    void *_a[] = {(void*)&qax_result};
    qt_metacall(QMetaObject::InvokeMetaMethod, 17, _a);
    return qax_result;
}

inline bool IEquipmentWC::wSendCommChkType(int value)
{
    bool qax_result;
    void *_a[] = {(void*)&qax_result, (void*)&value};
    qt_metacall(QMetaObject::InvokeMetaMethod, 18, _a);
    return qax_result;
}

inline void IEquipmentWC::wSendCommEMC(QVariantList meterC, int currfcoe)
{
    void *_a[] = {0, (void*)&meterC, (void*)&currfcoe};
    qt_metacall(QMetaObject::InvokeMetaMethod, 19, _a);
}

inline void IEquipmentWC::wSendCommEMCAll(qlonglong meterC, int currfcoe)
{
    void *_a[] = {0, (void*)&meterC, (void*)&currfcoe};
    qt_metacall(QMetaObject::InvokeMetaMethod, 20, _a);
}

inline void IEquipmentWC::wSendCommEMCAll_2(const QString& meterC, int currfcoe)
{
    void *_a[] = {0, (void*)&meterC, (void*)&currfcoe};
    qt_metacall(QMetaObject::InvokeMetaMethod, 21, _a);
}

inline void IEquipmentWC::wSendCommEMC_2(QVariantList meterC)
{
    void *_a[] = {0, (void*)&meterC};
    qt_metacall(QMetaObject::InvokeMetaMethod, 22, _a);
}

inline void IEquipmentWC::wSendCommEMC_3(const QStringList& meterC)
{
    void *_a[] = {0, (void*)&meterC};
    qt_metacall(QMetaObject::InvokeMetaMethod, 23, _a);
}

inline void IEquipmentWC::wSendCommFcoe(double fcoe)
{
    void *_a[] = {0, (void*)&fcoe};
    qt_metacall(QMetaObject::InvokeMetaMethod, 24, _a);
}

inline void IEquipmentWC::wSendCommFcoeNeedt(int second)
{
    void *_a[] = {0, (void*)&second};
    qt_metacall(QMetaObject::InvokeMetaMethod, 25, _a);
}

inline void IEquipmentWC::wSendCommN(uint chkR)
{
    void *_a[] = {0, (void*)&chkR};
    qt_metacall(QMetaObject::InvokeMetaMethod, 26, _a);
}

inline void IEquipmentWC::wSendCommPulseType(bool direction)
{
    void *_a[] = {0, (void*)&direction};
    qt_metacall(QMetaObject::InvokeMetaMethod, 27, _a);
}

inline void IEquipmentWC::wSendCommSTEMC(qlonglong stemc)
{
    void *_a[] = {0, (void*)&stemc};
    qt_metacall(QMetaObject::InvokeMetaMethod, 28, _a);
}

inline void IEquipmentWC::wSendCommSTEMC_2(const QString& stemc)
{
    void *_a[] = {0, (void*)&stemc};
    qt_metacall(QMetaObject::InvokeMetaMethod, 29, _a);
}

inline QString IEquipmentWC::wSendCommarr_ReadERR(uint curremno)
{
    QString qax_result;
    void *_a[] = {(void*)&qax_result, (void*)&curremno};
    qt_metacall(QMetaObject::InvokeMetaMethod, 30, _a);
    return qax_result;
}

inline QString IEquipmentWC::wSendCommarr_ReadPulse(uint curremno)
{
    QString qax_result;
    void *_a[] = {(void*)&qax_result, (void*)&curremno};
    qt_metacall(QMetaObject::InvokeMetaMethod, 31, _a);
    return qax_result;
}

inline QString IEquipmentWC::wSendCommarr_ZZ(uint curremno)
{
    QString qax_result;
    void *_a[] = {(void*)&qax_result, (void*)&curremno};
    qt_metacall(QMetaObject::InvokeMetaMethod, 32, _a);
    return qax_result;
}

inline bool IEquipmentWC::wSendCtrolVoltageOnandOff(uint curremno, int openOrclose)
{
    bool qax_result;
    void *_a[] = {(void*)&qax_result, (void*)&curremno, (void*)&openOrclose};
    qt_metacall(QMetaObject::InvokeMetaMethod, 33, _a);
    return qax_result;
}

inline void IEquipmentWC::wSendMKSignal(uint emno, int status)
{
    void *_a[] = {0, (void*)&emno, (void*)&status};
    qt_metacall(QMetaObject::InvokeMetaMethod, 34, _a);
}

inline void IEquipmentWC::wSendMPulsef(uint emno, int chanel, int mcycle, int mcNums)
{
    void *_a[] = {0, (void*)&emno, (void*)&chanel, (void*)&mcycle, (void*)&mcNums};
    qt_metacall(QMetaObject::InvokeMetaMethod, 35, _a);
}

inline bool IEquipmentWC::wSendOpen485Chanels(int setConnector)
{
    bool qax_result;
    void *_a[] = {(void*)&qax_result, (void*)&setConnector};
    qt_metacall(QMetaObject::InvokeMetaMethod, 36, _a);
    return qax_result;
}

inline void IEquipmentWC::wSendOpen485Chanels_2(bool firstChanel)
{
    void *_a[] = {0, (void*)&firstChanel};
    qt_metacall(QMetaObject::InvokeMetaMethod, 37, _a);
}

inline bool IEquipmentWC::wSendOpen485Chanels_3(uint curremno, bool open485)
{
    bool qax_result;
    void *_a[] = {(void*)&qax_result, (void*)&curremno, (void*)&open485};
    qt_metacall(QMetaObject::InvokeMetaMethod, 38, _a);
    return qax_result;
}

inline bool IEquipmentWC::wSendOpenConnect(uint emno)
{
    bool qax_result;
    void *_a[] = {(void*)&qax_result, (void*)&emno};
    qt_metacall(QMetaObject::InvokeMetaMethod, 39, _a);
    return qax_result;
}

inline bool IEquipmentWC::wSendOpenPulseChanel(uint emno, uint chanel)
{
    bool qax_result;
    void *_a[] = {(void*)&qax_result, (void*)&emno, (void*)&chanel};
    qt_metacall(QMetaObject::InvokeMetaMethod, 40, _a);
    return qax_result;
}

inline bool IEquipmentWC::wSendReUCheck(int emno, uint inttell)
{
    bool qax_result;
    void *_a[] = {(void*)&qax_result, (void*)&emno, (void*)&inttell};
    qt_metacall(QMetaObject::InvokeMetaMethod, 41, _a);
    return qax_result;
}

inline QByteArray IEquipmentWC::wSendReadBaseInfoData(int emno, int set485Chanels)
{
    QByteArray qax_result;
    void *_a[] = {(void*)&qax_result, (void*)&emno, (void*)&set485Chanels};
    qt_metacall(QMetaObject::InvokeMetaMethod, 42, _a);
    return qax_result;
}

inline QByteArray IEquipmentWC::wSendReadTransData(int emno, int set485Chanels)
{
    QByteArray qax_result;
    void *_a[] = {(void*)&qax_result, (void*)&emno, (void*)&set485Chanels};
    qt_metacall(QMetaObject::InvokeMetaMethod, 43, _a);
    return qax_result;
}

inline void IEquipmentWC::wSendSwitchTml485Channel(uint emno, int status)
{
    void *_a[] = {0, (void*)&emno, (void*)&status};
    qt_metacall(QMetaObject::InvokeMetaMethod, 44, _a);
}

inline bool IEquipmentWC::wSendSwitchWeakTmnlChannel(uint emno, int status)
{
    bool qax_result;
    void *_a[] = {(void*)&qax_result, (void*)&emno, (void*)&status};
    qt_metacall(QMetaObject::InvokeMetaMethod, 45, _a);
    return qax_result;
}

inline bool IEquipmentWC::wSendTransData(int emno, const QByteArray& arr, int set485Chanels)
{
    bool qax_result;
    void *_a[] = {(void*)&qax_result, (void*)&emno, (void*)&arr, (void*)&set485Chanels};
    qt_metacall(QMetaObject::InvokeMetaMethod, 46, _a);
    return qax_result;
}

inline bool IEquipmentWC::wSendUCheck(int emno, int chku)
{
    bool qax_result;
    void *_a[] = {(void*)&qax_result, (void*)&emno, (void*)&chku};
    qt_metacall(QMetaObject::InvokeMetaMethod, 47, _a);
    return qax_result;
}

inline bool IEquipmentWC::wSendVouttoUpandDown(const QString& meterIndexs, double upTime, double dOtime, int num)
{
    bool qax_result;
    void *_a[] = {(void*)&qax_result, (void*)&meterIndexs, (void*)&upTime, (void*)&dOtime, (void*)&num};
    qt_metacall(QMetaObject::InvokeMetaMethod, 48, _a);
    return qax_result;
}

inline void IEquipmentWC::wSendWorkModel(uint emno, int status)
{
    void *_a[] = {0, (void*)&emno, (void*)&status};
    qt_metacall(QMetaObject::InvokeMetaMethod, 49, _a);
}

inline QString IEquipmentWC::wSend_Get_YaoCe(uint emno)
{
    QString qax_result;
    void *_a[] = {(void*)&qax_result, (void*)&emno};
    qt_metacall(QMetaObject::InvokeMetaMethod, 50, _a);
    return qax_result;
}

inline QByteArray IEquipmentWC::wSendandReadTransData(int emno, const QByteArray& arr, int set485Chanels, int time)
{
    QByteArray qax_result;
    void *_a[] = {(void*)&qax_result, (void*)&emno, (void*)&arr, (void*)&set485Chanels, (void*)&time};
    qt_metacall(QMetaObject::InvokeMetaMethod, 51, _a);
    return qax_result;
}

inline QVariantList IEquipmentWC::wc_readdatereturn(uint enmo)
{
    QVariantList qax_result;
    void *_a[] = {(void*)&qax_result, (void*)&enmo};
    qt_metacall(QMetaObject::InvokeMetaMethod, 52, _a);
    return qax_result;
}


inline bool IIndustrialControlPower::ControlVoteage(bool open)
{
    bool qax_result;
    void *_a[] = {(void*)&qax_result, (void*)&open};
    qt_metacall(QMetaObject::InvokeMetaMethod, 8, _a);
    return qax_result;
}

inline bool IIndustrialControlPower::ControlVoteage(bool open, const QString& gear)
{
    bool qax_result;
    void *_a[] = {(void*)&qax_result, (void*)&open, (void*)&gear};
    qt_metacall(QMetaObject::InvokeMetaMethod, 9, _a);
    return qax_result;
}


inline QString IndustrialControlPower::ToString() const
{
    QVariant qax_result = property("ToString");
    Q_ASSERT(qax_result.isValid());
    return *(QString*)qax_result.constData();
}

inline bool IndustrialControlPower::ControlVoteage(bool open)
{
    bool qax_result;
    void *_a[] = {(void*)&qax_result, (void*)&open};
    qt_metacall(QMetaObject::InvokeMetaMethod, 8, _a);
    return qax_result;
}

inline bool IndustrialControlPower::ControlVoteage(bool open, const QString& gear)
{
    bool qax_result;
    void *_a[] = {(void*)&qax_result, (void*)&open, (void*)&gear};
    qt_metacall(QMetaObject::InvokeMetaMethod, 9, _a);
    return qax_result;
}

inline bool IndustrialControlPower::Equals(const QVariant& obj)
{
    bool qax_result;
    void *_a[] = {(void*)&qax_result, (void*)&obj};
    qt_metacall(QMetaObject::InvokeMetaMethod, 10, _a);
    return qax_result;
}

inline int IndustrialControlPower::GetHashCode()
{
    int qax_result;
    void *_a[] = {(void*)&qax_result};
    qt_metacall(QMetaObject::InvokeMetaMethod, 11, _a);
    return qax_result;
}

inline mscorlib::_Type* IndustrialControlPower::GetType()
{
    mscorlib::_Type* qax_result = 0;
#ifdef QAX_DUMPCPP_MSCORLIB_H
    qRegisterMetaType<mscorlib::_Type*>("mscorlib::_Type*", &qax_result);
    qRegisterMetaType<mscorlib::_Type>("mscorlib::_Type", qax_result);
#endif
    void *_a[] = {(void*)&qax_result};
    qt_metacall(QMetaObject::InvokeMetaMethod, 12, _a);
    return qax_result;
}



#endif

}

QT_BEGIN_NAMESPACE

namespace QtMetaTypePrivate {
template<>
struct QMetaTypeFunctionHelper<COMEquipment::EquipmentNz2230, /* Accepted */ true> {
    static void Destruct(void *t)
    {
        Q_UNUSED(t)
        static_cast<COMEquipment::EquipmentNz2230*>(t)->COMEquipment::EquipmentNz2230::~EquipmentNz2230();
    }
    static void *Construct(void *where, const void *t)
    {
        Q_ASSERT(!t);
        Q_UNUSED(t)
        return new (where) COMEquipment::EquipmentNz2230;
    }
#ifndef QT_NO_DATASTREAM
    static void Save(QDataStream &stream, const void *t) { stream << *static_cast<const COMEquipment::EquipmentNz2230*>(t); }
    static void Load(QDataStream &stream, void *t) { stream >> *static_cast<COMEquipment::EquipmentNz2230*>(t); }
#endif // QT_NO_DATASTREAM
};

template<>
struct QMetaTypeFunctionHelper<COMEquipment::EquipmentSd2000, /* Accepted */ true> {
    static void Destruct(void *t)
    {
        Q_UNUSED(t)
        static_cast<COMEquipment::EquipmentSd2000*>(t)->COMEquipment::EquipmentSd2000::~EquipmentSd2000();
    }
    static void *Construct(void *where, const void *t)
    {
        Q_ASSERT(!t);
        Q_UNUSED(t)
        return new (where) COMEquipment::EquipmentSd2000;
    }
#ifndef QT_NO_DATASTREAM
    static void Save(QDataStream &stream, const void *t) { stream << *static_cast<const COMEquipment::EquipmentSd2000*>(t); }
    static void Load(QDataStream &stream, void *t) { stream >> *static_cast<COMEquipment::EquipmentSd2000*>(t); }
#endif // QT_NO_DATASTREAM
};

template<>
struct QMetaTypeFunctionHelper<COMEquipment::EquipmentWc, /* Accepted */ true> {
    static void Destruct(void *t)
    {
        Q_UNUSED(t)
        static_cast<COMEquipment::EquipmentWc*>(t)->COMEquipment::EquipmentWc::~EquipmentWc();
    }
    static void *Construct(void *where, const void *t)
    {
        Q_ASSERT(!t);
        Q_UNUSED(t)
        return new (where) COMEquipment::EquipmentWc;
    }
#ifndef QT_NO_DATASTREAM
    static void Save(QDataStream &stream, const void *t) { stream << *static_cast<const COMEquipment::EquipmentWc*>(t); }
    static void Load(QDataStream &stream, void *t) { stream >> *static_cast<COMEquipment::EquipmentWc*>(t); }
#endif // QT_NO_DATASTREAM
};

template<>
struct QMetaTypeFunctionHelper<COMEquipment::IEquipmentSJJ, /* Accepted */ true> {
    static void Destruct(void *t)
    {
        Q_UNUSED(t)
        static_cast<COMEquipment::IEquipmentSJJ*>(t)->COMEquipment::IEquipmentSJJ::~IEquipmentSJJ();
    }
    static void *Construct(void *where, const void *t)
    {
        Q_ASSERT(!t);
        Q_UNUSED(t)
        return new (where) COMEquipment::IEquipmentSJJ;
    }
#ifndef QT_NO_DATASTREAM
    static void Save(QDataStream &stream, const void *t) { stream << *static_cast<const COMEquipment::IEquipmentSJJ*>(t); }
    static void Load(QDataStream &stream, void *t) { stream >> *static_cast<COMEquipment::IEquipmentSJJ*>(t); }
#endif // QT_NO_DATASTREAM
};

template<>
struct QMetaTypeFunctionHelper<COMEquipment::IEquipmentWC, /* Accepted */ true> {
    static void Destruct(void *t)
    {
        Q_UNUSED(t)
        static_cast<COMEquipment::IEquipmentWC*>(t)->COMEquipment::IEquipmentWC::~IEquipmentWC();
    }
    static void *Construct(void *where, const void *t)
    {
        Q_ASSERT(!t);
        Q_UNUSED(t)
        return new (where) COMEquipment::IEquipmentWC;
    }
#ifndef QT_NO_DATASTREAM
    static void Save(QDataStream &stream, const void *t) { stream << *static_cast<const COMEquipment::IEquipmentWC*>(t); }
    static void Load(QDataStream &stream, void *t) { stream >> *static_cast<COMEquipment::IEquipmentWC*>(t); }
#endif // QT_NO_DATASTREAM
};

template<>
struct QMetaTypeFunctionHelper<COMEquipment::IIndustrialControlPower, /* Accepted */ true> {
    static void Destruct(void *t)
    {
        Q_UNUSED(t)
        static_cast<COMEquipment::IIndustrialControlPower*>(t)->COMEquipment::IIndustrialControlPower::~IIndustrialControlPower();
    }
    static void *Construct(void *where, const void *t)
    {
        Q_ASSERT(!t);
        Q_UNUSED(t)
        return new (where) COMEquipment::IIndustrialControlPower;
    }
#ifndef QT_NO_DATASTREAM
    static void Save(QDataStream &stream, const void *t) { stream << *static_cast<const COMEquipment::IIndustrialControlPower*>(t); }
    static void Load(QDataStream &stream, void *t) { stream >> *static_cast<COMEquipment::IIndustrialControlPower*>(t); }
#endif // QT_NO_DATASTREAM
};

template<>
struct QMetaTypeFunctionHelper<COMEquipment::IndustrialControlPower, /* Accepted */ true> {
    static void Destruct(void *t)
    {
        Q_UNUSED(t)
        static_cast<COMEquipment::IndustrialControlPower*>(t)->COMEquipment::IndustrialControlPower::~IndustrialControlPower();
    }
    static void *Construct(void *where, const void *t)
    {
        Q_ASSERT(!t);
        Q_UNUSED(t)
        return new (where) COMEquipment::IndustrialControlPower;
    }
#ifndef QT_NO_DATASTREAM
    static void Save(QDataStream &stream, const void *t) { stream << *static_cast<const COMEquipment::IndustrialControlPower*>(t); }
    static void Load(QDataStream &stream, void *t) { stream >> *static_cast<COMEquipment::IndustrialControlPower*>(t); }
#endif // QT_NO_DATASTREAM
};

} // namespace QtMetaTypePrivate
QT_END_NAMESPACE

#endif


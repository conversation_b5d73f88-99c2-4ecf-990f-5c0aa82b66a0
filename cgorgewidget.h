﻿#ifndef CGORGEWIDGET_H
#define CGORGEWIDGET_H

#include <QWidget>
#include "msk_global.h"

namespace Ui {
class CGorgeWidget;
}
enum gorgeBW
{
    gorgeBW_bw,       // 表位
    gorgeBW_mc,       // 串口名
    gorgeBW_bw2,       // 表位
    gorgeBW_mc2,       // 串口名
    gorgeBW_max
};


enum gorge
{
    gorge_bw,       // 表位
    gorge_mc,       // 串口名
    gorge_max
};

class CGorgeWidget : public QWidget
{
    Q_OBJECT

public:
    explicit CGorgeWidget(QWidget *parent = nullptr);
    ~CGorgeWidget();
    void initGorge();

private slots:

    void on_pushButton_clicked();       // 确定

    void on_pushButton_2_clicked();     // 取消

    void on_lxckhBtn_clicked();         // 连续串口号

    void on_pushButton_4_clicked();     // 连续串口号

    void on_pushButton_3_clicked();         // 保存

    void on_pushButton_5_clicked();

    void on_pushButton_6_clicked();

    void on_pushButton_7_clicked();

private:
    void tableWidgetInit();

    void rs485Init();
    void rs485_2Init();

Q_SIGNALS:
    void gorgeSignals(std::map<int, QString >&);

private:
    Ui::CGorgeWidget *ui;
};

#endif // CGORGEWIDGET_H

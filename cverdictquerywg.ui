<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>CVerdictQueryWg</class>
 <widget class="QWidget" name="CVerdictQueryWg">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>850</width>
    <height>618</height>
   </rect>
  </property>
  <property name="font">
   <font>
    <pointsize>12</pointsize>
   </font>
  </property>
  <property name="windowTitle">
   <string>Form</string>
  </property>
  <layout class="QGridLayout" name="gridLayout">
   <item row="0" column="0">
    <layout class="QHBoxLayout" name="horizontalLayout">
     <item>
      <widget class="QLabel" name="label">
       <property name="text">
        <string>日期</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QDateTimeEdit" name="beginDT">
       <property name="minimumSize">
        <size>
         <width>0</width>
         <height>30</height>
        </size>
       </property>
       <property name="date">
        <date>
         <year>2000</year>
         <month>1</month>
         <day>1</day>
        </date>
       </property>
       <property name="displayFormat">
        <string>yyyy/MM/dd HH:mm:ss</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QLabel" name="label_2">
       <property name="text">
        <string> - </string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QDateTimeEdit" name="endDT">
       <property name="minimumSize">
        <size>
         <width>0</width>
         <height>30</height>
        </size>
       </property>
       <property name="date">
        <date>
         <year>2000</year>
         <month>1</month>
         <day>1</day>
        </date>
       </property>
       <property name="displayFormat">
        <string>yyyy/MM/dd HH:mm:ss</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QLabel" name="label_3">
       <property name="text">
        <string>  方案</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QComboBox" name="faCB">
       <property name="sizePolicy">
        <sizepolicy hsizetype="Expanding" vsizetype="Fixed">
         <horstretch>0</horstretch>
         <verstretch>0</verstretch>
        </sizepolicy>
       </property>
       <property name="minimumSize">
        <size>
         <width>0</width>
         <height>30</height>
        </size>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QLabel" name="label_4">
       <property name="text">
        <string>  批次</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QComboBox" name="pcCB">
       <property name="sizePolicy">
        <sizepolicy hsizetype="Expanding" vsizetype="Fixed">
         <horstretch>0</horstretch>
         <verstretch>0</verstretch>
        </sizepolicy>
       </property>
       <property name="minimumSize">
        <size>
         <width>0</width>
         <height>30</height>
        </size>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QCheckBox" name="checkBox">
       <property name="text">
        <string>ID</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QLineEdit" name="idLE">
       <property name="enabled">
        <bool>false</bool>
       </property>
       <property name="minimumSize">
        <size>
         <width>0</width>
         <height>30</height>
        </size>
       </property>
      </widget>
     </item>
    </layout>
   </item>
   <item row="1" column="0">
    <layout class="QHBoxLayout" name="horizontalLayout_2">
     <item>
      <spacer name="horizontalSpacer">
       <property name="orientation">
        <enum>Qt::Horizontal</enum>
       </property>
       <property name="sizeHint" stdset="0">
        <size>
         <width>40</width>
         <height>20</height>
        </size>
       </property>
      </spacer>
     </item>
     <item>
      <widget class="QPushButton" name="queryBtn">
       <property name="styleSheet">
        <string notr="true">    QPushButton {background-color:#0089ff ;
    color: white;
    border-style: outset;
    border-width: 2px;
    border-radius: 10px;
    border-color: beige;
    font-size: 26px;
    padding: 6px;}

QPushButton:hover {background-color: #0072C6;         
                     }
</string>
       </property>
       <property name="text">
        <string>查询</string>
       </property>
      </widget>
     </item>
    </layout>
   </item>
   <item row="2" column="0">
    <widget class="QSplitter" name="splitter">
     <property name="orientation">
      <enum>Qt::Vertical</enum>
     </property>
     <widget class="QTableWidget" name="csdlTW">
      <property name="textElideMode">
       <enum>Qt::ElideRight</enum>
      </property>
     </widget>
     <widget class="QWidget" name="">
      <layout class="QHBoxLayout" name="horizontalLayout_3">
       <item>
        <widget class="QTableWidget" name="csxlTW">
         <property name="sizePolicy">
          <sizepolicy hsizetype="Preferred" vsizetype="Expanding">
           <horstretch>0</horstretch>
           <verstretch>0</verstretch>
          </sizepolicy>
         </property>
         <property name="minimumSize">
          <size>
           <width>800</width>
           <height>0</height>
          </size>
         </property>
         <property name="maximumSize">
          <size>
           <width>800</width>
           <height>16777215</height>
          </size>
         </property>
         <attribute name="horizontalHeaderCascadingSectionResizes">
          <bool>false</bool>
         </attribute>
         <attribute name="horizontalHeaderDefaultSectionSize">
          <number>260</number>
         </attribute>
         <attribute name="horizontalHeaderStretchLastSection">
          <bool>false</bool>
         </attribute>
        </widget>
       </item>
       <item>
        <widget class="QTextEdit" name="gcTE"/>
       </item>
      </layout>
     </widget>
    </widget>
   </item>
  </layout>
 </widget>
 <resources/>
 <connections/>
</ui>

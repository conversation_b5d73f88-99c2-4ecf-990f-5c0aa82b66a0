﻿#include "msk_global.h"

QString strScuOutAutoCheckIni = "C:/msk_scuoutautocheckIni/scuoutautocheck.ini";
QString strScuRqAppIni = "C:/msk_scuoutautocheckIni/scucheckrqapp.ini";
QString g_strScuoutcheckdb = "C:/msk_scuoutautocheckIni/scuoutcheck.db";;
QString CheckItemIni="C:/msk_scuoutautocheckIni/checkitem.ini";
QString installIni ="C:/msk_scuoutautocheckIni/installjiaobiao.ini";
QWidget *g_pFuncWindow = nullptr;
bool g_bTestSign = false;
bool g_bHbdy = false;
bool g_bSW = false;
bool g_bJcjl[g_njcjlNum];
bool g_bLyQuery[10];
bool g_bYxFinish[15];
bool g_bRtcYx[5] ;
bool g_bygMc[15];
bool g_bwgMc[15];
bool g_bmMc[15];
bool g_bSingleExportFinish = false;
bool g_bCertExport = false;
bool g_btcpserver = false;
bool g_bopenclosecapFinish = false;
bool g_bqueryLj = false;
bool g_bScuModifyip = false;
bool g_bstartExport = false;
bool g_bstartImport = false;
bool g_blxdldy[2];
bool g_bYx[41];
QMutex g_mtx;
QMutex g_yxmtx;
QMutex  g_mtxCertExport;
std::vector<QString> g_vtChkItem;
std::map<QString, int> g_mapLyName;
std::map<int, int> g_mapMcNum;

SMonitor g_SMonitor;
QMutex g_mtxMonitor;

QString queryCfg(QString strIniPath,QString strGroup, QString strKey)
{
    QSettings  settings(strIniPath, QSettings::IniFormat);
    settings.setIniCodec(QTextCodec::codecForName("UTF-8"));

    settings.beginGroup(strGroup);
    QString s =  settings.value(strKey).toString();
    settings.endGroup();
    return s;
}

void setCfg(QString strIniPath, QString strGroup, QString strKey, QVariant ss)
{
    QSettings  settings(strIniPath, QSettings::IniFormat);
    settings.setIniCodec(QTextCodec::codecForName("UTF-8"));

    settings.beginGroup(strGroup);
    settings.setValue(strKey, ss);
    settings.endGroup();
}

void delKeyCfg(QString strIniPath, QString strGroup, QString strKey)
{
    QSettings  settings(strIniPath, QSettings::IniFormat);
    settings.setIniCodec(QTextCodec::codecForName("UTF-8"));

    settings.beginGroup(strGroup);
    settings.remove(strKey);
    settings.endGroup();
}

void delALLKeyCfg(QString strIniPath, QString strGroup)
{
    QSettings  settings(strIniPath, QSettings::IniFormat);
    settings.setIniCodec(QTextCodec::codecForName("UTF-8"));

    settings.beginGroup(strGroup);

    QStringList keyList = settings.allKeys();
    for(int m = 0; m < keyList.size(); ++m)
    {
        settings.remove(keyList[m]);
    }
    settings.endGroup();
}

QByteArray convertQStringToAsciiCodes(const QString &s)
{
    QString ss;
    for(QChar ch :s)
    {
        int asciiValue = ch.unicode();
        ss += QString::number(asciiValue, 16);
    }
    QByteArray cc = QByteArray::fromHex(ss.toUtf8());
    return cc;
}

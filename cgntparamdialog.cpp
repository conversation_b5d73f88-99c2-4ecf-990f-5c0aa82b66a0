﻿#include "cgntparamdialog.h"
#include "ui_cgntparamdialog.h"
#include <QMessageBox>
#include <QDebug>

CGntParamdialog::CGntParamdialog(QWidget *parent) :
    QDialog(parent),
    ui(new Ui::CGntParamdialog)
{
    ui->setupUi(this);
    m_nFAID = 0;

    checkItemInit();
    rqAppInit();

    connect(ui->iotCB, SIGNAL(toggled(bool)), this, SLOT(on_iotCB_toggl(bool)));
    connect(ui->wirCB, SIGNAL(toggled(bool)), this, SLOT(on_wirCB_toggl(bool)));
    connect(ui->scsCB, SIGNAL(toggled(bool)), this, SLOT(on_secCB_toggl(bool)));
    connect(ui->rqTW, SIGNAL(itemChanged(QTreeWidgetItem *, int)), this, SLOT(on_rqTW_itemChg(QTreeWidgetItem *, int)));

    m_pGwtxpz = nullptr;
    m_pZztxdz = nullptr;
    m_pYtwtxpz = nullptr;
    m_pTsd = nullptr;
    m_paqjmpz = nullptr;
}

CGntParamdialog::~CGntParamdialog()
{
    delete ui;
}

void CGntParamdialog::initParam(QString strName,quint64 nFAID)
{
    QString strTitle = strName + "方案参数配置";
    setWindowTitle(strTitle);
    m_nFAID = nFAID;

    m_strRqName = "gntparam_rq_" + QString::number(m_nFAID);
    m_strRqHashName = "gntparam_rqhash_" + QString::number(m_nFAID);
    m_strRqAppName = "gntparam_rqApp_" + QString::number(m_nFAID);
    m_strRqAppHashName = "gntparam_rqApphash_" + QString::number(m_nFAID);
    m_strRqwAppName = "gntparam_rqwApp_" + QString::number(m_nFAID);
    m_strJCXName = "jcx_" + QString::number(m_nFAID);
    m_strScuSysName = "scusys_" + QString::number(m_nFAID);
    m_strGwtxpzName = "gwtxpz_" + QString::number(m_nFAID);
    m_strGwzztxdzName = "gwzztxdz_" + QString::number(m_nFAID);
    m_strYtwzztxdzName = "ytwzztxdz_" + QString::number(m_nFAID);
    m_strYtwtxpzName = "ytwtxpz_" + QString::number(m_nFAID);
    m_strTsdcspzName = "tsdcspz_" + QString::number(m_nFAID);
    m_strAqmscsName = "aqmscspz_" + QString::number(m_nFAID);
    m_strOtherName = "other_" + QString::number(m_nFAID);
    // 读取方案配置

    QSettings  settings(strScuOutAutoCheckIni, QSettings::IniFormat);
    settings.setIniCodec(QTextCodec::codecForName("UTF-8"));

    settings.beginGroup(m_strRqName);
    QStringList strList = settings.allKeys();

    std::map<QString, int> mapRq;
    for (int m = 0; m < strList.size(); ++m)
    {
        mapRq[strList[m]] = settings.value(strList[m]).toInt();
    }
    settings.endGroup();

    std::map<QString, int> mapRqNApp;

    settings.beginGroup(m_strRqAppName);
    strList = settings.allKeys();
    for (int m = 0; m < strList.size(); ++m)
    {
        mapRqNApp[strList[m]] = settings.value(strList[m]).toInt();
    }
    settings.endGroup();

    for (int m = 0; m < ui->rqTW_2->topLevelItemCount(); ++m)
    {
        QTreeWidgetItem *top = ui->rqTW_2->topLevelItem(m);
        top->setHidden(true);
        for (int n = 0; n < top->childCount(); ++n)
        {
            QTreeWidgetItem *child = top->child(n);
            child->setHidden(true);
        }
    }

    std::map<QString, QString>mapRqHash;
    settings.beginGroup(m_strRqHashName);
    strList = settings.allKeys();
    for (int m = 0; m < strList.size(); ++m)
    {
        mapRqHash[strList[m]] = settings.value(strList[m]).toString();
    }
    settings.endGroup();

    std::map<QString, QString>mapRqAppHash;
    settings.beginGroup(m_strRqAppHashName);
    strList = settings.allKeys();
    for (int m = 0; m < strList.size(); ++m)
    {
        mapRqAppHash[strList[m]] = settings.value(strList[m]).toString();
    }
    settings.endGroup();

    for (int m = 0; m < ui->rqTW_2->topLevelItemCount(); ++m)
    {
        QTreeWidgetItem *top = ui->rqTW_2->topLevelItem(m);
        auto iter = mapRq.find(top->text(0));
        if(iter == mapRq.end())
            continue;
        if(iter->second == 1)
        {
            top->setHidden(false);

            auto it = mapRqHash.find(top->text(0));
            if(it != mapRqHash.end())
            {
                top->setText(2, it->second);
            }
        }

        for (int n = 0; n < top->childCount(); ++n)
        {
            QTreeWidgetItem *pitem = top->child(n);
            auto it = mapRqNApp.find(pitem->text(0));
            if(it == mapRqNApp.end())
            {
                continue;
            }
            if(it->second == 1)
            {
                pitem->setHidden(false);
                auto tor = mapRqAppHash.find(pitem->text(0));
                if(tor != mapRqAppHash.end())
                {
                    pitem->setText(2, tor->second);
                }
            }
        }
    }

    for (int m = 0; m < ui->rqTW->topLevelItemCount(); ++m)
    {
        QTreeWidgetItem *top = ui->rqTW->topLevelItem(m);
        QString s1 = top->text(0);
        auto iter = mapRq.find(s1);
        if(iter == mapRq.end())
        {
            top->setCheckState(0, Qt::Unchecked);
            continue;
        }
        if(iter->second == 1)
            top->setCheckState(0, Qt::Checked);
        else
            top->setCheckState(0, Qt::Unchecked);

        auto to = mapRqHash.find(s1);
        if(to != mapRqHash.end())
        {
            top->setText(2, to->second);
        }

        for (int k = 0; k < top->childCount(); ++k)
        {
            QTreeWidgetItem *pitem = top->child(k);
            auto it = mapRqNApp.find(pitem->text(0));
            if(it == mapRqNApp.end())
            {
                pitem->setCheckState(0, Qt::Unchecked);
                continue;
            }
            if(it->second == 1)
                pitem->setCheckState(0, Qt::Checked);
            else
                pitem->setCheckState(0, Qt::Unchecked);

            auto too = mapRqAppHash.find(pitem->text(0));
            if(too != mapRqAppHash.end())
            {
                pitem->setText(2, too->second);
            }
        }
    }

    settings.beginGroup(m_strRqwAppName);
    if(settings.value("iotManager").toBool())
    {
        ui->iotCB->setChecked(true);
        ui->iotCB_2->setChecked(true);
    }
    else
    {
        ui->iotCB->setChecked(false);
        ui->iotCB_2->setChecked(false);
    }

    if(settings.value("scsMonitor").toBool())
    {
        ui->scsCB_2->setChecked(true);
        ui->scsCB->setChecked(true);
    }
    else
    {
        ui->scsCB_2->setChecked(false);
        ui->scsCB->setChecked(false);
    }

    if(settings.value("wirelessDCM").toBool())
    {
        ui->wirCB_2->setChecked(true);
        ui->wirCB->setChecked(true);
    }
    else
    {
        ui->wirCB_2->setChecked(false);
        ui->wirCB->setChecked(false);
    }

    if(settings.value("IotAgent").toBool())
    {
        ui->iotAgentCB->setChecked(true);
        ui->iotAgen_2t->setChecked(true);
    }
    else
    {
        ui->iotAgentCB->setChecked(false);
        ui->iotAgen_2t->setChecked(false);
    }

    if(settings.value("ExecShell").toBool())
    {
        ui->ExeCB->setChecked(true);
        ui->ExeCB_2->setChecked(true);
    }
    else
    {
        ui->ExeCB->setChecked(false);
        ui->ExeCB_2->setChecked(false);
    }

    settings.endGroup();

    settings.beginGroup(m_strJCXName);
    QString strJcx = settings.value("jcx").toString();
    settings.endGroup();

    checkItem(strJcx);

    settings.beginGroup(m_strScuSysName);
    ui->db1LE->setText(settings.value("db1").toString());
    ui->db2LE->setText(settings.value("db2").toString());
    ui->zkLE->setText(settings.value("zkb").toString());
    ui->yjLE->setText(settings.value("yjb").toString());
    ui->nhLE->setText(settings.value("nhb").toString());
    ui->bhLE->setText(settings.value("bhb").toString());
    ui->rqLE->setText(settings.value("rqb").toString());
    ui->jcLE->setText(settings.value("jcb").toString());
    ui->gjLE->setText(settings.value("gjxtb").toString());
    ui->czLE->setText(settings.value("czxtb").toString());
    ui->cpuLE->setText(settings.value("cpumax").toString());
    ui->memLE->setText(settings.value("memmax").toString());

    ui->db1LE_3->setText(settings.value("db1").toString());
    ui->db2LE_3->setText(settings.value("db2").toString());
    ui->zkLE_3->setText(settings.value("zkb").toString());
    ui->yjLE_3->setText(settings.value("yjb").toString());
    ui->nhLE_3->setText(settings.value("nhb").toString());
    ui->bhLE_3->setText(settings.value("bhb").toString());
    ui->rqLE_3->setText(settings.value("rqb").toString());
    ui->jcLE_3->setText(settings.value("jcb").toString());
    ui->gjLE_3->setText(settings.value("gjxtb").toString());
    ui->czLE_3->setText(settings.value("czxtb").toString());
    ui->cpuLE_3->setText(settings.value("cpumax").toString());
    ui->memLE_3->setText(settings.value("memmax").toString());

    settings.endGroup();

    settings.beginGroup(m_strGwtxpzName);
    ui->gwtxpzCB->blockSignals(true);
    ui->gwtxpzCB->setChecked(settings.value("valid").toBool());
    ui->gwtxpzCB->blockSignals(false);

    settings.endGroup();

    settings.beginGroup(m_strGwzztxdzName);
    ui->gwzztxCB->blockSignals(true);
    ui->gwzztxCB->setChecked(settings.value("valid").toBool());
    ui->gwzztxCB->blockSignals(false);
    settings.endGroup();

    settings.beginGroup(m_strYtwzztxdzName);
    ui->ytwzztxCB->blockSignals(true);
    ui->ytwzztxCB->setChecked(settings.value("valid").toBool());
    ui->ytwzztxCB->blockSignals(false);
    settings.endGroup();

    settings.beginGroup(m_strYtwtxpzName);
    ui->ytwtxCB->blockSignals(true);
    ui->ytwtxCB->setChecked(settings.value("valid").toBool());
    ui->ytwtxCB->blockSignals(false);
    settings.endGroup();

    settings.beginGroup(m_strTsdcspzName);
    ui->zdtsdcsCB->blockSignals(true);
    ui->zdtsdcsCB->setChecked(settings.value("valid").toBool());
    ui->zdtsdcsCB->blockSignals(false);
    settings.endGroup();

    settings.beginGroup(m_strAqmscsName);
    ui->aqmscsCB->blockSignals(true);
    ui->aqmscsCB->setChecked(settings.value("valid").toBool());
    ui->aqmscsCB->blockSignals(false);
    settings.endGroup();

    settings.beginGroup(m_strOtherName);
    ui->firvLE->setText(settings.value("firmWareV").toString());
    ui->harvLE->setText(settings.value("hardwareV").toString());
    ui->scuQDLE->setText(settings.value("scuqd").toString());
    ui->dywcLE->setText(settings.value("dywc").toString());
    ui->dlwcLE->setText(settings.value("dlwc").toString());
    ui->glyswcLE->setText(settings.value("glyswc").toString());
    ui->ipLE->setText(settings.value("mqttiotip").toString());
    ui->txmkLE->setText(settings.value("bdtxmk").toString());
    ui->ygglwcLE->setText(settings.value("ygglwc").toString());
    ui->wgglwcLE->setText(settings.value("wgglwc").toString());
    ui->sm4gCB->setChecked(settings.value("4gsm").toBool());
    ui->dm4gCB->setChecked(!settings.value("4gsm").toBool());
    ui->rtcLE->setText(settings.value("rtcwc").toString());
    ui->lyqdLE->setText(settings.value("lyqdhash").toString());

    ui->firvLE_3->setText(settings.value("firmWareV").toString());
    ui->harvLE_3->setText(settings.value("hardwareV").toString());
    ui->scuQDLE_3->setText(settings.value("scuqd").toString());
    ui->dywcLE_3->setText(settings.value("dywc").toString());
    ui->dlwcLE_3->setText(settings.value("dlwc").toString());
    ui->glyswcLE_3->setText(settings.value("glyswc").toString());
    ui->ipLE_2->setText(settings.value("mqttiotip").toString());
    ui->txmkLE_2->setText(settings.value("bdtxmk").toString());
    ui->ygglwcLE_2->setText(settings.value("ygglwc").toString());
    ui->wgglwcLE_2->setText(settings.value("wgglwc").toString());
    ui->sm4gCB_2->setChecked(settings.value("4gsm").toBool());
    ui->dm4gCB_2->setChecked(!settings.value("4gsm").toBool());
    ui->rtcLE_2->setText(settings.value("rtcwc").toString());
    ui->lyqdLE_2->setText(settings.value("lyqdhash").toString());
    settings.endGroup();

}

void CGntParamdialog::on_iotCB_toggl(bool on)
{
    rqwtoggled(on);
}

void CGntParamdialog::on_wirCB_toggl(bool on)
{
    rqwtoggled(on);
}

void CGntParamdialog::on_secCB_toggl(bool on)
{
    rqwtoggled(on);
}

void CGntParamdialog::on_rqTW_itemChg(QTreeWidgetItem * pitem, int col)
{
    if(col != 0)
    {
        return;
    }
    int nRqNum = ui->rqNumLb->text().toInt();  // 容器个数
    int nRqAppNum = ui->rqAppNumLb->text().toInt();     // 容器内APP数量
    int nAppTotal = ui->AppNumLb->text().toInt();       // 总的app数量

    QString sRq = "gntparam__rq_" + QString::number(m_nFAID);
    QString sRqApp = "gntparam__rqApp_" + QString::number(m_nFAID);
    int num = pitem->text(1).toInt();      // 1 是容器  2是app
    QString s = pitem->text(0);
    if(num == 1)
    {
        if(pitem->checkState(0) == Qt::Unchecked )
        {
            --nRqNum;
            for (int m = 0; m < pitem->childCount(); ++m)
            {
                if(pitem->child(m)->checkState(0) != Qt::Checked)
                {
                    continue;
                }
                --nRqAppNum;
                --nAppTotal;
                pitem->child(m)->setCheckState(0, Qt::Unchecked);
            }
        }
        else if(pitem->checkState(0) == Qt::Checked)
        {
            ++nRqNum;
        }
    }
    else if(num == 2)
    {
        if(pitem->checkState(0) == Qt::Checked)
        {
            if(pitem->parent()->checkState(0) == Qt::Unchecked)
            {
                pitem->parent()->setCheckState(0, Qt::Checked);
                ++nRqNum;
            }
            ++nRqAppNum;
            ++nAppTotal;
        }
        else if(pitem->checkState(0) == Qt::Unchecked)
        {
            --nRqAppNum;
            --nAppTotal;
        }
    }
    ui->rqAppNumLb->setText(QString::number(nRqAppNum));
    ui->rqNumLb->setText(QString::number(nRqNum));
    ui->AppNumLb->setText(QString::number(nAppTotal));
}

void CGntParamdialog::rqAppInit()
{
    QSettings  settings(strScuRqAppIni, QSettings::IniFormat);
    settings.setIniCodec(QTextCodec::codecForName("UTF-8"));

    settings.beginGroup("rqapp");
    QStringList sList = settings.allKeys();
    for (int m = 0; m < sList.size(); ++m )
    {
        QString s = settings.value(sList[m]).toString();
        QStringList strList = s.split("@");

        for (int k = 0; k < strList.size(); ++k)
        {
            m_mapRqApp[sList[m]].push_back(strList[k]);
        }
    }
    settings.endGroup();

    ui->rqTW->clear();
    ui->rqTW->headerItem()->setText(0, QString("容器-App"));
    ui->rqTW->headerItem()->setText(2, QString("版本-hash"));
    ui->rqTW->header()->setSectionResizeMode(QHeaderView::Fixed);
    // 设置固定的列宽
    ui->rqTW->header()->setStretchLastSection(false);
    ui->rqTW->header()->resizeSection(0, 300);
    ui->rqTW->header()->resizeSection(2, 600);
    ui->rqTW->hideColumn(1);

    ui->rqTW_2->clear();
    ui->rqTW_2->headerItem()->setText(0, QString("容器-App"));
    ui->rqTW_2->headerItem()->setText(2, QString("版本-hash"));
    ui->rqTW_2->header()->setSectionResizeMode(QHeaderView::Fixed);
    // 设置固定的列宽
    ui->rqTW_2->header()->setStretchLastSection(false);
    ui->rqTW_2->header()->resizeSection(0, 300);
    ui->rqTW_2->header()->resizeSection(2, 600);
    ui->rqTW_2->hideColumn(1);

    auto iter = m_mapRqApp.begin();
    for (;iter != m_mapRqApp.end() ; ++iter)
    {
        QTreeWidgetItem *pItem = new QTreeWidgetItem(ui->rqTW);
        pItem->setFlags(pItem->flags() | Qt::ItemIsEditable);
        pItem->setText(0, iter->first);
        pItem->setText(1, "1");
        pItem->setText(2, "");
        pItem->setCheckState(0, Qt::Unchecked);


        QTreeWidgetItem *pItem2 = new QTreeWidgetItem(ui->rqTW_2);
        pItem2->setHidden(true);
        pItem2->setText(0, iter->first);

        std::vector<QString> &vtApp = iter->second;
        for (int m = 0; m < vtApp.size(); ++m)
        {
            QTreeWidgetItem *item = new QTreeWidgetItem(pItem);
            item->setFlags(pItem->flags() | Qt::ItemIsEditable);
            item->setText(0, vtApp[m]);
            item->setText(1, "2");
            item->setCheckState(0, Qt::Unchecked);

            QTreeWidgetItem *item2 = new QTreeWidgetItem(pItem2);
            item2->setHidden(true);
            item2->setText(0, vtApp[m]);
        }
    }
}

void CGntParamdialog::rqwtoggled(bool on)
{
    int nRqwAppNum = ui->rqwAppNumLb->text().toInt();
    int nAppNum = ui->AppNumLb->text().toInt();
    if(on)
    {
        ++nRqwAppNum;
        ++nAppNum;
    }
    else
    {
        --nRqwAppNum;
        --nAppNum;
    }
    ui->AppNumLb->setText(QString::number(nAppNum));

    ui->rqwAppNumLb->setText(QString::number(nRqwAppNum));
}

void CGntParamdialog::checkItemInit()
{
    ui->chkItemTW->setColumnCount(2);

    QTableWidgetItem *pitem = new QTableWidgetItem();
    pitem->setText("检查项");
    ui->chkItemTW->setHorizontalHeaderItem(1, pitem);

    pitem = new QTableWidgetItem();
    pitem->setText("选择");
    ui->chkItemTW->setHorizontalHeaderItem(0, pitem);

    ui->chkItemTW->setColumnWidth(1, 800);


    ui->chkItemTW->setRowCount(g_vtChkItem.size());

    for (int m = 0; m < g_vtChkItem.size(); ++m)
    {
        QTableWidgetItem *item = new QTableWidgetItem();
        item->setFlags(pitem->flags() & ~Qt::ItemIsEnabled);
        item->setText(g_vtChkItem[m]);
        ui->chkItemTW->setItem(m, 1, item);

        QCheckBox *pcb = new QCheckBox();
        ui->chkItemTW->setCellWidget(m, 0, pcb);
    }

}

void CGntParamdialog::checkItem(QString str)
{
    ui->itemLW->clear();
    for (int m = 0; m < ui->chkItemTW->rowCount(); ++m)
    {
        QCheckBox *pcb = (QCheckBox *)ui->chkItemTW->cellWidget(m, 0);
        pcb->setChecked(false);
    }
    if(str.isEmpty())
        return;
    QStringList sList = str.split("@");
    for (int m = 0; m < sList.size(); ++m)
    {
        QString strName = sList[m];
        QListWidgetItem * item = new QListWidgetItem();
        item->setText(strName);
        ui->itemLW->addItem(item);

        for (int k = 0; k < ui->chkItemTW->rowCount(); ++k)
        {
            QTableWidgetItem *pitem = ui->chkItemTW->item(k, 1);
            if(pitem->text() == strName)
            {
                QCheckBox *pcb = (QCheckBox*)ui->chkItemTW->cellWidget(k, 0);
                pcb->setChecked(true);
                break;
            }
        }
    }
}


// 容器-App参数确定
void CGntParamdialog::on_rqOk_clicked()
{
    // 容器外App
    if(ui->scsCB->isChecked())
        setCfg(strScuOutAutoCheckIni, m_strRqwAppName, "scsMonitor", "1");
    else
        setCfg(strScuOutAutoCheckIni, m_strRqwAppName, "scsMonitor", "0");

    if(ui->wirCB->isChecked())
        setCfg(strScuOutAutoCheckIni, m_strRqwAppName, "wirelessDCM", "1");
    else
        setCfg(strScuOutAutoCheckIni, m_strRqwAppName, "wirelessDCM", "0");

    if(ui->iotCB->isChecked())
        setCfg(strScuOutAutoCheckIni, m_strRqwAppName, "iotManager", "1");
    else
        setCfg(strScuOutAutoCheckIni, m_strRqwAppName, "iotManager", "0");

    if(ui->ExeCB->isChecked())
        setCfg(strScuOutAutoCheckIni, m_strRqwAppName, "ExecShell", "1");
    else
        setCfg(strScuOutAutoCheckIni, m_strRqwAppName, "ExecShell", "0");

    if(ui->iotAgentCB->isChecked())
        setCfg(strScuOutAutoCheckIni, m_strRqwAppName, "IotAgent", "1");
    else
        setCfg(strScuOutAutoCheckIni, m_strRqwAppName, "IotAgent", "0");


    delALLKeyCfg(strScuOutAutoCheckIni, m_strRqName);
    delALLKeyCfg(strScuOutAutoCheckIni, m_strRqAppName);
    delALLKeyCfg(strScuOutAutoCheckIni, m_strRqHashName);
    delALLKeyCfg(strScuOutAutoCheckIni, m_strRqAppHashName);
    for (int m = 0; m < ui->rqTW->topLevelItemCount(); ++m)
    {
         QTreeWidgetItem *topItem = ui->rqTW->topLevelItem(m);
         QString s = topItem->text(0);
         if(topItem->checkState(0) == Qt::Checked)
             setCfg(strScuOutAutoCheckIni, m_strRqName, s, "1");
         else
             setCfg(strScuOutAutoCheckIni, m_strRqName, s, "0");

         setCfg(strScuOutAutoCheckIni, m_strRqHashName, s, topItem->text(2));

         for (int k = 0;  k < topItem->childCount(); ++k)
         {
                QTreeWidgetItem *pitem = topItem->child(k);
                QString s2 = pitem->text(0);
                if(pitem->checkState(0) == Qt::Checked)
                    setCfg(strScuOutAutoCheckIni, m_strRqAppName, s2, "1");
                else
                    setCfg(strScuOutAutoCheckIni, m_strRqAppName, s2, "0");

                setCfg(strScuOutAutoCheckIni, m_strRqAppHashName, s2, pitem->text(2));

         }
    }


    // 已选择容器及APP
    QSettings  settings(strScuOutAutoCheckIni, QSettings::IniFormat);
    settings.setIniCodec(QTextCodec::codecForName("UTF-8"));
    settings.beginGroup(m_strRqwAppName);
    ui->scsCB_2->setChecked(settings.value("scsMonitor").toBool());
    ui->iotCB_2->setChecked(settings.value("iotManager").toBool());
    ui->wirCB_2->setChecked(settings.value("wirelessDCM").toBool());
    ui->ExeCB_2->setChecked(settings.value("ExecShell").toBool());
    ui->iotAgen_2t->setChecked(settings.value("IotAgent").toBool());
    settings.endGroup();

    for (int m = 0; m < ui->rqTW_2->topLevelItemCount(); ++m)
    {
        QTreeWidgetItem *pitem = ui->rqTW_2->topLevelItem(m);
        pitem->setHidden(true);

        for (int n = 0; n < pitem->childCount(); ++n)
        {
            QTreeWidgetItem *pcitem = pitem->child(n);
            pcitem->setHidden(true);
        }
    }

    settings.beginGroup(m_strRqName);
    QStringList sList = settings.allKeys();
    for (int m = 0; m < sList.size(); ++m)
    {
        if(settings.value(sList[m]).toInt() == 1)
        {
            for (int n = 0; n < ui->rqTW_2->topLevelItemCount(); ++n)
            {
                if(ui->rqTW_2->topLevelItem(n)->text(0) == sList[m])
                {
                    ui->rqTW_2->topLevelItem(n)->setHidden(false);
                }
            }
        }
    }
    settings.endGroup();

    settings.beginGroup(m_strRqHashName);
    for (int m = 0; m < ui->rqTW_2->topLevelItemCount() ; ++m)
    {
        if(!ui->rqTW_2->topLevelItem(m)->isHidden())
            ui->rqTW_2->topLevelItem(m)->setText(2, settings.value(ui->rqTW_2->topLevelItem(m)->text(0)).toString());
    }
    settings.endGroup();

    settings.beginGroup(m_strRqAppName);
    sList = settings.allKeys();
    for (int m = 0; m < sList.size(); ++m)
    {
        if(settings.value(sList[m]).toInt() == 1)
        {
            for (int n = 0; n < ui->rqTW_2->topLevelItemCount(); ++n)
            {
                QTreeWidgetItem *ptree = ui->rqTW_2->topLevelItem(n);
                for (int k = 0; k < ptree->childCount(); ++k)
                {
                    QTreeWidgetItem *ctree= ptree->child(k);
                    if(ctree->text(0) == sList[m])
                    {
                        ctree->setHidden(false);
                    }
                }
            }
        }
    }
    settings.endGroup();

    settings.beginGroup(m_strRqAppHashName);

    for (int m = 0; m < ui->rqTW_2->topLevelItemCount(); ++m)
    {
        QTreeWidgetItem *ptree = ui->rqTW_2->topLevelItem(m);
        for (int k = 0; k < ptree->childCount(); ++k)
        {
            QTreeWidgetItem *ctree= ptree->child(k);
            if(!ctree->isHidden())
                ctree->setText(2, settings.value(ctree->text(0)).toString());
        }
    }
    settings.endGroup();


}

void CGntParamdialog::on_chkitemOk_clicked()
{
    std::vector<QString> vtGntchk;
    for (int m = 0; m < ui->chkItemTW->rowCount(); ++m)
    {
        QTableWidgetItem *pitem = ui->chkItemTW->item(m, 1);
        QCheckBox *pcb = (QCheckBox*)ui->chkItemTW->cellWidget(m, 0);
        if(pitem->text() == "用户名密码" && pcb->checkState() == Qt::Unchecked)
        {
            QMessageBox::about(this, "检查项", "用户名密码必须勾选");
            return;
        }
        if(pcb->checkState() != Qt::Checked)
        {
            continue;
        }
        vtGntchk.push_back(pitem->text());
    }
    QString s1;
    for (int m = 0; m < vtGntchk.size(); ++m)
    {
       s1 +=  vtGntchk[m];
       s1 += "@";
    }
    QString s2 = s1.left(s1.size()-1);
    setCfg(strScuOutAutoCheckIni, m_strJCXName, "jcx", s2);


    QSettings  settings(strScuOutAutoCheckIni, QSettings::IniFormat);
    settings.setIniCodec(QTextCodec::codecForName("UTF-8"));

    settings.beginGroup(m_strJCXName);
    QString s3 =  settings.value("jcx").toString();
    settings.endGroup();

    ui->itemLW->clear();

    QStringList sList = s3.split("@");
    for (int m = 0;m < sList.size() ; ++m)
    {
        if(sList[m].isEmpty())
            continue;
        QListWidgetItem *pitem = new QListWidgetItem();
        pitem->setText(sList[m]);
        ui->itemLW->addItem(pitem);
    }

}

void CGntParamdialog::on_scuOK_clicked()
{
    QSettings  settings(strScuOutAutoCheckIni, QSettings::IniFormat);
    settings.setIniCodec(QTextCodec::codecForName("UTF-8"));

    settings.beginGroup(m_strScuSysName);

    settings.setValue("db1", ui->db1LE->text());
    settings.setValue("db2", ui->db2LE->text());
    settings.setValue("zkb", ui->zkLE->text());
    settings.setValue("yjb", ui->yjLE->text());
    settings.setValue("nhb", ui->nhLE->text());
    settings.setValue("bhb", ui->bhLE->text());
    settings.setValue("rqb", ui->rqLE->text());
    settings.setValue("jcb", ui->jcLE->text());
    settings.setValue("gjxtb", ui->gjLE->text());
    settings.setValue("czxtb",ui->czLE->text());
    settings.setValue("cpumax", ui->cpuLE->text());
    settings.setValue("memmax", ui->memLE->text());
    settings.endGroup();

    settings.beginGroup(m_strScuSysName);
    ui->db1LE_3->setText(settings.value("db1").toString());
    ui->db2LE_3->setText(settings.value("db2").toString());
    ui->zkLE_3->setText(settings.value("zkb").toString());
    ui->yjLE_3->setText(settings.value("yjb").toString());
    ui->nhLE_3->setText(settings.value("nhb").toString());
    ui->bhLE_3->setText(settings.value("bhb").toString());
    ui->rqLE_3->setText(settings.value("rqb").toString());
    ui->jcLE_3->setText(settings.value("jcb").toString());
    ui->gjLE_3->setText(settings.value("gjxtb").toString());
    ui->czLE_3->setText(settings.value("czxtb").toString());
    ui->cpuLE_3->setText(settings.value("cpumax").toString());
    ui->memLE_3->setText(settings.value("memmax").toString());
    settings.endGroup();
}

void CGntParamdialog::on_gwtxpzCB_toggled(bool checked)
{
    if(checked)
    {
        setCfg(strScuOutAutoCheckIni, m_strGwtxpzName, "valid", 1);
        if(m_pGwtxpz == nullptr)
        {
            m_pGwtxpz = new CGwtxpzDialog(this);
        }
        m_pGwtxpz->setFAID(m_nFAID);
        m_pGwtxpz->show();

    }
    else
    {
        setCfg(strScuOutAutoCheckIni, m_strGwtxpzName, "valid", 0);
    }
}

void CGntParamdialog::on_gwzztxCB_toggled(bool checked)
{
    if(checked)
    {
        setCfg(strScuOutAutoCheckIni, m_strGwzztxdzName, "valid", 1);
        if(m_pZztxdz == nullptr)
        {
            m_pZztxdz = new CZztxcsDialog(this);
        }
        m_pZztxdz->setKeyName(m_strGwzztxdzName);
        m_pZztxdz->show();
    }
    else
    {
        setCfg(strScuOutAutoCheckIni, m_strGwzztxdzName, "valid", 0);
    }
}



void CGntParamdialog::on_ytwzztxCB_toggled(bool checked)
{
    if(checked)
    {
        setCfg(strScuOutAutoCheckIni, m_strYtwzztxdzName, "valid", 1);
        if(m_pZztxdz == nullptr)
        {
            m_pZztxdz = new CZztxcsDialog(this);
        }
        m_pZztxdz->setKeyName(m_strYtwzztxdzName);
        m_pZztxdz->show();
    }
    else
    {
        setCfg(strScuOutAutoCheckIni, m_strYtwzztxdzName, "valid", 0);
    }
}

void CGntParamdialog::on_ytwtxCB_toggled(bool checked)
{
    if(checked)
    {
        setCfg(strScuOutAutoCheckIni, m_strYtwtxpzName, "valid", 1);
        if(m_pYtwtxpz == nullptr)
        {
            m_pYtwtxpz = new CYtwtxpzDialog(this);
        }
        m_pYtwtxpz->setKeyName(m_strYtwtxpzName);
        m_pYtwtxpz->show();
    }
    else
    {
        setCfg(strScuOutAutoCheckIni, m_strYtwtxpzName, "valid", 0);
    }
}

void CGntParamdialog::on_zdtsdcsCB_toggled(bool checked)
{
    if(checked)
    {
        setCfg(strScuOutAutoCheckIni, m_strTsdcspzName, "valid", 1);
        if(m_pTsd == nullptr)
        {
            m_pTsd = new CTsdDialog(this);
        }
        m_pTsd->setKeyName(m_strTsdcspzName);
        m_pTsd->show();
    }
    else
    {
        setCfg(strScuOutAutoCheckIni, m_strTsdcspzName, "valid", 0);
    }
}

// 安全模式参数
void CGntParamdialog::on_aqmscsCB_toggled(bool checked)
{
    if(checked)
    {
        setCfg(strScuOutAutoCheckIni, m_strAqmscsName, "valid", 1);
        if(m_paqjmpz == nullptr)
        {
            m_paqjmpz = new CAqmsDialog(this);
        }
        m_paqjmpz->setKeyName(m_strAqmscsName);
        m_paqjmpz->show();
    }
    else
    {
        setCfg(strScuOutAutoCheckIni, m_strAqmscsName, "valid", 0);
    }
}



// 其他参数确定
void CGntParamdialog::on_otherok_clicked()
{
    QSettings  settings(strScuOutAutoCheckIni, QSettings::IniFormat);
    settings.setIniCodec(QTextCodec::codecForName("UTF-8"));

    settings.beginGroup(m_strOtherName);
    settings.setValue("fccs", 0);
    settings.setValue("firmWareV", ui->firvLE->text());
    settings.setValue("hardwareV", ui->harvLE->text());
    settings.setValue("bdtxmk", ui->txmkLE->text());
    settings.setValue("scuqd", ui->scuQDLE->text());
    settings.setValue("dywc", ui->dywcLE->text());
    settings.setValue("dlwc", ui->dlwcLE->text());
    settings.setValue("glyswc", ui->glyswcLE->text());
    settings.setValue("ygglwc", ui->ygglwcLE->text());
    settings.setValue("wgglwc", ui->wgglwcLE->text());
    settings.setValue("mqttiotip", ui->ipLE->text());
    settings.setValue("4gsm", (int)ui->sm4gCB->isChecked());
    settings.setValue("rtcwc", ui->rtcLE->text());
    settings.setValue("lyqdhash", ui->lyqdLE->text());
    settings.endGroup();

    settings.beginGroup(m_strOtherName);
    ui->firvLE_3->setText(settings.value("firmWareV").toString());
    ui->harvLE_3->setText(settings.value("hardwareV").toString());
    ui->scuQDLE_3->setText(settings.value("scuqd").toString());
    ui->dywcLE_3->setText(settings.value("dywc").toString());
    ui->dlwcLE_3->setText(settings.value("dlwc").toString());
    ui->glyswcLE_3->setText(settings.value("glyswc").toString());
    ui->ipLE_2->setText(settings.value("mqttiotip").toString());
    ui->txmkLE_2->setText(settings.value("bdtxmk").toString());
    ui->ygglwcLE_2->setText(settings.value("ygglwc").toString());
    ui->wgglwcLE_2->setText(settings.value("wgglwc").toString());
    ui->sm4gCB_2->setChecked(settings.value("4gsm").toBool());
    ui->dm4gCB_2->setChecked(!settings.value("4gsm").toBool());
    ui->rtcLE_2->setText(settings.value("rtcwc").toString());
    ui->lyqdLE_2->setText(settings.value("lyqdhash").toString());
    settings.endGroup();
}


// 全选
void CGntParamdialog::on_allBtn_clicked()
{
    for (int m = 0; m < ui->chkItemTW->rowCount(); ++m)
    {
        QCheckBox *pcb = (QCheckBox*)ui->chkItemTW->cellWidget(m, 0);
        pcb->setChecked(true);
    }
}

void CGntParamdialog::on_noallBtn_clicked()
{
    for (int m = 0; m < ui->chkItemTW->rowCount(); ++m)
    {
        QCheckBox *pcb = (QCheckBox*)ui->chkItemTW->cellWidget(m, 0);
        pcb->setChecked(false);
    }
}

void CGntParamdialog::on_query698Btn_clicked()
{
    int nindex = ui->comboBox->currentIndex();
    switch (nindex)
    {
    case 0:
        if(m_pGwtxpz == nullptr)
        {
            m_pGwtxpz = new CGwtxpzDialog(this);
        }
        m_pGwtxpz->setFAID(m_nFAID);
        m_pGwtxpz->show();
        break;
    case 1:
        if(m_pZztxdz == nullptr)
        {
            m_pZztxdz = new CZztxcsDialog(this);
        }
        m_pZztxdz->setKeyName(m_strGwzztxdzName);
        m_pZztxdz->show();
        break;
    case 2:
        if(m_pYtwtxpz == nullptr)
        {
            m_pYtwtxpz = new CYtwtxpzDialog(this);
        }
        m_pYtwtxpz->setKeyName(m_strYtwtxpzName);
        m_pYtwtxpz->show();
        break;
    case 3:
        if(m_pZztxdz == nullptr)
        {
            m_pZztxdz = new CZztxcsDialog(this);
        }
        m_pZztxdz->setKeyName(m_strYtwzztxdzName);
        m_pZztxdz->show();
        break;
    case 4:
        if(m_pTsd == nullptr)
        {
            m_pTsd = new CTsdDialog(this);
        }
        m_pTsd->setKeyName(m_strTsdcspzName);
        m_pTsd->show();
        break;
    case 5:
        if(m_paqjmpz == nullptr)
        {
            m_paqjmpz = new CAqmsDialog(this);
        }
        m_paqjmpz->setKeyName(m_strAqmscsName);
        m_paqjmpz->show();
        break;
    default:
        break;
    }
}

// 全选

void CGntParamdialog::on_allXZBtn_clicked()
{
    ui->scsCB->setChecked(true);
    ui->iotCB->setChecked(true);
    ui->wirCB->setChecked(true);

    for (int m = 0; m < ui->rqTW->topLevelItemCount(); ++m)
    {
        QTreeWidgetItem *top = ui->rqTW->topLevelItem(m);
        top->setCheckState(0, Qt::Checked);

        for (int n = 0; n < top->childCount(); ++n)
        {
            QTreeWidgetItem *child = top->child(n);
            child->setCheckState(0, Qt::Checked);
        }
    }
}

void CGntParamdialog::on_allnotBtn_clicked()
{
    ui->scsCB->setChecked(false);
    ui->iotCB->setChecked(false);
    ui->wirCB->setChecked(false);

    for (int m = 0; m < ui->rqTW->topLevelItemCount(); ++m)
    {
        QTreeWidgetItem *top = ui->rqTW->topLevelItem(m);
        top->setCheckState(0, Qt::Unchecked);

        for (int n = 0; n < top->childCount(); ++n)
        {
            QTreeWidgetItem *child = top->child(n);
            child->setCheckState(0, Qt::Unchecked);
        }
    }
}



void CGntParamdialog::on_dm4gCB_toggled(bool checked)
{
    ui->sm4gCB->setChecked(!checked);
}

void CGntParamdialog::on_sm4gCB_toggled(bool checked)
{
    ui->dm4gCB->setChecked(!checked);
}

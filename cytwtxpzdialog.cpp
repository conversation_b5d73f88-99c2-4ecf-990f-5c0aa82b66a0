﻿#include "cytwtxpzdialog.h"
#include "ui_cytwtxpzdialog.h"

CYtwtxpzDialog::CYtwtxpzDialog(QWidget *parent) :
    QDialog(parent),
    ui(new Ui::CYtwtxpzDialog)
{
    ui->setupUi(this);
    setWindowTitle("以太网通信配置");
}

CYtwtxpzDialog::~CYtwtxpzDialog()
{
    delete ui;
}

void CYtwtxpzDialog::setKeyName(QString strName)
{
    m_strKeyName = strName;
    QSettings  settings(strScuOutAutoCheckIni, QSettings::IniFormat);
    settings.setIniCodec(QTextCodec::codecForName("UTF-8"));

    settings.beginGroup(m_strKeyName);
    ui->gzmsCB->setCurrentText(settings.value("gzms").toString());
    ui->ljfsCB->setCurrentText(settings.value("ljfs").toString());
    ui->ljyyfsCB->setCurrentText(settings.value("ljyyfs").toString());
    ui->cssjLE->setText(settings.value("cssj").toString());
    ui->cfcsLE->setText(settings.value("cfcs").toString());
    ui->xtzqLE->setText(settings.value("xtzq").toString());
    ui->dlfuqLE->setText(settings.value("dlfwq").toString());
    ui->dldkLE->setText(settings.value("dldk").toString());

    QString s = settings.value("ztdklb").toString();
    QStringList sList = s.split("@");
    for (int m = 0; m < sList.size(); ++m)
    {
        if(sList[m].isEmpty())
            continue;
        ui->listWidget->addItem(sList[m]);
    }
    settings.endGroup();
}

void CYtwtxpzDialog::on_addBtn_clicked()
{
    if(ui->dkLE->text().isEmpty())
        return;

    QListWidgetItem *pitem = new QListWidgetItem();
    pitem->setText(ui->dkLE->text());
    ui->listWidget->addItem(pitem);
    ui->dkLE->clear();
}

void CYtwtxpzDialog::on_delBtn_clicked()
{
    int nRow = ui->listWidget->currentRow();
    if(nRow == -1)
        return;

    ui->listWidget->takeItem(nRow);
}

void CYtwtxpzDialog::on_okBtn_clicked()
{
    QSettings  settings(strScuOutAutoCheckIni, QSettings::IniFormat);
    settings.setIniCodec(QTextCodec::codecForName("UTF-8"));

    settings.beginGroup(m_strKeyName);
    settings.setValue("gzms", ui->gzmsCB->currentText());
    settings.setValue("ljfs", ui->ljfsCB->currentText());
    settings.setValue("ljyyfs", ui->ljyyfsCB->currentText());
    settings.setValue("cssj", ui->cssjLE->text());
    settings.setValue("cfcs", ui->cfcsLE->text());
    settings.setValue("xtzq", ui->xtzqLE->text());
    settings.setValue("dlfwq", ui->dlfuqLE->text());
    settings.setValue("dldk", ui->dldkLE->text());

    QString s;
    for (int m = 0;  m < ui->listWidget->count(); ++m)
    {
        s += ui->listWidget->item(m)->text();
        s += "@";
    }
    QString s2 = s.left(s.size() -1);
    settings.setValue("ztdklb", s2);
    settings.endGroup();
}

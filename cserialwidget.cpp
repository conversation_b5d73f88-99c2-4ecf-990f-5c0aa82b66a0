#include "cserialwidget.h"
#include "ui_cserialwidget.h"
#include<QThread>
#include<QMessageBox>
#include<QAxObject>
#include<QFile>
#include<QDir>
#include<QCheckBox>
#include<QTableWidget>
#include<QDateTime>
#include<QTcpSocket>
#include<QElapsedTimer>
#include<QThreadPool>
#include<QList>
#include"msk_global.h"

cserialWidget::cserialWidget(QWidget *parent) :
    QWidget(parent),
    ui(new Ui::cserialWidget)
{
    ui->setupUi(this);
    m_pequipmentsjj = nullptr;
    m_pequimentwc = nullptr;
    //m_test = nullptr;
    m_result = nullptr;



    m_timer = new QTimer(this);
    connect(m_timer,&QTimer::timeout,this,&cserialWidget::ontimer);
    m_timer->start(3000);

    m_database = new Database();
    if(m_database->openDb())
    {
        m_database->createTable();
    }

    ui->tableWidget->verticalHeader()->setVisible(false);
    ui->tableWidget->setColumnCount(7);

    QTableWidgetItem *pitem = new QTableWidgetItem();
    pitem->setText("表位号");
    pitem->setSelected(true);
    ui->tableWidget->setHorizontalHeaderItem(0, pitem);

    pitem = new QTableWidgetItem();
    pitem->setText("连接状态");
    ui->tableWidget->setHorizontalHeaderItem(1, pitem);

    pitem = new QTableWidgetItem();
    pitem->setText("Esn");
    ui->tableWidget->setHorizontalHeaderItem(2, pitem);

    pitem = new QTableWidgetItem();
    pitem->setText("Esam");
    ui->tableWidget->setHorizontalHeaderItem(3, pitem);

    pitem = new QTableWidgetItem();
    pitem->setText("测试方案");
    ui->tableWidget->setHorizontalHeaderItem(4, pitem);

    pitem = new QTableWidgetItem();
    pitem->setText("当前测试项");
    ui->tableWidget->setHorizontalHeaderItem(5, pitem);

    pitem = new QTableWidgetItem();
    pitem->setText("测试结果");
    ui->tableWidget->setHorizontalHeaderItem(6, pitem);

    ui->tableWidget->setColumnWidth(0,50);
    ui->tableWidget->setColumnWidth(2,200);
    ui->tableWidget->setColumnWidth(3,200);
    ui->tableWidget->setColumnWidth(5,100);

    ui->tableWidget->setRowCount(40);

    ui->tableWidget->setEditTriggers(QAbstractItemView::NoEditTriggers);
    ui->tableWidget->setSelectionBehavior(QAbstractItemView::SelectRows);
    ui->tableWidget->setItemSelected(ui->tableWidget->item(2,2),true);


    for(int row = 0;row<40;row++)
    {
        QTableWidgetItem *pitem = new QTableWidgetItem();
        pitem->setText(QString::number(row+1));
        pitem->setCheckState(Qt::Unchecked);
        ui->tableWidget->setItem(row, 0, pitem);
    }
}

cserialWidget::~cserialWidget()
{

    for (auto thread : threadList) {
        thread->quit();
        thread->wait();
    }
    delete ui;
}
void cserialWidget::on_pushButton_6_clicked()
{
    m_set = new cSetConfigure();
    m_set->show();
}

void cserialWidget::ontimer()
{
    this->comeEquipmentInit();
    QString  smonitor= m_pequipmentsjj->GetMonitor();

    QStringList sList = smonitor.split(",");
    if(sList.size() < 15)
        return;
    ui->label_2->setText(QString("A相电压:%1V，B相电压:%2V，C相电压:%3V").arg(sList[0]).arg(sList[7]).arg(sList[14]));
    ui->label_3->setText(QString("A相电流:%1A，B相电流:%2A，C相电流:%3A").arg(sList[1]).arg(sList[8]).arg(sList[15]));
//    qDebug()<<"cos1"<<sList[4]<<"sin"<<sList[5];
//    qDebug()<<"cos2"<<sList[11]<<"sin"<<sList[12];
//    qDebug()<<"cos3"<<sList[18]<<"sin"<<sList[19];
    IA = sList[1].toFloat();
    IB = sList[8].toFloat();
    IC = sList[15].toFloat();
    VA = sList[0].toFloat();
    VB = sList[7].toFloat();
    VC = sList[14].toFloat();
    emit signValues(IA,IB,IC,VA,VB,VC);
}
void cserialWidget::on_pushButton_3_clicked()
{
    ui->pushButton_3->setEnabled(false);
    ui->pushButton_3->setStyleSheet("QPushButton {background-color: red; color: white;font-size: 26px;padding: 8px;"
                              "border-style: outset; border-width: 2px; border-radius: 10px;border-color: beige;}");
    this->m_CheckedSum = 0;
    m_jbFinished.clear();
    m_allfinishedSum.clear();
    threadList.clear();
    m_testmap.clear();
    m_allTestResults.clear();
    m_result = new csResults();


    for (int i = 0; i < 40; i++) {

        if (ui->tableWidget->item(i, 0)->checkState() == Qt::Unchecked) continue;

        ui->tableWidget->setItem(i,1,new QTableWidgetItem(""));
        ui->tableWidget->setItem(i,2,new QTableWidgetItem(""));
        ui->tableWidget->setItem(i,3,new QTableWidgetItem(""));
        ui->tableWidget->setItem(i,5,new QTableWidgetItem(""));
        ui->tableWidget->setItem(i,6,new QTableWidgetItem(""));

        QString s = queryCfg(CheckItemIni,"currentpc","currentpc");
        QSettings  settings(CheckItemIni, QSettings::IniFormat);
        settings.setIniCodec(QTextCodec::codecForName("UTF-8"));
        settings.beginGroup("testpc");
        QStringList clist = settings.allKeys();
        for (int j =0 ;j<clist.size();j++) {
            QString m = queryCfg(CheckItemIni,"testpc",clist[j]);
            QStringList sl = m.split("@");
            if(sl[0]==s)
            {
                ui->tableWidget->setItem(i,4,new QTableWidgetItem(sl[1]));
            }
        }
        m_CheckedSum ++;
        m_jbFinished[i] = 0;

        Testlog_ui * m_log = new Testlog_ui();
        ui->stackedWidget->addWidget(m_log);
        m_mapTestUI[i] = m_log;
        cTestLog* m_test = new cTestLog();
        m_testmap[i] = m_test;
        connect(m_test, &cTestLog::signEsn, [this, i](const QString& esnNum) {
            QMetaObject::invokeMethod(this, [this, i, esnNum]() {
                QTableWidgetItem* item = new QTableWidgetItem(esnNum);
                ui->tableWidget->setItem(i, 2, item);
            }, Qt::QueuedConnection);
        });

        connect(m_test, &cTestLog::signEsam, [this, i](const QString& esamNum) {
            QMetaObject::invokeMethod(this, [this, i, esamNum]() {
                QTableWidgetItem* item = new QTableWidgetItem(esamNum);
                ui->tableWidget->setItem(i, 3, item);
            }, Qt::QueuedConnection);
        });

        connect(m_test, &cTestLog::signtestlog, [this,m_log](const QString& s) {
            QMetaObject::invokeMethod(this, [s,m_log]() {
               m_log->onslotsRevtest(s);

            }, Qt::QueuedConnection);
        });

        connect(m_test, &cTestLog::signTestShell, [this,m_log](const QString& s) {
            QMetaObject::invokeMethod(this, [s,m_log]() {
               m_log->onslotsRevShell(s);
            }, Qt::QueuedConnection);
        });

        connect(m_test, &cTestLog::signTestItemRes, [this,m_log](const QString& Item,const QString &Res) {
            QMetaObject::invokeMethod(this, [Item,Res,m_log]() {
               m_log->onslotsTestItem(Item,Res);
            }, Qt::QueuedConnection);
        });


        connect(m_test, &cTestLog::signState, [this, i](bool state) {
            QMetaObject::invokeMethod(this, [this, i, state]() {
                QTableWidgetItem* item = new QTableWidgetItem(state ? "已连接" : "断开连接");
                ui->tableWidget->setItem(i, 1, item);
            }, Qt::QueuedConnection);
        });

        connect(m_test, &cTestLog::signCurrentTestItem, [this,i](QString res){
            QMetaObject::invokeMethod(this, [this, i, res]() {
                QString s = res;
                QTableWidgetItem* item = new QTableWidgetItem(s);
                ui->tableWidget->setItem(i, 5, item);
            }, Qt::QueuedConnection);
        } );
        connect(m_test, &cTestLog::signTestResult, [this,i](QString res){
            QMetaObject::invokeMethod(this, [this, i, res]() {
                QString s = res;
                m_IsFinshed[i]=s;
                m_TestResList << res;
                QTableWidgetItem* item = new QTableWidgetItem(s);
                ui->tableWidget->setItem(i, 6, item);
                if(s=="合格")
                {
                    item->setBackgroundColor(Qt::green);
                }
                else if (s=="不合格") {
                    item->setBackgroundColor(Qt::red);
                }
            }, Qt::QueuedConnection);
        });
        connect(m_test,&cTestLog::signTableRes,this,&cserialWidget::onslotsTableRes);
        connect(m_test, &cTestLog::signTestResult, this,&cserialWidget::onslotsallFinshed);


        //if(queryCfg(CheckItemIni,"testitem","jiaobiao") == "1")
        {
            connect(m_test, &cTestLog::signJBTestRes, this, &cserialWidget::onslotsjbFinshed);
        }


        QThread* thread = new QThread;
        m_test->moveToThread(thread);
        connect(thread, &QThread::started, [i, m_test,this]() {
            QMetaObject::invokeMethod(m_test, [m_test, i,this]() {
                m_test->onSlotNUM(i);
                connect(this,&cserialWidget::signValues,m_test,&cTestLog::onSlotsValues);
                //m_test->onSlotsValues(IA,IB,IB,VA,VB,VC);
            }, Qt::QueuedConnection);
        });
        threadList.append(thread);
        connect(thread, &QThread::finished, thread, &QThread::deleteLater);
        connect(thread, &QThread::finished, m_test, &cTestLog::deleteLater);
        thread->start();
        }
}

void cserialWidget::on_pushButton_5_clicked()//断电
{
    ui->pushButton_5->setStyleSheet("QPushButton {background-color: red; color: white;font-size: 26px;padding: 8px;"
                                  "border-style: outset; border-width: 2px; border-radius: 10px;border-color: beige;}");
    comeEquipmentInit();
    m_pequipmentsjj->SendCommUIDownRapid();
    ui->pushButton_5->setStyleSheet("QPushButton {background-color: #0072C6; color: white;font-size: 26px;padding: 8px;"
                                  "border-style: outset; border-width: 2px; border-radius: 10px;border-color: beige;}");
}
void cserialWidget::on_pushButton_clicked()//上电
{
    ui->pushButton->setStyleSheet("QPushButton {background-color: red; color: white;font-size: 26px;padding: 8px;"
                                  "border-style: outset; border-width: 2px; border-radius: 10px;border-color: beige;}");
    //上电
    comeEquipmentInit();
    m_pequipmentsjj->SendCommUIDownRapid();
    QThread::sleep(1);
    for (int meterIndex = 0; meterIndex < ui->tableWidget->rowCount(); meterIndex++)
    {
        m_pequimentwc->wAutoShutdown(meterIndex+1, 0);
        QThread::msleep(20);

    }

    for (int meterIndex = 0; meterIndex < ui->tableWidget->rowCount(); meterIndex++)
    {
        if(ui->tableWidget->item(meterIndex, 0)->checkState() == Qt::Unchecked)
        {
            m_pequimentwc->wAutoShutdown(meterIndex+1, 1);
            QThread::msleep(20);
        }
    }

    m_pequimentwc->wAutoShutdown(41, 0);
    QThread::msleep(20);
    m_pequipmentsjj->Power_ON(true, 220 , 5, "50L", 50, 220, 5, 2, 7, 4);
    ui->pushButton->setStyleSheet("QPushButton {background-color: #0072C6; color: white;font-size: 26px;padding: 8px;"
                                  "border-style: outset; border-width: 2px; border-radius: 10px;border-color: beige;}");
}
void cserialWidget::comeEquipmentInit()
{
    if(m_pequipmentsjj == nullptr)
    {
        m_pequipmentsjj = new COMEquipment::EquipmentNz2230();

    }
    if(m_pequimentwc == nullptr)
    {
        m_pequimentwc = new COMEquipment::EquipmentWc();
        m_pequimentwc->ConnectEquipment("COM12");
        m_pequipmentsjj->ConnectSet("COM11","DSB301","COM13",false);
    }
}

void cserialWidget::onslotsjbFinshed(int num)
{
    m_jbFinished[num] = 1;  
    if (m_jbFinished.values().count(1) == m_CheckedSum)
    {
        this->comeNCheckInit();
        for (int i = 0; i < 40; i++)
        {
            if (ui->tableWidget->item(i, 0)->checkState() == Qt::Checked)
            {
                 cTestLog* testLog = m_testmap[i];
                 QMetaObject::invokeMethod(testLog, &cTestLog::njbByteArray, Qt::QueuedConnection);
            }
        }
    }
}

void cserialWidget::onslotsTableRes(const TestResult &result)
{
    m_allTestResults.append(result);
    
    // 获取测试批次和测试方案
    QString testBatch = queryCfg(CheckItemIni,"currentpc","currentpc");
    QString testScheme = "出厂测试"; // 可以从配置文件读取
    QString currentTime = QDateTime::currentDateTime().toString("yyyy-MM-dd hh:mm:ss");
    
    // 保存每个测试项到数据库
    for(const auto &item : result.subItems)
    {
        TestItemDetail itemDetail;
        itemDetail.esnNum = result.esn;
        itemDetail.esamNum = result.esam;
        itemDetail.testBatch = testBatch;
        itemDetail.testScheme = testScheme;
        itemDetail.testTime = currentTime;
        itemDetail.testMainItem = item.mainItem;
        itemDetail.testSubItem = item.subItem;
        itemDetail.testResult = item.result;
        itemDetail.stationId = result.stationId;
        
        m_database->insertTestItemData(itemDetail);
    }
    
    // 统计每个测试项的不合格表位
    static QMap<QString, QStringList> failedStationsByItem; // 测试项不合格表位统计
    static QStringList testItemOrder; // 记录测试项的出现顺序
    
//    qDebug() << "收到测试结果 - 表位:" << result.stationId
//             << "ESN:" << result.esn
//             << "ESAM:" << result.esam;
    
    // 遍历当前表位的所有测试项
    for(const auto &item : result.subItems)
    {
        QString itemKey = QString("%1@%2").arg(item.mainItem).arg(item.subItem);
        QString stationStr = QString::number(result.stationId);
        
        // 记录测试项顺序（只记录第一次出现的）
        if(!testItemOrder.contains(itemKey))
        {
            testItemOrder.append(itemKey);
        }
        
        if(item.result == "不合格")
        {
            // 统计测试项不合格表位
            if(!failedStationsByItem[itemKey].contains(stationStr))
            {
                failedStationsByItem[itemKey] << stationStr;
            }
        }
    }
    
    // 获取选择的表位顺序
    QStringList selectedStations;
    for(int i = 0; i < 40; i++)
    {
        if(ui->tableWidget->item(i, 0)->checkState() == Qt::Checked)
        {
            selectedStations << QString::number(i + 1);
        }
    }
    
    // 按测试项顺序输出统计结果
    QStringList statisticLines;
    for(const QString &itemKey : testItemOrder)
    {
        if(failedStationsByItem.contains(itemKey) && !failedStationsByItem[itemKey].isEmpty())
        {
            // 按选择的表位顺序排序不合格表位
            QStringList sortedFailedStations;
            for(const QString &station : selectedStations)
            {
                if(failedStationsByItem[itemKey].contains(station))
                {
                    sortedFailedStations << station;
                }
            }
            
            if(!sortedFailedStations.isEmpty())
            {
                QString line = QString("%1@%2 不合格").arg(itemKey).arg(sortedFailedStations.join(","));
                statisticLines << line;
            }
        }
        else
        {
            QStringList slist = itemKey.split("@");
            if(slist[0] != "清除文件" && slist[0] != "容器安装" && slist[0] != "APP安装" && slist[0] != "APP运行" && slist[0] != "版本对比")
            {
                qDebug()<<slist[0];
                // 测试项合格
                QString line = QString("%1@ 合格").arg(itemKey);
                statisticLines << line;
            }
        }
    }
    
    if(statisticLines.isEmpty())
    {
        QStringList slist;
        slist << "检测项@所有检测项@合格";
        m_result->CurrentTableRes(slist);
    }
    else
    {
        m_result->CurrentTableRes(statisticLines);
    }
    
   // qDebug() << statisticLines;

       // 输出当前批次的所有测试项详情
       QString currentBatch = queryCfg(CheckItemIni,"currentpc","currentpc");
       m_database->queryTestItemDataByBatch(currentBatch);
   
}

void cserialWidget::onslotsallFinshed(QString s)
{
    m_allfinishedSum<<s;
    if(m_allfinishedSum.size() == m_CheckedSum)
    {
        //CreatExcel();
        QString spc = queryCfg(CheckItemIni,"currentpc","currentpc");
        QString str1 = QString("testinfo");
        if(!m_database->isTableExist(str1))
        {
            //QMessageBox::information(nullptr,"提示","esn和easm信息保存失败");
        }
        for(int i = 0;i<40;i++)
        {
            if(ui->tableWidget->item(i,0)->checkState() == Qt::Checked)
            {
                QString esnNum = ui->tableWidget->item(i,2)->text();
                QString esamNum = ui->tableWidget->item(i,3)->text();
                if(esnNum!=""&&esamNum!="")
                {
                    w2dba w2dbaTest1 = {esnNum,esamNum, spc};
                    m_database->singleInsertData(w2dbaTest1);
                }
            }
        }
//        QMessageBox messageBox(QMessageBox::NoIcon, "提示", "测试已完成", QMessageBox::Ok);
//        messageBox.setStyleSheet(
//                    "QMessageBox {"
//                    "    min-width: 500px;"
//                    "    min-height: 300px;"
//                    "}"
//                    "QLabel {"
//                    "    min-width: 400px;"
//                    "    min-height: 150px;"
//                    "    font-size: 50px;"

//                    "    font-weight: bold;"
//                    "    padding: 20px;"
//                    "    qproperty-alignment: AlignCenter;"
//                    "}"
//                    "QPushButton {"
//                    "    width: 200px;"
//                    "    height: 60px;"
//                    "    font-size: 24px;"
//                    "}"
//        );
//        messageBox.exec();
        if(m_result != nullptr)
        {
            m_result->show();
        }


        ui->pushButton_3->setStyleSheet("QPushButton {background-color: #0072C6; color: white;font-size: 26px;padding: 8px;"
                                  "border-style: outset; border-width: 2px; border-radius: 10px;border-color: beige;}");
        comeEquipmentInit();
        m_pequipmentsjj->SendCommUIDownRapid();
    }
    ui->pushButton_3->setEnabled(true);
}

void cserialWidget::comeNCheckInit()
{
    m_pequipmentsjj->SendCommUIDownRapid();
    QThread::sleep(1);
    for (int meterIndex = 0; meterIndex < ui->tableWidget->rowCount(); meterIndex++)
    {
        m_pequimentwc->wAutoShutdown(meterIndex+1, 2);
        QThread::msleep(20);
    }

    for (int meterIndex = 0; meterIndex < ui->tableWidget->rowCount(); meterIndex++)
    {
        if(ui->tableWidget->item(meterIndex, 0)->checkState() != Qt::Checked)
        {
            m_pequimentwc->wAutoShutdown(meterIndex+1, 3);
            QThread::msleep(20);
        }
    }

    m_pequimentwc->wAutoShutdown(41, 2);
    QThread::msleep(20);
    m_pequipmentsjj->Power_ON(true, 220, 5, "50L", 50, 220, 5, 2, 7, 4);
}

void cserialWidget::on_checkBox_stateChanged(int arg1)
{
    if(arg1==2)
    {
        ui->checkBox->setText("取消全选");
        for(int i = 0; i < 40; i++)
        {
            ui->tableWidget->item(i,0)->setCheckState(Qt::Checked);
        }
    }
    else if(arg1==0)
    {
        ui->checkBox->setText("全选");
        for(int i = 0; i < 40; i++)
        {
            ui->tableWidget->item(i,0)->setCheckState(Qt::Unchecked);
        }
    }
}

void cserialWidget::on_pcBtn_clicked()
{
    m_scancode = new cScanCode();
    m_scancode->show();
}

void cserialWidget::on_tableWidget_itemDoubleClicked(QTableWidgetItem *item)
{
    int r = item->row();
    auto iter = m_mapTestUI.begin();
    for (; iter != m_mapTestUI.end();++iter) {
        if( r == iter->first )
        {
             ui->stackedWidget->setCurrentWidget(iter->second);
        }
    }
}

void cserialWidget::CreatExcel() {
    QString spc = queryCfg(CheckItemIni,"currentpc","currentpc");
    QString filePath = "E:/scu/log/"+spc+"/"+spc+".xlsx";
    // 检查文件是否存在
    bool fileExists = QFile::exists(filePath);

    // 创建Excel应用程序对象
    QAxObject *excel = new QAxObject("Excel.Application");
    excel->setProperty("Visible", false); // 不可见
    excel->setProperty("DisplayAlerts", false);

    QAxObject *workbooks = excel->querySubObject("Workbooks");
    QAxObject *workbook;

    try {
        if (fileExists) {
            workbook = workbooks->querySubObject("Open(const QString&)", filePath);
        } else {
            workbook = workbooks->querySubObject("Add");
        }

        QAxObject *sheets = workbook->querySubObject("Sheets");
        QAxObject *sheet = sheets->querySubObject("Item(int)", 1);

        // 写入表头（仅新文件）
        if (!fileExists) {

            QAxObject *cell = sheet->querySubObject("Cells(int,int)", 1, 2);
            cell->setProperty("Value2", "ESN号");
            delete cell;
            QAxObject *cell1 = sheet->querySubObject("Cells(int,int)", 1, 3);
            cell1->setProperty("Value2", "ESAM号");
            delete cell1;  
        }

        QAxObject* column = sheet->querySubObject("Columns(const QVariant&)", QVariant("C"));
        column->setProperty("NumberFormat", "@");

        // 找到最后一行
        QAxObject *usedRange = sheet->querySubObject("UsedRange");
        QAxObject *rows = usedRange->querySubObject("Rows");
        int lastRow = rows->property("Count").toInt();

        // 写入数据（从最后一行之后开始）
        int row = lastRow + 1;

        for (int col = 0; col < ui->tableWidget->rowCount(); col++)
        {
            if(ui->tableWidget->item(col, 0)->checkState() == Qt::Checked)
            {
                QAxObject *cell2 = sheet->querySubObject("Cells(int,int)", row+col, 1);
                QTableWidgetItem *item2 = ui->tableWidget->item(col, 0);
                cell2->setProperty("Value2", item2->text());
                delete cell2;
                 QAxObject *cell = sheet->querySubObject("Cells(int,int)", row+col, 2);
                 QTableWidgetItem *item = ui->tableWidget->item(col, 2);
                 cell->setProperty("Value2", item->text());
                 delete cell;
                 QAxObject *cell1 = sheet->querySubObject("Cells(int,int)", row+col, 3);
                 QTableWidgetItem *item1 = ui->tableWidget->item(col, 3);
                 cell1->setProperty("Value2", item1->text());
                 delete cell1;
            }
        }

        // 保存文件（直接覆盖）
        workbook->dynamicCall("SaveAs(const QString&)", filePath);

        // 释放资源
        delete sheet;
        delete sheets;
        workbook->dynamicCall("Close()");
        delete workbook;
    } catch (...) {
        QMessageBox::critical(this, "错误", "保存Excel文件失败！");
    }

    // 彻底退出Excel
    excel->dynamicCall("Quit()");
    delete workbooks;
    delete excel;
}
void cserialWidget::on_EndTestBtn_clicked()
{
    QMessageBox *box = new QMessageBox(QMessageBox::Question, "提示", "是否中断测试", QMessageBox::Yes | QMessageBox::No, this);
    box->button(QMessageBox::Yes)->setText("是");
    box->button(QMessageBox::No)->setText("否");
    int res = box->exec();
    if(QMessageBox::Yes == res)
    {
        ui->EndTestBtn->setStyleSheet("QPushButton {background-color: red; color: white;font-size: 26px;padding: 8px;"
                                      "border-style: outset; border-width: 2px; border-radius: 10px;border-color: beige;}");
        int j=0;
        for (int i = 0; i < 40; ++i)
        {
            if (ui->tableWidget->item(i, 0)->checkState() == Qt::Unchecked)
                continue;
            if(m_testmap.size() == 0 || threadList.size() == 0)
            {
                return;
            }
            if (auto test =  m_testmap[i])
            {
                QMetaObject::invokeMethod(test, &cTestLog::EndTest,Qt::BlockingQueuedConnection);
            }

            if (auto thread = threadList.value(j)) {
                thread->requestInterruption();
                thread->quit();

                QElapsedTimer timer;
                timer.start();
                while (thread->isRunning() && timer.elapsed() < 10000)
                {
                    QCoreApplication::processEvents();
                }

                if (thread->isRunning())
                {
                   thread->terminate();
                   thread->wait();
                }
                j++;
            }
            if(m_IsFinshed[i] == "合格"||m_IsFinshed[i] == "不合格")
            {
                continue;
            }
            else
            {
                QTableWidgetItem *pItem = new QTableWidgetItem();
                pItem->setForeground(QBrush(Qt::red));
                pItem->setText("不合格");
                ui->tableWidget->setItem(i, 6, pItem);
                ui->tableWidget->setItem(i, 5, new QTableWidgetItem("未完成"));
            }
        }
        ui->pushButton_3->setEnabled(true);
        ui->pushButton_3->setStyleSheet("QPushButton {background-color: #0072C6; color: white;font-size: 26px;padding: 8px;"
                                  "border-style: outset; border-width: 2px; border-radius: 10px;border-color: beige;}");
        ui->EndTestBtn->setStyleSheet("QPushButton {background-color: #0072C6; color: white;font-size: 26px;padding: 8px;"
                                  "border-style: outset; border-width: 2px; border-radius: 10px;border-color: beige;}");
        comeEquipmentInit();
        m_pequipmentsjj->SendCommUIDownRapid();
    }
    else if(QMessageBox::No == res)
    {
        return;
    }

}

void cserialWidget::on_checkBox_2_stateChanged(int arg1)
{
    if(arg1==2)
    {
        for(int i = 0; i < 20; i++)
        {
            ui->tableWidget->item(i,0)->setCheckState(Qt::Checked);
        }
    }
    else if(arg1==0)
    {
        for(int i = 0; i < 20; i++)
        {
            ui->tableWidget->item(i,0)->setCheckState(Qt::Unchecked);
        }
    }
}

void cserialWidget::on_checkBox_3_stateChanged(int arg1)
{
    if(arg1==2)
    {
        for(int i = 20; i < 40; i++)
        {
            ui->tableWidget->item(i,0)->setCheckState(Qt::Checked);
        }
    }
    else if(arg1==0)
    {
        for(int i = 20; i < 40; i++)
        {
            ui->tableWidget->item(i,0)->setCheckState(Qt::Unchecked);
        }
    }
}

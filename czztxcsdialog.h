﻿#ifndef CZZTXCSDIALOG_H
#define CZZTXCSDIALOG_H

#include <QDialog>
#include <msk_global.h>

namespace Ui {
class CZztxcsDialog;
}

class CZztxcsDialog : public QDialog
{
    Q_OBJECT

public:
    explicit CZztxcsDialog(QWidget *parent = nullptr);
    ~CZztxcsDialog();
    void setKeyName(QString strName);

private slots:
    void on_checkBox_toggled(bool checked);

    void on_checkBox_2_toggled(bool checked);

    void on_ok_clicked();

private:
    Ui::CZztxcsDialog *ui;
    QString m_strKeyName;
};

#endif // CZZTXCSDIALOG_H

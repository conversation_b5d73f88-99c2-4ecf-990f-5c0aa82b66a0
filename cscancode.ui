<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>cScanCode</class>
 <widget class="QWidget" name="cScanCode">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>1065</width>
    <height>737</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Form</string>
  </property>
  <layout class="QGridLayout" name="gridLayout">
   <item row="0" column="2">
    <widget class="QLabel" name="label">
     <property name="font">
      <font>
       <pointsize>11</pointsize>
      </font>
     </property>
     <property name="text">
      <string>选择检测批次：</string>
     </property>
    </widget>
   </item>
   <item row="0" column="4">
    <widget class="QPushButton" name="okBt">
     <property name="styleSheet">
      <string notr="true">    background-color: #0072C6;
    color: white;
    border-style: outset;
    border-width: 2px;
    border-radius: 10px;
    border-color: beige;
    font-size: 20px;
    padding: 6px;</string>
     </property>
     <property name="text">
      <string>  确  定  </string>
     </property>
    </widget>
   </item>
   <item row="1" column="0" colspan="5">
    <widget class="QTableWidget" name="tableWidget"/>
   </item>
   <item row="0" column="0">
    <widget class="QPushButton" name="CreatePcBt">
     <property name="styleSheet">
      <string notr="true">    background-color: #0072C6;
    color: white;
    border-style: outset;
    border-width: 2px;
    border-radius: 10px;
    border-color: beige;
    font-size: 20px;
    padding: 6px;</string>
     </property>
     <property name="text">
      <string>  新建批次  </string>
     </property>
    </widget>
   </item>
   <item row="0" column="1">
    <spacer name="horizontalSpacer">
     <property name="orientation">
      <enum>Qt::Horizontal</enum>
     </property>
     <property name="sizeHint" stdset="0">
      <size>
       <width>99</width>
       <height>20</height>
      </size>
     </property>
    </spacer>
   </item>
   <item row="0" column="3">
    <widget class="QLineEdit" name="lineEdit"/>
   </item>
  </layout>
 </widget>
 <resources/>
 <connections/>
</ui>

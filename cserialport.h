#ifndef CSERIALPORT_H
#define CSERIALPORT_H

#include <QWidget>
#include <QtSerialPort/QSerialPortInfo>
#include <QtSerialPort/QSerialPort>

namespace Ui {
class CserialPort;
}

class CserialPort : public QWidget
{
    Q_OBJECT

public:
    explicit CserialPort(QWidget *parent = nullptr);
    ~CserialPort();

private:
    Ui::CserialPort *ui;
    QSerialPort *m_SerialPort;
};

#endif // CSERIALPORT_H

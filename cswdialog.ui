<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>CSWDialog</class>
 <widget class="QDialog" name="CSWDialog">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>616</width>
    <height>679</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>SW1/2灯</string>
  </property>
  <layout class="QGridLayout" name="gridLayout">
   <item row="0" column="0">
    <layout class="QVBoxLayout" name="verticalLayout">
     <item>
      <widget class="QTableWidget" name="tableWidget"/>
     </item>
     <item>
      <layout class="QHBoxLayout" name="horizontalLayout">
       <item>
        <spacer name="horizontalSpacer">
         <property name="orientation">
          <enum>Qt::Horizontal</enum>
         </property>
         <property name="sizeHint" stdset="0">
          <size>
           <width>40</width>
           <height>20</height>
          </size>
         </property>
        </spacer>
       </item>
       <item>
        <widget class="QPushButton" name="ok">
         <property name="styleSheet">
          <string notr="true">    QPushButton {background-color:#0089ff ;
    color: white;
    border-style: outset;
    border-width: 2px;
    border-radius: 10px;
    border-color: beige;
    font-size: 26px;
    padding: 6px;}

QPushButton:hover {background-color: #0072C6;         
                     }
</string>
         </property>
         <property name="text">
          <string>确定</string>
         </property>
        </widget>
       </item>
      </layout>
     </item>
    </layout>
   </item>
  </layout>
 </widget>
 <resources/>
 <connections/>
</ui>

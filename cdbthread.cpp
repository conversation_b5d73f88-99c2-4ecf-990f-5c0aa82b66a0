﻿#include "cdbthread.h"
#include <QDebug>

CDBThread::CDBThread()
{
    m_db = QSqlDatabase::addDatabase("QSQLITE");
    m_db.setDatabaseName(g_strScuoutcheckdb);
    m_db.open();
    if(!existDLResult())
    {
        createDLResult();
    }
    if(!existXLResult())
    {
        createXlResult();
    }
    if(!existGc())
    {
        createGc();
    }
    m_db.close();

}

void CDBThread::run()
{
    while(true)
    {
        while (recordResultNum() != 0)
        {
            recordDLResult();
        }

        while(updateDlNum() != 0)
        {
            updateDLItem();
        }

        while (dealXlNum() != 0)
        {
            dealXl();
        }

        while (gcJhNum() != 0)
        {
            recordGc();
        }

        sleep(1);
    }
}

bool CDBThread::existDLResult()
{
    if(!m_db.isOpen())
        return false;

    QSqlQuery execSql(m_db);
    execSql.exec("SELECT 1 FROM sqlite_master WHERE type='table' AND name='SCUDETECTIONRESULT'");
    return execSql.next();
}

void CDBThread::createDLResult()
{
    if(!m_db.isOpen())
        return ;
    QSqlQuery execSql(m_db);

    execSql.exec("CREATE TABLE IF NOT EXISTS SCUDETECTIONRESULT ("
                                  "RQ DATETIME NOT NULL, "
                                  "OID INTEGER NOT NULL, "
                                  "CATEGORYID INTEGER NOT NULL,"
                                  "BATCHID INTEGER NOT NULL,"
                                  "IP TEXT, ESN TEXT,DEVID TEXT, LOGICADDR TEXT, CHECKRESULT INTEGER,USRPASSWD INTEGER, SYSTEMPARAM INTEGER,SCUPARAM INTEGER,IOTPARAM INTEGER,"
                                  "PARAM698 INTEGER, SCUTIMING, INTEGER, GORGE INTEGER, YXDPI INTEGER, TGWGMMC INTEGER, MEASURE INTEGER, BLUETOOTH INTEGER, HPLC4G INTEGER,"
                                  "DATACLEAR INTEGER, BASICINFOR INTEGER,RESERVEPOWER INTEGER,PORTS INTEGER, SWTEST INTEGER, SCURQ INTEGER, SCUAPP INTEGER, HLXJ INTEGER, CCZT INTEGER, CLEARDATA INTEGER, EXPORT INTEGER, IMPORT INTEGER, LXDYDL INTEGER,ZDIP  INTEGER)");

    execSql.exec("CREATE INDEX SCUPT ON SCUDETECTIONRESULT (RQ, CATEGORYID, BATCHID)");
    execSql.exec("CREATE INDEX SCUOID ON SCUDETECTIONRESULT (OID)");
}

bool CDBThread::existXLResult()
{
    if(!m_db.isOpen())
        return false;

    QSqlQuery execSql(m_db);
    execSql.exec("SELECT 1 FROM sqlite_master WHERE type='table' AND name='SCUXLDETECTIONRESULT'");
    return execSql.next();
}

void CDBThread::createXlResult()
{
    if(!m_db.isOpen())
        return ;
    QSqlQuery execSql(m_db);

   bool bsuc =  execSql.exec("CREATE TABLE IF NOT EXISTS SCUXLDETECTIONRESULT (OID INTEGER NOT NULL, DLNAME TEXT, XLNAME TEXT,RESULT INTEGER)");

    bool ccc = execSql.exec("CREATE INDEX SCUXLOID ON SCUXLDETECTIONRESULT (OID)");
    qDebug() << "createxl" << bsuc << ":" << ccc;
}

bool CDBThread::existGc()
{
    if(!m_db.isOpen())
        return false;

    QSqlQuery execSql(m_db);
    execSql.exec("SELECT 1 FROM sqlite_master WHERE type='table' AND name='SCUDETECTIONGC'");
    return execSql.next();
}

void CDBThread::createGc()
{
    if(!m_db.isOpen())
        return ;
    QSqlQuery execSql(m_db);

    execSql.exec("CREATE TABLE IF NOT EXISTS SCUDETECTIONGC (OID INTEGER NOT NULL, DLNAME TEXT, XLNAME TEXT,GC TEXT)");
    execSql.exec("CREATE INDEX SCUGCOID ON SCUDETECTIONGC (OID)");
    execSql.exec("CREATE INDEX SCUGCNAME ON SCUDETECTIONGC (DLNAME, XLNAME)");
}

int CDBThread::recordResultNum()
{
   m_mtxRecordResult.lock();
   int num = m_vtRecordResult.size();
   m_mtxRecordResult.unlock();
   return num;
}

void CDBThread::recordDLResult()
{
    m_mtxRecordResult.lock();
    SSCURecoreResult rr = m_vtRecordResult.front();
    m_vtRecordResult.pop();
    m_mtxRecordResult.unlock();

    m_db = QSqlDatabase::addDatabase("QSQLITE");
    m_db.setDatabaseName(g_strScuoutcheckdb);

    if(!m_db.isOpen())
    {
        m_db.open();
    }
    if(!m_db.isOpen())
    {
        return;
    }

    QSqlQuery execSql(m_db);
    QString sql;
    sql.sprintf("INSERT INTO SCUDETECTIONRESULT(RQ, OID, CATEGORYID, BATCHID, IP, ESN, DEVID, LOGICADDR) VALUES(:RQ, %lld,%d,%d,'%s', '%s', '%s', '%s')",  rr.noid, rr.nfaid, rr.npcid,
                rr.strIp.toStdString().c_str(), rr.strEsn.toStdString().c_str(), rr.strDevid.toStdString().c_str(),rr.strLogicaddr.toStdString().c_str());
    execSql.prepare(sql);
    execSql.bindValue(":RQ", rr.dtRq);
    bool b = execSql.exec();
    m_db.close();
}

int CDBThread::updateDlNum()
{
    m_mtxUpdateDl.lock();
    int num = m_vtUpdateResult.size();
    m_mtxUpdateDl.unlock();
    return num;
}

void CDBThread::updateDLItem()
{
    m_mtxUpdateDl.lock();
    SSCUUpdateResult rr = m_vtUpdateResult.front();
    m_vtUpdateResult.pop();
    m_mtxUpdateDl.unlock();

    m_db = QSqlDatabase::addDatabase("QSQLITE");
    m_db.setDatabaseName(g_strScuoutcheckdb);

    if(!m_db.isOpen())
    {
        m_db.open();
    }
    if(!m_db.isOpen())
    {
        return;
    }


    int ns = rr.bresult;
    QSqlQuery execSql(m_db);
    QString sql;
    if(rr.strDlName == "用户名密码")
        sql.sprintf("UPDATE SCUDETECTIONRESULT SET USRPASSWD = %d WHERE OID = %lld", ns, rr.noid);
    else if(rr.strDlName == "容器")
        sql.sprintf("UPDATE SCUDETECTIONRESULT SET  SCURQ = %d WHERE OID = %lld", ns, rr.noid);
    else if(rr.strDlName == "APP运行")
        sql.sprintf("UPDATE SCUDETECTIONRESULT SET  SCUAPP = %d WHERE OID = %lld", ns, rr.noid);
    else if(rr.strDlName == "系统和补丁版本")
        sql.sprintf("UPDATE SCUDETECTIONRESULT SET  SYSTEMPARAM = %d WHERE OID = %lld", ns, rr.noid);
    else if(rr.strDlName == "终端参数")
        sql.sprintf("UPDATE SCUDETECTIONRESULT SET  SCUPARAM = %d WHERE OID = %lld", ns, rr.noid);
    else if(rr.strDlName == "698参数配置")
        sql.sprintf("UPDATE SCUDETECTIONRESULT SET  PARAM698 = %d WHERE OID = %lld", ns, rr.noid);
    else if(rr.strDlName == "设备对时")
        sql.sprintf("UPDATE SCUDETECTIONRESULT SET  SCUTIMING = %d WHERE OID = %lld", ns, rr.noid);
    else if(rr.strDlName == "串口通信")
        sql.sprintf("UPDATE SCUDETECTIONRESULT SET  GORGE = %d WHERE OID = %lld", ns, rr.noid);
    else if(rr.strDlName == "遥信分辨率")
        sql.sprintf("UPDATE SCUDETECTIONRESULT SET  YXDPI = %d WHERE OID = %lld", ns, rr.noid);
    else if(rr.strDlName == "有功、无功、秒脉冲接口")
        sql.sprintf("UPDATE SCUDETECTIONRESULT SET  TGWGMMC = %d WHERE OID = %lld", ns, rr.noid);
    else if(rr.strDlName == "交采计量精度")
        sql.sprintf("UPDATE SCUDETECTIONRESULT SET  MEASURE = %d WHERE OID = %lld", ns, rr.noid);
    else if(rr.strDlName == "MQTTIOT配置")
        sql.sprintf("UPDATE SCUDETECTIONRESULT SET  IOTPARAM = %d WHERE OID = %lld", ns, rr.noid);
    else if(rr.strDlName == "蓝牙通信")
        sql.sprintf("UPDATE SCUDETECTIONRESULT SET  BLUETOOTH = %d WHERE OID = %lld", ns, rr.noid);
    else if(rr.strDlName == "HPLC/4G模块")
        sql.sprintf("UPDATE SCUDETECTIONRESULT SET  HPLC4G = %d WHERE OID = %lld", ns, rr.noid);
    else if(rr.strDlName == "数据清零")
        sql.sprintf("UPDATE SCUDETECTIONRESULT SET  DATACLEAR = %d WHERE OID = %lld", ns, rr.noid);
    else if(rr.strDlName == "回路巡检")
        sql.sprintf("UPDATE SCUDETECTIONRESULT SET  HLXJ = %d WHERE OID = %lld", ns, rr.noid);
    else if(rr.strDlName == "磁场状态")
        sql.sprintf("UPDATE SCUDETECTIONRESULT SET  CCZT = %d WHERE OID = %lld", ns, rr.noid);
    else if(rr.strDlName == "SW1/2按键")
        sql.sprintf("UPDATE SCUDETECTIONRESULT SET  SWTEST = %d WHERE OID = %lld", ns, rr.noid);
    else if(rr.strDlName == "端口检查")
        sql.sprintf("UPDATE SCUDETECTIONRESULT SET  PORTS = %d WHERE OID = %lld", ns, rr.noid);
    else if(rr.strDlName == "后备电源")
        sql.sprintf("UPDATE SCUDETECTIONRESULT SET  RESERVEPOWER = %d WHERE OID = %lld", ns, rr.noid);
    else if(rr.strDlName == "最后结论")
        sql.sprintf("UPDATE SCUDETECTIONRESULT SET  CHECKRESULT = %d WHERE OID = %lld", ns, rr.noid);
    else if(rr.strDlName == "清除多余文件")
        sql.sprintf("UPDATE SCUDETECTIONRESULT SET  CLEARDATA = %d WHERE OID = %lld", ns, rr.noid);
    else if(rr.strDlName == "参数设置")
        sql.sprintf("UPDATE SCUDETECTIONRESULT SET  ZDIP = %d WHERE OID = %lld", ns, rr.noid);
    else if(rr.strDlName == "证书导出")
        sql.sprintf("UPDATE SCUDETECTIONRESULT SET  EXPORT = %d WHERE OID = %lld", ns, rr.noid);
    else if(rr.strDlName == "证书导入")
        sql.sprintf("UPDATE SCUDETECTIONRESULT SET  IMPORT = %d WHERE OID = %lld", ns, rr.noid);
    else if(rr.strDlName == "零序检查")
        sql.sprintf("UPDATE SCUDETECTIONRESULT SET  LXDYDL = %d WHERE OID = %lld", ns, rr.noid);

    bool bsucupdate = execSql.exec(sql);

    m_db.close();

}

int CDBThread::dealXlNum()
{
    m_mtxXL.lock();
    int num = m_vtSCUXL.size();
    m_mtxXL.unlock();
    return  num;
}

void CDBThread::dealXl()
{
    m_mtxXL.lock();
    SSCUXL xl = m_vtSCUXL.front();
    m_vtSCUXL.pop();
    m_mtxXL.unlock();

    m_db = QSqlDatabase::addDatabase("QSQLITE");
    m_db.setDatabaseName(g_strScuoutcheckdb);

    if(!m_db.isOpen())
    {
        m_db.open();
    }
    if(!m_db.isOpen())
    {
        return;
    }

    if(existXl(xl))
        updateXl(xl);
    else
        recordXl(xl);

    m_db.close();

}

bool CDBThread::existXl(SSCUXL &xl)
{
    QString sql;
    sql.sprintf("SELECT 1 FROM SCUXLDETECTIONRESULT WHERE OID = %lld AND DLNAME = '%s' AND XLNAME = '%s'", xl.noid, xl.strDl.toStdString().c_str(), xl.strXl.toStdString().c_str());
    QSqlQuery execSql(m_db);
    bool busc = execSql.exec(sql);
    return  execSql.next();
}

void CDBThread::updateXl(SSCUXL &xl)
{
    int nr = xl.bresult ? 1:2;
    QString sql;
    sql.sprintf("UPDATE SCUXLDETECTIONRESULT SET RESULT = %d WHERE OID = %lld AND DLNAME = '%s' AND XLNAME = '%s'", nr, xl.noid, xl.strDl.toStdString().c_str(), xl.strXl.toStdString().c_str());
    QSqlQuery execSql(m_db);
    execSql.exec(sql);
}

void CDBThread::recordXl(SSCUXL &xl)
{
    int nr = xl.bresult ? 1:2;
    QString sql;
    sql.sprintf("INSERT INTO SCUXLDETECTIONRESULT(RESULT,OID,DLNAME,XLNAME) VALUES(%d, %lld, '%s', '%s')", nr, xl.noid, xl.strDl.toStdString().c_str(), xl.strXl.toStdString().c_str());
    QSqlQuery execSql(m_db);
    execSql.exec(sql);
}

int CDBThread::gcJhNum()
{
    m_mtxGC.lock();
    int num = m_vtSCUGC.size();
    m_mtxGC.unlock();
    return num;
}

void CDBThread::recordGc()
{
    m_mtxGC.lock();
    SGntJyXlGc gc = m_vtSCUGC.front();
    m_vtSCUGC.pop();
    m_mtxGC.unlock();

    m_db = QSqlDatabase::addDatabase("QSQLITE");
    m_db.setDatabaseName(g_strScuoutcheckdb);

    if(!m_db.isOpen())
    {
        m_db.open();
    }
    if(!m_db.isOpen())
    {
        return;
    }

    QString sql;
    sql.sprintf("INSERT INTO SCUDETECTIONGC(OID,DLNAME,XLNAME, GC) VALUES(%lld, '%s', '%s', '%s')",  gc.oid, gc.strCsDl.toStdString().c_str(), gc.strCsXl.toStdString().c_str(), gc.strCsGc.toStdString().c_str());
    QSqlQuery execSql(m_db);
    bool b =  execSql.exec(sql);
    m_db.close();
}

void CDBThread::dropTable()
{
    if(!m_db.isOpen())
        return;
    QSqlQuery execSql(m_db);
    execSql.exec("drop table if exists SCUDETECTIONRESULT");
}

void CDBThread::dlResultPush(SSCURecoreResult &rr)
{
    m_mtxRecordResult.lock();
    m_vtRecordResult.push(rr);
    m_mtxRecordResult.unlock();
}

void CDBThread::dlItemPush(SSCUUpdateResult &rr)
{
    m_mtxUpdateDl.lock();
    m_vtUpdateResult.push(rr);
    m_mtxUpdateDl.unlock();
}

void CDBThread::xlItemPust(SSCUXL &xl)
{
    m_mtxXL.lock();
    m_vtSCUXL.push(xl);
    m_mtxXL.unlock();
}

void CDBThread::gcItemPust(SGntJyXlGc &gc)
{
    m_mtxGC.lock();
    m_vtSCUGC.push(gc);
    m_mtxGC.unlock();
}

﻿#include "csetconfigure.h"
#include "ui_csetconfigure.h"
#include<QToolButton>
#include<QStandardItemModel>
#include<QDebug>
#include<QFile>
#include<QXmlStreamReader>
#include<iostream>
#include<QSettings>
#include"msk_global.h"
#include<QMessageBox>
#include <QFileDialog>


cSetConfigure::cSetConfigure(QWidget *parent) :
    QWidget(parent),
    ui(new Ui::cSetConfigure)
{
    ui->setupUi(this);

    this->setWindowTitle("方案配置");

    QDir dir1("E:/scu/");
    dir1.mkdir("txzFile");
    dir1.mkdir("license");
    dir1.mkdir("log");
    dir1.mkdir("db");
    dir1.mkdir("APP");
    dir1.mkdir("xml");

    ui->tabWidget->setCurrentIndex(0);

    QString dl = queryCfg(CheckItemIni,"wcConfigure","dl");
    QString dy = queryCfg(CheckItemIni,"wcConfigure","dy");
    QString glys = queryCfg(CheckItemIni,"wcConfigure","glys");
    ui->dlEdit->setText(dl);
    ui->dyEdit->setText(dy);


    ui->dbFileBtn->setEnabled(false);
    ui->appFIleBtn->setEnabled(false);
    ui->pushButton->setEnabled(false);


    for(auto item:additem)
    {
        ui->comboBox->addItem(item);
    }

    ui->textEdit->setReadOnly(true);
    checkBoxs.push_back(ui->ClearcheckBox);
    checkBoxs.push_back(ui->CompareCheck);
    checkBoxs.push_back(ui->ContainerCheck);
    checkBoxs.push_back(ui->APPCheck);
    checkBoxs.push_back(ui->RunningCheck);
    //checkBoxs.push_back(ui->JBCheck);
    checkBoxs.push_back(ui->updatadbCheckBox);
    checkBoxs.push_back(ui->LicenseCheckBox);
    checkBoxs.push_back(ui->txzCheckBox);
    checkBoxs.push_back(ui->ClearCodeCheck);
    checkBoxs.push_back(ui->SetTimeCheck);
    ui->AllCheckBox->setChecked(true);
    connect(ui->AllCheckBox, &QCheckBox::stateChanged, this, &cSetConfigure::onCheckBoxAllStateChanged);

    for (auto& checkbox1 : checkBoxs)
    {
         checkbox1 ->setChecked(true);
    }
    QString s = queryCfg(CheckItemIni,"currentpc","currentpc");
    QSettings  settings(CheckItemIni, QSettings::IniFormat);
    settings.setIniCodec(QTextCodec::codecForName("UTF-8"));
    settings.beginGroup("testpc");
    QStringList clist = settings.allKeys();
    for (int i =0 ;i<clist.size();i++) {
        QString m = queryCfg(CheckItemIni,"testpc",clist[i]);
        QStringList sl = m.split("@");

        if(sl[0]==s)
        {
            QSettings  setting(installIni, QSettings::IniFormat);
            setting.setIniCodec(QTextCodec::codecForName("UTF-8"));
            setting.beginGroup(sl[1]);
            QStringList clist1 = setting.allKeys();
            m_AppMap.clear();
            for (auto key :clist1) {
                QString ss = queryCfg(installIni,sl[1],key);
                    QStringList items = ss.split("@");
                    std::vector<QString> values;
                    for (const QString& item : items) {
                        values.push_back(item.trimmed());
                        m_AppMap[key].push_back(item);
                    }
            }
        }
    }

       ui->treeWidget->clear();
       ui->treeWidget->headerItem()->setText(0, QString("容器及APP选择"));

       auto iter = m_AppMap.begin();
       for (;iter != m_AppMap.end() ; ++iter)
       {
           QTreeWidgetItem *pItem = new QTreeWidgetItem(ui->treeWidget);
           pItem->setText(0, iter->first);
           pItem->setText(1, "1");
           pItem->setCheckState(0, Qt::Checked);

           std::vector<QString> &vtApp = iter->second;
           for (int m = 0; m < vtApp.size(); ++m)
           {
               QTreeWidgetItem *item = new QTreeWidgetItem(pItem);
               item->setText(0, vtApp[m]);
               item->setText(1, "2");
               item->setCheckState(0, Qt::Checked);
           }
       }


       //校验版本
       QSettings  settings2(CheckItemIni, QSettings::IniFormat);
       settings2.setIniCodec(QTextCodec::codecForName("UTF-8"));

       settings2.beginGroup("compareversion");
       QStringList clist2 = settings2.allKeys();
       for(auto item:clist2)
       {
           ui->comboBox->addItem(item);
       }
       ui->lineEdit->setReadOnly(true);
       ui->lineEdit_2->setReadOnly(true);
       ui->lineEdit_3->setReadOnly(true);
       ui->lineEdit_4->setReadOnly(true);
       ui->lineEdit_5->setReadOnly(true);
       ui->lineEdit_6->setReadOnly(true);
       ui->lineEdit_7->setReadOnly(true);

       ui->lineEdit_9->setReadOnly(true);
       ui->lineEdit_10->setReadOnly(true);
       ui->lineEdit_11->setReadOnly(true);
       ui->lineEdit_12->setReadOnly(true);
}
void cSetConfigure::onslotsTestPc(QString s)
{
    QString ssm = queryCfg(CheckItemIni,"testpc",s);
    QStringList sl = ssm.split("@");


     QSettings  settings1(CheckItemIni, QSettings::IniFormat);
     settings1.setIniCodec(QTextCodec::codecForName("UTF-8"));
     settings1.beginGroup("compareversion");
     QStringList clist = settings1.allKeys();
     for(auto item:clist)
     {
         ui->comboBox->addItem(item);
     }
     ui->comboBox->setCurrentText(sl[1]);
     this->onslotsChageAPP(sl[1]);

     QSettings  settings(CheckItemIni, QSettings::IniFormat);
     settings.setIniCodec(QTextCodec::codecForName("UTF-8"));

     settings.beginGroup("compare");

     settings.setValue("db1",ui->lineEdit->text().remove(" "));
     settings.setValue("db2",ui->lineEdit_2->text().remove(" "));
     settings.setValue("zk",ui->lineEdit_3->text().remove(" "));
     settings.setValue("yj",ui->lineEdit_4->text().remove(" "));
     settings.setValue("nh",ui->lineEdit_5->text().remove(" "));
     settings.setValue("bh",ui->lineEdit_6->text().remove(" "));
     settings.setValue("rq",ui->lineEdit_7->text().remove(" "));

     settings.setValue("gj",ui->lineEdit_9->text().remove(" "));
     settings.setValue("cz",ui->lineEdit_10->text().remove(" "));
     settings.setValue("dbFile",ui->lineEdit_11->text().remove(" "));
     settings.setValue("appFile",ui->lineEdit_12->text().remove(" "));

     settings.endGroup();

     ui->comboBox->setEditable(true);
     ui->lineEdit->setReadOnly(true);
     ui->lineEdit_2->setReadOnly(true);
     ui->lineEdit_3->setReadOnly(true);
     ui->lineEdit_4->setReadOnly(true);
     ui->lineEdit_5->setReadOnly(true);
     ui->lineEdit_6->setReadOnly(true);
     ui->lineEdit_7->setReadOnly(true);

     ui->lineEdit_9->setReadOnly(true);
     ui->lineEdit_10->setReadOnly(true);
     ui->dbFileBtn->setEnabled(false);
     ui->appFIleBtn->setEnabled(false);


     QString ss = ui->lineEdit->text().remove(" ") +"@"+ui->lineEdit_2->text().remove(" ") +"@"+
             ui->lineEdit_3->text().remove(" ")  +"@"+
             ui->lineEdit_4->text().remove(" ")  +"@"+
             ui->lineEdit_5->text().remove(" ")  +"@"+
             ui->lineEdit_6->text().remove(" ")  +"@"+
             ui->lineEdit_7->text().remove(" ")  +"@"+

             ui->lineEdit_9->text().remove(" ")  +"@"+
             ui->lineEdit_10->text().remove(" ") +"@"+
             ui->lineEdit_11->text().remove(" ") +"@"+
             ui->lineEdit_12->text().remove(" ") ;


     QSettings  settingf(CheckItemIni, QSettings::IniFormat);
     settingf.setIniCodec(QTextCodec::codecForName("UTF-8"));
     settingf.beginGroup("compareversion");
     QStringList list = settingf.allKeys();


     QTreeWidgetItem* rootItem =ui->treeWidget->invisibleRootItem();
     this->traverseTreeBFS(rootItem);
     if(ui->IotCheckBox->isChecked()==true)
     {
         setCfg(CheckItemIni, "installapp", "iotManager", "1");
     }
     else {
         setCfg(CheckItemIni, "installapp", "iotManager", "0");
     }
}
void cSetConfigure::onslotsChageAPP(QString s)
{
    QSettings  setting(installIni, QSettings::IniFormat);
    setting.setIniCodec(QTextCodec::codecForName("UTF-8"));
    setting.beginGroup(s);
    QStringList clist1 = setting.allKeys();
    m_AppMap.clear();
    for (auto key :clist1) {
        QString ss = queryCfg(installIni,s,key);
        QStringList items = ss.split("@");
        std::vector<QString> values;
        for (const QString& item : items) {
            values.push_back(item.trimmed());
            m_AppMap[key].push_back(item);
        }
    }
    ui->treeWidget->clear();
    auto iter = m_AppMap.begin();
    for (;iter != m_AppMap.end() ; ++iter)
    {
        QTreeWidgetItem *pItem = new QTreeWidgetItem(ui->treeWidget);
        pItem->setText(0, iter->first);
        pItem->setText(1, "1");
        pItem->setCheckState(0, Qt::Checked);

        std::vector<QString> &vtApp = iter->second;
        for (int m = 0; m < vtApp.size(); ++m)
        {
            QTreeWidgetItem *item = new QTreeWidgetItem(pItem);
            item->setText(0, vtApp[m]);
            item->setText(1, "2");
            item->setCheckState(0, Qt::Checked);
        }
    }

}

void cSetConfigure::traverseTreeBFS(QTreeWidgetItem* rootItem)
{
    delALLKeyCfg(CheckItemIni,"installcontainer");
    delALLKeyCfg(CheckItemIni,"installapp");
    if (!rootItem) return; //确保根节点有效
    for (int i = 0; i < rootItem->childCount(); i++) {
        QTreeWidgetItem* childItem = rootItem->child(i);
        if (childItem->checkState(0) == Qt::Checked) {
            if (childItem->text(1) == "1") {
                setCfg(CheckItemIni, "installcontainer", childItem->text(0), "1");
                for (int j = 0; j < childItem->childCount(); j++) {
                    QTreeWidgetItem* appItem = childItem->child(j);
                    setCfg(CheckItemIni, "installapp", appItem->text(0), "1");
                }
            } else {
                setCfg(CheckItemIni, "installcontainer", childItem->text(0), "0");
            }
        }
        else {
            setCfg(CheckItemIni, "installcontainer", childItem->text(0), "0");
            for (int j = 0; j < childItem->childCount(); j++)
            {
                QTreeWidgetItem* appItem = childItem->child(j);
                setCfg(CheckItemIni, "installapp", appItem->text(0), "0");
            }
        }
    }
}
void cSetConfigure::onCheckBoxAllStateChanged(int state)
{
    for (auto& checkbox1 : checkBoxs)
    {
        checkbox1->setChecked(true);
        if (state == Qt::Checked)
        {
            checkbox1->setChecked(true);
        }
        else if (state == Qt::Unchecked)
        {
            checkbox1->setChecked(false);
        }
    }
}

cSetConfigure::~cSetConfigure()
{
    delete ui;
}

void cSetConfigure::on_pushButton_2_clicked()
{
    if(ui->ClearcheckBox->isChecked()==true)
    {
        setCfg(CheckItemIni,"testitem","clearFile","1");
    }
    else {
        setCfg(CheckItemIni,"testitem","clearFile","0");
    }
    if(ui->CompareCheck->isChecked()==true)
    {
        setCfg(CheckItemIni,"testitem","compareversion","1");
    }
    else {
        setCfg(CheckItemIni,"testitem","compareversion","0");
    }
    if(ui->ContainerCheck->isChecked()==true)
    {
        setCfg(CheckItemIni,"testitem","installcontianer","1");
    }
    else {
        setCfg(CheckItemIni,"testitem","installcontianer","0");
    }
    if(ui->APPCheck->isChecked()==true)
    {
        setCfg(CheckItemIni,"testitem","installapp","1");
    }
    else {
        setCfg(CheckItemIni,"testitem","installapp","0");
    }
    if(ui->RunningCheck->isChecked()==true)
    {
        setCfg(CheckItemIni,"testitem","apprunning","1");
    }
    else {
        setCfg(CheckItemIni,"testitem","apprunning","0");
    }
    if(ui->JBCheck->isChecked()==true)
    {
        setCfg(CheckItemIni,"testitem","jiaobiao","1");
    }
    else {
        setCfg(CheckItemIni,"testitem","jiaobiao","0");
    }
    if(ui->txzCheckBox->isChecked() == true)
    {
        setCfg(CheckItemIni,"testitem","txzFile","1");
    }
    else {
        setCfg(CheckItemIni,"testitem","txzFile","0");
    }
    if(ui->updatadbCheckBox->isChecked() == true)
    {
        setCfg(CheckItemIni,"testitem","updatadb","1");
    }
    else {
        setCfg(CheckItemIni,"testitem","updatadb","0");
    }
    if(ui->LicenseCheckBox->isChecked() == true)
    {
        setCfg(CheckItemIni,"testitem","licenseFile","1");
    }
    else {
        setCfg(CheckItemIni,"testitem","licenseFile","0");
    }
    if(ui->ClearCodeCheck->isChecked() == true)
    {
        setCfg(CheckItemIni,"testitem","clearBTcode","1");
    }
    else
    {
        setCfg(CheckItemIni,"testitem","clearBTcode","0");
    }
    if(ui->SetTimeCheck->isChecked() == true)
    {
        setCfg(CheckItemIni,"testitem","SetDevTim","1");
    }
    else
    {
        setCfg(CheckItemIni,"testitem","SetDevTim","0");
    }
    this->close();

}

void cSetConfigure::on_pushButton_clicked() //save方案
{
    QSettings  settings(CheckItemIni, QSettings::IniFormat);
    settings.setIniCodec(QTextCodec::codecForName("UTF-8"));

    settings.beginGroup("compare");

    settings.setValue("db1",ui->lineEdit->text().remove(" "));
    settings.setValue("db2",ui->lineEdit_2->text().remove(" "));
    settings.setValue("zk",ui->lineEdit_3->text().remove(" "));
    settings.setValue("yj",ui->lineEdit_4->text().remove(" "));
    settings.setValue("nh",ui->lineEdit_5->text().remove(" "));
    settings.setValue("bh",ui->lineEdit_6->text().remove(" "));
    settings.setValue("rq",ui->lineEdit_7->text().remove(" "));

    settings.setValue("gj",ui->lineEdit_9->text().remove(" "));
    settings.setValue("cz",ui->lineEdit_10->text().remove(" "));
    settings.setValue("dbFile",ui->lineEdit_11->text().remove(" "));
    settings.setValue("appFile",ui->lineEdit_12->text().remove(" "));

    settings.endGroup();

    ui->comboBox->setEditable(true);
    ui->lineEdit->setReadOnly(true);
    ui->lineEdit_2->setReadOnly(true);
    ui->lineEdit_3->setReadOnly(true);
    ui->lineEdit_4->setReadOnly(true);
    ui->lineEdit_5->setReadOnly(true);
    ui->lineEdit_6->setReadOnly(true);
    ui->lineEdit_7->setReadOnly(true);

    ui->lineEdit_9->setReadOnly(true);
    ui->lineEdit_10->setReadOnly(true);
    ui->dbFileBtn->setEnabled(false);
    ui->appFIleBtn->setEnabled(false);

    QString ss = ui->lineEdit->text().remove(" ") +"@"+ui->lineEdit_2->text().remove(" ") +"@"+
            ui->lineEdit_3->text().remove(" ")  +"@"+
            ui->lineEdit_4->text().remove(" ")  +"@"+
            ui->lineEdit_5->text().remove(" ")  +"@"+
            ui->lineEdit_6->text().remove(" ")  +"@"+
            ui->lineEdit_7->text().remove(" ")  +"@"+
            ui->lineEdit_9->text().remove(" ")  +"@"+
            ui->lineEdit_10->text().remove(" ") +"@"+
            ui->lineEdit_11->text().remove(" ") +"@"+
            ui->lineEdit_12->text().remove(" ") ;


    QSettings  setting(CheckItemIni, QSettings::IniFormat);
    setting.setIniCodec(QTextCodec::codecForName("UTF-8"));
    setting.beginGroup("compareversion");
    QStringList list = setting.allKeys();

    QString configures = ui->textEdit->toPlainText();

    ui->textEdit->setReadOnly(true);
    if(list.contains(ui->comboBox->currentText())==true)
    {
        QString s = ui->comboBox->currentText().toUtf8();
        delKeyCfg(CheckItemIni,"compareversion",s);
        delALLKeyCfg(installIni,s);
        setCfg(CheckItemIni,"compareversion",s,ss);
        if(configures != nullptr)
        {
            QSettings  settings(installIni, QSettings::IniFormat);
            settings.setIniCodec(QTextCodec::codecForName("UTF-8"));
            settings.beginGroup(s);
            QStringList sl= configures.split("\n");
            for (auto item :sl) {
                if(item ==nullptr)
                {
                    continue;
                }
                QStringList list = item.split("=");
                setCfg(installIni,s,list[0],list[1]);
            }
        }
    }
    if(ui->comboBox->currentText()!=nullptr  && list.contains(ui->comboBox->currentText())==false)
    {
        QString s = ui->comboBox->currentText().toUtf8();
        ui->comboBox->addItem(s);
        setCfg(CheckItemIni,"compareversion",s,ss);
        if(configures != nullptr)
        {
            QSettings  settings(installIni, QSettings::IniFormat);
            settings.setIniCodec(QTextCodec::codecForName("UTF-8"));
            settings.beginGroup(s);
            QStringList sl= configures.split("\n");
            for (auto item :sl) {
                if(item == nullptr)
                {
                    continue;
                }
                QStringList list = item.split("=");
                setCfg(installIni,s,list[0],list[1]);
            }
        }
    }
    else if (ui->comboBox->currentText() == nullptr) {
        QMessageBox::information(nullptr, "提示", "添加信息为空");
    }
    QTreeWidgetItem* rootItem =ui->treeWidget->invisibleRootItem();
    this->traverseTreeBFS(rootItem);
    this->onslotsChageAPP(ui->comboBox->currentText());
    if(ui->IotCheckBox->isChecked()==true)
    {
        setCfg(CheckItemIni, "installapp", "iotManager", "1");
    }
    else {
        setCfg(CheckItemIni, "installapp", "iotManager", "0");
    }
    QMessageBox::information(nullptr,"提示","保存成功");
    ui->pushButton->setEnabled(false);

}
void cSetConfigure::on_pushButton_3_clicked()  //newadd
{

    QDialog dialog(this);
    dialog.setWindowTitle("输入密码");
    dialog.setFixedSize(500, 200);

    QLineEdit passwordInput;
    passwordInput.setEchoMode(QLineEdit::Password); // 隐藏输入
    QPushButton btnConfirm("确定", &dialog);

    QVBoxLayout layout(&dialog);
    layout.addWidget(&passwordInput);
    layout.addWidget(&btnConfirm);

    // 验证密码
    QObject::connect(&btnConfirm, &QPushButton::clicked, [&] {
        if (passwordInput.text() == "megsky@1234") {
            dialog.accept();
            ui->lineEdit->clear();
            ui->lineEdit_2->clear();
            ui->lineEdit_3->clear();
            ui->lineEdit_4->clear();
            ui->lineEdit_4->clear();
            ui->lineEdit_5->clear();
            ui->lineEdit_6->clear();
            ui->lineEdit_7->clear();

            ui->lineEdit_9->clear();
            ui->lineEdit_10->clear();
            ui->lineEdit_11->clear();
            ui->lineEdit_12->clear();

            ui->lineEdit->setReadOnly(false);
            ui->lineEdit_2->setReadOnly(false);
            ui->lineEdit_3->setReadOnly(false);
            ui->lineEdit_4->setReadOnly(false);
            ui->lineEdit_5->setReadOnly(false);
            ui->lineEdit_6->setReadOnly(false);
            ui->lineEdit_7->setReadOnly(false);

            ui->lineEdit_9->setReadOnly(false);
            ui->lineEdit_10->setReadOnly(false);
            ui->dbFileBtn->setEnabled(true);
            ui->appFIleBtn->setEnabled(true);

            ui->comboBox->setEditable(true);
            ui->comboBox->setEditText("");
            ui->textEdit->setReadOnly(false);
            ui->textEdit->clear();

            ui->pushButton_3->setEnabled(true);
            ui->pushButton->setEnabled(true);
        } else {
            QMessageBox::warning(&dialog, "错误", "密码错误");
            ui->pushButton_3->setEnabled(false);
            passwordInput.clear();
        }
    });
    dialog.exec();

}

void cSetConfigure::on_comboBox_currentIndexChanged(const QString &arg1)
{
    QString s = queryCfg(CheckItemIni,"compareversion",arg1);
    QStringList slist = s.split("@");
    if(slist.size()<11)
    {
        return;
    }
    qDebug()<<slist;
    ui->lineEdit->setText(slist[0]);
    ui->lineEdit_2->setText(slist[1]);
    ui->lineEdit_3->setText(slist[2]);
    ui->lineEdit_4->setText(slist[3]);
    ui->lineEdit_5->setText(slist[4]);
    ui->lineEdit_6->setText(slist[5]);
    ui->lineEdit_7->setText(slist[6]);

    ui->lineEdit_9->setText(slist[7]);
    ui->lineEdit_10->setText(slist[8]);
    ui->lineEdit_11->setText(slist[9]);
    ui->lineEdit_12->setText(slist[10]);
    QSettings  setting(installIni, QSettings::IniFormat);
    setting.setIniCodec(QTextCodec::codecForName("UTF-8"));
    setting.beginGroup(arg1);
    QStringList keys = setting.childKeys();
    ui->textEdit->clear();
    QMap<QString, QVariant> groupContents;
    foreach (const QString &key, keys) {
        QVariant value = setting.value(key);
        groupContents.insert(key, value);
        ui->textEdit->append(key + "=" + value.toString());
    }
    setting.endGroup();
    onslotsChageAPP(arg1);

}
void cSetConfigure::on_treeWidget_itemChanged(QTreeWidgetItem *item, int column)
{
    int num = item->text(1).toInt();
    QString s = item->text(0);
    if(num == 1)   //容器
    {
        if(item->checkState(0) == Qt::Unchecked )
        {
            for (int m = 0; m < item->childCount(); ++m)
            {
                if(item->child(m)->checkState(0) != Qt::Checked)
                {
                    continue;
                }
                item->child(m)->setCheckState(0, Qt::Unchecked);
            }
        }
        else if(item->checkState(0) == Qt::Checked)
        {
            for (int m = 0; m < item->childCount(); ++m)
            {
                if(ui->APPCheck->isChecked())
                {
                    item->child(m)->setCheckState(0,Qt::Checked);
                }
            }
        }
    }
    else if(num == 2)  //app
    {
        if(item->checkState(0) == Qt::Checked)
        {
            if(item->parent()->checkState(0) == Qt::Unchecked)
            {
                item->parent()->setCheckState(0, Qt::Checked);
            }

        }
    }
}

void cSetConfigure::on_dbFileBtn_clicked()
{
    QString dbFileName = QFileDialog::getOpenFileName(nullptr,"大包升级包","E:/scu/db","*.hpm");
    ui->lineEdit_11->setText(dbFileName);
}

void cSetConfigure::on_appFIleBtn_clicked()
{
    QString dirName = QFileDialog::getExistingDirectory(nullptr,"APP安装文件夹","E:/scu/APP");
    QDir dir(dirName);
    ui->lineEdit_12->setText(dirName);

    QStringList folderList = dir.entryList();
    dir.setFilter(QDir::Dirs | QDir::NoDotAndDotDot);
    qDebug()<<folderList;

    for (const QString &folder : folderList) {
        if(folder == "."||folder == "..")
        {
            continue;
        }
    }
}

void cSetConfigure::on_ChageBtn_clicked()
{
    QDialog dialog(this);
    dialog.setWindowTitle("输入密码");
    dialog.setFixedSize(500, 200);

    QLineEdit passwordInput;
    passwordInput.setEchoMode(QLineEdit::Password);
    QPushButton btnConfirm("确定", &dialog);

    QVBoxLayout layout(&dialog);
    layout.addWidget(&passwordInput);
    layout.addWidget(&btnConfirm);

    QObject::connect(&btnConfirm, &QPushButton::clicked, [&] {
        if (passwordInput.text() == "megsky@1234") {
            dialog.accept();
            ui->ChageBtn->setDisabled(false);
            ui->lineEdit->setReadOnly(false);
            ui->lineEdit_2->setReadOnly(false);
            ui->lineEdit_3->setReadOnly(false);
            ui->lineEdit_4->setReadOnly(false);
            ui->lineEdit_5->setReadOnly(false);
            ui->lineEdit_6->setReadOnly(false);
            ui->lineEdit_7->setReadOnly(false);

            ui->lineEdit_9->setReadOnly(false);
            ui->lineEdit_10->setReadOnly(false);
            ui->dbFileBtn->setEnabled(true);
            ui->appFIleBtn->setEnabled(true);
            ui->comboBox->setEditable(true);

            ui->ChageBtn->setEnabled(true);
            ui->textEdit->setReadOnly(false);
            ui->textEdit->clear();
            ui->pushButton->setEnabled(true);
            QString s = ui->comboBox->currentText();
            QSettings  setting(installIni, QSettings::IniFormat);
            setting.setIniCodec(QTextCodec::codecForName("UTF-8"));
            setting.beginGroup(s);
            QStringList keys = setting.childKeys();

            QMap<QString, QVariant> groupContents;
            foreach (const QString &key, keys) {
                QVariant value = setting.value(key);
                groupContents.insert(key, value);
                ui->textEdit->append(key + "=" + value.toString());
            }
            setting.endGroup();

        } else {
            QMessageBox::warning(&dialog, "错误", "密码错误");
            ui->ChageBtn->setEnabled(false);
            passwordInput.clear();
        }
    });
    dialog.exec();
}

void cSetConfigure::on_del_clicked()
{
    QDialog dialog(this);
    dialog.setWindowTitle("输入密码");
    dialog.setFixedSize(500, 200);

    QLineEdit passwordInput;
    passwordInput.setEchoMode(QLineEdit::Password);
    QPushButton btnConfirm("确定", &dialog);

    QVBoxLayout layout(&dialog);
    layout.addWidget(&passwordInput);
    layout.addWidget(&btnConfirm);

    QObject::connect(&btnConfirm, &QPushButton::clicked, [&] {
        if (passwordInput.text() == "megsky@1234") {
            dialog.accept();
            QString s = ui->comboBox->currentText();
            delKeyCfg(CheckItemIni,"compareversion",s);
            int index = ui->comboBox->findText(s);
            if (index != -1) {
                ui->comboBox->removeItem(index);
            }
            ui->del->setEnabled(true);
            delALLKeyCfg(installIni,s);

        } else {
            QMessageBox::warning(&dialog, "错误", "密码错误");
            ui->del->setEnabled(false);
            passwordInput.clear();
        }
    });
    dialog.exec();

}

void cSetConfigure::on_wuchaBtn_clicked()
{
    QDialog dialog(this);
    dialog.setWindowTitle("输入密码");
    dialog.setFixedSize(500, 200);

    QLineEdit passwordInput;
    passwordInput.setEchoMode(QLineEdit::Password);
    QPushButton btnConfirm("确定", &dialog);

    QVBoxLayout layout(&dialog);
    layout.addWidget(&passwordInput);
    layout.addWidget(&btnConfirm);

    QObject::connect(&btnConfirm, &QPushButton::clicked, [&] {
        if (passwordInput.text() == "megsky@1234") {
            dialog.accept();
            QString dl = ui->dlEdit->text();
            QString dy = ui->dyEdit->text();

            setCfg(CheckItemIni,"wcConfigure","dl",dl);
            setCfg(CheckItemIni,"wcConfigure","dy",dy);

        } else {
            QMessageBox::warning(&dialog, "错误", "密码错误");
            passwordInput.clear();
        }
    });
    dialog.exec();
}

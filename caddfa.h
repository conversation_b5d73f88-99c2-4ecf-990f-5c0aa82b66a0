﻿#ifndef CADDFA_H
#define CADDFA_H

#include <QDialog>

namespace Ui {
class CAddFa;
}

class CAddFa : public QDialog
{
    Q_OBJECT

public:
    explicit CAddFa(QWidget *parent = nullptr);
    ~CAddFa();

    QString getFa();

    QString getCpFa();

private slots:
    void on_ok_clicked();

    void on_fafzCB_toggled(bool checked);

    void queryFa();

private:
    Ui::CAddFa *ui;
};

#endif // CADDFA_H

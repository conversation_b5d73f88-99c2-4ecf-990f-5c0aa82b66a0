﻿#ifndef CFINISHDIALOG_H
#define CFINISHDIALOG_H

#include <QDialog>
#include "msk_global.h"
namespace Ui {
class CFinishDialog;
}

enum E_TestItem
{
    E_ITEM_DX,        // 测试大项
    E_ITEM_XX,        // 测试小项
    E_ITEM_JL,        // 结论
    E_ITEM_MAX
};

class CFinishDialog : public QDialog
{
    Q_OBJECT

public:
    explicit CFinishDialog(QWidget *parent = nullptr);
    ~CFinishDialog();
    void init(std::map<int, std::map<QString, SGntJyParam> >&);
private slots:
    void on_pushButton_clicked();

private:
    Ui::CFinishDialog *ui;
};

#endif // CFINISHDIALOG_H

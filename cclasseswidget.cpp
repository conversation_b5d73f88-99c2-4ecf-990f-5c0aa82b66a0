﻿#include "cclasseswidget.h"
#include "ui_cclasseswidget.h"
#include "caddfa.h"
#include <QDebug>
#include <QMessageBox>
#include <QCheckBox>
#include "cmmdialog.h"

CClassesWidget::CClassesWidget(QWidget *parent) :
    QWidget(parent),
    ui(new Ui::CClassesWidget)
{
    ui->setupUi(this);

    ui->tableWidget->setColumnCount(classes_max);

    QTableWidgetItem *pitem = new QTableWidgetItem();
    pitem->setText("选择");
    ui->tableWidget->setHorizontalHeaderItem(classes_xz, pitem);

    pitem = new QTableWidgetItem();
    pitem->setText("序号");
    ui->tableWidget->setHorizontalHeaderItem(classes_xh, pitem);

    QTableWidgetItem *item = new QTableWidgetItem();
    item->setText("方案名称");
    ui->tableWidget->setHorizontalHeaderItem(classes_cslb, item);

    ui->tableWidget->setColumnWidth(classes_cslb, 1000);
    ui->tableWidget->hideColumn(classes_xh);

  //  connect(ui->tableWidget, SIGNAL(itemChanged(QTableWidgetItem *)), this, SLOT(on_table_itemChanged(QTableWidgetItem *)));

    m_bChange = false;

    m_pParam = nullptr;
}

CClassesWidget::~CClassesWidget()
{
    delete ui;
}

void CClassesWidget::initClasses(quint64 nLbXh ,std::vector<sClasses> &vtClass)
{
    m_nFAID = nLbXh;
    m_vtClasses = vtClass;

    ui->tableWidget->setRowCount(vtClass.size());
    for (int m = 0; m < vtClass.size(); ++m)
    {
        QCheckBox *pCB = new QCheckBox();
        if(vtClass[m].strXh.toInt() == m_nFAID)
        {
            pCB->setCheckState(Qt::Checked);
        }
        connect(pCB, SIGNAL(stateChanged(int)), this, SLOT(on_check_chg(int)));

        ui->tableWidget->setCellWidget(m, classes_xz, pCB);

        QTableWidgetItem *pitem = new QTableWidgetItem();
        pitem->setFlags(pitem->flags() & ~Qt::ItemIsEnabled);
        pitem->setText(vtClass[m].strXh);
        ui->tableWidget->setItem(m, classes_xh, pitem);

        pitem = new QTableWidgetItem();
        pitem->setFlags(pitem->flags() & ~Qt::ItemIsEnabled);
        pitem->setText(vtClass[m].strJclb);
        ui->tableWidget->setItem(m, classes_cslb, pitem);
    }

    m_bChange = true;
}

void CClassesWidget::on_newBtn_clicked()
{

    CAddFa   fa;
    if(fa.exec() != QDialog::Accepted)
        return;

    int nFaXh = queryCfg(strScuOutAutoCheckIni, "FA", "FAXH").toInt();
    int nRow = ui->tableWidget->rowCount();
    ui->tableWidget->setRowCount(nRow+1);

    QCheckBox *pcb = new QCheckBox(this);
    connect(pcb, SIGNAL(stateChanged(int)), this, SLOT(on_check_chg(int)));
    ui->tableWidget->setCellWidget(nRow, classes_xz, pcb);

    QTableWidgetItem *pitem = new QTableWidgetItem();
    pitem->setFlags(pitem->flags() & ~Qt::ItemIsEnabled);
    pitem->setText(QString::number(++nFaXh));
    ui->tableWidget->setItem(nRow, classes_xh, pitem);

    pitem = new QTableWidgetItem();
    pitem->setFlags(pitem->flags() & ~Qt::ItemIsEnabled);
    pitem->setText(fa.getFa());
    ui->tableWidget->setItem(nRow, classes_cslb, pitem);

    setCfg(strScuOutAutoCheckIni, "FA", "FAXH", nFaXh);
    setCfg(strScuOutAutoCheckIni, "FA", QString::number(nFaXh), fa.getFa());

    if(fa.getCpFa().isEmpty())
        return;

    // 复制方案

    QString sFAID = QString::number(nFaXh);
    QString sCpFAID;
    QString sCpFa = fa.getCpFa();

    QSettings  settings(strScuOutAutoCheckIni, QSettings::IniFormat);
    settings.setIniCodec(QTextCodec::codecForName("UTF-8"));

    settings.beginGroup("FA");
    QStringList sList = settings.allKeys();

    for(int m = 0; m < sList.size(); ++m)
    {
        if(sList[m] == "FAXH" || sList[m] == "FAID")
            continue;

        if(sCpFa == settings.value(sList[m]).toString())
        {
            sCpFAID = sList[m];
            break;
        }
    }
    settings.endGroup();

    settings.beginGroup("jcx_" + sCpFAID);
    QString sjcx = settings.value("jcx").toString();
    settings.endGroup();

    settings.beginGroup("jcx_" + sFAID);
    settings.setValue("jcx", sjcx);
    settings.endGroup();


    cpFA("gntparam_rqApp_" + sCpFAID, "gntparam_rqApp_" + sFAID);

    cpFA("gntparam_rqApphash_" + sCpFAID, "gntparam_rqApphash_" + sFAID);

    cpFA("gntparam_rq_" + sCpFAID, "gntparam_rq_" + sFAID);

    cpFA("gntparam_rqhash_" + sCpFAID, "gntparam_rqhash_" + sFAID);

    cpFA("gntparam_rqwApp_" + sCpFAID, "gntparam_rqwApp_" + sFAID);

    cpFA("scusys_" + sCpFAID, "scusys_" + sFAID);

    cpFA("aqmscspz_" + sCpFAID, "aqmscspz_" + sFAID);

    cpFA("gwtxpz_" + sCpFAID, "gwtxpz_" + sFAID);

    cpFA("gwzztxdz_" + sCpFAID, "gwzztxdz_" + sFAID);

    cpFA("tsdcspz_" + sCpFAID, "tsdcspz_" + sFAID);

    cpFA("ytwtxpz_" + sCpFAID, "ytwtxpz_" + sFAID);

    cpFA("ytwzztxdz_" + sCpFAID, "ytwzztxdz_" + sFAID);

    cpFA("other_" + sCpFAID, "other_" + sFAID);
}

void CClassesWidget::on_table_itemChanged(QTableWidgetItem *pitem)
{
    if(!m_bChange)
        return;

    if(pitem == nullptr)
        return;

    int nCol = pitem->column();
    if(nCol == classes_cslb)
    {
        cslb_itemChanged(pitem);
    }
    else if(nCol == classes_xz)
    {
        sz_itemChanged(pitem);
    }

}


void CClassesWidget::on_delBtn_clicked()
{
    CMMDialog mm;
    if(mm.exec() != QDialog::Accepted)
    {
        return;
    }
    QString sFa;
    int nRow = -1;
    for(int x = 0 ;x < ui->tableWidget->rowCount(); ++x)
    {
        QCheckBox *pcb = (QCheckBox *)ui->tableWidget->cellWidget(x, classes_xz);
        if(pcb->checkState() == Qt::Checked)
        {
            nRow = x;
            sFa = ui->tableWidget->item(x, classes_cslb)->text();
            break;
        }
    }
    if(nRow == -1)
    {
        QMessageBox::about(this, "删除", "请先选择检测方案");
        return;
    }

    QString strtip = "删除方案：" + sFa + ", 是否继续？";
    int nRet = QMessageBox::information(this, "删除", strtip, "是", "否" );
    if(nRet != 0)
        return;

    QString strXh= ui->tableWidget->item(nRow, classes_xh)->text();
    delKeyCfg(strScuOutAutoCheckIni, "FA", strXh);

    ui->tableWidget->removeRow(nRow);
}


// 参数配置
void CClassesWidget::on_paramBtn_clicked()
{
    CMMDialog mm;
    if(mm.exec() != QDialog::Accepted)
    {
        return;
    }
    int nRow = -1;
    for (int m = 0; m < ui->tableWidget->rowCount(); ++m)
    {
        QCheckBox *pcb = (QCheckBox*)ui->tableWidget->cellWidget(m, classes_xz);
        if(pcb->checkState() == Qt::Checked)
        {
            nRow = m;
            break;
        }
    }
    if(nRow == -1)
    {
        QMessageBox::about(this, "参数配置", "请先选择方案");
        return;
    }

    quint64 nFAID = ui->tableWidget->item(nRow, classes_xh)->text().toULongLong();
    QString strName = ui->tableWidget->item(nRow, classes_cslb)->text();
    if(m_pParam == nullptr)
    {
        m_pParam = new CGntParamdialog(this);
    }
    m_pParam->initParam(strName, nFAID);
    m_pParam->showMaximized();
}

void CClassesWidget::on_scanBtn_clicked()
{
    bool bxz = false;
    for (int m = 0; m < ui->tableWidget->rowCount(); ++m )
    {
        QCheckBox *pCB = (QCheckBox*)ui->tableWidget->cellWidget(m, classes_xz);
        if(pCB->isChecked())
        {
            bxz = true;
        }
    }
    if(!bxz)
    {
        QMessageBox::about(this, "扫码", "请先选择方案");
        return;
    }
    emit(scanFlag());
}

void CClassesWidget::on_check_chg(int state)
{
    if(state == 0)
        return;
    QCheckBox *pcb = (QCheckBox*)sender();
    for (int m = 0; m < ui->tableWidget->rowCount(); ++m)
    {
        QCheckBox *pCB = (QCheckBox*)ui->tableWidget->cellWidget(m, classes_xz);
        if(pcb == pCB)
        {
            m_nFAID = ui->tableWidget->item(m, classes_xh)->text().toInt();
            continue;
        }
        pCB->setCheckState(Qt::Unchecked);

    }
    setCfg(strScuOutAutoCheckIni, "FA", "FAID", QString::number(m_nFAID));
}

void CClassesWidget::cslb_itemChanged(QTableWidgetItem *pitem)
{
    if(pitem->text().isEmpty())
        return;
    int nRow = pitem->row();

    QString s1 = ui->tableWidget->item(nRow, classes_xh)->text();
    QString s2 = pitem->text();

    for(int m = 0; m < ui->tableWidget->rowCount(); ++m)
    {
        QString ss = ui->tableWidget->item(m, classes_xh)->text();
        if(ss == s1)
        {
            continue;
        }
        QString ss2 = ui->tableWidget->item(m, classes_cslb)->text();
        if(ss2 == s2)
        {
            QMessageBox::about(this, "方案", "存在相同的方案，请重新输入");
            pitem->setText("");
            return;
        }
    }

    setCfg(strScuOutAutoCheckIni, "FA", s1, s2);

}

void CClassesWidget::sz_itemChanged(QTableWidgetItem *pitem)
{
    int nRow = pitem->row();
    if(ui->tableWidget->item(nRow, classes_xz)->checkState() != Qt::Checked)
    {
        return;
    }
    QTableWidgetItem *item = ui->tableWidget->item(nRow, classes_cslb);
    if(item == nullptr || item->text().isEmpty())
    {
        ui->tableWidget->item(nRow, classes_xz)->setCheckState(Qt::Unchecked);
        QMessageBox::about(this, "选择", "方案名称为空， 不能选择");
        return;
    }


    for (int m = 0; m < ui->tableWidget->rowCount(); ++m)
    {
        if(m == nRow)
            continue;
        QTableWidgetItem *pitem = ui->tableWidget->item(m, classes_xz);
        pitem->setCheckState(Qt::Unchecked);
    }

    setCfg(strScuOutAutoCheckIni, "FA", "FAID", ui->tableWidget->item(nRow, classes_xh)->text());
    m_nFAID = ui->tableWidget->item(nRow, classes_xh)->text().toInt();

}

void CClassesWidget::cpFA(QString strGroup1, QString strGroup2)
{
    std::map<QString, QString>mapStore;
    QSettings  settings(strScuOutAutoCheckIni, QSettings::IniFormat);
    settings.setIniCodec(QTextCodec::codecForName("UTF-8"));

    settings.beginGroup(strGroup1);
    QStringList sList = settings.allKeys();

    for (int m = 0; m < sList.size(); ++m)
    {
        mapStore[sList[m]] = settings.value(sList[m]).toString();
    }
    settings.endGroup();

    settings.beginGroup(strGroup2);

    auto it = mapStore.begin();

    while(it != mapStore.end())
    {
        settings.setValue(it->first, it->second);
        ++it;
    }
    settings.endGroup();
}

// 清空
void CClassesWidget::on_clearAllBtn_clicked()
{
    CMMDialog mm;
    if(mm.exec() != QDialog::Accepted)
    {
        return;
    }
    int nRet = QMessageBox::information(this, "清空", "该操作会将方案全部删除，是否继续", "是", "否");
    if(nRet != 0)
        return;

    ui->tableWidget->clearContents();
    ui->tableWidget->setRowCount(0);

    QSettings  settings(strScuOutAutoCheckIni, QSettings::IniFormat);
    settings.setIniCodec(QTextCodec::codecForName("UTF-8"));

    settings.beginGroup("FA");
    QStringList sList = settings.allKeys();

    for (int m = 0; m < sList.size(); ++m)
    {
        if(sList[m] == "FAXH")
            continue;
        settings.remove(sList[m]);
    }
    settings.endGroup();

}

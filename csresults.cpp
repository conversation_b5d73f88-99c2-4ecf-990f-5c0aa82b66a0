#include "csresults.h"
#include "ui_csresults.h"

csResults::csResults(QWidget *parent) :
    QWidget(parent),
    ui(new Ui::csResults)
{
    ui->setupUi(this);
    this->setWindowTitle("测试结果");

    //qtablewidget

    //大项测试项，小项测试项，测试结果，
    //传递参数：测试表位、ESN、大项、小项、结果
    //qmap<小项，结果>   QMap<大项，QMap<小项,结果>>


    ui->tableWidget->verticalHeader()->setVisible(false);
    ui->tableWidget->setColumnCount(3);

    QTableWidgetItem *pitem = new QTableWidgetItem();
    pitem->setText("测试大项");
    pitem->setSelected(true);
    ui->tableWidget->setHorizontalHeaderItem(0, pitem);

    pitem = new QTableWidgetItem();
    pitem->setText("测试小项");
    ui->tableWidget->setHorizontalHeaderItem(1, pitem);

    pitem = new QTableWidgetItem();
    pitem->setText("测试结果");
    ui->tableWidget->setHorizontalHeaderItem(2, pitem);

    ui->tableWidget->setColumnWidth(2,500);

    ui->tableWidget->setRowCount(60);

    ui->tableWidget->setEditTriggers(QAbstractItemView::NoEditTriggers);
    ui->tableWidget->setSelectionBehavior(QAbstractItemView::SelectRows);


}

csResults::~csResults()
{
    delete ui;
}

void csResults::CurrentTableRes(QStringList res)
{
    for (int row = 0; row < res.size(); row ++) {
        QStringList slist = res[row].split("@");
        for(int col = 0 ; col < slist.size(); col++)
        {
            ui->tableWidget->setItem(row,col,new QTableWidgetItem(slist[col]));
        }
    }
}



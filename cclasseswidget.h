﻿#ifndef CCLASSESWIDGET_H
#define CCLASSESWIDGET_H

#include <QWidget>
#include <QTableWidget>
#include "msk_global.h"
#include "cgntparamdialog.h"


namespace Ui {
class CClassesWidget;
}


enum classes
{
    classes_xz,
    classes_xh,
    classes_cslb,
    classes_max,
};

class CClassesWidget : public QWidget
{
    Q_OBJECT

public:
    explicit CClassesWidget(QWidget *parent = nullptr);
    ~CClassesWidget();

    void initClasses(quint64 nLbXh ,std::vector<sClasses> &);


private slots:
    void on_newBtn_clicked();       // 新增
    void on_table_itemChanged(QTableWidgetItem *);
    void on_delBtn_clicked();       // 删除

    void on_paramBtn_clicked();     // 参数配置

    void on_scanBtn_clicked();

    void on_check_chg(int);

    void on_clearAllBtn_clicked();      // 清空

private:
    void cslb_itemChanged(QTableWidgetItem *pitem);
    void sz_itemChanged(QTableWidgetItem *pitem);

    void cpFA(QString strGroup1, QString strGroup2);


Q_SIGNALS:
    void scanFlag();            // 扫码

private:
    quint64 m_nFAID;                    // 当前方案ID
    std::vector<sClasses>  m_vtClasses;  // 总的检测方案
    bool m_bChange;
private:
    CGntParamdialog   *m_pParam;
    Ui::CClassesWidget *ui;

};

#endif // CCLASSESWIDGET_H

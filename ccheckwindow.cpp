﻿#include "ccheckwindow.h"
#include "ui_ccheckwindow.h"
#include<QToolButton>
#include<QToolBar>
#include<QDir>

CCheckWindow::CCheckWindow(QWidget *parent) :
    QMainWindow(parent),
    ui(new Ui::CCheckWindow)
{
    ui->setupUi(this);
    this->setWindowTitle("SCU出厂检测软件—校表台");
    this->ToolBarinit();

    QDir dirtxz("E:/");
    dirtxz.mkdir("scu");

    m_pAbout = nullptr;
    m_serial = nullptr;
    m_log = nullptr;
    m_set = nullptr;
    m_portset = nullptr;
    m_bInfo = nullptr;
    m_hisInfo = nullptr;
}

CCheckWindow::~CCheckWindow()
{
    delete ui;
}
void CCheckWindow::ToolBarinit()
{
    QToolBar *toolBar = new QToolBar(this);


    toolBar->setFixedSize(QSize(this->maximumWidth(),65));

    toolBar->setMovable(false);     // 禁止移动
    toolBar->setIconSize(QSize(30, 30));        // 图标大小
    toolBar->setToolButtonStyle(Qt::ToolButtonTextUnderIcon);


    QPixmap pix("./icon/ckpz.ico");
    pix.scaled(30, 30);
    QAction *actCk = new QAction(QIcon(pix), "串口配置");
    connect(actCk, SIGNAL(triggered()), this, SLOT(on_actCk_trigger()));
    toolBar->addAction(actCk);

    toolBar->addSeparator();

    pix.load("./icon/gncs.ico");

    pix.scaled(50,50);
    QAction *actJl = new QAction(QIcon(pix), "方案配置");
    toolBar->addAction(actJl);
    connect(actJl, SIGNAL(triggered()), this, SLOT(on_actJl_trigger()));

    pix.load("./icon/jlcx.jpg");
    pix.scaled(50,50);
    QAction *actTest = new QAction(QIcon(pix), "  检测  ");
    toolBar->addAction(actTest);
    connect(actTest, SIGNAL(triggered()), this, SLOT(on_actTest_trigger()));
    toolBar->addSeparator();


    pix.load("./icon/jlcx.jpg");
    pix.scaled(50,50);
    QAction *actbInfo = new QAction(QIcon(pix), "  批次信息  ");
    toolBar->addAction(actbInfo);
    connect(actbInfo, SIGNAL(triggered()), this, SLOT(on_actbInfo_trigger()));
    toolBar->addSeparator();

    pix.load("./icon/jlcx.jpg");
    pix.scaled(50,50);
    QAction *actHisInfo = new QAction(QIcon(pix), "  历史信息  ");
    toolBar->addAction(actHisInfo);
    connect(actHisInfo, SIGNAL(triggered()), this, SLOT(on_actHisInfo_trigger()));
    toolBar->addSeparator();

    pix.load("./icon/gy.png");
    pix.scaled(50,50);
    QAction *actGy = new QAction(QIcon(pix), "  关于  ");
    toolBar->addAction(actGy);
    connect(actGy, SIGNAL(triggered()), this, SLOT(on_actGy_trigger()));
}

void CCheckWindow::on_actGy_trigger()
{
    if(m_pAbout == nullptr)
    {
        m_pAbout = new CAboutDialog(this);
    }
    m_pAbout->show();
}

void CCheckWindow::on_actCk_trigger()
{
    if(m_portset == nullptr)
    {
        m_portset = new cPortset(ui->stackedWidget);
        ui->stackedWidget->addWidget(m_portset);
    }
    ui->stackedWidget->setCurrentWidget(m_portset);
}
void CCheckWindow::on_actJl_trigger()
{
    if(m_set == nullptr)
    {
        m_set = new cSetConfigure();
    }
    m_set->show();

}
void CCheckWindow::on_actTest_trigger()
{
    if(m_serial == nullptr)
    {
        m_serial = new cserialWidget(ui->stackedWidget);
        ui->stackedWidget->addWidget(m_serial);
    }
    ui->stackedWidget->setCurrentWidget(m_serial);
}
void CCheckWindow::on_actbInfo_trigger()
{
    if(m_bInfo == nullptr)
    {
        m_bInfo = new cTestBatchInfo(ui->stackedWidget);
        ui->stackedWidget->addWidget(m_bInfo);
    }
    ui->stackedWidget->setCurrentWidget(m_bInfo);
}

void CCheckWindow::on_actHisInfo_trigger()
{
    if(m_hisInfo == nullptr)
    {
        m_hisInfo = new csHistoryInfo(ui->stackedWidget);
        ui->stackedWidget->addWidget(m_hisInfo);
    }
    ui->stackedWidget->setCurrentWidget(m_hisInfo);
}


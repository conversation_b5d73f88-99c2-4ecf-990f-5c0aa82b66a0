﻿#ifndef CSWDIALOG_H
#define CSWDIALOG_H

#include <QDialog>
#include "msk_global.h"

namespace Ui {
class CSWDialog;
}

class CSWDialog : public QDialog
{
    Q_OBJECT

public:
    explicit CSWDialog(QWidget *parent = nullptr);
    ~CSWDialog();
     void setSW(std::vector<int> &);
Q_SIGNALS:
     void okSig();

private slots:
    void on_ok_clicked();

private:
    Ui::CSWDialog *ui;
};

#endif // CSWDIALOG_H

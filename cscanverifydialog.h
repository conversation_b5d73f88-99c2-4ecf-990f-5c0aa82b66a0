﻿#ifndef CSCANVERIFYDIALOG_H
#define CSCANVERIFYDIALOG_H

#include "msk_global.h"
#include "cpcdialog.h"
#include <QDialog>
#include <QMessageBox>
#include <QKeyEvent>

enum SScanInfo
{
    SScanInfo_BWID,  // 表位ID
    SScanInfo_Cs,       // 参数
    SScanInfo_CsJg,     // 参数值
    SScanInfo_max,
};


enum EScanParam
{
    EScanParam_IP,          // ip
    EScanParam_Ljdz,        // 逻辑地址
    EScanParam_ZdLx,        // 终端类型
    EScanParam_Cs,          // 厂商
    EScanParam_Xh,          // 型号
    EScanParam_ID,          //ID
    EScanParam_Esn,         // Esn
    EScanParam_YjBb,        // 硬件版本
    EScanParam_Scrq,        // 生产日期
    EScanParam_Max
};

namespace Ui {
class CScanVerifyDialog;
}

class CScanVerifyDialog : public QDialog
{
    Q_OBJECT

public:
    explicit CScanVerifyDialog(QWidget *parent = nullptr);
    ~CScanVerifyDialog();
    void init(int nfaid, QString strname);

    void scanInfo(SComRead &);

protected:
    virtual void keyReleaseEvent(QKeyEvent *event) override;

private slots:
    void  on_pcBtn_clicked();       // 批次管理
    void on_refreshCfg(std::map<QString, QString>);           // 刷新

    void on_okBtn_clicked();

Q_SIGNALS:
    void scanInfoSig(std::map<int, SSCUParam>&);

private:

    void tableInit();
    void scanInfoBw(SComRead &);        // 表位数据处理
    void scanInfoIp(SComRead &);        // IP数据处理
    void scanInfoZdcs(SComRead &);      // 二维码信息

    void pcCb();

private:
    int m_nCurRow;       // 当前表位
    int m_nFAID;
    QString m_strfamc;      // 方案名称
    CPcDialog   *m_pc;      // 批次管理

    std::vector<SPC> m_vtPc;        // 方案下的批次
    QString m_strSm;
private:
    Ui::CScanVerifyDialog *ui;
};



#endif // CSCANVERIFYDIALOG_H

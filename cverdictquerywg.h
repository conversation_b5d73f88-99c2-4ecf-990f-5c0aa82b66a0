﻿#ifndef CVERDICTQUERYWG_H
#define CVERDICTQUERYWG_H

#include <QWidget>
#include "msk_global.h"
#include "cresultquery.h"
#include <QMessageBox>
#include <QTableWidget>
class CGntTestWidget;

namespace Ui {
class CVerdictQueryWg;
}



enum EDL
{
    EDL_RQ,
    EDL_FA,
    EDL_PC,
    EDL_ESN,
    EDL_DEVID,
    EDL_LJDZ,
    EDL_IP,
    EDL_JL,
    EDL_YHMMM,
    EDL_CLEARNR,
    EDL_XTBD,
    EDL_SCURQ,
    EDL_APP,
    EDL_ZDCS,
    EDL_MQTTIOT,
    EDL_PARAM698,
    EDL_EXPORT,
    EDL_IMPORT,
    EDL_ZDIP,
    EDL_SBDS,
    EDL_CKTX,
    EDL_YXFBL,
    EDL_YWGMMC,
    EDL_JCJL,
    EDL_LYTX,
    EDL_HPLC,
    EDL_SJQL,
    EDL_SW,
    EDL_HLXJ,
    EDL_CCZT,
    EDL_LXDYDL,
    EDL_DKJC,
    EDL_HBDY,
    EDL_OID,
    EDL_MAX
};

enum EXL
{
    EXL_OID,
    EXL_DL,
    EXL_XL,
    EXL_RES,
    EXL_MAX,
};

class CVerdictQueryWg : public QWidget
{
    Q_OBJECT

public:
    explicit CVerdictQueryWg(QWidget *parent = nullptr);
    ~CVerdictQueryWg();
    void init(CGntTestWidget*);
    void setJlcx(std::vector<SDLQuery> vtdL);
    void setXlcx(std::vector<SXLQuery> vtxl);
    void setGc(QString);
    bool isQuery();

private slots:
    void on_checkBox_toggled(bool checked);
    void on_fa_currentTextChanged(const QString &);
    void on_csDL_itemClicked(QTableWidgetItem *);
    void on_csXL_itemClicked(QTableWidgetItem *);

    void on_queryBtn_clicked();
private:
    void csDLTWInit();
    void csXLTWInit();

private:
    Ui::CVerdictQueryWg *ui;

    CResultQuery     m_resultQuery;

    CGntTestWidget  *m_pGntTest;
};

#endif // CVERDICTQUERYWG_H

﻿#ifndef CRESULTQUERY_H
#define CRESULTQUERY_H

#include <QSqlDatabase>
#include <QSqlQuery>
#include <QSqlError>
#include <QObject>
#include <qthread.h>
#include "msk_global.h"


struct SDLQueryParam
{
    QDateTime beginDT;
    QDateTime endDT;
    QString strFA;
    QString strPC;
    bool bID;
    QString strID;
};

class CResultQuery :public QThread
{
    Q_OBJECT
public:
    CResultQuery();
    virtual void run();
    void queryDl(SDLQueryParam &);
    void queryXl(quint64 oid);
    void queryGc(SGntJyXlGc &);
    bool isQuery();
private:
    void queryDLRes();
    void queryXlRes();
    void queryGcRes();
    QString translate(int);

private:
    bool m_bdlQuery;
    SDLQueryParam m_dlQuery;
    bool m_bxlQuery;
    quint64 m_noid;
    bool m_bgcQuery;
    SGntJyXlGc m_gcQuery;


};

#endif // CRESULTQUERY_H

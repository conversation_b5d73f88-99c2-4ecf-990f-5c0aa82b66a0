<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>CGntReportDialog</class>
 <widget class="QDialog" name="CGntReportDialog">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>1000</width>
    <height>120</height>
   </rect>
  </property>
  <property name="minimumSize">
   <size>
    <width>1000</width>
    <height>120</height>
   </size>
  </property>
  <property name="maximumSize">
   <size>
    <width>1000</width>
    <height>120</height>
   </size>
  </property>
  <property name="windowTitle">
   <string>生成报告</string>
  </property>
  <layout class="QGridLayout" name="gridLayout">
   <item row="0" column="0">
    <layout class="QHBoxLayout" name="horizontalLayout_2">
     <item>
      <widget class="QLabel" name="label">
       <property name="font">
        <font>
         <pointsize>12</pointsize>
        </font>
       </property>
       <property name="text">
        <string>目录</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QLineEdit" name="lineEdit">
       <property name="sizePolicy">
        <sizepolicy hsizetype="Expanding" vsizetype="Preferred">
         <horstretch>0</horstretch>
         <verstretch>0</verstretch>
        </sizepolicy>
       </property>
       <property name="font">
        <font>
         <pointsize>12</pointsize>
        </font>
       </property>
       <property name="readOnly">
        <bool>true</bool>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QPushButton" name="brownBtn">
       <property name="styleSheet">
        <string notr="true">    QPushButton {background-color:#0089ff ;
    color: white;
    border-style: outset;
    border-width: 2px;
    border-radius: 10px;
    border-color: beige;
    font-size: 26px;
    padding: 6px;}

QPushButton:hover {background-color: #0072C6;         
                     }
</string>
       </property>
       <property name="text">
        <string>浏览</string>
       </property>
      </widget>
     </item>
    </layout>
   </item>
   <item row="1" column="0">
    <layout class="QHBoxLayout" name="horizontalLayout">
     <item>
      <spacer name="horizontalSpacer">
       <property name="orientation">
        <enum>Qt::Horizontal</enum>
       </property>
       <property name="sizeHint" stdset="0">
        <size>
         <width>40</width>
         <height>20</height>
        </size>
       </property>
      </spacer>
     </item>
     <item>
      <widget class="QPushButton" name="okBtn">
       <property name="styleSheet">
        <string notr="true">    QPushButton {background-color:#0089ff ;
    color: white;
    border-style: outset;
    border-width: 2px;
    border-radius: 10px;
    border-color: beige;
    font-size: 26px;
    padding: 6px;}

QPushButton:hover {background-color: #0072C6;         
                     }
</string>
       </property>
       <property name="text">
        <string>确定</string>
       </property>
      </widget>
     </item>
    </layout>
   </item>
  </layout>
 </widget>
 <tabstops>
  <tabstop>okBtn</tabstop>
  <tabstop>brownBtn</tabstop>
  <tabstop>lineEdit</tabstop>
 </tabstops>
 <resources/>
 <connections/>
</ui>

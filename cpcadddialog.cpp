﻿#include "cpcadddialog.h"
#include "ui_cpcadddialog.h"
#include <QSettings>
#include <QTextCodec>
#include <QDebug>
CPcAddDialog::CPcAddDialog(QWidget *parent) :
    QDialog(parent),
    ui(new Ui::CPcAddDialog)
{
    ui->setupUi(this);
}

CPcAddDialog::~CPcAddDialog()
{
    delete ui;
}

void CPcAddDialog::setFaId(int faid)
{
    m_nFaId = faid;
}

void CPcAddDialog::setPcList(QStringList strPcList)
{
    m_strListPcName = strPcList;
}

QString CPcAddDialog::getPcName()
{
    return ui->lineEdit->text();
}

// 确定
void CPcAddDialog::on_pushButton_clicked()
{
    QString s = ui->lineEdit->text();
    if(s.isEmpty())
    {
        QMessageBox::about(this, "批次添加", "批次名称不能为空");
        return;
    }

    QSettings  settings(strScuOutAutoCheckIni, QSettings::IniFormat);
    settings.setIniCodec(QTextCodec::codecForName("UTF-8"));

    settings.beginGroup("PC_" + QString::number(m_nFaId));
    QStringList sList = settings.allKeys();

    for (int m = 0; m < sList.size(); ++m)
    {
        if(settings.value(sList[m]).toString() == s)
        {
            QMessageBox::about(this, "批次添加", "存在相同名称的批次：" + s);
            settings.endGroup();
            return;
        }
    }

    for (int m = 0; m < m_strListPcName.size(); ++m)
    {
        if(m_strListPcName[m] == s)
        {
            QMessageBox::about(this, "批次添加", "存在相同名称的批次：" + s);
            return;;
        }
    }
    settings.endGroup();
    accept();
}

﻿#include "cgntscuinfodialog.h"
#include "ui_cgntscuinfodialog.h"

CGntScuInfoDialog::CGntScuInfoDialog(QWidget *parent) :
    QDialog(parent),
    ui(new Ui::CGntScuInfoDialog)
{
    ui->setupUi(this);
    tableInit();
}

CGntScuInfoDialog::~CGntScuInfoDialog()
{
    delete ui;
}

void CGntScuInfoDialog::init(SSCUParam &scuParam)
{
    ui->tableWidget->clearContents();

    QTableWidgetItem *tem = new QTableWidgetItem();
    tem->setText(QString::number(scuParam.nBw));
    ui->tableWidget->setItem(0, EGntScuDetail_ID, tem);

    for (int m = 0; m < ui->tableWidget->rowCount(); ++m)
    {
        QTableWidgetItem *pitem = new QTableWidgetItem();
        QTableWidgetItem *item = new QTableWidgetItem();
        switch (m)
        {
        case 0:
            pitem->setText("IP");
            item->setText(scuParam.strIp);
            break;
        case 1:
            pitem->setText("逻辑地址");
            item->setText(scuParam.strLjdz);
            break;
        case 2:
            pitem->setText("终端类型");
            item->setText(scuParam.strZdLx);
            break;
        case 3:
            pitem->setText("厂商");
            item->setText(scuParam.strCs);
            break;
        case 4:
            pitem->setText("型号");
            item->setText(scuParam.strXh);
            break;
        case 5:
            pitem->setText("ID");
            item->setText(scuParam.strID);
            break;
        case 6:
            pitem->setText("ESN");
            item->setText(scuParam.strEsn);
            break;
        case 7:
            pitem->setText("硬件版本");
            item->setText(scuParam.strYjBb);
            break;
        case 8:
            pitem->setText("生产日期");
            item->setText(scuParam.strScrq);
            break;
        case 9:
            pitem->setText("方案");
            item->setText(scuParam.strFA);
            break;
        case 10:
            pitem->setText("批次");
            item->setText(scuParam.strPC);
            break;
        default:
            break;
        }
        ui->tableWidget->setItem(m, EGntScuDetail_ParamName, pitem);
        ui->tableWidget->setItem(m, EGntScuDetail_Param, item);
    }
    ui->tableWidget->resizeColumnsToContents();
}

void CGntScuInfoDialog::tableInit()
{
    ui->tableWidget->setRowCount(11);
    ui->tableWidget->setColumnCount(EGntScuDetail_MAX);
    ui->tableWidget->setAlternatingRowColors(true);

    QTableWidgetItem *pitem = new QTableWidgetItem();
    pitem->setText("表位");
    ui->tableWidget->setHorizontalHeaderItem(EGntScuDetail_ID, pitem);

    pitem = new QTableWidgetItem();
    pitem->setText("参数");
    ui->tableWidget->setHorizontalHeaderItem(EGntScuDetail_ParamName, pitem);

    pitem = new QTableWidgetItem();
    pitem->setText("参数值");
    ui->tableWidget->setHorizontalHeaderItem(EGntScuDetail_Param, pitem);

    ui->tableWidget->setSpan(0, EGntScuDetail_ID, 11, 1);
}

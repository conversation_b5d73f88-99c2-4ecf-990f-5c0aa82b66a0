<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>CPcDialog</class>
 <widget class="QDialog" name="CPcDialog">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>982</width>
    <height>646</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>批次管理</string>
  </property>
  <layout class="QGridLayout" name="gridLayout">
   <item row="0" column="0">
    <layout class="QHBoxLayout" name="horizontalLayout">
     <item>
      <widget class="QTableWidget" name="tableWidget"/>
     </item>
     <item>
      <layout class="QVBoxLayout" name="verticalLayout">
       <item>
        <widget class="QPushButton" name="addBtn">
         <property name="styleSheet">
          <string notr="true">    QPushButton {background-color:#0089ff ;
    color: white;
    border-style: outset;
    border-width: 2px;
    border-radius: 10px;
    border-color: beige;
    font-size: 26px;
    padding: 6px;}

QPushButton:hover {background-color: #0072C6;         
                     }
</string>
         </property>
         <property name="text">
          <string>添加</string>
         </property>
        </widget>
       </item>
       <item>
        <widget class="QPushButton" name="delBtn">
         <property name="styleSheet">
          <string notr="true">    QPushButton {background-color:#0089ff ;
    color: white;
    border-style: outset;
    border-width: 2px;
    border-radius: 10px;
    border-color: beige;
    font-size: 26px;
    padding: 6px;}

QPushButton:hover {background-color: #0072C6;         
                     }
</string>
         </property>
         <property name="text">
          <string>删除</string>
         </property>
        </widget>
       </item>
       <item>
        <widget class="QPushButton" name="okBtn">
         <property name="styleSheet">
          <string notr="true">    QPushButton {background-color:#0089ff ;
    color: white;
    border-style: outset;
    border-width: 2px;
    border-radius: 10px;
    border-color: beige;
    font-size: 26px;
    padding: 6px;}

QPushButton:hover {background-color: #0072C6;         
                     }
</string>
         </property>
         <property name="text">
          <string>保存</string>
         </property>
        </widget>
       </item>
       <item>
        <spacer name="verticalSpacer_2">
         <property name="orientation">
          <enum>Qt::Vertical</enum>
         </property>
         <property name="sizeHint" stdset="0">
          <size>
           <width>20</width>
           <height>40</height>
          </size>
         </property>
        </spacer>
       </item>
      </layout>
     </item>
    </layout>
   </item>
  </layout>
 </widget>
 <resources/>
 <connections/>
</ui>

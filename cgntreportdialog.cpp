﻿#include "cgntreportdialog.h"
#include "ui_cgntreportdialog.h"
#include <QSettings>
#include <QFileDialog>
#include <QMessageBox>
#include "msk_global.h"

CGntReportDialog::CGntReportDialog(QWidget *parent) :
    QDialog(parent),
    ui(new Ui::CGntReportDialog)
{
    ui->setupUi(this);   

    ui->lineEdit->setText(queryCfg(strScuOutAutoCheckIni, "report", "reportFile"));
}

CGntReportDialog::~CGntReportDialog()
{
    delete ui;
}

// 确定
void CGntReportDialog::on_okBtn_clicked()
{
    QString strReportFile= ui->lineEdit->text();
    if(strReportFile.isEmpty())
    {
        QMessageBox::about(this, "生成报告", "文件路径不能为空");
        return;
    }
    QDir dir;
    if(!dir.exists(strReportFile))
    {
        QMessageBox::about(this, "生成报告", "文件路径不存在");
        return;
    }

    hide();
    accept();
}

// 浏览
void CGntReportDialog::on_brownBtn_clicked()
{
    QFileDialog fileDialog;
    QString folderPath = QFileDialog::getExistingDirectory(nullptr, QObject::tr("Open Directory"),
                                                           ui->lineEdit->text().isEmpty() ? QDir::homePath():ui->lineEdit->text() );
    if(folderPath.isEmpty())
    {
        return;
    }
    ui->lineEdit->setText(folderPath);

    setCfg(strScuOutAutoCheckIni, "report", "reportFile", folderPath);
}

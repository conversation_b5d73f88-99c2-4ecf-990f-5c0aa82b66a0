#ifndef CPORTSET_H
#define CPORTSET_H

#include <QWidget>
#include<QSerialPort>

namespace Ui {
class cPortset;
}

class cPortset : public QWidget
{
    Q_OBJECT

public:
    explicit cPortset(QWidget *parent = nullptr);
    ~cPortset();


public slots:

signals:
    void signserialportRead(int num,QSerialPort* port);



private slots:
    void on_SetPortBtn_clicked();

    void on_OKBtn_clicked();

private:
    Ui::cPortset *ui;

    void portconnect(QString s);
    void portInfo();
    QVector<QSerialPort*> Ports;

};

#endif // CPORTSET_H

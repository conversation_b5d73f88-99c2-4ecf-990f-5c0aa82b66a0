<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>cserialWidget</class>
 <widget class="QWidget" name="cserialWidget">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>1064</width>
    <height>541</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Form</string>
  </property>
  <layout class="QGridLayout" name="gridLayout" columnstretch="7,4">
   <item row="0" column="0" colspan="2">
    <spacer name="horizontalSpacer_2">
     <property name="orientation">
      <enum>Qt::Horizontal</enum>
     </property>
     <property name="sizeHint" stdset="0">
      <size>
       <width>40</width>
       <height>20</height>
      </size>
     </property>
    </spacer>
   </item>
   <item row="1" column="0" colspan="2">
    <spacer name="horizontalSpacer">
     <property name="orientation">
      <enum>Qt::Horizontal</enum>
     </property>
     <property name="sizeHint" stdset="0">
      <size>
       <width>40</width>
       <height>20</height>
      </size>
     </property>
    </spacer>
   </item>
   <item row="2" column="0" colspan="2">
    <layout class="QHBoxLayout" name="horizontalLayout_2">
     <item>
      <widget class="QCheckBox" name="checkBox">
       <property name="font">
        <font>
         <family>宋体</family>
         <pointsize>10</pointsize>
         <weight>50</weight>
         <italic>false</italic>
         <bold>false</bold>
        </font>
       </property>
       <property name="styleSheet">
        <string notr="true">font: 10pt &quot;宋体&quot;;</string>
       </property>
       <property name="text">
        <string>全选</string>
       </property>
       <property name="checked">
        <bool>false</bool>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QCheckBox" name="checkBox_2">
       <property name="text">
        <string>1-20</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QCheckBox" name="checkBox_3">
       <property name="text">
        <string>21-40</string>
       </property>
      </widget>
     </item>
     <item>
      <spacer name="horizontalSpacer_4">
       <property name="orientation">
        <enum>Qt::Horizontal</enum>
       </property>
       <property name="sizeHint" stdset="0">
        <size>
         <width>40</width>
         <height>20</height>
        </size>
       </property>
      </spacer>
     </item>
     <item>
      <layout class="QVBoxLayout" name="verticalLayout_2">
       <item>
        <widget class="QLabel" name="label_2">
         <property name="text">
          <string>A相电流： ，B相电流： ，C相电流：</string>
         </property>
        </widget>
       </item>
       <item>
        <widget class="QLabel" name="label_3">
         <property name="text">
          <string>A相电压： ，B相电压： ，C相电压：</string>
         </property>
        </widget>
       </item>
      </layout>
     </item>
     <item>
      <spacer name="horizontalSpacer_3">
       <property name="orientation">
        <enum>Qt::Horizontal</enum>
       </property>
       <property name="sizeHint" stdset="0">
        <size>
         <width>40</width>
         <height>20</height>
        </size>
       </property>
      </spacer>
     </item>
     <item>
      <widget class="QPushButton" name="pushButton">
       <property name="styleSheet">
        <string notr="true">    background-color: #0072C6;
    color: white;
    border-style: outset;
    border-width: 2px;
    border-radius: 10px;
    border-color: beige;
    font-size: 26px;
    padding: 8px;</string>
       </property>
       <property name="text">
        <string>  上  电  </string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QPushButton" name="pushButton_5">
       <property name="styleSheet">
        <string notr="true">    background-color: #0072C6;
    color: white;
    border-style: outset;
    border-width: 2px;
    border-radius: 10px;
    border-color: beige;
    font-size: 26px;
    padding: 8px;</string>
       </property>
       <property name="text">
        <string>  断  电  </string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QPushButton" name="pcBtn">
       <property name="styleSheet">
        <string notr="true">    background-color: #0072C6;
    color: white;
    border-style: outset;
    border-width: 2px;
    border-radius: 10px;
    border-color: beige;
    font-size: 26px;
    padding: 8px;</string>
       </property>
       <property name="text">
        <string>  检测批次  </string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QPushButton" name="pushButton_3">
       <property name="styleSheet">
        <string notr="true">    background-color: #0072C6;
    color: white;
    border-style: outset;
    border-width: 2px;
    border-radius: 10px;
    border-color: beige;
    font-size: 26px;
    padding: 8px;</string>
       </property>
       <property name="text">
        <string>  开始测试  </string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QPushButton" name="EndTestBtn">
       <property name="styleSheet">
        <string notr="true">    background-color: #0072C6;
    color: white;
    border-style: outset;
    border-width: 2px;
    border-radius: 10px;
    border-color: beige;
    font-size: 26px;
    padding: 8px;</string>
       </property>
       <property name="text">
        <string>  结束测试  </string>
       </property>
      </widget>
     </item>
    </layout>
   </item>
   <item row="3" column="0">
    <widget class="QTableWidget" name="tableWidget"/>
   </item>
   <item row="3" column="1">
    <widget class="QStackedWidget" name="stackedWidget">
     <property name="currentIndex">
      <number>1</number>
     </property>
     <widget class="QWidget" name="page"/>
     <widget class="QWidget" name="page_2"/>
    </widget>
   </item>
  </layout>
 </widget>
 <resources/>
 <connections/>
</ui>

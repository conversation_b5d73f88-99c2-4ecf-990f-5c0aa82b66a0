﻿#ifndef CGNTSCUINFODIALOG_H
#define CGNTSCUINFODIALOG_H

#include <QDialog>
#include <msk_global.h>

namespace Ui {
class CGntScuInfoDialog;
}

enum EGntScuDetail
{
    EGntScuDetail_ID,
    EGntScuDetail_ParamName,
    EGntScuDetail_Param,
    EGntScuDetail_MAX
};


class CGntScuInfoDialog : public QDialog
{
    Q_OBJECT

public:
    explicit CGntScuInfoDialog(QWidget *parent = nullptr);
    ~CGntScuInfoDialog();

    void init(SSCUParam &);
private:
    void tableInit();

private:
    Ui::CGntScuInfoDialog *ui;
};

#endif // CGNTSCUINFODIALOG_H

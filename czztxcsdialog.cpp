﻿#include "czztxcsdialog.h"
#include "ui_czztxcsdialog.h"
#include <QMessageBox>
CZztxcsDialog::CZztxcsDialog(QWidget *parent) :
    QDialog(parent),
    ui(new Ui::CZztxcsDialog)
{
    ui->setupUi(this);
}

CZztxcsDialog::~CZztxcsDialog()
{
    delete ui;
}

void CZztxcsDialog::setKeyName(QString strName)
{
    m_strKeyName = strName;
    if(m_strKeyName.startsWith("gw"))
    {
        setWindowTitle("公网主站通信参数配置");
    }
    else
    {
        setWindowTitle("以太网主站通信参数配置");
    }

    QSettings  settings(strScuOutAutoCheckIni, QSettings::IniFormat);
    settings.setIniCodec(QTextCodec::codecForName("UTF-8"));

    settings.beginGroup(m_strKeyName);
    ui->zyipLE->setText(settings.value("zyip").toString());
    ui->zydkLE->setText(settings.value("zydk").toString());
    ui->byipLE->setText(settings.value("byip").toString());
    ui->bydkLE->setText(settings.value("bydk").toString());
    ui->checkBox->setChecked(settings.value("zy").toBool());
    ui->checkBox_2->setChecked(settings.value("by").toBool());
    settings.endGroup();
}



void CZztxcsDialog::on_checkBox_toggled(bool checked)
{
    ui->zyipLE->setEnabled(checked);
    ui->zydkLE->setEnabled(checked);
    if(checked)
    {
       setCfg(strScuOutAutoCheckIni, m_strKeyName, "zy", "1");
    }
    else
    {
        setCfg(strScuOutAutoCheckIni, m_strKeyName, "zy", "0");
    }
}

void CZztxcsDialog::on_checkBox_2_toggled(bool checked)
{
    ui->byipLE->setEnabled(checked);
    ui->bydkLE->setEnabled(checked);
    if(checked)
    {
       setCfg(strScuOutAutoCheckIni, m_strKeyName, "by", "1");
    }
    else
    {
        setCfg(strScuOutAutoCheckIni, m_strKeyName, "by", "0");
    }
}

void CZztxcsDialog::on_ok_clicked()
{
    if(ui->checkBox_2->isChecked() && !ui->checkBox->isChecked())
    {
        QMessageBox::about(this, "确定", "请先配置主用地址");
        return;
    }
    setCfg(strScuOutAutoCheckIni, m_strKeyName, "zyip", ui->zyipLE->text());
    setCfg(strScuOutAutoCheckIni, m_strKeyName, "zydk", ui->zydkLE->text().toInt());
    setCfg(strScuOutAutoCheckIni, m_strKeyName, "byip", ui->byipLE->text());
    setCfg(strScuOutAutoCheckIni, m_strKeyName, "bydk", ui->bydkLE->text().toInt());
}

﻿#ifndef CGNTREPORTDIALOG_H
#define CGNTREPORTDIALOG_H

#include <QDialog>

namespace Ui {
class CGntReportDialog;
}

class CGntReportDialog : public QDialog
{
    Q_OBJECT

public:
    explicit CGntReportDialog(QWidget *parent = nullptr);
    ~CGntReportDialog();

private slots:
    void on_okBtn_clicked();

    void on_brownBtn_clicked();

private:
    Ui::CGntReportDialog *ui;
};

#endif // CGNTREPORTDIALOG_H

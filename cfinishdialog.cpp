﻿#include "cfinishdialog.h"
#include "ui_cfinishdialog.h"

CFinishDialog::CFinishDialog(QWidget *parent) :
    QDialog(parent),
    ui(new Ui::CFinishDialog)
{
    ui->setupUi(this);


    // 设置整个表格不可编辑
    ui->resTable->setEditTriggers(QTableWidget::NoEditTriggers);
    ui->resTable->setSelectionBehavior(QAbstractItemView::SelectRows);
    ui->resTable->setSelectionMode(QAbstractItemView::SingleSelection);
    ui->resTable->setAlternatingRowColors(true);


    ui->resTable->setColumnCount(E_ITEM_MAX);

    QTableWidgetItem *pitem = new QTableWidgetItem();
    pitem->setText("测试大项");
    ui->resTable->setHorizontalHeaderItem(E_ITEM_DX, pitem);

    pitem = new QTableWidgetItem();
    pitem->setText("测试小项");
    ui->resTable->setHorizontalHeaderItem(E_ITEM_XX, pitem);

    pitem = new QTableWidgetItem();
    pitem->setText("结论");
    ui->resTable->setHorizontalHeaderItem(E_ITEM_JL, pitem);


}

CFinishDialog::~CFinishDialog()
{
    delete ui;
}

void CFinishDialog::init(std::map<int, std::map<QString, SGntJyParam> > &mapGntJyParam)
{
    ui->resTable->clearContents();
    ui->resTable->setRowCount(0);
    // 查找有多少个测试小项， 有多少个测试小项就有多少行
    if(mapGntJyParam.size() == 0)
    {
        return;
    }

    int rows = 0;
    auto iter = mapGntJyParam.begin();
    std::map<QString, SGntJyParam> &mapGnt = iter->second;
    auto it = mapGnt.begin();
    for (; it != mapGnt.end(); ++it)
    {
        rows += it->second.gntCsParam.size();
    }

    ui->resTable->setRowCount(rows);

   int nrow = 0 ;
   // 大项小项填充到表格中
   it = mapGnt.begin();
   for (; it != mapGnt.end(); ++it)
   {
        std::vector<SGntJyXlParam> &vtgntparam = it->second.gntCsParam;
        for (int m = 0; m < vtgntparam.size(); ++m)
        {
            QTableWidgetItem *pitem = new QTableWidgetItem();
            pitem->setText(it->first);
            ui->resTable->setItem(nrow, E_ITEM_DX, pitem);

            pitem = new QTableWidgetItem();
            pitem->setText(vtgntparam[m].strCsXl);
            ui->resTable->setItem(nrow, E_ITEM_XX, pitem);

            ++nrow;
        }
   }

   // 填充结论

   rows = ui->resTable->rowCount();
   for (int m = 0; m < rows; ++m)
   {
        QString sDX = ui->resTable->item(m, E_ITEM_DX)->text();
        QString sXX = ui->resTable->item(m, E_ITEM_XX)->text();

        iter = mapGntJyParam.begin();

        QString strResult;

        for (; iter != mapGntJyParam.end(); ++iter)
        {
              std::map<QString, SGntJyParam> &mapJy = iter->second;
              auto tor = mapJy.begin();
              for ( ;tor != mapJy.end() ; ++tor)
              {
                  if(tor->first != sDX)
                  {
                      continue;
                  }
                  std::vector<SGntJyXlParam> &vtGntParam = tor->second.gntCsParam;
                  for (int m = 0; m < vtGntParam.size(); ++m)
                  {
                      if(vtGntParam[m].strCsXl != sXX)
                      {
                          continue;
                      }
                      if((int)vtGntParam[m].bCsXlJg != 1)
                      {
                          strResult += "表位：" + QString::number(iter->first) + ",";
                      }
                      break;
                  }
                  break;
              }
        }
        QTableWidgetItem *pitem = new QTableWidgetItem();
        if(strResult.isEmpty())
        {
           pitem->setText("全部合格");
        }
        else
        {
            pitem->setText(strResult.left(strResult.size()-1));
            pitem->setBackgroundColor(QColor(230, 27, 35));
        }
        ui->resTable->setItem(m, E_ITEM_JL, pitem);
   }

   ui->resTable->resizeColumnsToContents();




}


// 确定
void CFinishDialog::on_pushButton_clicked()
{
    hide();
}

﻿#include "chomedialog.h"
#include "ui_chomedialog.h"
#include "msk_global.h"
CHomeDialog::CHomeDialog(QWidget *parent) :
    QDialog(parent),
    ui(new Ui::CHomeDialog)
{
    ui->setupUi(this);
    QString s = queryCfg(strScuOutAutoCheckIni, "homedialog", "gnt");
    QString ss = queryCfg(strScuOutAutoCheckIni, "homedialog", "jbt");

    if(s == "1")
    {
        ui->radioButton->setChecked(true);
    }
    else if(ss == "1")
    {
        ui->radioButton_2->setChecked(true);
    }


}

CHomeDialog::~CHomeDialog()
{
    delete ui;
}

quint16 CHomeDialog::getTtType()
{
    if(ui->radioButton->isChecked())
        return 1;
    else if(ui->radioButton_2->isChecked())
        return 2;
    return 0;
}

void CHomeDialog::on_pushButton_clicked()
{
    if(ui->radioButton->isChecked())
    {
        setCfg(strScuOutAutoCheckIni, "homedialog", "gnt", "1");
        setCfg(strScuOutAutoCheckIni, "homedialog", "jbt", "0");
    }
    else if(ui->radioButton_2->isChecked())
    {
        setCfg(strScuOutAutoCheckIni, "homedialog", "gnt", "0");
        setCfg(strScuOutAutoCheckIni, "homedialog", "jbt", "1");
    }
    accept();
}

#include "cportset.h"
#include "ui_cportset.h"
#include<QSerialPort>
#include<QDebug>
#include<QMessageBox>
#include"msk_global.h"

cPortset::cPortset(QWidget *parent) :
    QWidget(parent),
    ui(new Ui::cPortset)
{
    ui->setupUi(this);

    QString s = queryCfg(CheckItemIni,"PortSet","COM");
    if(s!="")
    {
        QStringList slist = s.split("@");
        ui->XTEdit->setText(slist[0]);
        ui->WcEdit->setText(slist[1]);
        ui->BzbBtn->setText(slist[2]);
    }


    ui->tableWidget->setColumnCount(3);
    ui->tableWidget->setRowCount(40);

    QTableWidgetItem *pitem = new QTableWidgetItem();
    pitem->setText("表位号");
    pitem->setSelected(true);
    ui->tableWidget->setHorizontalHeaderItem(0, pitem);

    pitem = new QTableWidgetItem();
    pitem->setText("串口号");
    ui->tableWidget->setHorizontalHeaderItem(1, pitem);

    pitem = new QTableWidgetItem();
    pitem->setText("IP地址");
    ui->tableWidget->setHorizontalHeaderItem(2, pitem);


    for(int row = 0; row<40;row++)
    {
        ui->tableWidget->setItem(row,0,new QTableWidgetItem(QString("表位%1").arg(row+1)));
         //ui->tableWidget->setItem(row,1,new QTableWidgetItem(QString("COM%1").arg(row+50)));
         QString ipadress = QString("192.168.127.%1").arg(101+row);
         ui->tableWidget->setItem(row,2,new QTableWidgetItem(ipadress));
         QString s = queryCfg(CheckItemIni,"COMLIST",QString::number(row+1));
         if(s!="")
         {
             ui->tableWidget->setItem(row,1,new QTableWidgetItem(s));
         }
    }


    connect(ui->tableWidget,&QTableWidget::itemChanged,[this](QTableWidgetItem *item){
        if (item->text() == "串口未连接")
        {
            for (int col = 0; col < ui->tableWidget->columnCount(); ++col)
            {
                QTableWidgetItem *cellItem = ui->tableWidget->item(item->row(), col);
                if (cellItem)
                {
                    cellItem->setBackgroundColor(Qt::gray);
                }

            }
        }
        else if(item->text() == "失败")
        {
            for (int col = 0; col < ui->tableWidget->columnCount(); ++col) {
                QTableWidgetItem *cellItem = ui->tableWidget->item(item->row(), col);
                if (cellItem) {
                    cellItem->setBackgroundColor(Qt::red);
                }
            }
        }
    });
}

cPortset::~cPortset()
{
    delete ui;
}

void cPortset::on_SetPortBtn_clicked()
{
    Ports.clear();
    QTableWidgetItem *item = ui->tableWidget->item(0,1);
    if (item) {
        QString text = item->text();
        if (!text.isEmpty()) {
            QString s = ui->tableWidget->item(0,1)->text();
            int num = s.mid(3).toInt();
            for(int row = 0;row < 40;row++)
            {
                QTableWidgetItem *item1 = new QTableWidgetItem();
                item1->setText(QString("COM%1").arg(num+row));
                ui->tableWidget->setItem(row,1,item1);
                QString s=QString::number(item1->row()+1);
                setCfg(CheckItemIni,"COMLIST",s,item1->text());
            }
        } else {
            QMessageBox::critical(nullptr,"提示","输入的内容为空");
        }
    } else {
        QMessageBox::critical(nullptr,"提示","请输入开始的串口号");
        return;
    }

}


void cPortset::on_OKBtn_clicked()
{
    QString strXt = ui->XTEdit->text();
    QString strWc = ui->WcEdit->text();
    QString strBzb = ui->BzbBtn->text();

    QString s = strXt +"@"+strWc+"@"+strBzb;
    setCfg(CheckItemIni,"PortSet","COM",s);
}

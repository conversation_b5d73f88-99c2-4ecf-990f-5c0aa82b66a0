﻿#include "ctsddialog.h"
#include "ui_ctsddialog.h"
#include <QDebug>
CTsdDialog::CTsdDialog(QWidget *parent) :
    QDialog(parent),
    ui(new Ui::CTsdDialog)
{
    ui->setupUi(this);
    ui->tableWidget->resizeColumnsToContents();
    ui->tableWidget->verticalHeader()->setVisible(false);

    for (int m = 1;  m < 17; ++m)
    {
        ui->zjsCB->addItem(QString::number(m));
    }

}

CTsdDialog::~CTsdDialog()
{
    delete ui;
}

void CTsdDialog::setKeyName(QString strKeyName)
{
    m_strKeyName = strKeyName;
    QSettings  settings(strScuOutAutoCheckIni, QSettings::IniFormat);
    settings.setIniCodec(QTextCodec::codecForName("UTF-8"));

    settings.beginGroup(m_strKeyName);
    if(settings.value("cjbz").toBool())
        ui->yxCB->setChecked(true);
    else
        ui->wxCB->setChecked(true);

    ui->cldCB->setCurrentText(settings.value("cjcld").toString());
    ui->cdsjjgLE->setText(settings.value("cdsjjg").toString());
    ui->cdsjxzLE->setText(settings.value("cdsjxz").toString());
    QString s = settings.value("dqtdsjdnb").toString();
    QStringList sList = s.split("@");
    qDebug() << "sList" << sList.size();
    ui->tableWidget->clearContents();
    ui->tableWidget->setRowCount(sList.size());
    bool bValid = false;
    for (int m = 0; m < sList.size(); ++m)
    {
        if(sList[m].isEmpty())
            continue;
        bValid = true;
        QTableWidgetItem *pitem = new QTableWidgetItem();
        pitem->setText(QString::number(m+1));
        ui->tableWidget->setItem(m, 0, pitem);

        pitem = new QTableWidgetItem();
        pitem->setText(sList[m]);
        ui->tableWidget->setItem(m, 1, pitem);
    }
    if(!bValid)
        ui->tableWidget->setRowCount(0);
    ui->tdzxyxjgLE->setText(settings.value("tdsjzxyxjg").toString());
    ui->tdzdyxjgLE->setText(settings.value("tdsjzdyxjg").toString());
    ui->tdqzpcxzLE->setText(settings.value("tdqzsjpcxz").toString());
    ui->tdqdpcxzLE->setText(settings.value("tdsjqdpcxz").toString());
    ui->tddydzLE->setText(settings.value("tdfsdyxz").toString());
    ui->tdhfdyszLE->setText(settings.value("tdhfdyxz").toString());
    settings.endGroup();

}

void CTsdDialog::on_addBtn_clicked()
{
    QString s = ui->dzLE->text();
    if(s.isEmpty())
        return;
    int nRow = ui->tableWidget->rowCount();
    ui->tableWidget->setRowCount(nRow+1);

    QTableWidgetItem *pitem = new QTableWidgetItem();
    pitem->setText(QString::number(nRow +1));
    ui->tableWidget->setItem(nRow, 0, pitem);

    pitem = new QTableWidgetItem();

    while (s.size() < ui->zjsCB->currentText().toInt() *2)
    {
        s = "0" + s;
    }
    pitem->setText(s);
    ui->tableWidget->setItem(nRow, 1, pitem);

    ui->dzLE->clear();
}

void CTsdDialog::on_delBtn_clicked()
{
    int nRow = ui->tableWidget->currentRow();
    if(nRow == -1)
    {
        return;
    }
    ui->tableWidget->removeRow(nRow);
}

void CTsdDialog::on_clearBtn_clicked()
{
    ui->tableWidget->clearContents();
    ui->tableWidget->setRowCount(0);
}

void CTsdDialog::on_okBtn_clicked()
{
    QSettings  settings(strScuOutAutoCheckIni, QSettings::IniFormat);
    settings.setIniCodec(QTextCodec::codecForName("UTF-8"));

    settings.beginGroup(m_strKeyName);
    if(ui->yxCB->isChecked())
        settings.setValue("cjbz", 1);
    else if(ui->wxCB->isChecked())
        settings.setValue("cjbz", 0);

    settings.setValue("cjcld", ui->cldCB->currentText());
    settings.setValue("cdsjjg", ui->cdsjjgLE->text());
    settings.setValue("cdsjxz", ui->cdsjxzLE->text());
    QString s;
    for (int m = 0; m < ui->tableWidget->rowCount(); ++m)
    {
        s += ui->tableWidget->item(m, 1)->text();
        s += "@";
    }
    QString s2 = s.left(s.size()-1);
    settings.setValue("dqtdsjdnb", s2);

    settings.setValue("tdsjzxyxjg", ui->tdzxyxjgLE->text());
    settings.setValue("tdsjzdyxjg", ui->tdzdyxjgLE->text());
    settings.setValue("tdqzsjpcxz", ui->tdqzpcxzLE->text());
    settings.setValue("tdsjqdpcxz", ui->tdqdpcxzLE->text());
    settings.setValue("tdfsdyxz", ui->tddydzLE->text());
    settings.setValue("tdhfdyxz", ui->tdhfdyszLE->text());
    settings.endGroup();
}

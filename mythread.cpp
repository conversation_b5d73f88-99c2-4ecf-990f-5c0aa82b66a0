#include "mythread.h"
#include<QThread>

MyThread::MyThread(QObject *parent) : QThread(parent)
{


}
void MyThread::run()
{
//    qDebug() << "当前线程对象的地址: " << QThread::currentThread();

//    m_test = new cTestLog();
//    m_test ->onSlotNUM(m_num);
//    connect(m_test,&cTestLog::signtestlog,this,&MyThread::onslotsthreadRevLog,Qt::QueuedConnection);


//    exec();
}
void MyThread::onslotsRevNum(int i)
{
    m_num = i;
}
void MyThread::onslotsthreadRevLog(QString s)
{
    emit signtheadSendLog(s);
}


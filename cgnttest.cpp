﻿#include "cgnttest.h"
#include <QtDebug>
#include <QThread>
#include <cmath>
#include <QRandomGenerator>
#include <QCryptographicHash>
#include <QDirIterator>

CGntTest::CGntTest()
{
    m_pSerialPort = nullptr;
    m_pSsh = nullptr;
    m_pSocket = nullptr;
    m_pclient = nullptr;
    m_nTypeCType = 0;
    m_ptimer = new QTimer();
    connect(m_ptimer, SIGNAL(timeout()), this, SLOT(on_time_timeout()));
    m_ptimer2 = new QTimer();
    connect(m_ptimer2, SIGNAL(timeout()), this, SLOT(on_time_timeout2()));

    m_pSocket = nullptr;
    m_pRs485 = nullptr;
    m_bNodealssherr = false;
    m_bLock = false;
}

CGntTest::~CGntTest()
{

}

void CGntTest::init(SGntParam &gntParam)
{
    m_nAppNum = 0;
    m_pRs485 = nullptr;
    m_vtCompareApp.clear();
    m_ntimeout = 0;
    m_bFirst = true;
    m_nsshLj = 0;
    m_pSocket = nullptr;
    m_pSsh = nullptr;
    m_vtRqName.clear();
    m_vtCmd.clear();
    m_vtrqwApp.clear();
    m_bScuRqwAppChk = true;
    m_bScuAppChk = true;
    m_bcheck = true;
    m_nLx = 0;
    m_nJyCs = 0;
    m_ndkjcLx = 0;
    m_nHbdylx = 0;
    m_gntParam = gntParam;
    m_gntParam.nJyCs = 1;
    m_gntJyXl.nBw = gntParam.nBw;
    m_gntJyJg.nBw = gntParam.nBw;
    m_gntJyXl.oid = gntParam.noid;
    m_gntJyJg.oid = gntParam.noid;
    m_nTypeCType = 0;

    if(m_pclient != nullptr)
    {
        m_pclient->disconnect();
        m_pclient->disconnectFromHost();
        delete  m_pclient;
        m_pclient = nullptr;
    }
    m_pclient = new QMqttClient();
    connect(m_pclient, &QMqttClient::stateChanged, this, &CGntTest::on_mqtt_StateChange);
    connect(m_pclient, &QMqttClient::messageReceived, this, &CGntTest::on_mqtt_mesReceived);
    if(m_pSerialPort != nullptr)
    {
        m_pSerialPort->disconnect();
        delete m_pSerialPort;
        m_pSerialPort = nullptr;
    }

    m_pSerialPort = new QSerialPort();

    m_pSerialPort->setPortName(m_gntParam.strTypeCCom.toUpper());
    m_pSerialPort->setBaudRate(115200);

    m_pSerialPort->setDataBits(QSerialPort::Data8);

    m_pSerialPort->setStopBits(QSerialPort::OneStop);

    m_pSerialPort->setParity(QSerialPort::NoParity);

    m_pSerialPort->setFlowControl(QSerialPort::NoFlowControl);
    m_pSerialPort->setReadBufferSize(0);

}

// 开始校验
void CGntTest::check()
{
    m_bNodealssherr = false;
    m_nJyCs = 0;
    if(!g_bTestSign)
    {
        m_gntParam.vtItem.clear();
    }
    if(m_gntParam.vtItem.size() == 0)
    {
        clear();
        emit(endSig());
        return;
    }
    m_bSendSshOff = true;
    m_strChkItem = m_gntParam.vtItem[0];
    m_gntParam.vtItem.erase(m_gntParam.vtItem.begin());
    checkItem();
}

void CGntTest::clear()
{
    if(m_pSsh != nullptr)
    {
        if(m_pSsh->state() == QSsh::SshConnection::Connected)
        {
            m_pSsh->disconnectFromHost();

        }
        delete m_pSsh;
        m_pSsh = nullptr;
    }
    if(m_pSocket != nullptr)
    {
        m_pSocket->close();
    }
    if(m_pclient != nullptr)
    {
        delete  m_pclient;
        m_pclient = nullptr;
    }

    if(m_ptimer->isActive())
    {
        m_ptimer->stop();
    }

    if(m_ptimer2->isActive())
    {
        m_ptimer2->stop();
    }

    m_pSerialPort->close();
}

void CGntTest::on_ssh_connected()
{
    m_nReSsh = 0;
    QString s;
    QString cmd ;
    QByteArray cmdByte;
    switch (m_nsshLj)
    {
    case 1:
        m_gntJyXl.strCsGc = "设置IP成功";
        QApplication::postEvent((QObject*)g_pFuncWindow, new CCsXlGcMsg(m_gntJyXl));

        m_gntJyJg.bCsXlJg = true;
        QApplication::postEvent((QObject*)g_pFuncWindow, new CCsXlJgMsg(m_gntJyJg));

        m_gntJyXl.strCsXl = m_gntJyJg.strCsXl = "设置SCU-IP";
        m_gntJyXl.strCsGc = "设置SCU-IP";
        sendGcEvent();
        m_gntJyXl.strCsGc ="打开端口：container config c_master_yx -p 9001:9001";
        sendGcEvent();
        // 开启9001端口
        m_nLx = 17;
        createRemoteProcess("container config c_master_yx -p 9001:9001");
        break;
    case 2:
        m_nLx = 30;
        createRemoteProcess("ifconfig FE0:1");
        break;
    case 3:
        m_ntimeout = 11;
        m_nLx = 11;
        createRemoteProcess("container status");
        m_ptimer->start(5000);
        break;
    case 4:
        m_nLx = 12;
        createRemoteProcess("docker ps");
        break;
    case 5:
        m_nLx = 13;
        s = m_vtRqName[0];
        m_vtRqName.erase(m_vtRqName.begin());
        cmd = "appm -I -c " + s;
        createRemoteProcess(cmd.toUtf8());
        break;
    case 6:
        m_nLx = 14;
        m_strRqwApp = m_vtrqwApp[0];
        m_vtrqwApp.erase(m_vtrqwApp.begin());
        cmdByte = QString("ps -ef | grep " + m_strRqwApp ).toUtf8();
        createRemoteProcess(cmdByte);
        break;
    case 7:
        m_strRqwApp = m_vtrqwApp[0];
        m_vtrqwApp.erase(m_vtrqwApp.begin());
        cmdByte = QString("ps -ef | grep " + m_strRqwApp).toUtf8();
        createRemoteProcess(cmdByte);
        break;
    case 8:
        m_ntimeout = 9999;
        m_nLx = 1;
        createRemoteProcess("version -b");
        m_ptimer->start(5000);
        break;
    case 9:
        m_nLx = 2;
        m_ntimeout = 9999;
        createRemoteProcess("cat /etc/build_version");
        m_ptimer->start(5000);
        break;
    case 10:
        m_nLx = 3;
        m_ntimeout = 9999;
        createRemoteProcess("devcfg -mbn");
        m_ptimer->start(5000);
        break;
    case 11:
        m_nLx = 4;
        m_ntimeout = 9999;
        createRemoteProcess("devctl -H");
        m_ptimer->start(5000);
        break;
    case 12:
        m_nLx = 5;
        m_ntimeout = 9999;
        createRemoteProcess("version -k");
        m_ptimer->start(5000);
        break;
    case 13:
        m_nLx = 6;
        m_ntimeout = 9999;
        createRemoteProcess("verssion -k");
        m_ptimer->start(5000);
        break;
    case 14:
        m_nLx = 7;
        m_ntimeout = 9999;
        createRemoteProcess("docker -v");
        m_ptimer->start(5000);
        break;
    case 15:
        m_nLx = 8;
        m_ntimeout = 9999;
        createRemoteProcess("read_jc_ver -v");
        m_ptimer->start(5000);
        break;
    case 16:
        m_nLx = 9;
        m_ntimeout = 9999;
        createRemoteProcess("cat /var/log/urdebug.log");
        m_ptimer->start(5000);
        break;
    case 17:
        m_nLx = 10;
        m_ntimeout = 9999;
        createRemoteProcess("cat /etc/os-version.yaml");
        m_ptimer->start(5000);
        break;
    case 18:
        cmd = m_vtCmd[0];
        m_vtCmd.erase(m_vtCmd.begin());
        m_nLx = 15;
        createRemoteProcess(cmd.toUtf8());
        break;
    case 19:
        cmd = m_vtCmd[0];
        m_vtCmd.erase(m_vtCmd.begin());
        createRemoteProcess(cmd.toUtf8());
        break;
    case 20:
        m_nLx = 16;
        createRemoteProcess("cd /usr/sbin && ./vendor_eeprom show -a");
        break;
    case 21:
        m_nLx = 17;
        createRemoteProcess("container config c_master_yx -p 9001:9001");
        break;
    case 22:
        m_gntJyJg.strCsXl = m_gntJyXl.strCsXl = "打开9001端口";
        m_gntJyXl.strCsGc ="打开端口：container config c_master_yx -p 9001:9001";
        sendGcEvent();
        m_nLx = 17;
        createRemoteProcess("container config c_master_yx -p 9001:9001");
        break;
    case 23:
        m_ntimeout = 9999;
        createRemoteProcess("echo 'Zgdky@guest123' | sudo -S iptables -t nat -A PREROUTING -p tcp --dport 1883 -j DNAT --to-destination **********:1883");
        m_ptimer->start(2000);
        break;
    case 24:
        m_nLx =18;
        m_dt2 = QDateTime::currentDateTime();
        createRemoteProcess("date +%Y%m%d%H%M%S");
        break;
    case 25:
        m_nLx = 19;
        m_dt2 = QDateTime::currentDateTime();
        createRemoteProcess("date +%Y%m%d%H%M%S");
        break;
    case 26:
        s = m_vtCmd[0];
        m_vtCmd.erase(m_vtCmd.begin());
        m_strtty = s.right(s.size() -15).trimmed();
        m_gntJyXl.strCsGc = "查找文件："+m_strtty + ", 命令：" + s;
        sendGcEvent();
        m_btimercheck = false;        
        m_nLx = 20;
        m_ntimeout = 9999;
        createRemoteProcess(s.toUtf8());
        m_ptimer->start(6000);

        break;
    case 27:
        s = m_vtCmd[0];
        m_vtCmd.erase(m_vtCmd.begin());
        m_strtty = s.right(s.size() -15).trimmed();
        m_gntJyXl.strCsGc = "查找文件："+m_strtty + ", 命令：" + s;
        sendGcEvent();        
        createRemoteProcess(s.toUtf8());
        m_ntimeout = 9999;
        m_ptimer->start(6000);

        break;
    case 28:
        m_nLx = 21;
        createRemoteProcess("devctl -l");
        break;
    case 29:
        m_nLx = 22;
        createRemoteProcess("wwan modem show dev all");
        break;
    case 30:
        m_nLx = 17;
        createRemoteProcess("container config c_master_yx -p 9001:9001");
        break;
    case 31:
        m_ntimeout = 9999;
        createRemoteProcess("echo 'Zgdky@guest123' | sudo -S iptables -t nat -A PREROUTING -p tcp --dport 1883 -j DNAT --to-destination **********:1883");
        m_ptimer->start(6000);
        break;
    case 32:
        m_nLx = 17;
        createRemoteProcess("container config c_master_yx -p 9001:9001");
        break;
    case 33:
        if(m_pSocket != nullptr)
        {
            m_pSocket->disconnect();
            m_pSocket->disconnectFromHost();
            delete m_pSocket;
            m_pSocket = nullptr;
        }
        m_ntimeout = 50;
        createRemoteProcess(" echo 'Zgdky@guest123' | sudo -S /opt/om_core/bin/sqlite3 /data/config/om_core/db/AgentDB.db \"update ContainerConfTable set Port='' where ContainerName='c_master_yx'\"");
        m_ptimer->start(3000);
        break;
    case 34:
        if(m_pSsh->state() == QSsh::SshConnection::Connected)
        {
            m_gntJyXl.strCsGc = "scu重启成功";
            sendGcEvent();
            m_gntJyXl.strCsGc = "ssh成功连接scu";
            sendGcEvent();
            m_gntJyXl.strCsGc = "端口检查， 执行命令：echo Zgdky@guest123 | sudo -S iptables -t nat -nvL --line-number | grep DNAT | awk -F'to:' '{print$2}'";
            sendGcEvent();
            m_ndkjcLx = 2;
            m_nLx = 23;
            m_ntimeout = 9999;
            createRemoteProcess("echo 'Zgdky@guest123' | sudo -S iptables -t nat -nvL --line-number | grep DNAT | awk -F'to:' '{print$2}'");
            m_ptimer->start(10000);
        }
        break;
    case 35:
        m_gntJyJg.strCsXl = m_gntJyXl.strCsXl = "清空电量文件";
        m_gntJyXl.strCsGc = "清空电量文件， 命令：echo Zgdky@guest123 | sudo -S rm -rf /data/app/MskIEC104/commFile/HISTORY/*";
        sendGcEvent();

        createRemoteProcess("echo 'Zgdky@guest123' | sudo -S rm -rf /data/app/MskIEC104/commFile/HISTORY/*");
        m_ntimeout = 9999;
        m_ptimer->start(6000);

        break;
    case 36:
        m_nHbdy = 0;
        hbdycheck();
        break;
    case 37:
        m_ntimeout = 9999;
        m_nLx = 25;
        createRemoteProcess("date");
        m_ptimer->start(6000);
        break;
    case 38:
        m_ntimeout = 9999;
        m_nLx = 26;
        createRemoteProcess("echo 'Zgdky@guest123' | sudo -S rs232mgt set 1 rs485");
        m_ptimer->start(6000);
        break;
    case 39:
        m_ntimeout = 9999;
        m_nLx = 27;
        createRemoteProcess("echo 'Zgdky@guest123' | sudo -S rs232mgt set 2 rs485");
        m_ptimer->start(6000);
        break;
    case 40:
        m_nLx = 17;
        createRemoteProcess("container config c_master_yx -p 9001:9001");
        break;
    case 41:
        sshClose();

        break;
    case 42:
        m_ntimeout = 9999;
        m_nLx = 28;
        createRemoteProcess("cat /data/app/SCMQTTIoT/configFile/paramFile.json");
        m_ptimer->start(6000);

        break;
    case 43:
        m_channel = m_pSsh->createSftpChannel();
        if(m_channel)
        {
            connect(m_channel.data(), SIGNAL(initialized()),this,
                    SLOT(onChannelInitialized1()));
            connect(m_channel.data(), SIGNAL(channelError(const QString &)),this,
                    SLOT(onChannelError1(const QString &)));
            connect(m_channel.data(), SIGNAL(finished(QSsh::SftpJobId, QString)),this,
                    SLOT(onOpfinished(QSsh::SftpJobId, QString)));
            m_channel->initialize();
        }
        break;
    case 44:
        s = m_vtCmd[0];
        m_vtCmd.erase(m_vtCmd.begin());
        m_strtty = s.right(s.size() -15).trimmed();
        m_gntJyXl.strCsGc = "查找文件："+m_strtty + ", 命令：" + s;
        sendGcEvent();
        m_nLx = 31;
        m_ntimeout = 9999;
        createRemoteProcess(s.toUtf8());
        m_ptimer->start(6000);

        break;
    case 45:
        m_ntimeout = 1;
        createRemoteProcess("echo 'Zgdky@guest123' | sudo -S rm -rf /data/license_not_verify");
        m_ptimer->start(2000);
        break;
    case 46:
        m_ntimeout = 2;
        createRemoteProcess("echo 'Zgdky@guest123' | sudo -S rm -rf /data/license_not_verify");
        m_ptimer->start(2000);
        break;
    case 47:
        m_nLx = 32;
        createRemoteProcess("ps -eo pid,lstart,cmd|grep '/opt/om_core/bin/DeviceManager' | grep -v 'grep'| awk -F' ' '{printf$5}'");
        break;
    case 48:
        if(m_vtCompareApp.size() == 0)
        {
            m_gntJyJg.bCsXlJg = m_bcheck;
            sendJgEvent();
            rqwappchk();
            return;
        }
        ++m_nAppNum;
        s = "/opt/om_core/bin/sqlite3 /data/config/om_core/db/AgentDB.db \"select appStartTime from app where appName='";
        m_strComeApp = m_vtCompareApp[0];
        s += m_strComeApp;
        s += "'\"";

        m_gntJyXl.strCsGc = "获取app:" + m_vtCompareApp[0] + "启动时间, 命令：";
        m_gntJyXl.strCsGc += s;
        sendGcEvent();

        m_vtCompareApp.erase(m_vtCompareApp.begin());

        m_ntimeout = 3;
        m_nLx = 33;
        createRemoteProcess(s.toUtf8());
        m_ptimer->start(3000);
        break;
    case 49:
        m_gntJyXl.strCsXl = m_gntJyJg.strCsXl = "RS485_I";
        m_gntJyXl.strCsGc = "RS485_I通信测试, 设置串口波特率： echo Zgdky@guest123 | sudo -S stty -F /dev/ttyRS0 raw speed 9600 cs8 -cstopb parenb -parodd";
        sendGcEvent();
        m_nLx = 34;
        createRemoteProcess("echo 'Zgdky@guest123' | sudo -S stty -F /dev/ttyRS0 raw speed 9600 cs8 -cstopb parenb -parodd");
        break;
    case 50:
        m_zrsdata.clear();
        m_gntJyXl.strCsGc = "发送数据：4851, 命令：echo 485148514851 > /dev/ttyRS0";
        sendGcEvent();
        createRemoteProcess("echo 485148514851 > /dev/ttyRS0");
        createRemoteProcess("echo 485148514851 > /dev/ttyRS0");
        ++m_nRS485Num;
        m_ntimeout = 4;
        m_ptimer->start(2000);
        break;
    case 51:
        m_gntJyJg.strCsXl = m_gntJyXl.strCsXl = "RS485_II";
        m_gntJyXl.strCsGc = "设置串口波特率， 命令： sudo stty -F /dev/ttyRS1 raw speed 9600 cs8 -cstopb parenb -parodd";
        sendGcEvent();
        m_nLx = 35;
        createRemoteProcess("echo 'Zgdky@guest123' | sudo -S stty -F /dev/ttyRS1 raw speed 9600 cs8 -cstopb parenb -parodd");
        break;
    case 52:
        m_zrsdata.clear();
        m_gntJyXl.strCsGc = "发送数据：4852, 命令：echo 4852485248524852 > /dev/ttyRS1";
        sendGcEvent();
        createRemoteProcess("echo 4852485248524852 > /dev/ttyRS1");
        createRemoteProcess("echo 4852485248524852 > /dev/ttyRS1");
        ++m_nRS485Num;
        m_ntimeout = 5;
        m_ptimer->start(2000);
        break;
    case 53:
        m_gntJyJg.strCsXl = m_gntJyXl.strCsXl = "RS485_III";
        m_gntJyXl.strCsGc = "设置串口波特率， 命令： sudo stty -F /dev/ttyRS2 raw speed 9600 cs8 -cstopb parenb -parodd";
        sendGcEvent();
        m_nLx = 36;
        createRemoteProcess("echo 'Zgdky@guest123' | sudo -S stty -F /dev/ttyRS2 raw speed 9600 cs8 -cstopb parenb -parodd");
        break;
    case 54:
        m_zrsdata.clear();
        m_gntJyXl.strCsGc = "发送数据：4853, 命令：echo 4853485348534853 > /dev/ttyRS2";
        sendGcEvent();
        createRemoteProcess("echo 4853485348534853 > /dev/ttyRS2");
        createRemoteProcess("echo 4853485348534853 > /dev/ttyRS2");
        ++m_nRS485Num;
        m_ntimeout = 6;
        m_ptimer->start(2000);
        break;
    case 55:
        m_gntJyJg.strCsXl = m_gntJyXl.strCsXl = "RS485_IV";
        m_gntJyXl.strCsGc = "设置串口波特率， 命令： sudo stty -F /dev/ttyRS3 raw speed 9600 cs8 -cstopb parenb -parodd";
        sendGcEvent();
        m_nLx = 37;
        createRemoteProcess("echo 'Zgdky@guest123' | sudo -S stty -F /dev/ttyRS3 raw speed 9600 cs8 -cstopb parenb -parodd");
        break;
    case 56:
        m_zrsdata.clear();
        m_gntJyXl.strCsGc = "发送数据：4854, 命令：echo 48544854485448544854 > /dev/ttyRS3";
        sendGcEvent();
        createRemoteProcess("echo 48544854485448544854 > /dev/ttyRS3");
        createRemoteProcess("echo 48544854485448544854 > /dev/ttyRS3");
        ++m_nRS485Num;
        m_ntimeout = 7;
        m_ptimer->start(2000);
        break;
    case 57:
        createRemoteProcess("echo 'Zgdky@guest123' | sudo -S iptables -t nat -A PREROUTING -p tcp --dport 1883 -j DNAT --to-destination **********:1883");
        m_ntimeout = 8;
        m_ptimer->start(3000);
        break;
    case 58:
        m_nLx = 38;
        createRemoteProcess("echo 'Zgdky@guest123' | sudo -S cat /dev/ttyRS0");
        m_ntimeout = 12;
        m_ptimer->start(1000);
        break;
    case 59:
        m_nLx = 39;
        createRemoteProcess("echo 'Zgdky@guest123' | sudo -S cat /dev/ttyRS1");
        m_ntimeout = 13;
        m_ptimer->start(1000);

        break;
    case 60:
        m_nLx = 40;
        createRemoteProcess("echo 'Zgdky@guest123' | sudo -S cat /dev/ttyRS2");
        m_ntimeout = 14;
        m_ptimer->start(1000);

        break;
    case 61:
        m_nLx = 41;
        createRemoteProcess("echo 'Zgdky@guest123' | sudo -S cat /dev/ttyRS3");
        m_ntimeout = 15;
        m_ptimer->start(1000);
        break;
    case 62:
        createRemoteProcess("echo 'Zgdky@guest123' | sudo -S iptables -t nat -A PREROUTING -p tcp --dport 1883 -j DNAT --to-destination **********:1883");
        m_ntimeout = 16;
        m_ptimer->start(3000);
        break;
     case 63:
        m_gntJyXl.strCsGc = "重启成功";
        sendGcEvent();

        m_gntJyXl.strCsGc = "执行命令：cd /mnt/internal_storage && echo Zgdky@guest123 | sudo -S rm -rf security_proxy_config";
        sendGcEvent();

        createRemoteProcess("cd /mnt/internal_storage && echo 'Zgdky@guest123' | sudo -S rm -rf security_proxy_config");
        m_ntimeout = 17;
        m_ptimer->start(3000);
        break;
    case 64:
        m_channel = m_pSsh->createSftpChannel();
        if(m_channel)
        {
            connect(m_channel.data(), SIGNAL(initialized()),this,
                    SLOT(onChannelInitialized2()));
            connect(m_channel.data(), SIGNAL(channelError(const QString &)),this,
                    SLOT(onChannelError1(const QString &)));
            connect(m_channel.data(), SIGNAL(finished(QSsh::SftpJobId, QString)),this,
                    SLOT(onOpfinished2(QSsh::SftpJobId, QString)));
            m_channel->initialize();
        }
        break;
    case 65:
        m_gntJyXl.strCsGc = "执行命令：ps -ef | grep DeviceManager | grep -v grep | awk '{print $2}'";
        sendGcEvent();
        m_nLx = 42;
        createRemoteProcess("ps -ef | grep DeviceManager | grep -v grep | awk '{print $2}'");

        m_ntimeout = 37;
        m_ptimer->start(2000);
        break;
    case 66:
        m_gntJyXl.strCsGc = "执行命令：";
        m_gntJyXl.strCsGc += m_strKill.toUtf8();
        sendGcEvent();
        createRemoteProcess(m_strKill.toUtf8());
        m_ntimeout = 18;
        m_ptimer->start(1000);
        break;
    case 67:
        m_nLx = 43;
        createRemoteProcess("ls /usr/sec-app/package/work");
        break;
    case 68:
        m_gntJyXl.strCsGc = "执行命令：cd /mnt/custom && echo Zgdky@guest123 | sudo -S chmod 777 TID";
        sendGcEvent();
        createRemoteProcess("cd /mnt/custom && echo 'Zgdky@guest123' | sudo -S chmod 777 TID");
        m_ntimeout = 19;
        m_ptimer->start(1000);
    case 70:
        cmdByte = "cd /mnt/custom/  && echo 'Zgdky@guest123' | sudo -S echo ";
        cmdByte += m_gntParam.scuParam.strID;
        cmdByte += " > TID";
        m_gntJyXl.strCsGc = cmdByte;
        sendGcEvent();
        createRemoteProcess(cmdByte);

        m_ntimeout = 20;
        m_ptimer->start(1000);
        break;
    case 71:
        m_gntJyXl.strCsGc = "执行命令:cd /usr/sec-app/package/work/ && echo Zgdky@guest123 | sudo -S chmod 777 security_proxy_bin";
        sendGcEvent();
        createRemoteProcess("cd /usr/sec-app/package/work/ && echo 'Zgdky@guest123' | sudo -S chmod 777 security_proxy_bin");
        m_ntimeout = 21;
        m_ptimer->start(1000);
        break;
    case 72:
        m_gntJyXl.strCsGc = "执行命令:cd /usr/sec-app/package/work/ && echo Zgdky@guest123 | sudo -S ./security_proxy_bin -f";
        sendGcEvent();
        m_nLx = 44;
        createRemoteProcess("cd /usr/sec-app/package/work/ && echo 'Zgdky@guest123' | sudo -S ./security_proxy_bin -f");
        m_ntimeout = 39;
        m_ptimer->start(3000);
        break;
    case 73:
        m_gntJyXl.strCsGc = "执行命令:cd /usr/sec-app/package/work/ && echo Zgdky@guest123 | sudo -S chmod 755 security_proxy_bin";
        sendGcEvent();
        createRemoteProcess("cd /usr/sec-app/package/work/ && echo 'Zgdky@guest123' | sudo -S chmod 755 security_proxy_bin");
        m_ntimeout = 22;
        m_ptimer->start(1000);
        break;
    case 74:
        m_gntJyXl.strCsGc = "执行命令:ps -ef | grep DeviceManager | grep -v grep | awk '{print $2}'";
        sendGcEvent();
        m_nLx = 45;
        createRemoteProcess("ps -ef | grep DeviceManager | grep -v grep | awk '{print $2}'");

        m_ntimeout = 38;
        m_ptimer->start(2000);
        break;
    case 75:
        m_gntJyXl.strCsGc = "执行命令:cd /mnt/custom && echo Zgdky@guest123 | sudo -S chmod 755 TID";
        sendGcEvent();
        createRemoteProcess("cd /mnt/custom && echo 'Zgdky@guest123' | sudo -S chmod 755 TID");
        m_ntimeout = 23;
        m_ptimer->start(1000);
        break;
    case 76:
        m_gntJyXl.strCsGc = "执行命令:";
        m_gntJyXl.strCsGc += m_strKill.toUtf8();
        sendGcEvent();

        createRemoteProcess(m_strKill.toUtf8());
        m_ntimeout = 24;
        m_ptimer->start(1000);
        break;
    case 77:
        m_gntJyXl.strCsGc = "执行命令:cd /mnt/internal_storage && echo Zgdky@guest123 | sudo -S rm -rf security_proxy_config";
        sendGcEvent();
        createRemoteProcess("cd /mnt/internal_storage && echo 'Zgdky@guest123' | sudo -S rm -rf security_proxy_config");
        m_ntimeout = 25;
        m_ptimer->start(1000);
        break;
    case 78:
        m_channel = m_pSsh->createSftpChannel();
        if(m_channel)
        {
            connect(m_channel.data(), SIGNAL(initialized()),this,
                    SLOT(onChannelInitialized2()));
            connect(m_channel.data(), SIGNAL(channelError(const QString &)),this,
                    SLOT(onChannelError1(const QString &)));
            connect(m_channel.data(), SIGNAL(finished(QSsh::SftpJobId, QString)),this,
                    SLOT(onOpfinished2(QSsh::SftpJobId, QString)));
            m_channel->initialize();
        }
        break;
    case 79:
        m_gntJyXl.strCsGc = "执行命令： ps -ef | grep DeviceManager | grep -v grep | awk '{print $2}'";
        sendGcEvent();
        m_nLx = 46;
        createRemoteProcess("ps -ef | grep DeviceManager | grep -v grep | awk '{print $2}'");
        m_ntimeout = 36;
        m_ptimer->start(2000);
        break;
    case 80:

        m_gntJyXl.strCsGc = "执行命令：";
        m_gntJyXl.strCsGc += m_strKill;
        sendGcEvent();

        m_nstartchecknum = 0;
        createRemoteProcess(m_strKill.toUtf8());
        m_ntimeout = 26;
        m_ptimer->start(1000);
        break;
    case 81:
        m_gntJyXl.strCsGc = "执行命令：cd /mnt/internal_storage && echo Zgdky@guest123 | sudo -S rm -rf security_proxy_config";
        sendGcEvent();
        createRemoteProcess("cd /mnt/internal_storage && echo 'Zgdky@guest123' | sudo -S rm -rf security_proxy_config");
        m_ntimeout = 27;
        m_ptimer->start(3000);
        break;
    case 82:
        m_channel = m_pSsh->createSftpChannel();
        if(m_channel)
        {
            connect(m_channel.data(), SIGNAL(initialized()),this,
                    SLOT(onChannelInitialized2()));
            connect(m_channel.data(), SIGNAL(channelError(const QString &)),this,
                    SLOT(onChannelError1(const QString &)));
            connect(m_channel.data(), SIGNAL(finished(QSsh::SftpJobId, QString)),this,
                    SLOT(onOpfinished2(QSsh::SftpJobId, QString)));
            m_channel->initialize();
        }
        break;
    case 83:
        m_gntJyXl.strCsGc = "执行命令：ps -ef | grep DeviceManager | grep -v grep | awk '{print $2}'";
        sendGcEvent();
        m_nLx = 47;
        createRemoteProcess("ps -ef | grep DeviceManager | grep -v grep | awk '{print $2}'");
        m_ntimeout = 40;
        m_ptimer->start(3000);
        break;
    case 84:
        m_gntJyXl.strCsGc = "执行命令：";
        m_gntJyXl.strCsGc += m_strKill;
        sendGcEvent();
        createRemoteProcess(m_strKill.toUtf8());
        m_ntimeout = 28;
        m_ptimer->start(1000);
        break;
    case 85:
        m_gntJyXl.strCsGc = "执行命令：cd /usr/sec-app/package/work/ && echo Zgdky@guest123 | sudo -S chmod 755 security_proxy_bin";
        sendGcEvent();
        createRemoteProcess("cd /usr/sec-app/package/work/ && echo 'Zgdky@guest123' | sudo -S chmod 755 security_proxy_bin");
        m_ntimeout = 29;
        m_ptimer->start(1000);
        break;
    case 86:
        m_gntJyXl.strCsGc = "执行命令：cd /mnt/custom && echo Zgdky@guest123 | sudo -S chmod 755 TID";
        sendGcEvent();
        createRemoteProcess("cd /mnt/custom && echo 'Zgdky@guest123' | sudo -S chmod 755 TID");
        m_ntimeout = 30;
        m_ptimer->start(1000);
        break;
    case 87:
        m_gntJyXl.strCsGc = "执行命令：cd /mnt && echo Zgdky@guest123 | sudo -S chmod 777 internal_storage";
        sendGcEvent();
        createRemoteProcess("cd /mnt && echo 'Zgdky@guest123' | sudo -S chmod 777 internal_storage");
        m_ntimeout = 31;
        m_ptimer->start(1000);
        break;
    case 88:
        m_gntJyXl.strCsGc = "执行命令：cd /mnt && echo Zgdky@guest123 | sudo -S chmod 755 internal_storage";
        sendGcEvent();
        createRemoteProcess("cd /mnt && echo 'Zgdky@guest123' | sudo -S chmod 755 internal_storage");
        m_ntimeout = 32;
        m_ptimer->start(1000);
        break;
    case 89:
        m_bNodealssherr = true;
        m_gntJyXl.strCsGc = "重启SCU";
        sendGcEvent();
        createRemoteProcess(" echo 'Zgdky@guest123' | sudo -S reboot\n");
        m_ntimeout = 33;
        m_ptimer->start(150000);
        break;
    case 90:
        m_nLx = 48;
        createRemoteProcess("cat /var/log/om_core/log/DeviceManager.log");
        break;
    case 91:
        createRemoteProcess("echo 'Zgdky@guest123' | sudo -S chmod -R 777 /data/app/SCMQTTIoT/");
        m_ntimeout = 35;
        m_ptimer->start(1000);
        break;
    case 92:
        m_nLx = 49;
        createRemoteProcess("cat /data/app/SCMQTTIoT/configFile/paramFile.json");
        break;
    case 93:
        m_nLx = 50;
        createRemoteProcess("cd /usr/sbin && ./vendor_eeprom show -a");
        break;
    case 94:
        m_nLx = 51;
        createRemoteProcess("cat /data/app/SCMQTTIoT/configFile/paramFile.json");
        break;
    case 95:
        m_nLx = 52;
        createRemoteProcess("devcfg -esam");
        break;
    case 96:
        // 判断/data/devinfo 文件夹是否存在
        m_gntJyXl.strCsGc = "判断/data/devinfo文件夹是否存在,命令：cd /data && ls";
        sendGcEvent();
        m_nLx = 53;
        createRemoteProcess("cd /data && ls");
        break;
    case 97:
        m_gntJyXl.strCsGc = "赋予/data/devinfo文件夹777权限，命令：echo Zgdky@guest123 | sudo -S chmod 777 /data/devinfo/";
        sendGcEvent();
        createRemoteProcess("echo 'Zgdky@guest123' | sudo -S chmod 777 /data/devinfo/");
        m_ntimeout = 41;
        m_ptimer->start(3000);
        break;
    case 98:
        m_gntJyXl.strCsGc = "创建文件夹/data/devinfo, 命令： ";
        sendGcEvent();
        createRemoteProcess("echo 'Zgdky@guest123' | sudo -S mkdir /data/devinfo");
        m_ntimeout = 42;
        m_ptimer->start(2000);
        break;
    case 99:
        m_gntJyXl.strCsGc = "开始上传license文件";
        sendGcEvent();

        m_channel = m_pSsh->createSftpChannel();
        if(m_channel)
        {
            connect(m_channel.data(), SIGNAL(initialized()),this,
                    SLOT(onChannelInitialized3()));
            connect(m_channel.data(), SIGNAL(channelError(const QString &)),this,
                    SLOT(onChannelError1(const QString &)));
            connect(m_channel.data(), SIGNAL(finished(QSsh::SftpJobId, QString)),this,
                    SLOT(onOpfinished3(QSsh::SftpJobId, QString)));
            m_channel->initialize();
        }

        break;
    case 100:
        m_gntJyXl.strCsGc = "删除/data/devinfo/license文件， 命令:echo Zgdky@guest123 | sudo -S  rm -rf /data/devinfo/license";
        sendGcEvent();
        createRemoteProcess("echo 'Zgdky@guest123' | sudo -S  rm -rf /data/devinfo/license");
        m_ntimeout = 43;
        m_ptimer->start(3000);
        break;
    case 101:
        m_gntJyXl.strCsGc = "写入到license文件";
        sendGcEvent();
        cmd = "echo \"$(cat /tmp/";
        cmd += m_strEsamId;
        cmd += ".txt)\" > /data/devinfo/license";
        createRemoteProcess(cmd.toUtf8());
        m_ntimeout = 44;
        m_ptimer->start(3000);
        break;
    case 102:
        m_gntJyXl.strCsGc = "修改/data/devinfo/license文件格式";
        sendGcEvent();
        cmd = "sed -i 's/\\r$//' /data/devinfo/license";
        createRemoteProcess(cmd.toUtf8());
        m_ntimeout = 45;
        m_ptimer->start(3000);
        break;
    case 103:
        m_gntJyXl.strCsGc = "/data/devinfo/license文件存在确认，命令:ls /data/devinfo";
        sendGcEvent();
        m_nLx = 54;
        createRemoteProcess("ls /data/devinfo");
        m_ntimeout = 46;
        m_ptimer->start(3000);
        break;
    case 104:
        m_bNodealssherr = true;
        m_gntJyXl.strCsGc = "重启SCU，命令：echo Zgdky@guest123 | sudo -S reboot";
        sendGcEvent();
        createRemoteProcess("echo 'Zgdky@guest123' | sudo -S reboot\n");
        m_ntimeout = 47;
        m_ptimer->start(150000);
        break;
    case 105:
        m_gntJyXl.strCsGc = "重启成功";
        m_gntJyJg.bCsXlJg = true;
        sendGcEvent();
        sendJgEvent();
        m_ntimeout = 48;
        m_ptimer->start(1000);
        break;
    case 106:
        if(m_vtCompareApp.size() == 0)
        {
            m_gntJyJg.bCsXlJg = m_bcheck;
            sendJgEvent();
            rqwappchk();
            return;
        }
        ++m_nAppNum;
        s = "/opt/om_core/bin/sqlite3 /data/config/om_core/db/AgentDB.db \"select appStartTime from app where appName='";
        s += m_strComeApp;
        s += "'\"";

        m_gntJyXl.strCsGc = "获取app:" + m_vtCompareApp[0] + "启动时间, 命令：";
        m_gntJyXl.strCsGc += s;
        sendGcEvent();

        m_ntimeout = 3;
        m_nLx = 33;
        createRemoteProcess(s.toUtf8());
        m_ptimer->start(3000);
        break;
    case 107:
        m_gntJyXl.strCsGc = "文件清除确认， 命令：ls /data/";
        sendGcEvent();
        m_nLx = 55;
        createRemoteProcess("ls /data/");
        break;
    case 108:
        m_ndkjcLx = 1;
        m_bNodealssherr = true;
        m_gntJyXl.strCsGc = "重启SCU， 执行命令： echo Zgdky@guest123 | sudo -S reboot";
        sendGcEvent();
        createRemoteProcess("echo 'Zgdky@guest123' | sudo -S reboot\n");

        if(!g_bTestSign)
        {
            check();
            return;
        }
        m_pSsh->disconnect();
        m_ntimeout = 9999;
        m_ptimer->start(150000);
        break;
    case 109:
        m_gntJyXl.strCsGc = "备份原有串口驱动， 命令： sudo mv /lib/modules/hi1711_ko/uart_drv.ko  /lib/modules/hi1711_ko/shanxi_scu_old_uart_drv.ko";
        sendGcEvent();
        createRemoteProcess("echo 'Zgdky@guest123' | sudo -S mv /lib/modules/hi1711_ko/uart_drv.ko  /lib/modules/hi1711_ko/shanxi_scu_old_uart_drv.ko");
        m_ntimeout = 51;
        m_ptimer->start(2000);
        break;
    case 110:
        m_nLx = 56;
        m_gntJyXl.strCsGc = "确认是否备份成功， 命令： ls /lib/modules/hi1711_ko/shanxi_scu_old_uart_drv.ko";
        createRemoteProcess("ls /lib/modules/hi1711_ko/shanxi_scu_old_uart_drv.ko");
        m_ntimeout = 52;
        m_ptimer->start(2000);
        break;
    case 111:
        m_gntJyXl.strCsGc = "上传新的串口驱动";
        sendGcEvent();
        m_channel = m_pSsh->createSftpChannel();
        if(m_channel)
        {
            connect(m_channel.data(), SIGNAL(initialized()),this,
                    SLOT(onChannelInitialized4()));
            connect(m_channel.data(), SIGNAL(channelError(const QString &)),this,
                    SLOT(onChannelError1(const QString &)));
            connect(m_channel.data(), SIGNAL(finished(QSsh::SftpJobId, QString)),this,
                    SLOT(onOpfinished4(QSsh::SftpJobId, QString)));
            m_channel->initialize();
        }
        break;
    case 112:
        m_gntJyXl.strCsGc = "判断文件uart_drv.ko是否存在， 命令： ls /lib/modules/hi1711_ko/uart_drv.ko";
        sendGcEvent();
        m_nLx = 57;
        createRemoteProcess("ls /lib/modules/hi1711_ko/uart_drv.ko");
        m_ntimeout = 55;
        m_ptimer->start(2000);
        break;
    case 113:
        m_bNodealssherr = true;
        m_gntJyXl.strCsGc = "重启SCU， 命令：sudo reboot";
        sendGcEvent();
        createRemoteProcess("echo 'Zgdky@guest123' | sudo -S reboot\n");
        m_ntimeout = 56;
        m_ptimer->start(150000);
        break;
    case 114:
        m_gntJyXl.strCsGc = "查询蓝牙密码";
        sendGcEvent();
        cmd = "echo -e -n \"\\xFE\\xFE\\xFE\\xFE\\x68\\x00\\x00\\x03\\x00\\x00\\x0B\\xF2\\xFF\\xFF\\xFF\\xFF\\xFF\\xFF\\x00\\x00\\x00\\x00\\x68\\xCA\\x16\" > /dev/ttySS7";
        cmdByte += cmd.toUtf8();
        createRemoteProcess(cmdByte);
        /*m_ntimeout = 57;
        m_ptimer->start(2000);*/
        ++m_nLymm;
        m_ntimeout = 58;
        m_nLx = 58;
        createRemoteProcess("echo 'Zgdky@guest123' | sudo -S cat /proc/kbox/regions/panic");
        m_ptimer->start(20000);
        break;
    case 115:
        ++m_nLymm;
        m_ntimeout = 58;
        m_nLx = 58;
        createRemoteProcess("echo 'Zgdky@guest123' | sudo -S cat /proc/kbox/regions/panic");
        m_ptimer->start(20000);
        break;
    case 117:
        ++m_nLymm;
        m_gntJyXl.strCsGc = "恢复串口驱动";
        sendGcEvent();
        createRemoteProcess("echo 'Zgdky@guest123' | sudo -S mv /lib/modules/hi1711_ko/shanxi_scu_old_uart_drv.ko /lib/modules/hi1711_ko/uart_drv.ko");
        m_ntimeout = 59;
        m_ptimer->start(3000);
        break;
    case 118:
        m_nLx = 59;
        createRemoteProcess("ls /lib/modules/hi1711_ko/shanxi_scu_old_uart_drv.ko");
        m_ntimeout = 60;
        m_ptimer->start(3000);
        break;
    case 119:
        createRemoteProcess("sudo stty -F /dev/ttySS7 raw speed 115200 cs8 -cstopb parenb -parodd");
        m_ntimeout = 63;
        m_ptimer->start(3000);
        break;
    case 120:
        cmd = "echo -e -n \"\\xFE\\xFE\\xFE\\xFE\\x68\\x09\\x00\\x02\\x00\\x00\\x0B\\xF2\\xFF\\xFF\\xFF\\xFF\\xFF\\xFF\\x00\\x00\\x00\\x00\\x68\\x0F\\x01\\x00\\x01\\x00\\x01\\x00\\x01\\x00\\xE5\\x16\" > /dev/ttySS7";
        cmdByte = cmd.toUtf8();
        createRemoteProcess(cmdByte);
        m_ntimeout = 64;
        m_ptimer->start(3000);
        break;
    case 121:
        m_gntJyXl.strCsGc = "判断文件/lib/modules/hi1711_ko/shanxi_scu_old_uart_drv.ko是否存在";
        sendGcEvent();
        m_nLx = 60;
        createRemoteProcess("ls /lib/modules/hi1711_ko/shanxi_scu_old_uart_drv.ko");
        m_ntimeout = 65;
        m_ptimer->start(3000);
        break;
     case 122:
        ++m_nLymm;
        m_gntJyJg.strCsXl = m_gntJyXl.strCsXl = "修改导出配置文件";
        m_gntJyXl.strCsGc = "修改导出配置文件：/mnt/internal_storage/security_proxy_config";
        sendGcEvent();
        m_channel = m_pSsh->createSftpChannel();
        if(m_channel)
        {
            connect(m_channel.data(), SIGNAL(initialized()),this,
                    SLOT(onChannelInitialized5()));
            connect(m_channel.data(), SIGNAL(channelError(const QString &)),this,
                    SLOT(onChannelError1(const QString &)));
            connect(m_channel.data(), SIGNAL(finished(QSsh::SftpJobId, QString)),this,
                    SLOT(onOpfinished5(QSsh::SftpJobId, QString)));
            m_channel->initialize();
        }
        break;
    case 123:
        m_gntJyXl.strCsGc = "修改确认";
        sendGcEvent();
        m_nLx = 61;
        createRemoteProcess("cat /mnt/internal_storage/security_proxy_config");
        m_ntimeout = 67;
        m_ptimer->start(3000);
        break;
    case 124:
        createRemoteProcess("echo 'Zgdky@guest123' | sudo -S mv /tmp/security_proxy_config /mnt/internal_storage/security_proxy_config");
        m_ntimeout = 68;
        m_ptimer->start(3000);
        break;
    case 125:
        m_gntJyXl.strCsGc = "security_proxy_config文件确认, 命令：cat /mnt/internal_storage/security_proxy_config";
        sendGcEvent();
        m_nLx = 63;
        createRemoteProcess("cat /mnt/internal_storage/security_proxy_config");
        break;
    case 126:
        m_gntJyXl.strCsGc = "证书导出确认";
        sendGcEvent();
        m_gntJyXl.strCsGc = "查询终端ID, 命令：cd /usr/sbin && ./vendor_eeprom show -a";
        sendGcEvent();
        m_nLx = 64;
        createRemoteProcess("cd /usr/sbin && ./vendor_eeprom show -a");
        break;
    case 127:
        m_nLx = 65;
        createRemoteProcess("md5sum /lib/modules/hi1711_ko/uart_drv.ko");
        m_ntimeout = 69;
        m_ptimer->start(3000);
        break;
    case 128:
        m_gntJyXl.strCsXl = m_gntJyJg.strCsXl = "终端停上电事件有效标识" ;
        m_gntJyXl.strCsGc = "设置终端停上电事件有效标识为无效";
        sendGcEvent();
        m_nJcLx = 9999;
        m_nTcp = 9;
        m_nLx = 17;
        createRemoteProcess("container config c_master_yx -p 9001:9001");
        break;
    case 129:
        m_gntJyXl.strCsXl = m_gntJyJg.strCsXl = "设置终端停上电事件有效标识" ;
        m_gntJyXl.strCsGc = "设置终端停上电事件有效标识为有效";
        sendGcEvent();
        m_nJcLx = 9999;
        m_nTcp = 10;
        m_nLx = 17;
        createRemoteProcess("container config c_master_yx -p 9001:9001");
        break;
    case 130:
        createRemoteProcess("echo 'Zgdky@guest123' | sudo -S iptables -t nat -A PREROUTING -p tcp --dport 1883 -j DNAT --to-destination **********:1883");
        m_ntimeout = 71;
        m_ptimer->start(2000);
        break;
    default:
        break;

    }
}

void CGntTest::on_ssh_error()
{
    if(m_bNodealssherr)
    {
        return;
    }

    if(m_bLock)
    {
        g_mtxCertExport.unlock();
        m_bLock = false;
    }
    if(m_nReSsh < 3 )
    {
        QThread::sleep(1);
        sshConnect();
        return;
    }
    m_gntJyXl.strCsGc = "ssh连接失败";
    QApplication::postEvent((QObject*)g_pFuncWindow, new CCsXlGcMsg(m_gntJyXl));
    m_gntJyJg.bCsXlJg = false;
    QApplication::postEvent((QObject*)g_pFuncWindow, new CCsXlJgMsg(m_gntJyJg));


    if(m_bSendSshOff)
    {
        QApplication::postEvent((QObject*)g_pFuncWindow, new CSSHOFF(m_gntParam.nBw, m_strChkItem));
    }

    if(m_strChkItem == "交采计量精度")
    {
        QApplication::postEvent((QObject*)g_pFuncWindow, new CCsTtKzMsg(0));
    }

    check();

}

void CGntTest::on_ssh_readyReadStandardOutput()
{
    QByteArray byte = sshRemote->readAllStandardOutput();
    if(m_nLx != 38 && m_nLx != 39 && m_nLx != 40 && m_nLx != 41)
    {
        sshRemote->disconnect();
        sshRemote->close();
        sshClose();
    }

    QString strByte = QString::fromUtf8(byte);
    switch (m_nLx)
    {
    case 1:
        dbbb1(strByte);
        break;
    case 2:
        dbbb2(strByte);
        break;
    case 3:
        zkb(strByte);
        break;
    case 4:
        yjb(strByte);
        break;
    case 5:
        nhb(strByte);
        break;
    case 6:
        bhb(strByte);
        break;
    case 7:
        rqb(strByte);
        break;
    case 8:
        jcb(strByte);
        break;
    case 9:
        gjxtb(strByte);
        break;
    case 10:
        czxtb(strByte);
        break;
    case 11:
        rqchk(strByte);
        break;
    case 12:
        APPchk(strByte);
        break;
    case 13:
        apprealchk(strByte);
        break;
    case 14:
        rqwApp(strByte);
        break;
    case 15:
        zdcs(strByte);
        break;
    case 16:
        zdcsjg(strByte);
        break;
    case 17:
        dk9001(strByte);
        break;
    case 18:
        ds1(strByte);
        break;
    case 19:
        ds2(strByte);
        break;
    case 20:
        tty(strByte);
        break;
    case 21:
        devctl(strByte);
        break;
    case 22:
        mk4g(strByte);
        break;
    case 23:
        dkzk(strByte);
        break;
    case 25:
        hbdy(strByte);
        break;
    case 26:
        setSW1(strByte);
        break;
    case 27:
        setSW2(strByte);
        break;
    case 28:
        mqttIot(strByte);
        break;
    case 29:
        mqttIot2(strByte);
        break;
    case 30:
        ipVerify(strByte);
        break;
    case 31:
        g4tty(strByte);
        break;
    case 32:
        devapptime(strByte);
        break;
    case 33:
        apptime(strByte);
        break;
    case 34:
        rs4851(strByte);
        break;
    case 35:
        rs485II(strByte);
        break;
    case 36:
        rs485III(strByte);
        break;
    case 37:
        rs485IV(strByte);
        break;
    case 38:
        rs4851_1(strByte);
        break;
    case 39:
        rs485II_1(strByte);
        break;
    case 40:
        rs485III_1(strByte);
        break;
    case 41:
        rs485IV_1(strByte);
        break;
    case 42:
        psdev4(strByte);
        break;
     case 43:
        fileExist(strByte);
        break;
    case 44:
        createJm(strByte);
        break;
    case 45:
        psdev(strByte);
        break;
    case 46:
        psdev2(strByte);
        break;
    case 47:
        psdev3(strByte);
        break;
    case 48:
        break;
    case 49:
        mqttIoTFile(strByte);
        break;
    case 50:
        esnQuery(strByte);
        break;
    case 51:
        verifyfile(strByte);
        break;
    case 52:
        queryEsamId(strByte);
        break;
    case 53:
        devInfoExist(strByte);
        break;
    case 54:
        licenseExist(strByte);
        break;
    case 55:
        fileClearVerify(strByte);
        break;
    case 56:
        bakFileVerify(strByte);
        break;
    case 57:
        newFileVerify(strByte);
        break;
    case 58:
        lymmVerify(strByte);
        break;
    case 59:
        lyqdVerify(strByte);
        break;
    case 60:
        oldUartdrvFile(strByte);
        break;
    case 61:
        configVerify(strByte);
        break;
    case 62:
        modifyconfig(strByte);
        break;
    case 63:
        secufileVerify(strByte);
        break;
    case 64:
        scuIdQuery(strByte);
        break;
    case 65:
        md5Verify(strByte);
        break;
    default:
        break;
    }
}

void CGntTest::on_tcp_error(QAbstractSocket::SocketError e)
{

    if(m_bNodealssherr)
    {
        m_ptimer->stop();
        m_ptimer->start(100000);
        return;
    }
    m_pSocket->disconnect();

    if(m_bLock)
    {
        g_mtxCertExport.unlock();
        m_bLock = false;
    }
    if( m_nReTcp < 3)
    {
        QThread::sleep(5);
        dk9001("no dk");
        return;
    }

    if(m_strChkItem == "用户名密码")
    {
        m_gntJyXl.strCsGc = "SCU-IP设置失败";
    }
    else if(m_strChkItem == "698参数配置")
    {
        m_gntJyXl.strCsGc = "698参数配置失败";
    }
    else
    {
        m_gntJyXl.strCsGc = "tcp连接断开";
    }

    sendGcEvent();
    m_gntJyJg.bCsXlJg = false;
    sendJgEvent();

    if(m_strChkItem == "交采计量精度")
    {
        QApplication::postEvent((QObject*)g_pFuncWindow, new CCsTtKzMsg(0));
    }

    if(m_strChkItem == "交采计量精度" || m_strChkItem == "后备电源")
        check();
    else
        checkItem();
}

void CGntTest::on_tcp_stateChanged(QAbstractSocket::SocketState state)
{
    if(state == QAbstractSocket::ConnectedState)
    {
        m_nReTcp = 0;
        if(m_strChkItem == "用户名密码")
        {
           char pcFrame[8192] = {0};
           m_oad.attr_OI = 0x4510;
           m_oad.attr_ID = 0x04;
           m_oad.attr_index = 0;
           QByteArray sa = QByteArray::fromHex("AAAAAAAAAAAA");
           int j = m_698Handle.buildSetRequestApduByOAD(pcFrame, m_oad, 1, m_gntParam.scuParam.strIp);
           m_698Handle.packagingFrame698(pcFrame, j,sa.data(), sa.size()-1);
           m_bRecv = false;
           QByteArray data(pcFrame, j);

           if(m_pSocket->write(data) > 0)
           {
               m_gntJyXl.strCsGc = "成功发送报文：" + data.toHex();
               sendGcEvent();
           }
           else
           {
               m_gntJyXl.strCsGc = "发送报文：" + data.toHex() + "失败";
               m_gntJyJg.bCsXlJg = false;
               sendGcEvent();
               sendGcEvent();
           }
           m_ntimeout = 9999;
           m_ptimer->start(10*1000);
        }
        else if(m_strChkItem == "698参数配置")
        {
            cs698Item();
        }
        else if(m_strChkItem == "数据清零")
        {
            sjqltcp();
        }
        else if(m_strChkItem == "磁场状态")
        {
            cczttcp();
        }
        else if(m_strChkItem == "终端参数")
        {
            zdcstcp();
        }
        else if(m_strChkItem == "交采计量精度")
        {
            jcjltcp();
        }
    }
    else if(state == QAbstractSocket::ClosingState)
    {
        m_pSocket->disconnect();
    }
}

void CGntTest::on_tcp_readyRead()
{
    QByteArray data = m_pSocket->readAll();
    m_gntJyXl.strCsGc = "收到报文：" + data.toHex();
    sendGcEvent();

    if(m_strChkItem == "用户名密码")
    {
        m_ptimer->stop();
        bool bSuc = true;
        if(data.size() < 22)
        {
            bSuc = false;
        }
        else
        {
            bSuc = data[21] == '\x00' ? true:false;
        }
        if(bSuc)
        {
           m_gntJyXl.strCsGc = "SCU-IP设置成功";
           sendGcEvent();
           m_gntJyJg.bCsXlJg = true;
           sendJgEvent();

           if(m_pSocket->state() == QTcpSocket::ConnectedState)
           {
               m_pSocket->close();
           }

           check();
        }
        else
        {
            m_gntJyXl.strCsGc = "SCU-IP设置失败";
            sendGcEvent();
            m_gntJyJg.bCsXlJg = false;
            sendJgEvent();
            checkItem();
        }
    }
    else if(m_strChkItem == "698参数配置")
    {
        bool bSuc = true;
        if(data.size() < 22)
        {
            bSuc = false;
        }
        else
        {
            bSuc = data[21] == '\x00' ? true:false;
        }
        if(bSuc)
        {
           m_gntJyXl.strCsGc = m_gntJyXl.strCsXl + "成功";
           sendGcEvent();
           m_gntJyJg.bCsXlJg = true;
           sendJgEvent();

           cs698Item();
        }
        else
        {
            m_bcheck = false;
            m_gntJyXl.strCsGc = m_gntJyXl.strCsXl + "失败";
            sendGcEvent();
            m_gntJyJg.bCsXlJg = false;
            sendJgEvent();

            cs698Item();
        }
    }
    else if(m_strChkItem == "数据清零")
    {
        m_ptimer->stop();
        bool bsuc = true;
        if(data.size() < 22)
        {
            bsuc = false;
        }
        else
        {
            bsuc = true;
        }

        if(bsuc)
        {
            if(data[17] =='\xf2' && data[18] == '\x08')
            {
                if(data[21] == '\x00')
                {
                    m_gntJyXl.strCsGc = "电能量清零1成功";
                    sendGcEvent();
                    m_gntJyJg.bCsXlJg = true;
                    sendJgEvent();
                }
                else
                {

                    m_gntJyXl.strCsGc = "电能量清零失败";
                    sendGcEvent();
                    m_gntJyJg.bCsXlJg = false;
                    sendJgEvent();
                }
                dk9001("no dk");
            }
            else if(data[17] == '\x90' && data[18] == '\x00' && data[19] == '\x03')
            {
                m_gntJyXl.strCsGc = "电能量清零2成功";
                sendGcEvent();
                m_gntJyJg.bCsXlJg = true;
                sendJgEvent();

                dk9001("no dk");
            }
            else if(data[17] == '\x60' && data[18] == '\x00' && data[19] == '\x86')
            {
                if(data[21] == '\x00')
                {
                    m_gntJyXl.strCsGc = "清空档案成功";
                    sendGcEvent();
                    m_gntJyJg.bCsXlJg = true;
                    sendJgEvent();
                }
                else
                {
                    m_gntJyXl.strCsGc = "清空档案失败";
                    sendGcEvent();
                    m_gntJyJg.bCsXlJg = false;
                    sendJgEvent();
                }
                dk9001("no dk");
            }
            else if(data[17] == '\x60' && data[18] == '\x12' && data[19] == '\x81')
            {
                if(data[21] == '\x00')
                {
                    m_gntJyXl.strCsGc = "清空任务成功";
                    sendGcEvent();
                    m_gntJyJg.bCsXlJg = true;
                    sendJgEvent();
                }
                else
                {
                    m_gntJyXl.strCsGc = "清空任务失败";
                    sendGcEvent();
                    m_gntJyJg.bCsXlJg = false;
                    sendJgEvent();
                }
                dk9001("no dk");
            }
            else if(data[17] == '\x60' && data[18] == '\x14' && data[19] == '\x81')
            {
                if(data[21] == '\x00')
                {
                    m_gntJyXl.strCsGc = "清空方案成功";
                    sendGcEvent();
                    m_gntJyJg.bCsXlJg = true;
                    sendJgEvent();
                }
                else
                {
                    m_gntJyXl.strCsGc = "清空方案失败";
                    sendGcEvent();
                    m_gntJyJg.bCsXlJg = false;
                    sendJgEvent();
                }

                if(m_bsjqlcheck)
                {
                    check();
                }
                else
                {
                    checkItem();
                }
            }
        }
        else
        {
            m_gntJyXl.strCsGc = "数据清零失败";
            sendGcEvent();
            m_gntJyJg.bCsXlJg = false;
            sendJgEvent();

            checkItem();
        }
    }

    else if(m_strChkItem == "磁场状态")
    {
        m_ptimer->stop();
        if(data[17] == '\x30' && data[18] == '\x2a' && data[19] == '\x04')
        {
            if(data[23] == '\x00' && data[24] == '\x00')
            {
                m_gntJyXl.strCsGc = "磁场状态正常";
                sendGcEvent();
                m_gntJyJg.bCsXlJg = true;
                sendJgEvent();

                check();
            }
            else
            {
                m_gntJyXl.strCsGc = "磁场状态异常";
                sendGcEvent();
                m_gntJyJg.bCsXlJg = false;
                sendJgEvent();

                checkItem();
            }
        }
    }
    else if(m_strChkItem == "终端参数")
    {
        if(data[17] == '\x40' && data[18] == '\x01' && data[19] == '\x02')
        {
            if(data[21] == '\x00')
            {
                m_gntJyXl.strCsGc = "通信地址设置成功";
                sendGcEvent();
            }
            else
            {
                m_bcheck = false;
                m_gntJyXl.strCsGc = "通信地址设置失败";
                sendGcEvent();
            }
            m_gntJyJg.bCsXlJg = m_bcheck;
            sendJgEvent();

            if(m_bcheck)
            {
                check();
                return;
            }
            else
            {
                checkItem();
                return;
            }
        }
    }
    else if(m_strChkItem == "交采计量精度")
    {
        m_ptimer->stop();

        if(m_pSocket->state() == QTcpSocket::ConnectedState)
        {
            m_pSocket->close();
        }

        if(data[17] == '\x20' && data[18] == '\x00' && data[19] == '\x02')      // 电压
        {
            jcjldy(data);
        }
        else if(data[17] == '\x20' && data[18] == '\x01' && data[19] == '\x02')     // 电流
        {
            jcjldl(data);
        }
        else if(data[17] == '\x20' && data[18] == '\x01' && data[19] == '\x04')     // 零线电流
        {
            jcjllxdl(data);
        }
        else if(data[17] == '\x20' && data[18] == '\x0a' && data[19] == '\x02')     // 功率因数
        {
            jcjlglys(data);
        }
        else if(data[17] == '\x20' && data[18] == '\x04' && data[19] == '\x02')     // 有功功率
        {
            jcjlyg(data);
        }
        else if(data[17] == '\x20' && data[18] == '\x05' && data[19] == '\x02')     // 无功功率
        {
            jcjlwg(data);
        }
        else if(data[17] == '\x31' && data[18] == '\x06' && data[19] == '\x09')    // 终端有效标识
        {
            zdyxbs(data);
        }

    }
}

void CGntTest::on_time_timeout()
{
    m_ptimer->stop();

    int num = 0;
    bool byx = true;
    switch(m_ntimeout)
    {
    case 1:
        m_gntJyXl.strCsGc = "清除文件成功";
        sendGcEvent();

        m_nsshLj = 107;
        sshConnect();
        return;
    case 2:
        m_gntJyXl.strCsGc = "清除文件命令下发成功";
        sendGcEvent();

        // 文件清除确认
        m_nsshLj = 107;
        sshConnect();
        return;

    case 3:
        sshClose();
        if(m_nAppNum < 20)
        {
            QThread::sleep(2);
            m_nsshLj = 106;
            sshConnect();
            return;
        }

        m_gntJyXl.strCsGc = "获取失败";
        sendGcEvent();

        m_bcheck = false;
        if(m_vtCompareApp.size() == 0)
        {
            m_gntJyJg.bCsXlJg = m_bcheck;
            sendJgEvent();

            rqwappchk();
        }
        else
        {
            m_nsshLj = 48;
            sshConnect();
        }
        return;
    case 4:
        sshClose();

        if(!m_zrsdata.trimmed().contains("4851"))
        {
            if(m_nRS485Num < 50)
            {
                m_nsshLj = 49;
                sshConnect();
            }
            else
            {
                m_nRS485Num = 0;
                m_gntJyXl.strCsGc = "串口收到数据：" + m_zrsdata.trimmed()  + "中不包含SCU发送数据4851，失败";
                sendGcEvent();

                m_gntJyJg.bCsXlJg = false;
                sendJgEvent();

                m_nsshLj = 51;
                sshConnect();
            }

        }
        else
        {
            m_nRS485Num = 0;
            m_gntJyXl.strCsGc = "串口收到数据中包含SCU发送数据：4851，成功";
            sendGcEvent();

            m_nsshLj = 58;
            sshConnect();
        }

        return;
    case 5:
        sshClose();

        if(!m_zrsdata.trimmed().contains("4852"))
        {
            if(m_nRS485Num < 50)
            {
                m_nsshLj = 51;
                sshConnect();
            }
            else
            {
                m_nRS485Num = 0;
                m_gntJyXl.strCsGc = "串口收到数据：" + m_zrsdata.trimmed()  + "中不包含SCU发送数据：4852，失败";
                sendGcEvent();

                m_gntJyJg.bCsXlJg = false;
                sendJgEvent();

                m_nsshLj = 53;
                sshConnect();
            }

        }
        else
        {
            m_nRS485Num = 0;
            m_gntJyXl.strCsGc = "串口收到数据中包含SCU发送数据：4852，成功";
            sendGcEvent();

            m_Ck.clear();
            m_nsshLj = 59;
            sshConnect();

        }

        return;
    case 6:
        sshClose();

        if(!m_zrsdata.trimmed().contains("4853"))
        {
            if(m_nRS485Num < 50)
            {
                m_nsshLj = 53;
                sshConnect();
            }
            else
            {
                m_nRS485Num = 0;
                m_gntJyXl.strCsGc = "串口收到数据：" + m_zrsdata.trimmed()  + "不包含SCU发送数据:4853，失败";
                sendGcEvent();

                m_gntJyJg.bCsXlJg = false;
                sendJgEvent();

                m_nsshLj = 55;
                sshConnect();
            }

        }
        else
        {
            m_nRS485Num = 0;
            m_gntJyXl.strCsGc = "串口收到数据中包含SCU发送数据：4853，成功";
            sendGcEvent();

            m_Ck.clear();
            m_nsshLj = 60;
            sshConnect();

        }

        return;
    case 7:
        sshClose();

        if(!m_zrsdata.trimmed().contains("4854"))
        {
            if(m_nRS485Num < 50)
            {
                m_nsshLj = 55;
                sshConnect();
            }
            else
            {
                m_nRS485Num = 0;
                m_gntJyXl.strCsGc = "串口收到数据：" + m_zrsdata.trimmed()  + "不包含SCU发送数据4854，失败";
                sendGcEvent();

                m_gntJyJg.bCsXlJg = false;
                sendJgEvent();

                m_pRs485->close();

                check();
            }

        }
        else
        {
            m_nRS485Num = 0;
            m_gntJyXl.strCsGc = "串口收到数据中包含SCU发送数据：4854，成功";
            sendGcEvent();

            m_Ck.clear();
            m_nsshLj = 61;
            sshConnect();
        }

        return;

    case 8:
        ++m_nYxChkNum;
        m_mapYx.clear();
        m_gntJyXl.strCsXl = m_gntJyJg.strCsXl = "第一路遥信";
        m_gntJyXl.strCsGc = "触发四路遥信";
        sendGcEvent();
        if(m_pclient->state() != QMqttClient::Connected)
        {
            m_pclient->setHostname(m_gntParam.scuParam.strIp);
            m_pclient->setPort(1883);
            m_pclient->setUsername("admin");
            m_pclient->setPassword("123456");
            m_pclient->connectToHost();
            int nLen = 0;
            while(nLen < 40)
            {
                QApplication::processEvents();
                QThread::msleep(300);
                ++nLen;
                if(m_pclient->state() == QMqttClient::Connected)
                {
                    QApplication::processEvents();
                    break;
                }
            }
        }
        if(m_pclient->state() != QMqttClient::Connected)
        {
            m_gntJyXl.strCsGc = "连接mqtt失败";
            sendGcEvent();
        }
        else
        {
            m_gntJyXl.strCsGc = "mqtt已连接";
            sendGcEvent();

            m_pclient->unsubscribe(QString("rspSample/Broadcast/JSON/report/notification/chgEvent"));
            QThread::msleep(500);
            m_pclient->subscribe(QString("rspSample/Broadcast/JSON/report/notification/chgEvent"));
            QApplication::processEvents();
        }
        QApplication::postEvent((QObject*)g_pFuncWindow, new CCsYxMsg(m_nYxChkNum));
        m_bSendSshOff = false;

        m_ntimeout2 = 5;
        m_ptimer2->start(3000);

        return;

    case 9:
        if(m_mapYx.find(1) != m_mapYx.end())
        {
            m_bYx1Chk[m_nYxChkNum] = true;
        }
        else
        {
            m_bYx1Chk[m_nYxChkNum] =  false;
        }

        if(m_mapYx.find(2) != m_mapYx.end())
        {
            m_bYx2Chk[m_nYxChkNum] =  true;
        }
        else
        {
            m_bYx2Chk[m_nYxChkNum] =  false;
        }

        if(m_mapYx.find(3) != m_mapYx.end())
        {
           m_bYx3Chk[m_nYxChkNum] =  true;
        }
        else
        {
            m_bYx3Chk[m_nYxChkNum] =  false;
        }
        if(m_mapYx.find(4) != m_mapYx.end())
        {
            m_bYx4Chk[m_nYxChkNum] =  true;
        }
        else
        {
            m_bYx4Chk[m_nYxChkNum] = false;
        }

        if(m_nYxChkNum < 10)
        {
            m_ntimeout = 8;
            m_ptimer->start(1000);
            return;
        }
        else
        {
            if(m_bYx1Chk[1] || m_bYx1Chk[2] || m_bYx1Chk[3] || m_bYx1Chk[4] || m_bYx1Chk[5] || m_bYx1Chk[6] || m_bYx1Chk[7] || m_bYx1Chk[8] || m_bYx1Chk[9] || m_bYx1Chk[10])
            {
                m_gntJyXl.strCsGc = "收到第一路遥信";
                sendGcEvent();
                m_gntJyJg.bCsXlJg = true;
                sendJgEvent();
            }
            else
            {
                m_gntJyXl.strCsGc = "未收到第一路遥信";
                sendGcEvent();
                m_gntJyJg.bCsXlJg = false;
                sendJgEvent();
            }
            m_gntJyXl.strCsXl = m_gntJyJg.strCsXl = "第二路遥信";
            m_gntJyXl.strCsGc = "触发第二路遥信";
            sendGcEvent();
            if(m_bYx2Chk[1] || m_bYx2Chk[2] || m_bYx2Chk[3] || m_bYx2Chk[4] || m_bYx2Chk[5] || m_bYx2Chk[6] || m_bYx2Chk[7] || m_bYx2Chk[8] || m_bYx2Chk[9] || m_bYx2Chk[10])
            {
                m_gntJyXl.strCsGc = "收到第二路遥信";
                sendGcEvent();
                m_gntJyJg.bCsXlJg = true;
                sendJgEvent();
            }
            else
            {
                m_gntJyXl.strCsGc = "未收到第二路遥信";
                sendGcEvent();
                m_gntJyJg.bCsXlJg = false;
                sendJgEvent();
            }

            m_gntJyXl.strCsXl = m_gntJyJg.strCsXl = "第三路遥信";
            m_gntJyXl.strCsGc = "触发第三路遥信";
            sendGcEvent();
            if(m_bYx3Chk[1] || m_bYx3Chk[2] || m_bYx3Chk[3]  || m_bYx3Chk[4]  || m_bYx3Chk[5] || m_bYx3Chk[6] || m_bYx3Chk[7] || m_bYx3Chk[8]  || m_bYx3Chk[9]  || m_bYx3Chk[10])
            {
                m_gntJyXl.strCsGc = "收到第三路遥信";
                sendGcEvent();
                m_gntJyJg.bCsXlJg = true;
                sendJgEvent();
            }
            else
            {
                m_gntJyXl.strCsGc = "未收到第三路遥信";
                sendGcEvent();
                m_gntJyJg.bCsXlJg = false;
                sendJgEvent();
            }
            m_gntJyXl.strCsXl = m_gntJyJg.strCsXl = "第四路遥信";
            m_gntJyXl.strCsGc = "触发第四路遥信";
            sendGcEvent();
            if(m_bYx4Chk[1] || m_bYx4Chk[2] || m_bYx4Chk[3] || m_bYx4Chk[4] || m_bYx4Chk[5] || m_bYx4Chk[6] || m_bYx4Chk[7] || m_bYx4Chk[8] || m_bYx4Chk[9] || m_bYx4Chk[10])
            {
                m_gntJyXl.strCsGc = "收到第四路遥信";
                sendGcEvent();
                m_gntJyJg.bCsXlJg = true;
                sendJgEvent();
            }
            else
            {
                m_gntJyXl.strCsGc = "未收到第四路遥信";
                sendGcEvent();
                m_gntJyJg.bCsXlJg = false;
                sendJgEvent();
            }
        }
        m_pclient->unsubscribe(QString("rspSample/Broadcast/JSON/report/notification/chgEvent"));
        check();
        return;
    case 11:
        m_gntJyJg.bCsXlJg = false;
        sendJgEvent();
        return;
    case 12:
        m_ntimeout2 = 1;
        ++m_nRS485Num ;
        m_pRs485->write("48514851485148514851485148514851485148514851");
        m_pRs485->waitForBytesWritten(500);
        m_ptimer2->start(2000);
        return;
    case 13:
        m_ntimeout2 = 2;
        ++m_nRS485Num ;

        m_pRs485->write("48524852485248524852485248524852485248524852");
        m_pRs485->waitForBytesWritten(500);

        m_ptimer2->start(2000);
        return;
    case 14:
        m_ntimeout2 = 3;
        ++m_nRS485Num ;

        m_pRs485->write("48534853485348534853485348534853485348534853");
        m_pRs485->waitForBytesWritten(500);

        m_ptimer2->start(2000);
        return;
    case 15:
        m_ntimeout2 = 4;
        ++m_nRS485Num ;

        m_pRs485->write("48544854485448544854485448544854485448544854");
        m_pRs485->waitForBytesWritten(500);


        m_ptimer2->start(2000);
        return;

    case 16:

        ++m_nRtcChkNum;
        if(m_pclient->state() != QMqttClient::Connected)
        {
            m_pclient->setHostname(m_gntParam.scuParam.strIp);
            m_pclient->setPort(1883);
            m_pclient->setUsername("admin");
            m_pclient->setPassword("123456");
            m_pclient->connectToHost();
            int nLen = 0;
            while(nLen < 40)
            {
                QApplication::processEvents();
                QThread::msleep(300);
                ++nLen;
                if(m_pclient->state() == QMqttClient::Connected)
                {
                    QApplication::processEvents();
                    break;
                }
            }
        }
        if(m_pclient->state() != QMqttClient::Connected)
        {
            m_gntJyXl.strCsGc = "连接mqtt失败";
            sendGcEvent();
        }
        else
        {
            m_gntJyXl.strCsGc = "mqtt已连接";
            sendGcEvent();
            m_pclient->unsubscribe(QString("rspSample/Broadcast/JSON/report/notification/chgEvent"));
            m_pclient->subscribe(QString("rspSample/Broadcast/JSON/report/notification/chgEvent"));
            QApplication::processEvents();
        }
        m_dtZdRtc[m_nRtcChkNum].setDate(QDate(0,0,0));

        // rtc时间
        QApplication::postEvent((QObject*)g_pFuncWindow, new CRtcMsg(m_nRtcChkNum));
        m_bSendSshOff = false;

        while (!g_bRtcYx[m_nRtcChkNum])
        {
            QApplication::processEvents();
            if(!g_bTestSign)
            {
                check();
                return;
            }
            QThread::msleep(100);
        }

        num = 0;
        while (num < 30)
        {
            QApplication::processEvents();
            QThread::msleep(100);
            ++num;
        }

        if(m_nRtcChkNum < 3)
        {
            m_ntimeout = 16;
            m_ptimer->start(1000);
            return;
        }

        if(m_dtZdRtc[1].date().year() == 0 && m_dtZdRtc[1].date().month() == 0 && m_dtZdRtc[1].date().day() == 0
              &&  m_dtZdRtc[2].date().year() == 0 && m_dtZdRtc[2].date().month() == 0 && m_dtZdRtc[2].date().day() == 0
              &&  m_dtZdRtc[3].date().year() == 0 && m_dtZdRtc[3].date().month() == 0 && m_dtZdRtc[3].date().day() == 0)
        {
            m_gntJyXl.strCsGc = "获取rtc时间失败";
            sendGcEvent();
            m_gntJyJg.bCsXlJg = false;
            sendJgEvent();
        }
        else
        {
            if(qAbs(m_dtSys[1].secsTo(m_dtZdRtc[1])) < m_gntParam.other.frtcwc)
            {
                QString s1 = m_dtZdRtc[1].toString("yyyy-MM-dd HH:mm:ss");
                QString s2 = m_dtSys[1].toString("yyyy-MM-dd HH:mm:ss");
                m_gntJyXl.strCsGc = "rtc时间：" + s1;
                sendGcEvent();
                m_gntJyXl.strCsGc = "系统时间：" + s2;
                sendGcEvent();
                m_gntJyXl.strCsGc = "误差小于规定值：" + QString::number(m_gntParam.other.frtcwc);
                sendGcEvent();
                m_gntJyJg.bCsXlJg = true;
                sendJgEvent();
            }
            else if(qAbs(m_dtSys[2].secsTo(m_dtZdRtc[2])) < m_gntParam.other.frtcwc)
            {
                QString s1 = m_dtZdRtc[2].toString("yyyy-MM-dd HH:mm:ss");
                QString s2 = m_dtSys[2].toString("yyyy-MM-dd HH:mm:ss");
                m_gntJyXl.strCsGc = "rtc时间：" + s1;
                sendGcEvent();
                m_gntJyXl.strCsGc = "系统时间：" + s2;
                sendGcEvent();
                m_gntJyXl.strCsGc = "误差小于规定值：" + QString::number(m_gntParam.other.frtcwc);
                sendGcEvent();
                m_gntJyJg.bCsXlJg = true;
                sendJgEvent();
            }
            else if(qAbs(m_dtSys[3].secsTo(m_dtZdRtc[3])) < m_gntParam.other.frtcwc)
            {
                QString s1 = m_dtZdRtc[3].toString("yyyy-MM-dd HH:mm:ss");
                QString s2 = m_dtSys[3].toString("yyyy-MM-dd HH:mm:ss");
                m_gntJyXl.strCsGc = "rtc时间：" + s1;
                sendGcEvent();
                m_gntJyXl.strCsGc = "系统时间：" + s2;
                sendGcEvent();
                m_gntJyXl.strCsGc = "误差小于规定值：" + QString::number(m_gntParam.other.frtcwc);
                sendGcEvent();
                m_gntJyJg.bCsXlJg = true;
                sendJgEvent();
            }
            else
            {
                QString s1 = m_dtZdRtc[1].toString("yyyy-MM-dd HH:mm:ss");
                QString s2 = m_dtSys[1].toString("yyyy-MM-dd HH:mm:ss");
                m_gntJyXl.strCsGc = "rtc时间：" + s1;
                sendGcEvent();
                m_gntJyXl.strCsGc = "系统时间：" + s2;
                sendGcEvent();
                m_gntJyXl.strCsGc = "误差不小于规定值：" + QString::number(m_gntParam.other.frtcwc);
                sendGcEvent();
                m_gntJyJg.bCsXlJg = false;
                sendJgEvent();
            }
        }
        m_pclient->unsubscribe(QString("rspSample/Broadcast/JSON/report/notification/chgEvent"));

        m_nsshLj = 23;
        sshConnect();
        return;
    case 17:
        m_nsshLj = 87;
        sshConnect();

        return;
    case 18:
        sshClose();
        //发送开始导出标志
        if(m_strChkItem == "证书导出")
        {
             QApplication::postEvent((QObject*)g_pFuncWindow, new CNormalMsg(7, m_gntParam.nBw));
             while (!g_bstartExport)
             {
                 if(!g_bTestSign)
                 {
                     check();
                     return;
                 }
                 QThread::msleep(100);
             }
        }
        else
        {
            QApplication::postEvent((QObject*)g_pFuncWindow, new CNormalMsg(8, m_gntParam.nBw));
            while (!g_bstartImport)
            {
                if(!g_bTestSign)
                {
                    check();
                    return;
                }
                QThread::msleep(100);
            }
        }

        g_mtxCertExport.lock();         // 上锁， 导出成功之后解锁
        m_bLock = true;
        g_bCertExport = false;
        QApplication::postEvent((QObject*)g_pFuncWindow, new CErtExportMsg());
        while(!g_bCertExport)
        {
            if(!g_bTestSign)
            {
                g_mtxCertExport.unlock();
                m_bLock = false;
                check();
                return;
            }
            QThread::msleep(100);
        }


        m_nsshLj = 67;
        sshConnect();
        return;
    case 19:
        m_nsshLj = 70;
        sshConnect();
        return;
    case 20:
        m_nsshLj = 71;
        sshConnect();
        return;
    case 21:
        m_nsshLj = 72;
        sshConnect();
        return;
    case 22:
        m_nsshLj = 75;
        sshConnect();
       return;;
    case 23:
        sshClose();
        g_mtxCertExport.unlock();
        m_bLock = false;
        check();
        return;
    case 24:
        m_nsshLj = 77;
        sshConnect();
        return;
    case 25:
        m_nsshLj = 78;
        sshConnect();
        return;
    case 26:
    {
        sshClose();
        ++m_nstartchecknum;
        QThread::sleep(3);

        if(g_btcpserver)
        {
            if(m_strChkItem == "证书导出")
            {
                QApplication::postEvent((QObject*)g_pFuncWindow, new CNormalMsg(1, m_gntParam.nBw));
            }
            else
            {
                QApplication::postEvent((QObject*)g_pFuncWindow, new CNormalMsg(6, m_gntParam.nBw));
            }

            while (!g_bSingleExportFinish)
            {
                QThread::msleep(30);
            }
            g_bSingleExportFinish = false;

            m_nsshLj = 81;
            sshConnect();
        }
        else
        {
            if(m_nstartchecknum < 15)
            {
                m_ntimeout = 26;
                m_ptimer->start(5000);
            }
            else
            {
                g_bqueryLj = false;
                g_mtxCertExport.unlock();         // 解锁
                m_bLock = false;
                m_gntJyXl.strCsGc = "证书导出程序与SCU连接失败";
                sendGcEvent();
                m_gntJyJg.bCsXlJg = false;
                sendJgEvent();
                check();
            }
        }
    }
        return;
    case 27:
        m_nsshLj = 82;
        sshConnect();
        return;
    case 28:
        QThread::sleep(10);
        m_nsshLj = 85;
        sshConnect();
        return;
    case 29:
        m_nsshLj = 86;
        sshConnect();
        return;
    case 30:
        m_nsshLj = 88;
        sshConnect();
        return;
    case 31:
         m_nsshLj = 64;
         sshConnect();
        return;
    case 32:
        sshClose();
        QApplication::postEvent((QObject*)g_pFuncWindow, new CNormalMsg(2, m_gntParam.nBw));
        g_mtxCertExport.unlock();
        m_bLock = false;
        if(m_strChkItem == "证书导出")
        {
            m_nsshLj = 126;
            sshConnect();
        }
        else
        {
            check();
        }

        return;
    case 33:
        m_bNodealssherr = false;
        m_nsshLj = 63;
        sshConnect();
        return;
    case 34:
        if(!g_bTestSign)
        {
            check();
            return;
        }
        m_nsshLj = 90;
        sshConnect();
        return;
    case 35:
        m_gntJyXl.strCsGc = "文件权限设置成功";
        sendGcEvent();

        m_gntJyXl.strCsGc = "读取SCMQTTIoT/configFile/paramFile.json文件内容";
        sendGcEvent();

        m_nsshLj = 92;
        sshConnect();
        return;
    case 36:
        m_nsshLj = 79;
        sshConnect();
        return;
    case 37:
        m_nsshLj = 65;
        sshConnect();
        return;
    case 38:
        m_nsshLj = 74;
        sshConnect();
        return;
    case 39:
        m_nsshLj = 72;
        sshConnect();
        return;
    case 40:
        m_nsshLj = 83;
        sshConnect();
        return;
    case 41:
        m_nsshLj = 100;
        sshConnect();
        return;
    case 42:
        m_nsshLj = 97;
        sshConnect();
        return;
    case 43:
        m_nsshLj = 99;
        sshConnect();
        return;
    case 44:
        m_nsshLj = 102;
        sshConnect();
        return;
    case 45:
        m_nsshLj = 103;
        sshConnect();
        return;
    case 46:
        m_gntJyXl.strCsGc = "未找到文件，失败";
        sendGcEvent();
        m_gntJyJg.bCsXlJg = false;
        sendJgEvent();
        check();
        return;
    case 47:
        m_nsshLj = 105;
        sshConnect();
        m_ntimeout = 49;
        m_ptimer->start(5000);
        return;
    case 48:
        check();
        return;
    case 49:
        m_gntJyXl.strCsGc = "重启失败";
        sendGcEvent();
        m_gntJyJg.bCsXlJg = false;
        sendJgEvent();
        check();
        return;
    case 50:
        m_nsshLj = 108;
        sshConnect();
        return;
    case 51:
        m_nsshLj = 110;
        sshConnect();
        return;
    case 53:
        m_gntJyXl.strCsGc = "未找到备份文件， 失败";
        sendGcEvent();
        m_gntJyJg.bCsXlJg = false;
        sendJgEvent();
        checkItem();
        return;
    case 54:
        m_nsshLj = 112;
        sshConnect();
        return;
    case 55:
        m_gntJyXl.strCsGc = "未找到文件， 失败";
        sendGcEvent();
        m_gntJyJg.bCsXlJg = false;
        sendJgEvent();
        check();
        return;
    case 56:
        m_bNodealssherr = false;
        m_nsshLj = 114;
        sshConnect();
        return;
    case 57:
        m_nsshLj = 115;
        sshConnect();
        return;
    case 58:
        if(m_nLymm < 8)
        {
            m_nsshLj = 114;
            sshConnect();
        }
        else
        {
            m_gntJyXl.strCsGc = "查询失败";
            sendGcEvent();
            m_gntJyJg.bCsXlJg = false;
            sendJgEvent();
            m_nsshLj = 117;
            sshConnect();
        }

        return;
    case 59:
        m_gntJyXl.strCsGc = "串口驱动恢复确认";
        sendGcEvent();
        m_nsshLj = 118;
        sshConnect();
        return;
    case 60:

        m_gntJyXl.strCsGc = "成功";
        sendGcEvent();
        m_gntJyXl.strCsGc = "蓝牙驱动文件hash值确认， 命令：md5sum /lib/modules/hi1711_ko/uart_drv.ko";
        sendGcEvent();
        m_nsshLj = 127;
        sshConnect();
        return;
    case 61:
    {
        ++ m_nlytxnum;
        m_gntJyJg.strCsXl = m_gntJyXl.strCsXl = "蓝牙通信";
        m_gntJyXl.strCsGc = "开始检测";
        sendGcEvent();

        QApplication::postEvent((QObject*)g_pFuncWindow, new CCsLyMsg(m_nlytxnum));
        m_bSendSshOff = false;

        while(!g_bLyQuery[m_nlytxnum])
        {
            if(!g_bTestSign)
            {
                check();
                return;
            }
            QThread::msleep(500);
        }

        g_mtx.lock();
        std::map<QString,int>mapLyName= g_mapLyName;
        g_mtx.unlock();

        QString strLyName = m_gntParam.scuParam.strLjdz.right(3);

        if(mapLyName.find(strLyName) != mapLyName.end())
        {
            m_blytx[m_nlytxnum] = true;
        }


        if(m_nlytxnum < 8)
        {
            m_ntimeout = 61;
            m_ptimer->start(2000);
            return;
        }

        if(m_blytx[3] || m_blytx[1] || m_blytx[2] || m_blytx[4] || m_blytx[5] || m_blytx[6] || m_blytx[7] || m_blytx[8])
        {
            m_gntJyXl.strCsGc = "找到蓝牙名称：" + strLyName;
            sendGcEvent();
            m_gntJyJg.bCsXlJg = true;
            sendJgEvent();
        }
        else
        {
            m_gntJyXl.strCsGc = "未找到蓝牙名称：" + strLyName;
            sendGcEvent();
            m_gntJyJg.bCsXlJg = false;
            sendJgEvent();
        }

        check();
        return;
    }
    case 63:
        m_nsshLj = 120;
        sshConnect();
        return;
    case 64:
        m_gntJyJg.strCsXl = m_gntJyXl.strCsXl = "蓝牙密码验证";
        m_gntJyXl.strCsGc = "蓝牙密码清空校验";
        sendGcEvent();
        m_nsshLj = 121;
        sshConnect();
        return;
    case 65:
        m_gntJyXl.strCsGc = "文件不存在";
        sendGcEvent();
        m_nsshLj = 109;
        sshConnect();
        return;
    case 66:
        m_gntJyXl.strCsGc = "完成修改";
        sendGcEvent();
        m_nsshLj = 123;
        sshConnect();
        return;
    case 67:
        if(m_nLymm <4)
        {
            m_nsshLj = 122;
            sshConnect();
        }
        else
        {
            m_gntJyXl.strCsGc = "修改失败";
            sendGcEvent();
            m_gntJyJg.bCsXlJg = false;
            sendJgEvent();
            check();
        }
        return;
    case 68:
        m_nsshLj = 123;
        sshConnect();
        return;
    case 69:
        sshClose();
        m_gntJyXl.strCsGc = "hash确认失败";
        sendGcEvent();
        m_gntJyJg.bCsXlJg = false;
        sendJgEvent();
        m_ntimeout = 61;
        m_ptimer->start(1000);
        return;
    case 70:
        m_gntJyJg.strCsXl = m_gntJyXl.strCsXl = "设置标准源";
        m_gntJyXl.strCsGc = "设置标准源";
        sendGcEvent();
        m_nJcLx = 0;
        QApplication::postEvent((QObject*)g_pFuncWindow, new CCsJcjlMsg(0));
        while(!g_bJcjl[m_nJcLx])
        {
            if(!g_bTestSign)
            {
                check();
                return;
            }
            QThread::msleep(500);
        }

        m_gntJyJg.bCsXlJg = true;
        sendJgEvent();

        m_gntJyJg.strCsXl = m_gntJyXl.strCsXl = "U=1，I=1，φ=60，H=50";
        m_gntJyXl.strCsGc = "打开9001端口. 命令：container config c_master_yx -p 9001:9001";
        sendGcEvent();
        m_bcheck = true;

        m_nTcp = 0;
        m_nsshLj = 40;
        sshConnect();
        return;
    case 71:
    {
        sshClose();
        if(m_pclient->state() != QMqttClient::Connected)
        {
            m_pclient->setHostname(m_gntParam.scuParam.strIp);
            m_pclient->setPort(1883);
            m_pclient->setUsername("admin");
            m_pclient->setPassword("123456");
            m_pclient->connectToHost();
            int nLen = 0;
            while(nLen < 40)
            {
                QApplication::processEvents();
                QThread::msleep(300);
                ++nLen;
                if(m_pclient->state() == QMqttClient::Connected)
                {
                    QApplication::processEvents();
                    break;
                }
            }
        }
        if(m_pclient->state() != QMqttClient::Connected)
        {
            m_gntJyXl.strCsGc = "连接mqtt失败";
            sendGcEvent();
            m_gntJyJg.bCsXlJg = false;
            sendJgEvent();
            checkItem();
            return;
        }
        else
        {
            m_pclient->unsubscribe(QString("acMeter/dataCenter/JSON/set/request/TTU/TTU_f833b65f53e20695"));
            m_pclient->subscribe(QString("acMeter/dataCenter/JSON/set/request/TTU/TTU_f833b65f53e20695"));
            QApplication::processEvents();
        }

        m_gntJyXl.strCsGc = "等待设置检查环境Ua=200V,Ub=220V,Uc=240V,Ia=3A,Ib=4A,Ic=5A";
        sendGcEvent();

        QApplication::postEvent((QObject*)g_pFuncWindow, new CNormalMsg(9, m_gntParam.nBw));
        while (!g_blxdldy[0])
        {
            if(!g_bTestSign)
            {
                check();
                return;
            }
            QThread::msleep(500);
        }
        int num = 0;
        while(num < 20)
        {
            if(!g_bTestSign)
            {
                check();
                return;
            }
            QThread::sleep(1);
            ++num;
        }

        m_gntJyXl.strCsGc = "检查环境设置完成";
        sendGcEvent();

        bool blxV = false;
        bool blxI = false;
        float flxV  = 0.f;
        float flxI = 0.f;
        float fbzV = 11.547;
        float fbzI = 0.577;
        for(int num = 0; num < 6; ++num)
        {
            int count = 0;
            while (count < 6)
            {
                QThread::msleep(500);
                QApplication::processEvents();
                ++count   ;
            }

            for(int m = 0; m < m_bodyary.size(); ++m)
            {
                QJsonObject qjson = m_bodyary[m].toObject();
                if(qjson["name"] == "SeqV_c0")
                {
                    flxV = qjson["val"].toString().toFloat();
                    flxV /= 3;
                    if(flxV >= fbzV *(1 - 0.02) && flxV <= fbzV *(1 + 0.02))
                    {
                        blxV = true;
                    }
                }
                else if(qjson["name"] == "SeqA_c0")
                {
                    flxI = qjson["val"].toString().toFloat();
                    flxI /= 3;
                    if(flxI >= fbzI * (1-0.02) && flxI <= fbzI * (1 + 0.02))
                    {
                        blxI = true;
                    }
                    break;
                }
            }

            if(blxI && blxV)
            {
                break;
            }
        }

        if(blxI && blxV)
        {
            m_gntJyXl.strCsGc = "零序电压："+QString::number(flxV) + ", 零序电流：" + QString::number(flxI) + ", 满足要求";
            sendGcEvent();
            m_gntJyJg.bCsXlJg = true;
            sendJgEvent();
        }
        else
        {
            m_gntJyXl.strCsGc = "零序电压："+QString::number(flxV) + ", 零序电流：" + QString::number(flxI) + ", 不满足要求";
            sendGcEvent();
            m_gntJyJg.bCsXlJg = false;
            sendJgEvent();
        }
        m_gntJyXl.strCsXl = m_gntJyJg.strCsXl = "零序2";
        m_gntJyXl.strCsGc = "等待设置检查环境Ua=176V,Ub=220V,Uc=240V,Ia=3A,Ib=4A,Ic=5A";
        sendGcEvent();

        QApplication::postEvent((QObject*)g_pFuncWindow, new CNormalMsg(10, m_gntParam.nBw));
        while (!g_blxdldy[1])
        {
            if(!g_bTestSign)
            {
                check();
                return;
            }
            QThread::msleep(500);
        }
        num = 0;
        while(num < 20)
        {
            if(!g_bTestSign)
            {
                check();
                return;
            }
            QThread::sleep(1);
            ++num;
        }

        m_gntJyXl.strCsGc = "检查环境设置完成";
        sendGcEvent();


        blxV = false;
        blxI = false;
        flxV  = 0.f;
        flxI = 0.f;
        fbzV = 18.903;
        fbzI = 0.577;
        for(int num = 0; num < 6; ++num)
        {
            int count = 0;
            while (count < 6)
            {
                QThread::msleep(500);
                QApplication::processEvents();
                ++count   ;
            }

            for(int m = 0; m < m_bodyary.size(); ++m)
            {
                QJsonObject qjson = m_bodyary[m].toObject();
                if(qjson["name"] == "SeqV_c0")
                {
                    flxV = qjson["val"].toString().toFloat();
                    flxV /= 3;
                    if(flxV >= fbzV *(1 - 0.02) && flxV <= fbzV *(1 + 0.02))
                    {
                        blxV = true;
                    }
                }
                else if(qjson["name"] == "SeqA_c0")
                {
                    flxI = qjson["val"].toString().toFloat();
                    flxI /= 3;
                    if(flxI >= fbzI * (1-0.02) && flxI <= fbzI * (1 + 0.02))
                    {
                        blxI = true;
                    }
                    break;
                }
            }

            if(blxI && blxV)
            {
                break;
            }
        }

        if(blxI && blxV)
        {
            m_gntJyXl.strCsGc = "零序电压："+QString::number(flxV) + ", 零序电流：" + QString::number(flxI) + ", 满足要求";
            sendGcEvent();
            m_gntJyJg.bCsXlJg = true;
            sendJgEvent();
        }
        else
        {
            m_gntJyXl.strCsGc = "零序电压："+QString::number(flxV) + ", 零序电流：" + QString::number(flxI) + ", 不满足要求";
            sendGcEvent();
            m_gntJyJg.bCsXlJg = false;
            sendJgEvent();
        }
        m_pclient->unsubscribe(QString("acMeter/dataCenter/JSON/set/request/TTU/TTU_f833b65f53e20695"));
        check();
        return;
    }
    case 72:
    {
        ++m_nYgMcChkNum;
        m_gntJyXl.strCsXl = m_gntJyJg.strCsXl = "有功脉冲";
        m_gntJyXl.strCsGc = "触发有功脉冲";
        sendGcEvent();

        QApplication::postEvent((QObject*)g_pFuncWindow, new CCsYgmcMsg(m_nYgMcChkNum));

        while(!g_bygMc[m_nYgMcChkNum])
        {
            if(!g_bTestSign)
            {
                check();
                return;
            }
            QThread::msleep(500);
        }

        auto iter = g_mapMcNum.find(m_gntParam.nBw);
        if(iter == g_mapMcNum.end())
        {
            m_bYgMcChk[m_nYgMcChkNum] = false;
        }
        else
        {
            m_bYgMcChk[m_nYgMcChkNum] = true;
        }

        if(m_nYgMcChkNum < 3)
        {
            m_ntimeout = 72;
            m_ptimer->start(2000);
            return;
        }

        if(m_bYgMcChk[1] || m_bYgMcChk[2] || m_bYgMcChk[3] || m_bYgMcChk[4] || m_bYgMcChk[5] || m_bYgMcChk[6])
        {
            m_gntJyXl.strCsGc = "收到有功脉冲,合格";
            sendGcEvent();
            m_gntJyJg.bCsXlJg = true;
            sendJgEvent();
        }
        else
        {
            m_gntJyXl.strCsGc = "未收到有功脉冲,不合格";
            sendGcEvent();
            m_gntJyJg.bCsXlJg = false;
            sendJgEvent();
        }
        m_ntimeout = 73;
        m_ptimer->start(1000);
        return;
    }
    case 73:
    {
        ++m_nWgMcChkNum;
        m_gntJyXl.strCsXl = m_gntJyJg.strCsXl = "无功脉冲";
        m_gntJyXl.strCsGc = "触发无功脉冲";
        sendGcEvent();

        QApplication::postEvent((QObject*)g_pFuncWindow, new CCsWgmcMsg(m_nWgMcChkNum));

        while (!g_bwgMc[m_nWgMcChkNum])
        {
            if(!g_bTestSign)
            {
                check();
                return;
            }
            QThread::msleep(500);
        }


        auto iter = g_mapMcNum.find(m_gntParam.nBw);
        if(iter == g_mapMcNum.end())
        {
            m_bWgMcChk[m_nWgMcChkNum] = false;
        }
        else
        {
            m_bWgMcChk[m_nWgMcChkNum] = true;
        }

        if(m_nWgMcChkNum < 3)
        {
            m_ntimeout = 73;
            m_ptimer->start(2000);
            return;
        }

        if(m_bWgMcChk[1] || m_bWgMcChk[2] || m_bWgMcChk[3] || m_bWgMcChk[4] || m_bWgMcChk[5] || m_bWgMcChk[6])
        {
            m_gntJyXl.strCsGc = "收到无功脉冲, 合格";
            sendGcEvent();
            m_gntJyJg.bCsXlJg = true;
            sendJgEvent();
        }
        else
        {
            m_gntJyXl.strCsGc = "未收到无功脉冲，不合格";
            sendGcEvent();
            m_gntJyJg.bCsXlJg = false;
            sendJgEvent();
        }
        m_ntimeout = 74;
        m_ptimer->start(1000);
        return;
    }
    case 74:
    {
        ++m_nMmcChkNum;
        m_gntJyXl.strCsXl = m_gntJyJg.strCsXl = "秒脉冲";
        m_gntJyXl.strCsGc = "触发秒脉冲";
        sendGcEvent();

        QApplication::postEvent((QObject*)g_pFuncWindow, new CCsmmcMsg(m_nMmcChkNum));

        while (!g_bmMc[m_nMmcChkNum])
        {
            if(!g_bTestSign)
            {
                check();
                return;
            }
            QThread::msleep(500);
        }


        auto iter = g_mapMcNum.find(m_gntParam.nBw);
        if(iter == g_mapMcNum.end())
        {
            m_bMmcChk[m_nMmcChkNum] = false;
        }
        else
        {
            m_bMmcChk[m_nMmcChkNum] = true;
        }
        if(m_nMmcChkNum < 3)
        {
            m_ntimeout = 74;
            m_ptimer->start(2000);
            return;
        }

        if(m_bMmcChk[1] || m_bMmcChk[2] || m_bMmcChk[3] || m_bMmcChk[4] || m_bMmcChk[5] || m_bMmcChk[6])
        {
            m_gntJyXl.strCsGc = "收到秒脉冲， 合格";
            sendGcEvent();
            m_gntJyJg.bCsXlJg = true;
            sendJgEvent();
        }
        else
        {
            m_gntJyXl.strCsGc = "未收到秒脉冲， 不合格";
            sendGcEvent();
            m_gntJyJg.bCsXlJg = false;
            sendJgEvent();
        }
        check();
        return;
    }
    default:
        break;
    }

    if(m_strChkItem == "用户名密码")
    {
        m_gntJyXl.strCsGc = "设置SCU-IP失败";
        sendGcEvent();
        m_gntJyJg.bCsXlJg = false;
        sendJgEvent();

        if(m_pSocket->state() == QTcpSocket::ConnectedState)
        {
            m_pSocket->close();
        }

        checkItem();
    }
    else if (m_strChkItem == "清除多余文件")
    {
        m_gntJyJg.bCsXlJg = true;
        sendJgEvent();

        checkItem();
    }
    else if(m_strChkItem == "SW1/2按键")
    {
        sshClose();
        m_gntJyXl.strCsGc = "设置SW1/2灯常亮失败";
        sendGcEvent();
        m_gntJyJg.bCsXlJg = false;
        sendJgEvent();

        checkItem();
    }
    else if(m_strChkItem == "MQTTIOT配置")
    {
        switch (m_nLx)
        {
        case 99997:
            mvParam();
            break;
        case 99998:
            mvParam2();
            break;
        case 99999:
            addQx();
            break;
        case 28:
            m_gntJyXl.strCsGc = "读取失败";
            sendGcEvent();
            m_gntJyJg.bCsXlJg = false;
            sendJgEvent();
            checkItem();
            break;
        default:
            break;
        }

    }
    else if(m_strChkItem == "HPLC/4G模块")
    {
        sshClose();
        m_bcheck = false;
        m_gntJyXl.strCsGc = "未找到文件："+ m_strtty;
        sendGcEvent();
        if(m_nsshLj == 27 || m_nsshLj == 26)
            hplccheck();
        else if(m_nsshLj == 44)
            g4check();
    }
    else if(m_strChkItem == "数据清零" || m_strChkItem == "磁场状态")
    {
        m_bsjqlcheck = false;
        m_gntJyXl.strCsGc = "失败";
        sendGcEvent();
        m_gntJyJg.bCsXlJg = false;
        sendJgEvent();
        dk9001("no dk");
    }
    else if(m_strChkItem == "端口检查")
    {
        m_bNodealssherr = false;

        if(m_ndkjcLx == 1)
        {
            m_gntJyXl.strCsGc = "ssh连接scu";
            sendGcEvent();

            QThread::sleep(80);

            m_nsshLj = 34;
            sshConnect();
        }
        else
        {
            m_gntJyXl.strCsGc = "未发现2404端口";
            sendGcEvent();
            m_gntJyJg.bCsXlJg = false;
            sendJgEvent();

            checkItem();
        }

    }
    else if(m_strChkItem == "设备对时")
    {
        sshClose();
        m_gntJyXl.strCsXl = m_gntJyJg.strCsXl = "设备对时";
        m_gntJyXl.strCsGc = "连接mqtt";
        sendGcEvent();
        if(m_pclient->state() != QMqttClient::Connected)
        {

            m_pclient->setHostname(m_gntParam.scuParam.strIp);
            m_pclient->setPort(1883);
            m_pclient->setUsername("admin");
            m_pclient->setPassword("123456");
            m_pclient->connectToHost();
            int nLen = 0;
            while(nLen < 6)
            {
                QApplication::processEvents(QEventLoop::AllEvents, 5000);
                QThread::sleep(2);
                ++nLen;
                if(m_pclient->state() == QMqttClient::Connected)
                {
                    break;
                }
            }
        }
        if(m_pclient->state() != QMqttClient::Connected)
        {
            m_gntJyXl.strCsGc = "连接mqtt失败";
            sendGcEvent();

            m_gntJyJg.bCsXlJg = false;
            sendJgEvent();

            sbdsJc();
            return;
        }
        else
        {
             m_pclient->subscribe(QString("OS-system/puAmr/JSON/response/setTime"));
        }
        QDateTime curTime = QDateTime::currentDateTime();
        QString s= curTime.toString("yyyy-MM-ddTHH:mm:ss");
        QJsonObject json;
        json["token"] = 1;
        json["timestamp"] = "1";

        QJsonObject ob;
        ob["setTime"] = s;
        json["body"] = ob;

        QJsonDocument doc(json);
        QMqttTopicName topic = QString("puAmr/OS-system/JSON/request/setTime");

        m_gntJyXl.strCsGc = "连接mqtt成功";
        sendGcEvent();
        m_gntJyXl.strCsGc = "时间同步, 设置时间："+ s;
        sendGcEvent();
        m_pclient->publish(topic, doc.toJson());
    }
    else if(m_strChkItem == "后备电源")
    {
        if(m_nHbdylx == 1)
        {
            m_gntJyXl.strCsGc = "清空完成";
            sendGcEvent();
            m_gntJyJg.bCsXlJg = true;
            sendJgEvent();

            m_gntJyJg.strCsXl = m_gntJyXl.strCsXl = "后备电源";
            m_gntJyXl.strCsGc = "开始检查后备电源";
            sendGcEvent();

            // 发送后备电源检测事件
            QApplication::postEvent((QObject*)g_pFuncWindow, new CCsHbdyMsg());
            m_bSendSshOff = false;

            while(!g_bHbdy)
            {
                if(!g_bTestSign)
                {
                    check();
                    return;
                }
                QThread::msleep(500);
            }
            QThread::sleep(2);
            m_nHbdylx = 2;
            m_nsshLj = 36;
            sshConnect();
        }
        else
        {
            sshClose();
            m_gntJyXl.strCsGc = "SCU停电第" + QString::number(m_nHbdy * 60) + "秒后后备电源检查异常";
            sendGcEvent();
            m_gntJyJg.bCsXlJg = false;
            sendJgEvent();

            check();
        }

    }
    else if(m_strChkItem == "回路巡检")
    {
        m_nchkHLXJ++;
        m_nHLXJ[m_nchkHLXJ] = 0;
        sshClose();
        m_gntJyJg.strCsXl = m_gntJyXl.strCsXl = "回路巡检";
        m_gntJyXl.strCsGc = "连接mqtt，获取消息";
        sendGcEvent();

        if(m_pclient->state() != QMqttClient::Connected)
        {
            m_pclient->setHostname(m_gntParam.scuParam.strIp);
            m_pclient->setPort(1883);
            m_pclient->setUsername("admin");
            m_pclient->setPassword("123456");
            m_pclient->connectToHost();
            int nLen = 0;
            while(nLen < 6)
            {
                QApplication::processEvents(QEventLoop::AllEvents, 5000);
                QThread::sleep(2);
                ++nLen;
                if(m_pclient->state() == QMqttClient::Connected)
                {
                    QApplication::processEvents();
                    QApplication::processEvents();
                    break;
                }
            }
        }
        if(m_pclient->state() != QMqttClient::Connected)
        {
            m_gntJyXl.strCsGc = "连接mqtt失败";
            sendGcEvent();
            m_gntJyJg.bCsXlJg = false;
            sendJgEvent();
            checkItem();
            return;
        }
        else
        {
            m_pclient->unsubscribe(QString("lcMonitor/dataCenter/A-XDR"));
            m_pclient->subscribe(QString("lcMonitor/dataCenter/A-XDR"));
        }

        if(m_nHLXJ[m_nchkHLXJ] == 0)
        {
            int nWait = 0;
            while (nWait < 150)
            {
                QApplication::processEvents(QEventLoop::AllEvents, 1000);
                QThread::sleep(1);
                nWait += 1;
                if(m_nHLXJ[m_nchkHLXJ] != 0)
                {
                    break;
                }
            }
        }

        if(m_nchkHLXJ < 3)
        {
            m_ntimeout = 9999;
            m_ptimer->start(2000);
            return;
        }

        m_gntJyXl.strCsGc = "回路巡检状态:";
        if(m_nHLXJ[1] == 1 || m_nHLXJ[2] == 1 || m_nHLXJ[3] == 1)
        {
            m_gntJyJg.bCsXlJg = true;
            m_gntJyXl.strCsGc += "正常";
        }
        else if(m_nHLXJ[1] == 2 || m_nHLXJ[2] == 2 || m_nHLXJ[3] == 2)
        {
            m_gntJyJg.bCsXlJg = false;
            m_gntJyXl.strCsGc += "异常";
        }
        else
        {
            m_gntJyJg.bCsXlJg = false;
            m_gntJyXl.strCsGc += "未获得";
        }
        sendGcEvent();
        sendJgEvent();
        m_pclient->unsubscribe(QString("lcMonitor/dataCenter/A-XDR"));
        check();

    }
    else if(m_strChkItem == "交采计量精度")
    {
        on_tcp_error(QTcpSocket::RemoteHostClosedError);
    }
    else if(m_strChkItem == "系统和补丁版本")
    {
        m_gntJyJg.bCsXlJg = false;
        sendJgEvent();
        check();
    }

}

void CGntTest::on_time_timeout2()
{
    if(m_ntimeout2 != 5)
    {
        m_ptimer2->stop();
    }
    switch (m_ntimeout2)
    {
    case 1:
        m_ptimer->stop();
        sshClose();

        if(m_nRS485Num < 50)
        {
            m_nsshLj = 58;
            sshConnect();
        }
        else
        {
            m_gntJyXl.strCsGc = "SCU未收到串口发送的数据：4851，不合格";
            sendGcEvent();
            m_gntJyJg.bCsXlJg = false;
            sendJgEvent();

            m_nRS485Num = 0;
            m_nsshLj = 51;
            sshConnect();
        }
        break;

    case 2:
        m_ptimer->stop();
        sshClose();

        if(m_nRS485Num < 50)
        {
            m_nsshLj = 59;
            sshConnect();
        }
        else
        {
            m_nRS485Num = 0;
            m_gntJyXl.strCsGc = "SCU未收到串口发送的数据：4852，不合格";
            sendGcEvent();
            m_gntJyJg.bCsXlJg = false;
            sendJgEvent();

            m_nsshLj = 53;
            sshConnect();
        }
        break;

    case 3:
        m_ptimer->stop();
        sshClose();

        if(m_nRS485Num < 50)
        {
            m_nsshLj = 60;
            sshConnect();
        }
        else
        {
            m_gntJyXl.strCsGc = "SCU未收到串口发送的数据：4853，不合格";
            sendGcEvent();
            m_gntJyJg.bCsXlJg = false;
            sendJgEvent();

            m_nsshLj = 55;
            sshConnect();
        }

        break;

    case 4:
        m_ptimer->stop();
        sshClose();

        if(m_nRS485Num < 50)
        {
            m_nsshLj = 61;
            sshConnect();
        }
        else
        {
            m_gntJyXl.strCsGc = "SCU未收到串口发送的数据：4854，不合格";
            sendGcEvent();
            m_gntJyJg.bCsXlJg = false;
            sendJgEvent();

            if(m_pRs485->isOpen())
            {
                m_pRs485->close();
            }

            check();
        }
        break;
    case 5:
        if(g_bYxFinish[m_nYxChkNum])
        {
            m_ptimer2->stop();
            m_ntimeout = 9;
            m_ptimer->start(5000);
        }
        break;
    default:
        break;
    }
}

void CGntTest::on_mqtt_mesReceived(const QByteArray &message, const QMqttTopicName &topic)
{
    QByteArray msg = message.trimmed();

    if(topic.name() == "OS-system/puAmr/JSON/response/setTime")
    {
        QJsonParseError error;
        QJsonDocument doc = QJsonDocument::fromJson(msg,&error);
        if(doc.isEmpty() || error.error != QJsonParseError::NoError)
        {
            return;
        }
        if(!doc.isObject())
        {
            return;
        }

        QJsonObject obj = doc.object();
        if(obj["statusCode"].toInt() ==  0)
        {
            m_gntJyXl.strCsGc = "时间同步成功";
            sendGcEvent();

            QThread::sleep(10);
            m_nsshLj = 24;
            sshConnect();
        }
        else
        {
            m_gntJyXl.strCsGc = "时间同步失败";
            sendGcEvent();
            sbdsJc();
        }
    }
    else if(topic.name() == "lcMonitor/dataCenter/A-XDR")
    {
        if(message.size() != 55)
        {
            return;
        }
        if(message[52] == '\x00' && message[53] == '\x00' && message[54] == '\x00')
        {
            m_nHLXJ[m_nchkHLXJ] = 1;
        }
        else
        {
            m_nHLXJ[m_nchkHLXJ] = 2;
        }
    }
    else if(topic.name() == "rspSample/Broadcast/JSON/report/notification/chgEvent")
    {
        QJsonParseError error;
        QJsonDocument doc = QJsonDocument::fromJson(msg);
        if(doc.isEmpty())
        {
            return;
        }
        if(!doc.isObject())
        {
            return;
        }

        QJsonObject obj = doc.object();
        QJsonArray bodyary = obj["body"].toArray();
        for(int m = 0; m < bodyary.size(); ++m)
        {
            QJsonObject obj2 = bodyary[m].toObject();
            if(obj2["chgStatus"].toInt() == 1)
            {
                m_mapYx[obj2["channel"].toInt()] = 1;
                if(m_strChkItem == "设备对时")
                {
                    QString s1 = obj2["chgtime"].toString();
                    QString s2 = s1.left(s1.size() -4);
                    m_dtZdRtc[m_nRtcChkNum] = QDateTime::fromString(s2, "yyyy-MM-dd HH:mm:ss");
                    m_dtSys[m_nRtcChkNum] = QDateTime::currentDateTime();
                    break;

                }
            }
        }
    }
    else if(topic.name() == "acMeter/dataCenter/JSON/set/request/TTU/TTU_f833b65f53e20695")
    {
        QJsonParseError error;
        QJsonDocument doc = QJsonDocument::fromJson(msg);
        if(doc.isEmpty())
        {
            return;
        }
        if(!doc.isObject())
        {
            return;
        }

        QJsonObject obj = doc.object();
        m_bodyary = obj["body"].toArray();
    }
}

void CGntTest::on_mqtt_StateChange()
{
    if(m_pclient->state() == QMqttClient::Connected)
    {
        m_pclient->subscribe(QString("OS-system/puAmr/JSON/response/setTime"));
    }
}

void CGntTest::onChannelInitialized1()
{
    QString strSrcFile = "/data/app/SCMQTTIoT/configFile/paramFile.json";
    quint32 len = m_channel->uploadFile(m_strDestFile, strSrcFile, QSsh::SftpOverwriteExisting);
}

void CGntTest::onChannelInitialized2()
{
    QString strSrcFile;
    QString strDestFile;
    if(m_nsshLj == 78)
    {
        strSrcFile = "/mnt/internal_storage/security_proxy_config";
        strDestFile = "security_proxy_config";
    }
    else if(m_nsshLj == 82)
    {
        strSrcFile = "/mnt/internal_storage/security_proxy_config";
        strDestFile = "security_proxy_config_bak";
    }
    else if(m_nsshLj == 64)
    {
        strSrcFile = "/mnt/internal_storage/security_proxy_config";
        strDestFile = "security_proxy_config_bak";
    }

    m_gntJyXl.strCsGc = "上传文件：security_proxy_config";
    sendGcEvent();
    quint32 len = m_channel->uploadFile(strDestFile, strSrcFile, QSsh::SftpOverwriteExisting);
}

void CGntTest::onChannelInitialized3()
{
    QString strLocalFile = m_gntParam.other.strLicense + m_strEsamId + ".txt";
    QString strRemoteFile = "/tmp/" + m_strEsamId + ".txt";
    quint32 len = m_channel->uploadFile(strLocalFile, strRemoteFile, QSsh::SftpOverwriteExisting);
}

void CGntTest::onChannelInitialized4()
{
    QString strLocalFile = "uart_drv.ko";
    QString strRemoteFile = "/tmp/uart_drv.ko";
    quint32 len = m_channel->uploadFile(strLocalFile, strRemoteFile, QSsh::SftpOverwriteExisting);
}

void CGntTest::onChannelInitialized5()
{
    QString strLocalFile = "security_proxy_config";
    QString strRemoteFile = "/tmp/security_proxy_config";
    quint32 len = m_channel->uploadFile(strLocalFile, strRemoteFile, QSsh::SftpOverwriteExisting);
}

void CGntTest::onChannelError1(const QString &s)
{
    m_channel->closeChannel();

    m_gntJyXl.strCsGc = "上传失败";
    sendGcEvent();
    m_gntJyJg.bCsXlJg = false;
    sendJgEvent();

    check();
}

void CGntTest::onOpfinished(QSsh::SftpJobId, QString s)
{
    m_channel->closeChannel();

    m_gntJyXl.strCsGc = "上传成功";
    sendGcEvent();


    m_gntJyXl.strCsGc = "确认， 执行命令：cat /data/app/SCMQTTIoT/configFile/paramFile.json";
    sendGcEvent();

    m_nsshLj = 94;
    sshConnect();
}

void CGntTest::onOpfinished2(QSsh::SftpJobId, QString)
{

    m_gntJyXl.strCsGc = "文件：security_proxy_config上传成功";
    sendGcEvent();

    m_channel->closeChannel();
    sshClose();

    if(m_nsshLj == 78)
    {
        m_nsshLj = 79;
        sshConnect();
    }
    else if(m_nsshLj == 82)
    {
        m_nsshLj = 83;
        sshConnect();
    }
    else if(m_nsshLj == 64)
    {
        // 增加一个查询逻辑， 判断是否上传成功
        m_nsshLj = 125;
        sshConnect();
    }
}

void CGntTest::onOpfinished3(QSsh::SftpJobId, QString)
{
    m_gntJyXl.strCsGc = "上传成功";
    sendGcEvent();

    m_nsshLj = 101;
    sshConnect();
}

void CGntTest::onOpfinished4(QSsh::SftpJobId, QString)
{
    m_channel->closeChannel();

    m_gntJyXl.strCsGc = "上传成功";
    sendGcEvent();


    m_gntJyXl.strCsGc = "新串口驱动移动到/lib/modules/hi1711_ko目录下， 命令:sudo mv /tmp/uart_drv.ko /lib/modules/hi1711_ko/uart_drv.ko";
    sendGcEvent();
    createRemoteProcess("echo 'Zgdky@guest123' | sudo -S mv /tmp/uart_drv.ko /lib/modules/hi1711_ko/uart_drv.ko");

    m_ntimeout = 54;
    m_ptimer->start(3000);
}

void CGntTest::onOpfinished5(QSsh::SftpJobId, QString)
{
    m_channel->closeChannel();

    m_gntJyXl.strCsGc = "上传成功";
    sendGcEvent();

    m_gntJyXl.strCsGc = "移到/mnt/internal_storage/目录下";
    sendGcEvent();

    m_nsshLj = 124;
    sshConnect();

}

void CGntTest::on_rs485_readyRead()
{
    QByteArray data = m_pRs485->readAll();
    m_zrsdata += data;
}

void CGntTest::checkItem()
{
    if(!g_bTestSign)
    {
        check();
        return;
    }
    m_nReTcp = 0;
    m_nReSsh = 0;
    m_gntJyJg.strCsDl = m_strChkItem;
    m_gntJyXl.strCsDl = m_strChkItem;

    if(m_strChkItem != "用户名密码")
    {
        if(m_bFirst)
        {
            int num = 0;
            while(num < 180)
            {
                if(!g_bTestSign)
                {
                    check();
                    return;
                }
                QThread::sleep(1);
                ++num;
            }

            m_bFirst = false;
        }
        else
        {
           int num = 0;
           while(num < 10)
           {
               if(!g_bTestSign)
               {
                   check();
                   return;
               }
               QThread::sleep(1);
               ++num;
           }
        }
    }

    if(m_strChkItem == "用户名密码")
    {
        userPassWd();
    }
    else if(m_strChkItem == "清除多余文件")
    {
        qcdywjJc();
    }
    else if(m_strChkItem == "容器")
    {
        rqJc();
    }
    else if(m_strChkItem == "APP运行")
    {
        APPJc();
    }
    else if(m_strChkItem == "系统和补丁版本")
    {
        xtbdJc();
    }
    else if(m_strChkItem == "终端参数")
    {
        zdcsJc();
    }
    else if(m_strChkItem == "698参数配置")
    {
        cs698Jc();
    }
    else if(m_strChkItem == "设备对时")
    {
        sbdsJc();
    }
    else if(m_strChkItem == "串口通信")
    {
        cktxJc();
    }
    else if(m_strChkItem == "HPLC/4G模块")
    {
        hplc4GJc();
    }
    else if(m_strChkItem == "数据清零")
    {
        sjqlJc();
    }
    else if(m_strChkItem == "回路巡检")
    {
        hlxjJc();
    }
    else if(m_strChkItem == "磁场状态")
    {
        ccztJc();
    }
    else if(m_strChkItem == "端口检查")
    {
        dkJc();
    }
    else if(m_strChkItem == "蓝牙通信")
    {
        lytxJc();
    }
    else if(m_strChkItem == "后备电源")
    {
        hbdyJc();
    }
    else if(m_strChkItem == "SW1/2按键")
    {
        SWJc();
    }
    else if(m_strChkItem == "交采计量精度")
    {
        jcjlJc();
    }
    else if(m_strChkItem == "MQTTIOT配置")
    {
        mqttIotJc();
    }
    else if(m_strChkItem == "有功、无功、秒脉冲接口")
    {
        ywmmcJc();
    }
    else if(m_strChkItem == "遥信分辨率")
    {
        yxfblJc();
    }
    else if(m_strChkItem == "证书导出")
    {
        zsdcJc();
    }
    else if(m_strChkItem == "证书导入")
    {
        zsdrJc();
    }
    else if(m_strChkItem == "参数设置")
    {
        zdipJc();
    }
    else if(m_strChkItem == "导入license")
    {
        importLicense();
    }
    else if(m_strChkItem == "零序检查")
    {
        lxJc();
    }
    else
    {
        check();
    }
}

// 用户名密码
void CGntTest::userPassWd()
{
    ++m_nJyCs;
    if(m_nJyCs > 3)
    {
        m_gntParam.vtItem.clear();
        clear();
        emit(endSig());
        return;
    }
    m_gntJyXl.strCsXl = m_gntJyJg.strCsXl = "type-C串口连接SCU";
    if(m_nJyCs == 1)
    {
        m_gntJyXl.strCsGc = "等待SCU启动";
        sendGcEvent();
        QThread::sleep(m_gntParam.other.nSCUqd);
    }

    if(!m_pSerialPort->isOpen() && !m_pSerialPort->open(QIODevice::ReadWrite))
    {
        m_gntJyXl.strCsGc = "打开串口" + m_gntParam.strTypeCCom + "失败";
        sendGcEvent();

        m_gntJyJg.bCsXlJg = false;
        sendJgEvent();

        checkItem();
        return;
    }

    m_gntJyXl.strCsGc = "开始连接SCU";
    sendGcEvent();

    QByteArray senddata = "sysadm\n";
    int size = m_pSerialPort->write(senddata);

    QByteArray recvData;
    int nWaitTime = 0;
    bool bFindPassword = false;
    bool bFindScu = false;
    while(nWaitTime < 2000)
    {
        QThread::msleep(100);
        m_pSerialPort->waitForReadyRead(100);
        recvData += m_pSerialPort->readAll();
        nWaitTime += 100;
        if(recvData.indexOf("Password:") != -1 )
        {
            bFindPassword = true;
            break;
        }
        else if(recvData.indexOf("command not found") != -1)
        {
            bFindScu = true;
            break;
        }
    }
    if(bFindPassword)
    {
        senddata = "Zgdky@guest123\n";
        m_pSerialPort->write(senddata);
        m_pSerialPort->waitForBytesWritten(500);
        nWaitTime = 0;
        recvData.clear();
        while(nWaitTime < 2000)
        {
            QThread::msleep(100);
            m_pSerialPort->waitForReadyRead(100);
            recvData += m_pSerialPort->readAll();
            nWaitTime += 100;
            if(recvData.indexOf("@SCU:~$") != -1 || recvData.indexOf("Authorized uses only.") != -1)
            {
                bFindScu = true;
                break;
            }
        }
    }

    // 连接失败
    if(!bFindScu)
    {
        m_pSerialPort->close();
        m_gntJyXl.strCsGc = "连接SCU失败";
        QApplication::postEvent((QObject*)g_pFuncWindow, new CCsXlGcMsg(m_gntJyXl));
        m_gntJyJg.bCsXlJg = false;
        QApplication::postEvent((QObject*)g_pFuncWindow, new CCsXlJgMsg(m_gntJyJg));

        checkItem();
        return ;
    }
    m_gntJyXl.strCsGc = "连接SCU成功";
    QApplication::postEvent((QObject*)g_pFuncWindow, new CCsXlGcMsg(m_gntJyXl));
    m_gntJyJg.bCsXlJg = true;
    QApplication::postEvent((QObject*)g_pFuncWindow, new CCsXlJgMsg(m_gntJyJg));

    m_gntJyJg.strCsXl = m_gntJyXl.strCsXl = "设置IP";

    m_gntJyXl.strCsGc = "开始设置IP";
    QApplication::postEvent((QObject*)g_pFuncWindow, new CCsXlGcMsg(m_gntJyXl));

    senddata = "setip FE0 ";
    senddata += m_gntParam.scuParam.strIp ;
    senddata += " --netmask ************* -gateway ";
    int c = m_gntParam.scuParam.strIp.lastIndexOf(".");
    QString s2 = m_gntParam.scuParam.strIp.left(c);
    s2 += ".1\n";
    senddata += s2;
    m_pSerialPort->write(senddata);
    m_pSerialPort->waitForBytesWritten(500);
    m_gntJyXl.strCsGc = senddata;
    QApplication::postEvent((QObject*)g_pFuncWindow, new CCsXlGcMsg(m_gntJyXl));

    QThread::sleep(2);
    senddata = "setip FE0:1 192.168.1.";
    senddata +=  QString::number(m_gntParam.nBw + 1);
    senddata += " --netmask ************* -gateway ***********\n";
    m_pSerialPort->write(senddata);
    m_pSerialPort->waitForBytesWritten(500);
    m_gntJyXl.strCsGc = senddata;
    QApplication::postEvent((QObject*)g_pFuncWindow, new CCsXlGcMsg(m_gntJyXl));

    QThread::sleep(2);
    m_pSerialPort->close();


    int num = 0;
    while(num < 35)
    {
        if(!g_bTestSign)
        {
            check();
            return;
        }
        QThread::sleep(1);
        ++num;
    }

    m_nsshLj = 1;
    sshConnect();
}

void CGntTest::rqJc()
{
    ++m_nJyCs;
    if(m_nJyCs > m_gntParam.nJyCs)
    {
        check();
        return;
    }

    m_gntJyJg.strCsXl = m_gntJyXl.strCsXl = "容器";
    m_gntJyXl.strCsGc = "开始校验容器";
    QApplication::postEvent((QObject*)g_pFuncWindow, new CCsXlGcMsg(m_gntJyXl));

    m_nsshLj = 3;
    sshConnect();
}

void CGntTest::qcdywjJc()
{
    ++m_nJyCs;
    if(m_nJyCs > m_gntParam.nJyCs)
    {
        check();
        return;
    }

    m_gntJyJg.strCsXl = m_gntJyXl.strCsXl = "清除多余文件";
    m_gntJyXl.strCsGc = "清除多余文件, 命令：echo Zgdky@guest123 | sudo -S rm -rf /data/license_not_verify";
    QApplication::postEvent((QObject*)g_pFuncWindow, new CCsXlGcMsg(m_gntJyXl));

    m_nsshLj = 45;
    sshConnect();
}

void CGntTest::APPJc()
{
    ++m_nJyCs;
    if(m_nJyCs > m_gntParam.nJyCs)
    {
        check();
        return;
    }
    m_bcheck = true;

    m_gntJyJg.strCsXl = m_gntJyXl.strCsXl = "容器内APP检查";
    m_gntJyXl.strCsGc = "容器内APP检查:";
    sendGcEvent();

    m_nsshLj = 4;
    sshConnect();
}

void CGntTest::xtbdJc()
{
    ++m_nJyCs;
    if(m_nJyCs > m_gntParam.nJyCs)
    {
        check();
        return;
    }
    m_gntJyJg.strCsXl = m_gntJyXl.strCsXl = "大包版本";
    m_gntJyXl.strCsGc = "校验大包版本 version -b";
    sendGcEvent();

    m_nsshLj = 8;
    sshConnect();
}

void CGntTest::zdcsJc()
{
    ++m_nJyCs;
    if(m_nJyCs > m_gntParam.nJyCs)
    {
        check();
        return;
    }
    m_gntJyJg.strCsXl = m_gntJyXl.strCsXl = "终端参数";
    m_vtCmd.clear();

    QString cmd = "cd /usr/sbin && ./vendor_eeprom devName -w " + m_gntParam.scuParam.strZdLx;
    m_vtCmd.push_back(cmd);

    cmd = "cd /usr/sbin && ./vendor_eeprom devType -w " + m_gntParam.scuParam.strZdLx;
    m_vtCmd.push_back(cmd);

    cmd = "cd /usr/sbin && ./vendor_eeprom hardwareModel -w " + m_gntParam.scuParam.strXh;
    m_vtCmd.push_back(cmd);

    cmd = "cd /usr/sbin && ./vendor_eeprom hardwareVersion -w " + m_gntParam.scuParam.strYjBb;
    m_vtCmd.push_back(cmd);

    cmd = "cd /usr/sbin && ./vendor_eeprom vendorId -w " + m_gntParam.scuParam.strCs;
    m_vtCmd.push_back(cmd);

    cmd = "cd /usr/sbin && ./vendor_eeprom devId  -w " + m_gntParam.scuParam.strID;
    m_vtCmd.push_back(cmd);

    cmd = "cd /usr/sbin && ./vendor_eeprom devID -w " + m_gntParam.scuParam.strID;
    m_vtCmd.push_back(cmd);

    cmd = "cd /usr/sbin && ./vendor_eeprom devSn -w " + m_gntParam.scuParam.strID;
    m_vtCmd.push_back(cmd);

    cmd = "cd /usr/sbin && ./vendor_eeprom logicalAddr -w " + m_gntParam.scuParam.strLjdz;
    m_vtCmd.push_back(cmd);

    cmd = "cd /usr/sbin && ./vendor_eeprom manufDate -w " + m_gntParam.scuParam.strScrq;
    m_vtCmd.push_back(cmd);

    m_nsshLj = 18;
    sshConnect();
}

void CGntTest::cs698Jc()
{
    ++m_nJyCs;
    if(m_nJyCs > m_gntParam.nJyCs)
    {
        check();
        return;
    }
    m_bcheck = true;
    m_vtCmd.clear();

    if(m_gntParam.gwtxpz.bvalid)
        m_vtCmd.push_back("公网通信配置");
    if(m_gntParam.gwtxdz.bvalid)
        m_vtCmd.push_back("公网主站通信参数");
    if(m_gntParam.ytwtxpz.bvalid)
        m_vtCmd.push_back("以太网通信配置");
    if(m_gntParam.ytwtxdz.bvalid)
        m_vtCmd.push_back("以太网主站通信参数");
    if(m_gntParam.tsdpzcs.bvalid)
        m_vtCmd.push_back("终端停/上电事件配置参数");
    if(m_gntParam.aqjmpz.bvalid)
        m_vtCmd.push_back("安全模式参数");

    m_nsshLj = 22;
    sshConnect();
}

void CGntTest::sbdsJc()
{
    ++m_nJyCs;
    if(m_nJyCs > m_gntParam.nJyCs)
    {
        check();
        return;
    }

    m_nRtcChkNum = 0;

    m_gntJyJg.strCsXl = m_gntJyXl.strCsXl = "RTC校验";
    m_gntJyXl.strCsGc = "RTC开始校验";
    sendGcEvent();

    m_nsshLj = 62;
    sshConnect();
}

void CGntTest::cktxJc()
{
    ++m_nJyCs;
    if(m_nJyCs > m_gntParam.nJyCs)
    {
        check();
        return;
    }
    m_nRS485Num = 0;

    m_nsshLj = 49;
    sshConnect();

}

void CGntTest::hplc4GJc()
{
    ++m_nJyCs;
    if(m_nJyCs > m_gntParam.nJyCs)
    {
        check();
        return;
    }

    m_gntJyJg.strCsXl = m_gntJyXl.strCsXl = "HPLC";


    m_bcheck = true;
    m_vtCmd.clear();

    m_vtCmd.push_back("ls /dev/ | grep ttyPLC0");
    m_vtCmd.push_back("ls /dev/ | grep ttyPLC1");

    m_nsshLj = 26;
    sshConnect();

}

void CGntTest::sjqlJc()
{
    ++m_nJyCs;
    if(m_nJyCs > 3)
    {
        check();
        return;
    }

    m_bsjqlcheck = true;

    m_vtCmd.clear();
    m_vtCmd.push_back("电能量清零1");
    m_vtCmd.push_back("电能量清零2");
    m_vtCmd.push_back("清空档案");
    m_vtCmd.push_back("清空任务");
    m_vtCmd.push_back("清空方案");

    m_gntJyXl.strCsXl = m_gntJyJg.strCsXl = "电能量清零1";
    m_gntJyXl.strCsGc = "打开9001端口";
    sendGcEvent();

    m_nsshLj = 30;
    sshConnect();
}

void CGntTest::hlxjJc()
{

    ++m_nJyCs;
    if(m_nJyCs > m_gntParam.nJyCs)
    {
        check();
        return;
    }
    m_nchkHLXJ = 0;
    for(int m = 0 ; m < 6; ++m)
    {
        m_nHLXJ[m] = 0;
    }

    m_nsshLj = 31;
    sshConnect();
}

void CGntTest::ccztJc()
{
    ++m_nJyCs;
    if(m_nJyCs > m_gntParam.nJyCs)
    {
        check();
        return;
    }

    m_gntJyJg.strCsXl = m_gntJyXl.strCsXl = "磁场状态";
    m_gntJyXl.strCsGc = "打开9001端口： 命令：container config c_master_yx -p 9001:9001" ;
    sendGcEvent();

    m_nsshLj = 32;
    sshConnect();

}

void CGntTest::dkJc()
{
    ++m_nJyCs;
    if(m_nJyCs > 3)
    {
        m_gntJyJg.bCsXlJg = false;
        sendJgEvent();
        check();
        return;
    }
    m_gntJyJg.strCsXl = m_gntJyXl.strCsXl = "端口检查";
    m_gntJyXl.strCsGc = "关闭端口， 执行命令:echo Zgdky@guest123 | sudo -S /opt/om_core/bin/sqlite3 /data/config/om_core/db/AgentDB.db \"update ContainerConfTable set Port='' where ContainerName='c_master_yx'\"";
    sendGcEvent();
    m_ndkjc = 1;

    m_nsshLj = 33;
    sshConnect();

}

void CGntTest::lytxJc()
{
    ++m_nJyCs;
    if(m_nJyCs > 3)
    {
        check();
        return;
    }
    for(int m = 0; m < 10; ++m)
    {
        m_blytx[m] = false;
    }
    m_nlytxnum = 0 ;
    m_nLymm = 0;

    m_ntimeout = 64;
    m_ptimer->start(1000);
}

void CGntTest::hbdyJc()
{
    ++m_nJyCs;
    if(m_nJyCs > m_gntParam.nJyCs)
    {
        check();
        return;
    }
    m_nsshLj = 35;
    m_nHbdylx = 1;
    sshConnect();
}

void CGntTest::SWJc()
{
    ++m_nJyCs;
    if(m_nJyCs > m_gntParam.nJyCs)
    {
        check();
        return;
    }
    m_gntJyXl.strCsXl = "SW1/2检查";
    m_gntJyXl.strCsGc = "SW1/2检查";
    sendGcEvent();

    QApplication::postEvent((QObject*)g_pFuncWindow, new CCsSWMsg());

    while(!g_bSW)
    {
        if(!g_bTestSign)
        {
            check();
            return;
        }
        QThread::msleep(500);
    }
    m_nsshLj = 38;
    sshConnect();

}

void CGntTest::jcjlJc()
{
    ++m_nJyCs;
    if(m_nJyCs > m_gntParam.nJyCs)
    {
        check();
        return;
    }

    m_nLymm = 0;
    m_nsshLj = 128;
    sshConnect();
}

void CGntTest::mqttIotJc()
{
    ++m_nJyCs;
    if(m_nJyCs > m_gntParam.nJyCs)
    {
        check();
        return;
    }


    m_gntJyJg.strCsXl = m_gntJyXl.strCsXl = "MQTTIOT配置";
    m_gntJyXl.strCsGc = "查询esn号， 命令： cd /usr/sbin && ./vendor_eeprom show -a";
    m_nsshLj = 93;
    sshConnect();

}

void CGntTest::ywmmcJc()
{
    ++m_nJyCs;
    if(m_nJyCs > m_gntParam.nJyCs)
    {
        check();
        return;
    }
    m_nYgMcChkNum = 0;
    m_nWgMcChkNum = 0;
    m_nMmcChkNum = 0;

    for(int m = 0; m < 10; ++m)
    {
        m_bYgMcChk[m] = false;
        m_bWgMcChk[m] = false;
        m_bMmcChk[m] = false;
    }
    m_ntimeout = 72;
    m_ptimer->start(1000);
}

void CGntTest::yxfblJc()
{
    ++m_nJyCs;
    if(m_nJyCs > m_gntParam.nJyCs)
    {
        check();
        return;
    }
    for(int m = 0; m < 15 ; ++m)
    {
        m_bYx1Chk[m] = false;
        m_bYx2Chk[m] = false;
        m_bYx3Chk[m] = false;
        m_bYx4Chk[m] = false;
    }
    m_nYxChkNum = 0;
    m_nsshLj = 57;
    sshConnect();
}

void CGntTest::zsdcJc()
{
    ++m_nJyCs;
    if(m_nJyCs > m_gntParam.nJyCs)
    {
        check();
        return;
    }
    g_bSingleExportFinish = false;
    m_gntJyJg.strCsXl = m_gntJyXl.strCsXl = "证书导出";

    m_gntJyXl.strCsGc = "开始导出证书";
    sendGcEvent();

    m_nsshLj = 89;
    sshConnect();
}

void CGntTest::zsdrJc()
{
    ++m_nJyCs;
    if(m_nJyCs > m_gntParam.nJyCs)
    {
        check();
        return;
    }
    g_bSingleExportFinish = false;
    m_gntJyJg.strCsXl = m_gntJyXl.strCsXl = "证书导入";

    m_gntJyXl.strCsGc = "开始导入证书";
    sendGcEvent();

    m_nsshLj = 89;
    sshConnect();
}

void CGntTest::zdipJc()
{
    ++m_nJyCs;
    if(m_nJyCs > m_gntParam.nJyCs)
    {
        check();
        return;
    }
    QApplication::postEvent((QObject*)g_pFuncWindow, new CNormalMsg(5, m_gntParam.nBw));

    while (!g_bScuModifyip)
    {
        if(!g_bTestSign)
        {
            check();
            return;
        }
        QThread::msleep(500);
    }

    m_gntJyXl.strCsXl = m_gntJyJg.strCsXl = "终端IP设置";
    m_gntJyXl.strCsGc = "通过串口连接SCU";
    sendGcEvent();

    if(!m_pSerialPort->isOpen() && !m_pSerialPort->open(QIODevice::ReadWrite))
    {
        m_gntJyXl.strCsGc = "打开串口" + m_gntParam.strTypeCCom + "失败";
        sendGcEvent();

        m_gntJyJg.bCsXlJg = false;
        sendJgEvent();

        checkItem();
        return;
    }

    QByteArray senddata = "sysadm\n";
    int size = m_pSerialPort->write(senddata);

    QByteArray recvData;
    int nWaitTime = 0;
    bool bFindPassword = false;
    bool bFindScu = false;
    while(nWaitTime < 2000)
    {
        QThread::msleep(100);
        m_pSerialPort->waitForReadyRead(100);
        recvData += m_pSerialPort->readAll();
        nWaitTime += 100;
        if(recvData.indexOf("Password:") != -1 )
        {
            bFindPassword = true;
            break;
        }
        else if(recvData.indexOf("command not found") != -1)
        {
            bFindScu = true;
            break;
        }
    }
    if(bFindPassword)
    {
        senddata = "Zgdky@guest123\n";
        m_pSerialPort->write(senddata);
        m_pSerialPort->waitForBytesWritten(500);
        nWaitTime = 0;
        recvData.clear();
        while(nWaitTime < 2000)
        {
            QThread::msleep(100);
            m_pSerialPort->waitForReadyRead(100);
            recvData += m_pSerialPort->readAll();
            nWaitTime += 100;
            if(recvData.indexOf("@SCU:~$") != -1 || recvData.indexOf("Authorized uses only.") != -1)
            {
                bFindScu = true;
                break;
            }
        }
    }

    // 连接失败
    if(!bFindScu)
    {
        m_pSerialPort->close();
        m_gntJyXl.strCsGc = "连接SCU失败";
        QApplication::postEvent((QObject*)g_pFuncWindow, new CCsXlGcMsg(m_gntJyXl));
        m_gntJyJg.bCsXlJg = false;
        QApplication::postEvent((QObject*)g_pFuncWindow, new CCsXlJgMsg(m_gntJyJg));

        checkItem();
        return ;
    }
    m_gntJyXl.strCsGc = "串口连接SCU成功";
    QApplication::postEvent((QObject*)g_pFuncWindow, new CCsXlGcMsg(m_gntJyXl));
    m_gntJyJg.bCsXlJg = true;
    QApplication::postEvent((QObject*)g_pFuncWindow, new CCsXlJgMsg(m_gntJyJg));

    QStringList strList = m_gntParam.scuParam.strIp.split(".");


    if(strList.size() != 4)
    {
        m_gntJyXl.strCsGc = "面膜IP错误";
        sendGcEvent();
        m_gntJyJg.bCsXlJg = false;
        sendJgEvent();
        check();
        return;
    }

    senddata = "setip FE0:1 192.168.1.";
    senddata += strList[3];
    senddata += " --netmask ************* -gateway ***********\n";
    m_gntJyXl.strCsGc = "设置IP命令：";
    m_gntJyXl.strCsGc += senddata;

    sendGcEvent();

    m_pSerialPort->write(senddata);
    m_pSerialPort->waitForBytesWritten(500);

    QThread::sleep(2);
    m_pSerialPort->close();

    int num = 0;
    while(num < 35)
    {
        if(!g_bTestSign)
        {
            check();
            return;
        }
        QThread::sleep(1);
        ++num;
    }

    m_gntJyJg.strCsXl = m_gntJyXl.strCsXl = "ip确认";
    m_gntJyXl.strCsGc = "ip确认, 命令：ifconfig FE0:1";
    sendGcEvent();

    m_nsshLj = 2;
    sshConnect();
}


// 导入License文件
void CGntTest::importLicense()
{
    m_strEsamId.clear();

    m_gntJyJg.strCsXl = m_gntJyXl.strCsXl = "导入License文件";
    m_gntJyXl.strCsGc = "开始导入";
    sendGcEvent();

    // 查找esamid号码
    m_nsshLj = 95;
    sshConnect();
}

// 零序电压电流检查
void CGntTest::lxJc()
{
    ++m_nJyCs;
    if(m_nJyCs > 3)
    {
        check();
        return;
    }

    m_gntJyXl.strCsXl = m_gntJyJg.strCsXl = "零序1";
    m_gntJyXl.strCsGc = "开启1883端口";
    sendGcEvent();

    m_nsshLj = 130;
    sshConnect();
}

void CGntTest::hplccheck()
{
    if(!g_bTestSign)
    {
        check();
        return;
    }
    if(m_vtCmd.size() == 0)
    {
        m_gntJyXl.strCsGc = "校验载波模块，命令：devctl -l";
        sendGcEvent();

        m_nsshLj = 28;
        sshConnect();
        return;
    }

    m_nsshLj = 27;
    sshConnect();
}

void CGntTest::g4check()
{
    if(!g_bTestSign)
    {
        check();
        return;
    }
    if(m_vtCmd.size() == 0)
    {

        m_gntJyXl.strCsGc = "校验4G模块， 命令： wwan modem show dev all";
        sendGcEvent();

        m_nsshLj = 29;
        sshConnect();

        return;
    }

    m_nsshLj = 44;
    sshConnect();
}

void CGntTest::hbdycheck()
{
    ++m_nHbdy;
    m_gntJyXl.strCsGc = "后备电源开始进行第" + QString::number(m_nHbdy) + "次检查";
    sendGcEvent();
    QThread::sleep(60);
    m_nsshLj = 37;
    sshConnect();

}

void CGntTest::createRemoteProcess(QByteArray s)
{
    sshRemote = m_pSsh->createRemoteProcess(s);
    connect(sshRemote.data(), SIGNAL(readyReadStandardOutput()), this, SLOT(on_ssh_readyReadStandardOutput()));
    sshRemote->start();
}

void CGntTest::dbbb1(QString s)
{
    m_ptimer->stop();
    QString s2 = s.trimmed().right(s.size() - 18);
    m_gntJyXl.strCsGc = "大包版本："+ s2 + "\n大包版本设定值：" + m_gntParam.scuXTParam.strDB1;
    sendGcEvent();

    if(s2 == m_gntParam.scuXTParam.strDB1)
    {
        m_gntJyXl.strCsGc = "大包版本合格";
        m_gntJyJg.bCsXlJg = true;
    }
    else
    {
        m_gntJyXl.strCsGc = "大包版本不合格";
        m_gntJyJg.bCsXlJg = false;
    }

    sendGcEvent();
    sendJgEvent();

    m_gntJyXl.strCsXl = m_gntJyJg.strCsXl = "大包版本2";
    m_gntJyXl.strCsGc = "大包版本2：cat /etc/build_version";
    sendGcEvent();

    m_nsshLj = 9;
    sshConnect();
}

void CGntTest::dbbb2(QString s)
{
    m_ptimer->stop();
    QString s2 =  s.trimmed().right(s.size()-15);
    m_gntJyXl.strCsGc = "大包版本2："+ s2 + "\n大包版本2设定值：" + m_gntParam.scuXTParam.strDB2;
    sendGcEvent();

    if(s2 == m_gntParam.scuXTParam.strDB2)
    {
        m_gntJyXl.strCsGc = "大包版本2合格";
        m_gntJyJg.bCsXlJg = true;
    }
    else
    {
        m_gntJyXl.strCsGc = "大包版本2不合格";
        m_gntJyJg.bCsXlJg = false;
    }

    sendGcEvent();
    sendJgEvent();

    m_gntJyJg.strCsXl = m_gntJyXl.strCsXl = "主控板版本";
    m_gntJyXl.strCsGc = "主控板版本 devcfg -mbn";
    sendGcEvent();

    m_nsshLj = 10;
    sshConnect();
}

void CGntTest::zkb(QString s)
{
    m_ptimer->stop();
    QString s2 = s.trimmed().right(s.size() -15);
    m_gntJyXl.strCsGc = "主控板版本："+ s2 + "\n主控板版本设定值：" + m_gntParam.scuXTParam.strZKB;
    sendGcEvent();

    if(s2 == m_gntParam.scuXTParam.strZKB)
    {
        m_gntJyXl.strCsGc = "主控板版本合格";
        m_gntJyJg.bCsXlJg = true;
    }
    else
    {
        m_gntJyXl.strCsGc = "主控板版本不合格";
        m_gntJyJg.bCsXlJg = false;
    }

    sendGcEvent();
    sendJgEvent();

    m_gntJyJg.strCsXl = m_gntJyXl.strCsXl = "硬件版本";
    m_gntJyXl.strCsGc = "硬件版本 devctl -H";
    sendGcEvent();

    m_nsshLj = 11;
    sshConnect();


}

void CGntTest::yjb(QString s)
{
    m_ptimer->stop();

    QString s2 = s.trimmed().right(s.size() -18);

    m_gntJyXl.strCsGc = "硬件版本："+ s2 + "\n硬件版本设定值：" + m_gntParam.scuXTParam.strYJB;
    sendGcEvent();

    if(s2 == m_gntParam.scuXTParam.strYJB)
    {
        m_gntJyXl.strCsGc = "硬件版本合格";
        m_gntJyJg.bCsXlJg = true;
    }
    else
    {
        m_gntJyXl.strCsGc = "硬件版本不合格";
        m_gntJyJg.bCsXlJg = false;
    }

    sendGcEvent();
    sendJgEvent();


    m_gntJyJg.strCsXl = m_gntJyXl.strCsXl = "内核版本";
    m_gntJyXl.strCsGc = "内核版本 version -k";
    sendGcEvent();

    m_nsshLj = 12;
    sshConnect();
}

void CGntTest::nhb(QString s)
{
    m_ptimer->stop();

    QString s2 = s.trimmed().right(s.size() -16);

    m_gntJyXl.strCsGc = "内核版本："+ s2 + "\n内核版本设定值：" + m_gntParam.scuXTParam.strNHB;
    sendGcEvent();

    if(s2 == m_gntParam.scuXTParam.strNHB)
    {
        m_gntJyXl.strCsGc = "内核版本合格";
        m_gntJyJg.bCsXlJg = true;
    }
    else
    {
        m_gntJyXl.strCsGc = "内核版本不合格";
        m_gntJyJg.bCsXlJg = false;
    }

    sendGcEvent();
    sendJgEvent();

    m_gntJyJg.strCsXl = m_gntJyXl.strCsXl = "备核版本/出厂主核版本";
    m_gntJyXl.strCsGc = "备核版本/出厂主核版本 verssion -k";
    sendGcEvent();

    m_nsshLj = 13;
    sshConnect();

}

void CGntTest::bhb(QString s)
{
    m_ptimer->stop();

    QString s2 = s.trimmed().right(s.size() -16);

    m_gntJyXl.strCsGc = "备核版本/出厂主核版本："+ s2 + "\n备核版本/出厂主核版本设定值：" + m_gntParam.scuXTParam.strBHB;
    sendGcEvent();

    if(s2 == m_gntParam.scuXTParam.strBHB)
    {
        m_gntJyXl.strCsGc = "备核版本/出厂主核版本合格";
        m_gntJyJg.bCsXlJg = true;
    }
    else
    {
        m_gntJyXl.strCsGc = "备核版本/出厂主核版本不合格";
        m_gntJyJg.bCsXlJg = false;
    }

    sendGcEvent();
    sendJgEvent();


    m_gntJyJg.strCsXl = m_gntJyXl.strCsXl = "容器版本";
    m_gntJyXl.strCsGc = "容器版本 docker -v";
    sendGcEvent();

    m_nsshLj = 14;
    sshConnect();

}

void CGntTest::rqb(QString s)
{
    m_ptimer->stop();

    QString s2 = s.trimmed().mid(15, s.indexOf(",")-15);

    m_gntJyXl.strCsGc = "容器版本："+ s2 + "\n容器版本设定值：" + m_gntParam.scuXTParam.strRQB;
    sendGcEvent();

    if(s2 == m_gntParam.scuXTParam.strRQB)
    {
        m_gntJyXl.strCsGc = "容器版本合格";
        m_gntJyJg.bCsXlJg = true;
    }
    else
    {
        m_gntJyXl.strCsGc = "容器版本不合格";
        m_gntJyJg.bCsXlJg = false;
    }

    sendGcEvent();
    sendJgEvent();


    m_gntJyJg.strCsXl = m_gntJyXl.strCsXl = "交采程序版本";
    m_gntJyXl.strCsGc = "交采程序版本 read_jc_ver -v";
    sendGcEvent();

    m_nJcQueryNum = 1;
    m_nsshLj = 15;
    sshConnect();

}

void CGntTest::jcb(QString s)
{
    m_ptimer->stop();



    QString ss = s.trimmed().right(s.trimmed().size()-QString("软件版本号:").size());
    QStringList sList = ss.split("\n");

    QString sJcbbh;
    for(int m = 0; m < sList.size(); ++m)
    {
        if(sList[m].startsWith("Software version:"))
        {
            QStringList strList =  sList[m].split(":");
            if(strList.size()  > 1 )
            {
                sJcbbh = strList[1];
                break;
            }
        }
    }

    if(m_nJcQueryNum < 30 && (sJcbbh.contains("v0.0") || sJcbbh.isEmpty()))
    {
        QThread::sleep(3);
        ++m_nJcQueryNum;
        m_nsshLj = 15;
        sshConnect();
        return;
    }

    m_gntJyXl.strCsGc = "交采程序版本："+ sJcbbh + "\n交采程序版本设定值：" + m_gntParam.scuXTParam.strJCB;
    sendGcEvent();
    if(sJcbbh == m_gntParam.scuXTParam.strJCB)
    {
        m_gntJyXl.strCsGc = "交采程序版本合格";
        m_gntJyJg.bCsXlJg = true;
    }
    else
    {
        m_gntJyXl.strCsGc = "交采程序版本不合格";
        m_gntJyJg.bCsXlJg = false;
    }
    sendGcEvent();
    sendJgEvent();

    m_gntJyJg.strCsXl = m_gntJyXl.strCsXl = "关键系统文件版本";
    m_gntJyXl.strCsGc = "关键系统文件版本 cat /var/log/urdebug.log";
    sendGcEvent();

    m_nsshLj = 16;
    sshConnect();
}

void CGntTest::gjxtb(QString s)
{
    m_ptimer->stop();

    QString s2 = s.trimmed().mid(4, s.size()-9);
    m_gntJyXl.strCsGc = "关键系统文件版本："+ s2 + "\n关键系统文件版本设定值：" + m_gntParam.scuXTParam.strGJXT;
    sendGcEvent();

    if(s2 == m_gntParam.scuXTParam.strGJXT)
    {
        m_gntJyXl.strCsGc = "关键系统文件版本合格";
        m_gntJyJg.bCsXlJg = true;
    }
    else
    {
        m_gntJyXl.strCsGc = "关键系统文件版本不合格";
        m_gntJyJg.bCsXlJg = false;
    }

    sendGcEvent();
    sendJgEvent();

    m_gntJyJg.strCsXl = m_gntJyXl.strCsXl = "操作系统版本";
    m_gntJyXl.strCsGc = "操作系统版本 cat /etc/os-version.yaml";
    sendGcEvent();
    m_nsshLj = 17;
    sshConnect();
}

void CGntTest::czxtb(QString s)
{
    m_ptimer->stop();

    QString s2 = s.trimmed().right(s.size() - s.indexOf("name:") -6);
    m_gntJyXl.strCsGc = "操作系统版本："+ s2 + "\n操作系统版本设定值：" + m_gntParam.scuXTParam.strCZXT;
    sendGcEvent();

    if(s2 == m_gntParam.scuXTParam.strCZXT)
    {
        m_gntJyXl.strCsGc = "操作系统版本合格";
        m_gntJyJg.bCsXlJg = true;
    }
    else
    {
        m_gntJyXl.strCsGc = "操作系统版本不合格";
        m_gntJyJg.bCsXlJg = false;
    }

    sendGcEvent();
    sendJgEvent();

    check();
}

void CGntTest::rqchk(QString s)
{
    m_ptimer->stop();

    QStringList strList = s.trimmed().split("\n");
    bool bCheck = true;

    bool brq = false;
    QString strRqName;
    for(int m = 1; m < strList.size(); ++m)
    {
        if(strList[m].isEmpty())
            continue;
        if(strList[m].trimmed().startsWith("container name:"))
        {
            QStringList sList = strList[m].split(": ");
            if(sList.size() != 2)
                continue;
            strRqName = sList[1].trimmed();
            auto iter = m_gntParam.rqApp.mapScuRq.find(strRqName);
            if(iter == m_gntParam.rqApp.mapScuRq.end())
            {
                m_gntJyXl.strCsGc = "SCU存在未要求安装的容器：" + strRqName;
                sendGcEvent();
                bCheck = false;
                brq = false;
                continue;
            }
            else
            {
                m_gntJyXl.strCsGc = "SCU中要求安装的容器：" + strRqName + "已安装";
                sendGcEvent();
                brq = true;
                iter->second = true;
            }
        }
        else if(brq && strList[m].trimmed().startsWith("container version:"))
        {
            QStringList sList = strList[m].split(": ");
            if(sList.size() != 2)
                continue;
            auto iter = m_gntParam.rqApp.mapScuRqHash.find(strRqName);
            if(iter == m_gntParam.rqApp.mapScuRqHash.end())
                continue;
            if(sList[1].trimmed() != iter->second)
            {
                m_gntJyXl.strCsGc = "容器：" + strRqName + ",读取版本号："+ sList[1].trimmed() + "， 设置版本号：" + iter->second + "。版本号不一致";
                sendGcEvent();
                bCheck = false;
            }
            else
            {
                m_gntJyXl.strCsGc = "容器：" + strRqName + ",读取版本号："+ sList[1].trimmed() + "， 设置版本号：" + iter->second + "。版本号一致";
                sendGcEvent();
            }
        }
        else if(brq && strList[m].trimmed().startsWith("container status:"))
        {
            QStringList sList = strList[m].split(": ");
            if(sList.size() != 2)
                continue;
            if(sList[1].trimmed() != "running")
            {
                m_gntJyXl.strCsGc = "容器：" + strRqName + "状态不是running";
                sendGcEvent();
                bCheck = false;
            }
            else
            {
                m_gntJyXl.strCsGc = "容器：" + strRqName + "状态是running";
                sendGcEvent();
            }
        }

    }
    auto iter = m_gntParam.rqApp.mapScuRq.begin();
    while(iter != m_gntParam.rqApp.mapScuRq.end())
    {
        if(!iter->second)
        {
            bCheck = false;
            m_gntJyXl.strCsGc = "指定配置容器：" + iter->first + "未安装";
            sendGcEvent();
        }
        ++iter;
    }

    m_gntJyJg.bCsXlJg = bCheck;
    sendJgEvent();

    if(!bCheck)
    {
        checkItem();
    }
    else
    {
        check();
    }

}

void CGntTest::APPchk(QString s)
{
    QStringList strList = s.trimmed().split("\n");
    bool bCheck = true;
    m_vtRqName.clear();

    for(int m = 1 ; m < strList.size(); ++m)
    {
        QStringList sList = strList[m].split("\t");
        m_vtRqName.push_back(sList[sList.size()-1].trimmed());
    }
    APPitem();
}

void CGntTest::apprealchk(QString s)
{
    if(s.startsWith("File is not exist."))
    {
        APPitem();
        return;
    }
    bool bChkOthers = false;
    QString strAppName;
    QStringList sList =  s.split("\n");
    for(int m = 0; m < sList.size(); ++m)
    {
        QStringList strList = sList[m].split(":");
        if(strList.size() < 2)
            continue;
        if(strList[0].startsWith("App name"))
        {
            strAppName =  strList[1].trimmed();
            auto iter = m_gntParam.rqApp.mapScuApp.find(strAppName);
            if(iter == m_gntParam.rqApp.mapScuApp.end())
            {
                m_gntJyXl.strCsGc = "SCU中存在未要求安装的APP：" + strAppName;
                sendGcEvent();
                bChkOthers = false;
                m_bScuAppChk = false;
            }
            else
            {
                m_gntJyXl.strCsGc = "SCU指定安装App：" + strAppName + "已安装";
                sendGcEvent();
                iter->second = true;
                bChkOthers = true;
            }
        }
        else if(bChkOthers && strList[0].startsWith("CPU usage"))
        {
            QString s = strList[1].trimmed().left(strList[1].trimmed().size()-1);
            if(s.toFloat() > m_gntParam.scuXTParam.strCPU.toFloat())
            {
                m_bScuAppChk = false;
                m_gntJyXl.strCsGc = "APP：" + strAppName + " CPU usage = " + s + ",超出最大值：" + m_gntParam.scuXTParam.strCPU;
                sendGcEvent();
            }
            else
            {
                m_gntJyXl.strCsGc = "APP：" + strAppName + " CPU usage = " + s + ",未超出最大值：" + m_gntParam.scuXTParam.strCPU;
                sendGcEvent();
            }
        }
        else if(bChkOthers && strList[0].startsWith("Mem usage"))
        {
            QString s = strList[1].trimmed().left(strList[1].trimmed().size()-1);
            if(s.toFloat() > m_gntParam.scuXTParam.strMem.toFloat())
            {
                m_bScuAppChk = false;
                m_gntJyXl.strCsGc = "APP：" + strAppName + " Mem usage = " + s + ",超出最大值：" + m_gntParam.scuXTParam.strMem;
                sendGcEvent();
            }
            else
            {
                m_gntJyXl.strCsGc = "APP：" + strAppName + " Mem usage = " + s + ",未超出最大值：" + m_gntParam.scuXTParam.strMem;
                sendGcEvent();
            }
        }
        else if(bChkOthers && strList[0].startsWith("Service status"))
        {
            QString s = strList[1].trimmed();
            if(s != "running")
            {
                m_bScuAppChk = false;
                m_gntJyXl.strCsGc = "APP：" + strAppName + "状态为" + s;
                sendGcEvent();
            }
            else
            {
                m_vtCompareApp.push_back(strAppName);
                m_gntJyXl.strCsGc = "APP：" + strAppName + "状态为" + s;
                sendGcEvent();
            }
        }
        else if(bChkOthers && strList[0].startsWith("APP hash"))
        {
            QString s = strList[1].trimmed();
            auto tor = m_gntParam.rqApp.mapScuAppHash.find(strAppName);
            if(tor != m_gntParam.rqApp.mapScuAppHash.end())
            {
                if(tor->second != s)
                {
                    m_bScuAppChk = false;
                    m_gntJyXl.strCsGc = "APP：" + strAppName + "读取hash：" + s + "，设定hash：" + tor->second + "。hash不一致";
                    sendGcEvent();
                }
                else
                {
                    m_gntJyXl.strCsGc = "APP：" + strAppName + "读取hash：" + s + "，设定hash：" + tor->second + "。hash一致";
                    sendGcEvent();
                }
            }
        }
    }
    APPitem();
}

void CGntTest::rqwappchk()
{
    m_gntJyXl.strCsXl = m_gntJyJg.strCsXl = "容器外APP";
    m_gntJyXl.strCsGc = "检查容器外APP";
    sendGcEvent();

    m_vtrqwApp.push_back("iotManager");
    m_vtrqwApp.push_back("scsMonitor");
    m_vtrqwApp.push_back("wirelessDCM");
    m_vtrqwApp.push_back("IotAgent");
    m_vtrqwApp.push_back("ExecShell");

    m_nsshLj = 6;
    sshConnect();
}

void CGntTest::rqwApp(QString s)
{
    QStringList sList = s.trimmed().split("\n");
    for(int m = 0; m < sList.size(); ++m)
    {
        int nIndex = sList[m].trimmed().lastIndexOf("/");
        if(nIndex == -1)
            continue;
        QString appName =  sList[m].trimmed().right(sList[m].trimmed().size()- nIndex -1);

        if(appName != m_strRqwApp)
        {
            continue;
        }
        auto iter = m_gntParam.rqApp.mapRqwApp.find(appName);
        if(iter == m_gntParam.rqApp.mapRqwApp.end())
        {
            m_gntJyXl.strCsGc = "存在未指定安装容器外APP：" + m_strRqwApp ;
            sendGcEvent();
            m_bScuRqwAppChk = false;
        }
        else
        {
            m_gntJyXl.strCsGc = "要求安装的容器外APP：" + m_strRqwApp + "已安装" ;
            sendGcEvent();
            iter->second = true;
        }
     }

    rqwAppItem();

}

void CGntTest::zdcs(QString)
{

    if(!g_bTestSign)
    {
        check();
        return;
    }
    if(m_vtCmd.size() == 0)
    {
        m_nsshLj = 20;
        sshConnect();
        return;
    }

    m_nsshLj = 19;
    sshConnect();
}

void CGntTest::zdcsjg(QString ss)
{
    m_bcheck = true;
    QStringList sList = ss.split("\n");
    for(int m = 0; m < sList.size(); ++m)
    {
        QString s = sList[m].trimmed();
        if(!s.startsWith("itemNo"))
        {
            continue;
        }

        int nIndex1 = s.indexOf("key");
        int nIndex2 = s.indexOf("value");
        QString strKey = s.mid(nIndex1+6, nIndex2-nIndex1-7);
        QString strValue = s.right(s.size() - nIndex2 -8);

        if(strKey == "devName")
        {
            if( strValue== m_gntParam.scuParam.strZdLx)
            {
                m_gntJyXl.strCsGc = "devName：" + m_gntParam.scuParam.strZdLx +"设置成功";
            }
            else
            {
                m_bcheck = false;
                m_gntJyXl.strCsGc = "devName：" +m_gntParam.scuParam.strZdLx +"设置失败";
            }
            sendGcEvent();
        }
        else if (strKey == "devType")
        {
            if(strValue == m_gntParam.scuParam.strZdLx)
            {
                m_gntJyXl.strCsGc = "devType:" + m_gntParam.scuParam.strZdLx+"设置成功";
            }
            else
            {
                m_bcheck = false;
                m_gntJyXl.strCsGc = "devType:" + m_gntParam.scuParam.strZdLx+"设置失败";
            }
            sendGcEvent();
        }
        else if(strKey == "hardwareModel")
        {
            if(strValue == m_gntParam.scuParam.strXh)
            {
                m_gntJyXl.strCsGc = "hardwareModel: "+m_gntParam.scuParam.strXh+"设置成功";
            }
            else
            {
                m_bcheck = false;
                m_gntJyXl.strCsGc = "hardwareModel: "+m_gntParam.scuParam.strXh+"设置失败";
            }
            sendGcEvent();
        }
        else if (strKey == "hardwareVersion")
        {
            if(strValue == m_gntParam.scuParam.strYjBb)
            {
                m_gntJyXl.strCsGc = "hardwareVersion:"+ m_gntParam.scuParam.strYjBb + "设置成功";
            }
            else
            {
                m_bcheck = false;
                m_gntJyXl.strCsGc = "hardwareVersion:"+ m_gntParam.scuParam.strYjBb + "设置失败";
            }
            sendGcEvent();
        }
        else if(strKey == "vendorId")
        {
            if(strValue == m_gntParam.scuParam.strCs)
            {
                m_gntJyXl.strCsGc = "vendorId:"+ m_gntParam.scuParam.strCs+"设置成功";
            }
            else
            {
                m_bcheck = false;
                m_gntJyXl.strCsGc = "vendorId:"+ m_gntParam.scuParam.strCs+"设置失败";
            }
            sendGcEvent();
        }
        else if(strKey == "devId" || strKey == "devID" || strKey == "devSn")
        {
            if(strValue == m_gntParam.scuParam.strID)
            {
                m_gntJyXl.strCsGc = strKey + ":"+m_gntParam.scuParam.strID+ " 设置成功";
            }
            else
            {
                m_bcheck = false;
                m_gntJyXl.strCsGc = strKey + ":"+m_gntParam.scuParam.strID+ " 设置失败";
            }
            sendGcEvent();
        }
        else if (strKey == "logicalAddr")
        {
            if(strValue == m_gntParam.scuParam.strLjdz)
            {
                m_gntJyXl.strCsGc = "logicalAddr:"+m_gntParam.scuParam.strLjdz+"设置成功";
            }
            else
            {
                m_bcheck = false;
                m_gntJyXl.strCsGc = "logicalAddr:"+m_gntParam.scuParam.strLjdz+"设置失败";
            }
            sendGcEvent();
        }
        else if (strKey == "manufDate")
        {
            if(strValue == m_gntParam.scuParam.strScrq)
            {
                m_gntJyXl.strCsGc = "manufDate:" + m_gntParam.scuParam.strScrq + "设置成功";
            }
            else
            {
                m_bcheck = false;
                m_gntJyXl.strCsGc = "manufDate:" + m_gntParam.scuParam.strScrq + "设置失败";
            }
            sendGcEvent();
        }
        else if(strKey == "esn")
        {
            if(strValue == m_gntParam.scuParam.strEsn)
            {
                m_gntJyXl.strCsGc = "设备esn:" +strValue + "面膜esn：" + m_gntParam.scuParam.strEsn + "匹配成功";
            }
            else
            {
                m_bcheck = false;
                m_gntJyXl.strCsGc = "设备esn:" +strValue + "面膜esn：" + m_gntParam.scuParam.strEsn + "匹配失败";
            }
            sendGcEvent();
        }
    }

    m_gntJyXl.strCsGc = "设置通信地址";
    sendGcEvent();

    m_nsshLj = 21;
    sshConnect();
}

void CGntTest::dk9001(QString s)
{
    ++ m_nReTcp;
    if(s != "no dk")
    {
        m_gntJyXl.strCsGc = "端口打开成功";
        sendGcEvent();
    }
    if(m_strChkItem == "698参数配置")
    {
        m_gntJyJg.bCsXlJg = true;
        sendJgEvent();
    }


    if(m_pSocket != nullptr && m_pSocket->state() == QTcpSocket::ConnectedState)
    {
        m_pSocket->close();
    }

    m_pSocket = new QTcpSocket();

    connect(m_pSocket, SIGNAL(stateChanged(QAbstractSocket::SocketState)), this, SLOT(on_tcp_stateChanged(QAbstractSocket::SocketState)));
    connect(m_pSocket, SIGNAL(readyRead()), this, SLOT(on_tcp_readyRead()));
    connect(m_pSocket, SIGNAL(disconnected()), this, SLOT(on_tcp_error(QAbstractSocket::SocketError)));
    connect(m_pSocket, SIGNAL(error(QAbstractSocket::SocketError)), this, SLOT(on_tcp_error(QAbstractSocket::SocketError)));
    m_pSocket->connectToHost(m_gntParam.scuParam.strIp, 9001);

}

void CGntTest::ds1(QString s)
{
    QDateTime dt = QDateTime::fromString(s.trimmed(), "yyyyMMddHHmmss");
    m_gntJyXl.strCsGc = "scu时间：" + dt.toString("yyyy-MM-ddTHH:mm:ss") + "， 标准时间："+ m_dt2.toString("yyyy-MM-ddTHH:mm:ss");
    sendGcEvent();
    if(qAbs(dt.secsTo(m_dt2)) < 5)
    {
        m_gntJyXl.strCsGc = "第一次时间对比差值小于5s, 成功";
        sendGcEvent();
        QThread::sleep(10);
        m_nsshLj = 25;
        sshConnect();

    }
    else
    {
        m_gntJyXl.strCsGc = "第一次时间对比差值大于5s, 失败";
        sendGcEvent();
        m_gntJyJg.bCsXlJg = false;
        sendJgEvent();

        sbdsJc();
    }
}

void CGntTest::ds2(QString s)
{
    QDateTime dt = QDateTime::fromString(s.trimmed(), "yyyyMMddHHmmss");
    QDateTime dt2 = QDateTime::currentDateTime();
    m_gntJyXl.strCsGc = "scu时间：" + dt.toString("yyyy-MM-ddTHH:mm:ss") + "， 标准时间："+ m_dt2.toString("yyyy-MM-ddTHH:mm:ss");
    sendGcEvent();
    if(qAbs(dt.secsTo(m_dt2)) < 5)
    {
        m_gntJyXl.strCsGc = "第二次时间对比差值小于5s, 成功";
        sendGcEvent();
        m_gntJyJg.bCsXlJg = true;
        sendJgEvent();
        check();
    }
    else
    {
        m_gntJyXl.strCsGc = "第二次时间对比差值大于5s, 失败";
        sendGcEvent();
        m_gntJyJg.bCsXlJg = false;
        sendJgEvent();

        sbdsJc();
    }
}

void CGntTest::tty(QString s)
{
    m_ptimer->stop();

    bool bFind = false;
    QStringList sList =  s.trimmed().split("\n");
    for(int m =0; m < sList.size(); ++m)
    {
        if(sList[m] == m_strtty)
        {
            bFind = true;
            break;
        }
    }
    if(bFind)
    {
        m_gntJyXl.strCsGc = "找到文件：" + m_strtty;
        sendGcEvent();
    }
    else
    {
        m_bcheck = false;
        m_gntJyXl.strCsGc = "未找到文件：" + m_strtty;
        sendGcEvent();
    }

    hplccheck();
}

void CGntTest::g4tty(QString s)
{
    m_ptimer->stop();

    bool bFind = false;
    QStringList sList =  s.trimmed().split("\n");
    for(int m =0; m < sList.size(); ++m)
    {
        if(sList[m] == m_strtty)
        {
            bFind = true;
            break;
        }
    }
    if(bFind)
    {
        m_gntJyXl.strCsGc = "找到文件：" + m_strtty;
        sendGcEvent();
    }
    else
    {
        m_bcheck = false;
        m_gntJyXl.strCsGc = "未找到文件：" + m_strtty;
        sendGcEvent();
    }
    g4check();

}

void CGntTest::devapptime(QString s)
{
    m_bcheck = true;
    QStringList sList = s.trimmed().split(":");
    if(sList.size() < 3)
    {
        m_gntJyXl.strCsGc = "DeviceManager时间获取失败";
        sendGcEvent();
        m_bcheck = false;
    }
    else
    {
        m_gntJyXl.strCsGc = "获取成功， DeviceManager时间：" + s.trimmed();
        sendGcEvent();

        m_devtime = QTime::fromString(s, "HH:mm:ss");
    }

    m_nsshLj = 48;
    sshConnect();
}

void CGntTest::apptime(QString s)
{
    m_nAppNum = 0;
    m_ptimer->stop();
    QStringList sList = s.trimmed().split(" ");
    if(sList.size() < 2)
    {
        m_gntJyXl.strCsGc = "获取失败";
        sendGcEvent();
        m_bcheck = false;
    }
    else
    {
        QStringList strList = sList[1].split(":");
        if(strList.size() != 3)
        {
            m_gntJyXl.strCsGc = "获取失败";
            sendGcEvent();
            m_bcheck = false;
        }
        else
        {
            m_gntJyXl.strCsGc = "获取成功， app启动时间：" + s.trimmed();
            sendGcEvent();

            QTime aptime(strList[0].toInt(), strList[1].toInt(), strList[2].toInt());
            int ndiff = m_devtime.secsTo(aptime);
            if(ndiff <= 180 )
            {
                m_gntJyXl.strCsGc ="app启动时间与DeviceManager时间差值不大于180秒，合格";
            }
            else
            {
                m_gntJyXl.strCsGc ="app启动时间与DeviceManager时间差值大于180秒，不合格";
                m_bcheck = false;
            }
            sendGcEvent();

        }
    }

    if(m_vtCompareApp.size() == 0)
    {
        m_gntJyJg.bCsXlJg = m_bcheck;
        sendJgEvent();

        rqwappchk();
    }
    else
    {
        m_nsshLj = 48;
        sshConnect();
    }
}

void CGntTest::rs4851(QString)
{
    QStringList sList = m_gntParam.strRs485.split("@");
    if(!serialConnect(sList[0]))
    {
        m_gntJyXl.strCsGc = "打开串口" + sList[0] + "失败";
        sendGcEvent();
        m_gntJyJg.bCsXlJg = false;
        sendJgEvent();

        m_nsshLj = 51;
        sshConnect();
    }
    else
    {
        m_nsshLj = 50;
        sshConnect();
    }
}

void CGntTest::rs4851_1(QString s)
{
    m_Ck += s;
    if(m_Ck.trimmed().contains("4851"))
    {
        m_nRS485Num = 0;
        m_ntimeout2 = 9999;
        m_ptimer2->stop();
        m_Ck.clear();
        m_gntJyXl.strCsGc = "SCU收到串口发送的4851, 合格";
        sendGcEvent();

        m_gntJyJg.bCsXlJg = true;
        sendJgEvent();

        m_nsshLj = 51;
        sshConnect();
    }
}

void CGntTest::rs485II(QString)
{
    QStringList sList = m_gntParam.strRs485.split("@");
    if(!serialConnect(sList[1]))
    {
        m_gntJyXl.strCsGc = "打开串口" + sList[1] + "失败";
        sendGcEvent();
        m_gntJyJg.bCsXlJg = false;
        sendJgEvent();

        m_nsshLj = 53;
        sshConnect();
    }
    else
    {
        m_nsshLj = 52;
        sshConnect();
    }
}

void CGntTest::rs485II_1(QString s)
{
    m_Ck += s;
    if(m_Ck.trimmed().contains("4852"))
    {
        m_nRS485Num = 0;
        m_ntimeout2 = 9999;
        m_ptimer2->stop();
        m_Ck.clear();
        m_ptimer->stop();
        m_gntJyXl.strCsGc = "SCU收到串口发送的4852, 合格";
        sendGcEvent();

        m_gntJyJg.bCsXlJg = true;
        sendJgEvent();

        m_nsshLj = 53;
        sshConnect();
    }
}

void CGntTest::rs485III(QString)
{
    QStringList sList = m_gntParam.strRs485.split("@");
    if(!serialConnect(sList[2]))
    {
        m_gntJyXl.strCsGc = "打开串口" + sList[2] + "失败";
        sendGcEvent();
        m_gntJyJg.bCsXlJg = false;
        sendJgEvent();

        m_nsshLj = 55;
        sshConnect();
    }
    else
    {

        m_nsshLj = 54;
        sshConnect();
    }
}

void CGntTest::rs485III_1(QString s)
{
    m_Ck += s;
    if(m_Ck.trimmed().contains("4853"))
    {
        m_nRS485Num = 0;
        m_ntimeout2 = 9999;
        m_ptimer2->stop();

        m_Ck.clear();
        m_ptimer->stop();
        m_gntJyXl.strCsGc = "SCU收到串口发送的4853, 合格";
        sendGcEvent();

        m_gntJyJg.bCsXlJg = true;
        sendJgEvent();

        m_nsshLj = 55;
        sshConnect();
    }
}

void CGntTest::rs485IV(QString)
{
    sshClose();
    QStringList sList = m_gntParam.strRs485.split("@");
    if(!serialConnect(sList[3]))
    {
        m_gntJyXl.strCsGc = "打开串口" + sList[3] + "失败";
        sendGcEvent();
        m_gntJyJg.bCsXlJg = false;
        sendJgEvent();
        check();

    }
    else
    {
        m_nsshLj = 56;
        sshConnect();
    }
}

void CGntTest::rs485IV_1(QString s)
{
    m_Ck += s;
    if(m_Ck.trimmed().contains("4854"))
    {
        m_nRS485Num = 0;
        m_ntimeout2 = 9999;
        m_ptimer2->stop();

        m_Ck.clear();
        m_ptimer->stop();
        m_gntJyXl.strCsGc = "SCU收到串口发送的4854, 合格";
        sendGcEvent();

        m_gntJyJg.bCsXlJg = true;
        sendJgEvent();

        if(m_pRs485 != nullptr)
        {
            m_pRs485->close();
        }
        sshClose();

        check();

    }
}

void CGntTest::devctl(QString s)
{
    QStringList sList = s.trimmed().split("\n");
    if(sList.size() == 2)
    {
        QString ss = sList[1].trimmed();
        m_gntJyXl.strCsGc = "本地通讯模块查询值：" + ss + ", 设定值：" + m_gntParam.other.strBdtxmk;
        sendGcEvent();
        if(ss != m_gntParam.other.strBdtxmk)
        {
            m_bcheck = false;
        }
    }
    m_gntJyJg.bCsXlJg = m_bcheck;
    sendJgEvent();

    m_bcheck = true;
    m_gntJyXl.strCsXl = m_gntJyJg.strCsXl = "4G";


    m_vtCmd.clear();
    m_vtCmd.push_back("ls /dev/ | grep ttyLTE0_0");
    m_vtCmd.push_back("ls /dev/ | grep ttyLTE0_1");
    m_vtCmd.push_back("ls /dev/ | grep ttyLTE0_2");

    if(m_gntParam.other.bsm4g)
    {
        m_vtCmd.push_back("ls /dev/ |grep ttyLTE1_0");
        m_vtCmd.push_back("ls /dev/ |grep ttyLTE1_1");
        m_vtCmd.push_back("ls /dev/ |grep ttyLTE1_2");
    }
    m_nsshLj = 44;
    sshConnect();
}

void CGntTest::mk4g(QString s)
{
   m_gntJyXl.strCsGc = "命令执行成功";
   sendGcEvent();
   if(s.startsWith("the response timed out or the time was modified"))
   {
       m_gntJyXl.strCsGc = "未获取到有效值";
       sendGcEvent();
       m_gntJyJg.bCsXlJg = false;
       sendJgEvent();

       checkItem();
       return;
   }
    QStringList sList =  s.trimmed().split("\n"); 
    for(int m = 0; m < sList.size(); ++m)
    {
        if(sList[m].startsWith("Firmware Version"))
        {
            QStringList strList = sList[m].split("=");
            if(strList.size() == 2)
            {
                m_gntJyXl.strCsGc = "Firmware Version="+strList[1].trimmed() + "，设定值：" + m_gntParam.other.strFirv;
                sendGcEvent();
                if(strList[1].trimmed() != m_gntParam.other.strFirv)
                {
                    m_bcheck = false;
                }
            }
        }
        else if(sList[m].startsWith("Hardware Version"))
        {
            QStringList strList = sList[m].split("=");
            if(strList.size() == 2)
            {
                m_gntJyXl.strCsGc = "Hardware Version="+strList[1].trimmed() + "，设定值：" + m_gntParam.other.strHarv;
                sendGcEvent();
                if(strList[1].trimmed() != m_gntParam.other.strHarv)
                {
                    m_bcheck = false;
                }
            }

            if(!m_gntParam.other.bsm4g)
            {
                break;
            }
        }
    }

    if(m_bcheck)
    {
        m_gntJyJg.bCsXlJg = true;
        sendJgEvent();

        check();
    }
    else
    {
        m_gntJyJg.bCsXlJg = false;
        sendJgEvent();

        checkItem();
    }
}

void CGntTest::dkzk(QString s)
{
    m_ptimer->stop();
    bool bdk = true;
    bool bdk2404 = false;
    QStringList sList = s.trimmed().split("\n");
    for(int m = 0; m < sList.size(); ++ m)
    {
        QStringList strList = sList[m].split(":");
        if(strList.size() > 1)
        {
            QString s = strList[strList.size()-1].trimmed();
            m_gntJyXl.strCsGc = "存在端口：" + s;
            sendGcEvent();
            if(s == "2404")
            {
                bdk2404 = true;
                continue;
            }
            else if(s == "8888")
            {
                continue;
            }
            bdk = false;
        }
    }

    if(bdk)
    {

        m_gntJyXl.strCsGc = "未找到2404、8888以外的端口";
        sendGcEvent();

        m_gntJyJg.bCsXlJg = true;

        if(!bdk2404)
        {
            m_gntJyXl.strCsGc += ", 且未找到2404端口";
            m_gntJyJg.bCsXlJg = false;
        }

        sendJgEvent();
        if(m_gntJyJg.bCsXlJg)
        {
            check();
        }
        else
        {
            checkItem();
        }

    }
    else
    {
        m_gntJyXl.strCsGc = "找到2404、8888以外的端口";
        if(!bdk2404)
        {
            m_gntJyXl.strCsGc += ", 且未找到2404端口";
            m_gntJyJg.bCsXlJg = false;
        }
        sendGcEvent();  
        m_gntJyJg.bCsXlJg = false;
        sendJgEvent();

        checkItem();
    }
}

void CGntTest::hbdy(QString)
{
    m_ptimer->stop();

    m_gntJyXl.strCsGc = "SCU停电第" + QString::number(m_nHbdy *60) + "秒后后备电源检查正常";
    sendGcEvent();
    if(m_nHbdy == 3)
    {
        m_gntJyJg.bCsXlJg = true;
        sendJgEvent();

        check();
        return;
    }
    else
    {
        hbdycheck();

    }
}

void CGntTest::setSW1(QString)
{
    m_ptimer->stop();
    m_nsshLj = 39;
    sshConnect();

}

void CGntTest::setSW2(QString)
{
    m_ptimer->stop();
    QThread::sleep(5);
    check();
}

void CGntTest::mqttIot(QString s)
{
    m_ptimer->stop();
    m_gntJyXl.strCsGc = "读取成功";
    sendGcEvent();
    QString s3 = "\"" + m_gntParam.other.strMqttIotIp + "\",";
    bool bsame = true;
    QString ss = s.trimmed();
    QString s5;
    QStringList  sList= ss.split("\r\n");
    for(int m = 0; m < sList.size(); ++m)
    {
        if(!sList[m].trimmed().startsWith("\"HOST\""))
        {
            s5 += sList[m];
            s5 += "\r\n";
            continue;
        }
        QStringList strList = sList[m].trimmed().split(":");
        if(strList.size() == 2)
        {
            if(strList[1].trimmed() != s3)
            {
                s5 += "    ";
                s5 += strList[0];
                s5 += ": ";
                s5 += s3;
                s5 += "\r\n";
                bsame = false;
            }
        }
    }


    if(bsame)
    {
        m_gntJyXl.strCsGc = "IP一致，不进行处理";
        sendGcEvent();
        m_gntJyJg.bCsXlJg = true;
        sendJgEvent();

        check();
    }
    else
    {
        QString s = QDir::currentPath();
        int nindex = s.lastIndexOf("/");
        QString s2 = s.left(nindex+1);

        m_strDestFile = s2 + "download/" ;

        QDir dir;
        if(!dir.exists(m_strDestFile))
        {
            dir.mkdir(m_strDestFile);
        }
        m_strDestFile +=  QString::number(m_gntParam.nBw);
        m_strDestFile += "/";
        if(!dir.exists(m_strDestFile))
        {
            dir.mkdir(m_strDestFile);
        }

        // 删除所有文件
        QDir dir2(m_strDestFile);
        QStringList dirList = dir2.entryList();
        for(int m = 0; m < dirList.size(); ++m)
        {
            if(dirList[m] == "." || dirList[m] == "..")
                continue;
           dir2.remove(dirList[m]);
        }

        m_gntJyXl.strCsGc = "IP不一致，重新生成文件：paramFile.json到" + m_strDestFile;
        sendGcEvent();

        m_strDestFile +=  "paramFile.json";

        QFile file(m_strDestFile);
        if(!file.open(QFile::WriteOnly))
        {
            m_gntJyXl.strCsGc = "打开文件：" + m_strDestFile + "失败";
            sendGcEvent();
            m_gntJyJg.bCsXlJg = false;
            sendJgEvent();
            checkItem();
            return;
        }
        file.write(s5.toUtf8());
        file.close();

        m_gntJyXl.strCsGc = "文件生成成功, 开始上传到SCU /tmp/路径下";
        sendGcEvent();

        m_nsshLj = 43;
        sshConnect();
    }
}

void CGntTest::mqttIot2(QString s)
{
    m_gntJyXl.strCsGc = "读取成功";
    sendGcEvent();

    QString s3 = "\"" + m_gntParam.other.strMqttIotIp + "\",";
    bool bsame = true;
    QString ss = s.trimmed();
    QString s5;
    QStringList  sList= ss.split("\r\n");
    for(int m = 0; m < sList.size(); ++m)
    {
        if(!sList[m].trimmed().startsWith("\"HOST\""))
        {
            s5 += sList[m];
            s5 += "\r\n";
            continue;
        }
        QStringList strList = sList[m].trimmed().split(":");
        if(strList.size() == 2)
        {
            if(strList[1].trimmed() != s3)
            {
                s5 += "    ";
                s5 += strList[0];
                s5 += ": ";
                s5 += s3;
                s5 += "\r\n";
                bsame = false;
            }
        }
    }

    if(bsame)
    {
        m_gntJyXl.strCsGc = "确认成功, 与设置值一致";
        sendGcEvent();
        m_gntJyJg.bCsXlJg = true;
        sendJgEvent();

        check();
    }
    else
    {
        m_gntJyXl.strCsGc = "确认失败, 与设置值不一致";
        sendGcEvent();
        m_gntJyJg.bCsXlJg = false;
        sendJgEvent();

        checkItem();
    }
}

void CGntTest::ipVerify(QString s)
{

    QStringList strList = m_gntParam.scuParam.strIp.split(".");
    QString strIp = "192.168.1." + strList[3];
    QString s2;
    QStringList sList = s.trimmed().split("\n\n");
    for(int m = 0; m < sList.size(); ++m)
    {
        QString s = sList[m].left(5);

        int ndex1 = sList[m].indexOf("inet addr:");
        int ndex2 = sList[m].indexOf("Bcast:");
        int len = QString("inet addr:").size();
        s2 = sList[m].mid(ndex1 + len , ndex2-ndex1-len).trimmed();
        break;

    }
    if(s2 == strIp)
    {
        m_gntJyXl.strCsGc = "ip确认,合格，IP为：" + s2;
        sendGcEvent();
        m_gntJyJg.bCsXlJg = true;
        sendJgEvent(); 
    }
    else
    {
        m_gntJyXl.strCsGc = "ip确认, 不合格， IP为:" + s2;
        sendGcEvent();
        m_gntJyJg.bCsXlJg = false;
        sendJgEvent();
    }
    m_nLymm = 0;
    m_nsshLj = 122;
    sshConnect();
}

void CGntTest::esnQuery(QString s)
{
    QStringList strList = s.split("\n");
    for(int m = 0; m < strList.size(); ++m)
    {
        if(!strList[m].contains("key = esn"))
        {
            continue;
        }
        int nindex = strList[m].indexOf("value");

       QString ss = strList[m].right(strList[m].size() - nindex - 7);

       m_strEsn = ss.trimmed();
       break;
    }

    m_gntJyXl.strCsGc = "设置SCU设备SCMQTTIoT文件权限， 命令： echo Zgdky@guest123 | sudo -S chmod -R 777 /data/app/SCMQTTIoT/";
    sendGcEvent();

    m_nsshLj = 91;
    sshConnect();
}

void CGntTest::verifyfile(QString param)
{
    QJsonParseError error;
    QJsonDocument doc = QJsonDocument::fromJson(param.toUtf8());
    if(doc.isEmpty() || !doc.isObject())
    {
        m_gntJyXl.strCsGc = "文件/data/app/SCMQTTIoT/configFile/paramFile.json解析失败";
        sendGcEvent();
        m_gntJyJg.bCsXlJg = false;
        sendJgEvent();
        check();
        return;
    }

    QJsonObject obj =  doc.object();
    if(obj["HOST"] != m_gntParam.other.strMqttIotIp || obj["PORT"] != 1883 || obj["USERNAME"] != m_strEsn || obj["PASSWORD"] !=  ("GWSX@" + calculateSHA256(m_strEsn).left(20)))
    {
        m_gntJyXl.strCsGc = "确认文件， 结果错误";
        sendGcEvent();

        m_gntJyJg.bCsXlJg = false;
        sendJgEvent();
    }
    else
    {
        m_gntJyXl.strCsGc = "确认文件， 结果成功";
        sendGcEvent();

        m_gntJyJg.bCsXlJg = true;
        sendJgEvent();
    }
    check();
}

void CGntTest::queryEsamId(QString s)
{
    m_gntJyXl.strCsGc = "查询结果-" + s;
    sendGcEvent();

    QStringList sList = s.split(":");
    if(sList.size() != 2)
    {
        m_gntJyXl.strCsGc = "查询失败";
        sendGcEvent();
        m_gntJyJg.bCsXlJg = false;
        sendJgEvent();
        check();
        return;
    }

    m_strEsamId = sList[1].trimmed();

    m_gntJyXl.strCsGc = "根据esamid查找lisense文件";
    sendGcEvent();

    QString strFileName = m_strEsamId + ".txt";
    QDirIterator it(m_gntParam.other.strLicense, QStringList("*.txt"), QDir::Files);

    bool bfind = false;
    while (it.hasNext())
    {
        QString ss = it.next();
        if(it.fileName() == strFileName)
        {
            bfind = true;
            break;
        }
    }

    if(!bfind)
    {
        m_gntJyXl.strCsGc = "license文件查找失败";
        sendGcEvent();
        m_gntJyJg.bCsXlJg = false;
        sendJgEvent();
        check();
        return;
    }

    m_gntJyXl.strCsGc = "license文件查找成功";
    sendGcEvent();

    m_nsshLj = 96;
    sshConnect();


}

void CGntTest::devInfoExist(QString s)
{
    if(s.contains("devinfo"))
    {
        m_gntJyXl.strCsGc = "/data/devinfo文件存在";
        sendGcEvent();

        m_nsshLj = 97;
        sshConnect();
    }
    else
    {
        m_gntJyXl.strCsGc = "/data/devinfo文件不存在";
        sendGcEvent();

        m_nsshLj = 98;
        sshConnect();
    }
}

void CGntTest::licenseExist(QString s)
{
    if(s.contains("license"))
    {
        m_gntJyXl.strCsGc = "找到license文件";
        sendGcEvent();
    }
    else
    {
        m_gntJyXl.strCsGc = "未找到license文件";
        sendGcEvent();
        check();
        return;
    }

    // 重启SCU
    m_nsshLj = 104;
    sshConnect();
}

void CGntTest::fileClearVerify(QString s)
{
    bool bFindSign = false;
    QStringList strList = s.split("\n");
    for(int x = 0 ; x < strList.size(); ++x)
    {
        if(strList[x] == "license_not_verify")
        {
            bFindSign = true;
        }
    }

    if(bFindSign)
    {
        m_gntJyXl.strCsGc = "文件删除确认，结果失败， 存在要删除文件";
        m_gntJyJg.bCsXlJg = false;
    }
    else
    {
        m_gntJyXl.strCsGc = "文件删除确认，结果成功";
        m_gntJyJg.bCsXlJg = true;
    }
    sendGcEvent();
    sendJgEvent();

    check();

}

void CGntTest::bakFileVerify(QString s)
{
    m_ptimer->stop();
    bool bFind = false;
    QStringList strList = s.split("\n");
    for(int x = 0; x < strList.size(); ++x)
    {
        if(strList[x].trimmed() == "/lib/modules/hi1711_ko/shanxi_scu_old_uart_drv.ko")
        {
            bFind = true;
            break;
        }
    }
    if(bFind)
    {
        m_gntJyXl.strCsGc = "找到备份文件";
        sendGcEvent();
        m_nsshLj = 111;
        sshConnect();
    }
    else
    {
        m_gntJyXl.strCsGc = "未找到备份文件，失败";
        sendGcEvent();
        m_gntJyJg.bCsXlJg = false;
        sendJgEvent();
        checkItem();
    }
}

void CGntTest::newFileVerify(QString s)
{
    m_ptimer->stop();
    bool bFind = false;
    QStringList  strList = s.split("\n");
    for(int x = 0; x < strList.size(); ++x)
    {
        if(strList[x].trimmed() == "/lib/modules/hi1711_ko/uart_drv.ko")
        {
            bFind = true;
            break;
        }
    }
    if(bFind)
    {
        m_gntJyXl.strCsGc = " 找到文件";
        sendGcEvent();
        m_nsshLj = 113;
        sshConnect();
    }
    else
    {
        m_gntJyXl.strCsGc = "未找到文件， 失败";
        sendGcEvent();
        m_gntJyJg.bCsXlJg = false;
        sendJgEvent();
        check();
    }
}

void CGntTest::lymmVerify(QString s)
{
    bool bFind = false;
    m_ptimer->stop();
    QStringList strList = s.split("\n");
    for(int x = 0; x < strList.size(); ++x)
    {
        if(strList[x].contains("68 0f 01 00") || strList[x].contains("68 0f 00 00"))
        {
            bFind = true;
            break;
        }
    }
    if(bFind)
    {
        m_gntJyXl.strCsGc = "密码正确";
        sendGcEvent();
        m_gntJyJg.bCsXlJg = true;
        sendJgEvent();
    }
    else
    {
        if(m_nLymm > 50)
        {
            m_gntJyXl.strCsGc = "密码错误";
            sendGcEvent();
            m_gntJyJg.bCsXlJg = false;
            sendJgEvent();
        }
        else
        {
            m_nsshLj = 114;
            sshConnect();
            return;
        }
    }
    m_nLymm = 0;
    m_nsshLj = 117;
    sshConnect();
}

void CGntTest::lyqdVerify(QString s)
{
    m_ptimer->stop();
    if(m_nLymm > 3)
    {
        m_gntJyXl.strCsGc = "失败";
        sendGcEvent();
        m_gntJyJg.bCsXlJg = false;
        sendJgEvent();

        m_ntimeout = 61;
        m_ptimer->start(1000);
    }
    else
    {
        m_nsshLj = 117;
        sshConnect();
    }

}

void CGntTest::oldUartdrvFile(QString s)
{
    m_ptimer->stop();
    m_gntJyXl.strCsGc = "文件存在";
    sendGcEvent();
    m_nsshLj = 110;
    sshConnect();
}

void CGntTest::modifyconfig(QString)
{
    int c = 1;
}

void CGntTest::configVerify(QString s)
{
    m_ptimer->stop();
    bool bFind = false;
    QStringList strList = s.split("\n");
    for(int x = 0; x < strList.size(); ++x)
    {
        if(strList[x].trimmed().contains("\"Business Channel Mode(0-IEC104 1-MQTT)\" V_Value=\"1\""))
        {
            bFind = true;
            break;
        }
    }
    if(bFind)
    {
        m_gntJyXl.strCsGc = "确认合格";
        sendGcEvent();
        m_gntJyJg.bCsXlJg = true;
        sendJgEvent();
        check();
    }
    else
    {
        if(m_nLymm < 4)
        {
            m_nsshLj = 122;
            sshConnect();
        }
        else
        {
            m_gntJyXl.strCsGc = "确认不合格";
            sendGcEvent();
            m_gntJyJg.bCsXlJg = false;
            sendJgEvent();
            check();
        }
    }
}

void CGntTest::secufileVerify(QString s)
{
    m_ptimer->stop();
    bool bFind = false;
    QStringList strList = s.split("\n");
    for(int x = 0; x < strList.size(); ++x)
    {
        if(strList[x].trimmed().contains("\"Business Channel Mode(0-IEC104 1-MQTT)\" V_Value=\"0\""))
        {
            bFind = true;
            break;
        }
    }
    if(bFind)
    {
        m_gntJyXl.strCsGc = "确认合格";
        sendGcEvent();
        m_nsshLj = 65;
        sshConnect();

    }
    else
    {
        m_gntJyXl.strCsGc = "确认不合格";
        sendGcEvent();

        m_nsshLj = 63;
        sshConnect();
    }
}

void CGntTest::scuIdQuery(QString s)
{
    QStringList sList = s.split("\n");
    for(int m = 0; m < sList.size(); ++m)
    {
        if(sList[m].contains("devID"))
        {
            QStringList strList= sList[m].split("=");
            m_strdevId = strList[strList.size()-1].trimmed();
            break;
        }
    }

    QDir directory(m_gntParam.other.strZdzsdclj);
    QStringList cerFiles = directory.entryList(QStringList() << "*.cer", QDir::Files);

    bool bFind = false;
    for(int x = 0 ; x < cerFiles.size(); ++x)
    {
        if(cerFiles[x].contains(m_strdevId))
        {
            bFind = true;
            break;
        }
    }

    if(!bFind)
    {
        m_gntJyXl.strCsGc = "确认失败";
        sendGcEvent();
        m_gntJyJg.bCsXlJg = false;
        sendJgEvent();
    }
    else
    {
        m_gntJyXl.strCsGc = "确认成功";
        sendGcEvent();
    }
    check();

}

void CGntTest::md5Verify(QString s)
{
    m_ptimer->stop();
    QStringList strList = s.split(" ");
    if(strList.size() < 2 )
    {
        m_gntJyXl.strCsGc = "hash确认失败";
        sendGcEvent();
        m_gntJyJg.bCsXlJg = false;
        sendJgEvent();
        m_ntimeout = 61;
        m_ptimer->start(1000);
        return;
    }
    m_gntJyXl.strCsGc = "hash读取值：" + strList[0].trimmed() + ", hash值设定值：" + m_gntParam.other.strLyqdHash + ",";
    if(strList[0].trimmed() == m_gntParam.other.strLyqdHash)
    {
        m_gntJyXl.strCsGc += "hash值确认成功";
        sendGcEvent();
        m_gntJyJg.bCsXlJg = true;
        sendJgEvent();
    }
    else
    {
        m_gntJyXl.strCsGc += "hash值确认失败";
        sendGcEvent();
        m_gntJyJg.bCsXlJg = false;
        sendJgEvent();
    }
    m_ntimeout = 61;
    m_ptimer->start(1000);
}

void CGntTest::mvParam()
{
    m_gntJyXl.strCsGc = "备份成功, 开始把/tmp/paramFile.json移动到/data/app/SCMQTTIoT/configFile/路径下";
    sendGcEvent();

    m_nLx = 99998;
    createRemoteProcess("mv /tmp/paramFile.json /data/app/SCMQTTIoT/configFile/paramFile.json");
    m_ntimeout = 9999;
    m_ptimer->start(1000);
}

void CGntTest::mvParam2()
{
    m_gntJyXl.strCsGc = "移动成功, 开始赋予data/app/SCMQTTIoT/configFile/paramFile.json文件775权限";
    sendGcEvent();

    m_nLx = 99999;
    createRemoteProcess("echo 'Zgdky@guest123' | sudo -S chmod 775 /data/app/SCMQTTIoT/configFile/paramFile.json");
    m_ntimeout = 9999;
    m_ptimer->start(1000);
}

void CGntTest::addQx()
{
    m_gntJyXl.strCsGc = "权限赋予成功";
    sendGcEvent();

    m_gntJyXl.strCsGc = "读取确认";
    sendGcEvent();
    m_nLx = 29;
    createRemoteProcess("cat /data/app/SCMQTTIoT/configFile/paramFile.json");
}

void CGntTest::APPitem()
{
    if(m_vtRqName.size() == 0)
    {
        auto iter = m_gntParam.rqApp.mapScuApp.begin();
        while(iter != m_gntParam.rqApp.mapScuApp.end())
        {
            if(!iter->second)
            {
                m_bScuAppChk = false;
                m_gntJyXl.strCsGc = "指定安装APP："+ iter->first + "未安装";
                sendGcEvent();
            }
            ++iter;
        }
        m_gntJyJg.bCsXlJg = m_bScuAppChk;
        sendJgEvent();
        rqappsjdb();
        return;
    }
    m_nsshLj = 5;
    sshConnect();
}

void CGntTest::rqwAppItem()
{
    if(m_vtrqwApp.size() == 0)
    {
        auto iter = m_gntParam.rqApp.mapRqwApp.begin();
        while(iter != m_gntParam.rqApp.mapRqwApp.end())
        {
            if(!iter->second)
            {
                m_gntJyXl.strCsGc = "要求安装的容器外APP：" + iter->first + "未安装";
                sendGcEvent();
                m_bScuRqwAppChk = false;
            }
            ++iter;
        }

        m_gntJyJg.bCsXlJg = m_bScuRqwAppChk;
        sendJgEvent();

        if(!m_bScuRqwAppChk || !m_bScuAppChk)
            checkItem();
        else
            check();
        return;
    }

    m_nsshLj = 7;
    sshConnect();
}

void CGntTest::cs698Item()
{
    if(!g_bTestSign)
    {
        check();
        return;
    }
    if(m_vtCmd.size() == 0)
    {
        if(m_bcheck)
        {
            check();
        }
        else
        {
            checkItem();
        }
        return;
    }

    QThread::sleep(5);

    QString s = m_vtCmd[0];
    m_vtCmd.erase(m_vtCmd.begin());
    m_gntJyJg.strCsXl = m_gntJyXl.strCsXl = s;
    m_gntJyXl.strCsGc = s;
    sendGcEvent();
    char pcFrame[8192] = {0};

    if(s == "公网通信配置")
    {
        QByteArray sa = QByteArray::fromHex("AAAAAAAAAAAA");
        int j = m_698Handle.buildSetRequestGwTxpz(pcFrame, m_gntParam.gwtxpz);
        m_698Handle.packagingFrame698(pcFrame, j,sa.data(), sa.size()-1);
        QByteArray data(pcFrame, j);
        if(m_pSocket->write(data) > 0)
        {
            m_gntJyXl.strCsGc = "成功发送报文：" + data.toHex();
            sendGcEvent();
        }
        else
        {
            m_bcheck = false;
            m_gntJyXl.strCsGc = "发送报文：" + data.toHex() + "失败";
            sendGcEvent();
            cs698Item();
        }
    }
    else if(s == "以太网通信配置")
    {
        QByteArray sa = QByteArray::fromHex("AAAAAAAAAAAA");
        int j = m_698Handle.buildSetRequestYtwTxpz(pcFrame, m_gntParam.ytwtxpz);
        m_698Handle.packagingFrame698(pcFrame, j,sa.data(), sa.size()-1);
        QByteArray data(pcFrame, j);
        if(m_pSocket->write(data) > 0)
        {
            m_gntJyXl.strCsGc = "成功发送报文：" + data.toHex();
            sendGcEvent();
        }
        else
        {
            m_bcheck = false;

            m_gntJyXl.strCsGc = "发送报文：" + data.toHex() + "失败";
            sendGcEvent();
            cs698Item();
        }
    }
    else if(s == "公网主站通信参数")
    {
        QByteArray sa = QByteArray::fromHex("AAAAAAAAAAAA");
        int j = m_698Handle.buildSetRequestTxcs(pcFrame, m_gntParam.gwtxdz);
        m_698Handle.packagingFrame698(pcFrame, j,sa.data(), sa.size()-1);
        QByteArray data(pcFrame, j);
        if(m_pSocket->write(data) > 0)
        {
            m_gntJyXl.strCsGc = "成功发送报文：" + data.toHex();
            sendGcEvent();
        }
        else
        {
            m_bcheck = false;

            m_gntJyXl.strCsGc = "发送报文：" + data.toHex() + "失败";
            sendGcEvent();
            cs698Item();
        }
    }
    else if(s == "以太网主站通信参数")
    {
        QByteArray sa = QByteArray::fromHex("AAAAAAAAAAAA");
        int j = m_698Handle.buildSetRequestTxcsytw(pcFrame, m_gntParam.ytwtxdz);
        m_698Handle.packagingFrame698(pcFrame, j,sa.data(), sa.size()-1);
        QByteArray data(pcFrame, j);
        if(m_pSocket->write(data) > 0)
        {
            m_gntJyXl.strCsGc = "成功发送报文：" + data.toHex();
            sendGcEvent();
        }
        else
        {
            m_bcheck = false;
            m_gntJyXl.strCsGc = "发送报文：" + data.toHex() + "失败";
            sendGcEvent();
            cs698Item();
        }
    }
    else if (s == "终端停/上电事件配置参数")
    {
        QByteArray sa = QByteArray::fromHex("AAAAAAAAAAAA");
        int j = m_698Handle.buildSetRequestTsdpzcs(pcFrame, m_gntParam.tsdpzcs);
        m_698Handle.packagingFrame698(pcFrame, j,sa.data(), sa.size()-1);
        QByteArray data(pcFrame, j);
        if(m_pSocket->write(data) > 0)
        {
            m_gntJyXl.strCsGc = "成功发送报文：" + data.toHex();
            sendGcEvent();
        }
        else
        {
            m_bcheck = false;
            m_gntJyXl.strCsGc = "发送报文：" + data.toHex() + "失败";
            sendGcEvent();
            cs698Item();
        }
    }
    else if(s == "安全模式参数")
    {
        QByteArray sa = QByteArray::fromHex("AAAAAAAAAAAA");
        int j = m_698Handle.buildSetRequestaqms(pcFrame, m_gntParam.aqjmpz);
        m_698Handle.packagingFrame698(pcFrame, j,sa.data(), sa.size()-1);
        QByteArray data(pcFrame, j);
        if(m_pSocket->write(data) > 0)
        {
            m_gntJyXl.strCsGc = "成功发送报文：" + data.toHex();
            sendGcEvent();
        }
        else
        {
            m_bcheck = false;
            m_gntJyXl.strCsGc = "发送报文：" + data.toHex() + "失败";
            sendGcEvent();
            cs698Item();
        }
    }
}

void CGntTest::sjqltcp()
{
    QByteArray byte ;
    if(m_vtCmd.size() == 0)
    {
        if(m_bsjqlcheck)
        {
            check();
        }
        else
        {
            checkItem();
        }
        return;
    }
    QString s = m_vtCmd[0];
    m_vtCmd.erase(m_vtCmd.begin());
    if(s == "电能量清零1")
    {
        m_gntJyJg.strCsXl = m_gntJyXl.strCsXl = "电能量清零1";
        byte = QByteArray::fromHex("68 7D 00 43 45 AA AA AA AA AA AA 00 5C 77 07 01 00 F2 08 CA 00 02 14 06 00 00 00 00 06 00 00 00 00 06 00 00 00 00 06 00 00 00 00 06 00 00 00 00 06 00 00 00 00 06 00 00 00 00 06 00 00 00 00 06 00 00 00 00 06 00 00 00 00 05 00 00 00 00 05 00 00 00 00 05 00 00 00 00 05 00 00 00 00 05 00 00 00 00 05 00 00 00 00 05 00 00 00 00 05 00 00 00 00 05 00 00 00 00 05 00 00 00 00 00 11 AD 16");
        m_gntJyXl.strCsGc = "电能量清零1， 发送报文：" + byte.toHex();
        sendGcEvent();
    }
    else if(s == "电能量清零2")
    {
        m_gntJyJg.strCsXl = m_gntJyXl.strCsXl = "电能量清零2";
        byte = QByteArray::fromHex("68 17 00 43 45 AA AA AA AA AA AA 00 5B 4F 05 01 03 90 00 03 00 00 C0 B6 16");
        m_gntJyXl.strCsGc = "电能量清零2， 发送报文：" + byte.toHex();
        sendGcEvent();
    }
    else if(s == "清空档案")
    {
        m_gntJyJg.strCsXl = m_gntJyXl.strCsXl = "清空档案";
        byte = QByteArray::fromHex("68 18 00 43 45 AA AA AA AA AA AA 00 17 53 07 01 01 60 00 86 00 00 00 19 19 16");
        m_gntJyXl.strCsGc = "清空档案， 发送报文：" + byte.toHex();
        sendGcEvent();
    }
    else if(s == "清空任务")
    {
        m_gntJyJg.strCsXl = m_gntJyXl.strCsXl = "清空任务";
        byte = QByteArray::fromHex("68 18 00 43 45 AA AA AA AA AA AA 00 17 53 07 01 00 60 12 81 00 00 00 25 73 16");
        m_gntJyXl.strCsGc = "清空任务， 发送报文：" + byte.toHex();
        sendGcEvent();
    }
    else if(s == "清空方案")
    {
        m_gntJyJg.strCsXl = m_gntJyXl.strCsXl = "清空方案";
        byte = QByteArray::fromHex("68 18 00 43 45 AA AA AA AA AA AA 00 17 53 07 01 00 60 14 81 00 00 00 BD 48 16");
        m_gntJyXl.strCsGc = "清空方案， 发送报文：" + byte.toHex();
        sendGcEvent();
    }
    m_pSocket->write(byte);
    m_ntimeout = 9999;
    m_ptimer->start(60000);
}

void CGntTest::cczttcp()
{
    QByteArray byte = QByteArray::fromHex("68 17 00 43 45 AA AA AA AA AA AA 00 5B 4F 05 01 04 30 2A 04 00 00 2D 9F 16");
    m_gntJyXl.strCsGc = "发送报文：" + byte.toHex();
    sendGcEvent();
    m_pSocket->write(byte);
    m_ntimeout = 9999;
    m_ptimer->start(20000);
}

void CGntTest::zdcstcp()
{
    m_oad.attr_OI = 0x4001;
    m_oad.attr_ID = 0x02;
    m_oad.attr_index = 0x00;
    char pcFrame[8196] = {0};
    int j = m_698Handle.buildSetRequestApduByOAD(pcFrame, m_oad, 1, m_gntParam.scuParam.strLjdz);
    QByteArray sa = QByteArray::fromHex("AAAAAAAAAAAA");

    m_698Handle.packagingFrame698(pcFrame, j,sa.data(), sa.size()-1);
    QByteArray data(pcFrame, j);
    m_gntJyXl.strCsGc = "设置通信地址， 发送报文：" + data.toHex();
    sendGcEvent();
    m_pSocket->write(data);
}

void CGntTest::jcjltcp()
{
    m_gntJyJg.strCsXl = m_gntJyXl.strCsXl = "";
    switch(m_nJcLx)
    {
    case 0:
        m_gntJyJg.strCsXl += "U=1，I=1，φ=60，H=50";
        m_gntJyXl.strCsXl += "U=1，I=1，φ=60，H=50";
        break;
    case 1:
        m_gntJyJg.strCsXl += "U=0.8，I=1，φ=60，H=50";
        m_gntJyXl.strCsXl += "U=0.8，I=1，φ=60，H=50";
        break;
    case 2:
        m_gntJyJg.strCsXl += "U=0.6，I=1，φ=60，H=50";
        m_gntJyXl.strCsXl += "U=0.6，I=1，φ=60，H=50";
        break;
    case 3:
        m_gntJyJg.strCsXl += "U=1.2，I=1，φ=60，H=50";
        m_gntJyXl.strCsXl += "U=1.2，I=1，φ=60，H=50";
        break;
    case 4:
        m_gntJyJg.strCsXl += "U=1，I=0.9，φ=60，H=50";
        m_gntJyXl.strCsXl += "U=1，I=0.9，φ=60，H=50";
        break;
    case 5:
        m_gntJyJg.strCsXl += "U=1，I=0.6，φ=60，H=50";
        m_gntJyXl.strCsXl += "U=1，I=0.6，φ=60，H=50";
        break;
    case 6:
        m_gntJyJg.strCsXl += "U=1，I=0.2，φ=60，H=50";
        m_gntJyXl.strCsXl += "U=1，I=0.2，φ=60，H=50";
        break;
    case 7:
        m_gntJyJg.strCsXl += "U=1，I=1，φ=45，H=50";
        m_gntJyXl.strCsXl += "U=1，I=1，φ=45，H=50";
        break;
    case 8:
        m_gntJyJg.strCsXl += "U=1，I=0.6，φ=45，H=50";
        m_gntJyXl.strCsXl += "U=1，I=0.6，φ=45，H=50";
        break;
    case 9:
        m_gntJyJg.strCsXl += "U=1，I=0.9，φ=45，H=50";
        m_gntJyXl.strCsXl += "U=1，I=0.9，φ=45，H=50";
        break;
    case 10:
        m_gntJyJg.strCsXl += "U=1，I=0.2，φ=45，H=50";
        m_gntJyXl.strCsXl += "U=1，I=0.2，φ=45，H=50";
        break;
    case 11:
        m_gntJyJg.strCsXl += "U=1.2，I=0.2，φ=45，H=50";
        m_gntJyXl.strCsXl += "U=1.2，I=0.2，φ=45，H=50";
        break;
    case 12:
        m_gntJyJg.strCsXl += "U=0.8，I=0.2，φ=45，H=50";
        m_gntJyXl.strCsXl += "U=0.8，I=0.2，φ=45，H=50";
        break;
    case 13:
        m_gntJyJg.strCsXl += "U=1，I=1，φ=45，H=45";
        m_gntJyXl.strCsXl += "U=1，I=1，φ=45，H=45";
        break;
    case 14:
        m_gntJyJg.strCsXl += "U=0.6，I=1，φ=45，H=45";
        m_gntJyXl.strCsXl += "U=0.6，I=1，φ=45，H=45";
        break;
    case 15:
        m_gntJyJg.strCsXl += "U=0.6，I=1，φ=30，H=45";
        m_gntJyXl.strCsXl += "U=0.6，I=1，φ=30，H=45";
        break;
    case 16:
        m_gntJyJg.strCsXl += "U=0.8，I=1，φ=30，H=45";
        m_gntJyXl.strCsXl += "U=0.8，I=1，φ=30，H=45";
        break;
    case 17:
        m_gntJyJg.strCsXl += "U=0.6，I=0.6，φ=30，H=45";
        m_gntJyXl.strCsXl += "U=0.6，I=0.6，φ=30，H=45";
        break;
    case 18:
        m_gntJyJg.strCsXl += "U=0.8，I=0.6，φ=30，H=45";
        m_gntJyXl.strCsXl += "U=0.8，I=0.6，φ=30，H=45";
        break;
    case 19:
        m_gntJyJg.strCsXl += "U=1，I=0.015，φ=60，H=50";
        m_gntJyXl.strCsXl += "U=1，I=0.015，φ=60，H=50";
        break;
    case 20:
        m_gntJyJg.strCsXl += "U=1，N=1，φ=60，H=50";
        m_gntJyXl.strCsXl += "U=1，N=1，φ=60，H=50";
        break;
    case 21:
        m_gntJyJg.strCsXl += "U=1，N=0.6，φ=60，H=50";
        m_gntJyXl.strCsXl += "U=1，N=0.6，φ=60，H=50";
        break;
    case 22:
        m_gntJyJg.strCsXl += "U=0.8，N=1，φ=60，H=50";
        m_gntJyXl.strCsXl += "U=0.8，N=1，φ=60，H=50";
        break;
    default:
        break;
    }
    QByteArray cmdByte;
    if(m_nTcp == 0)  // 获取电压
    {
        m_gntJyXl.strCsGc = "获取电压， 发送报文：6817004345AAAAAAAAAAAA005B4F0501002000020000E5BE16";
        sendGcEvent();
        cmdByte = QByteArray::fromHex("68 17 00 43 45 AA AA AA AA AA AA 00 5B 4F 05 01 00 20 00 02 00 00 E5 BE 16");
    }
    else if(m_nTcp == 1) // 获取电流
    {
        m_gntJyXl.strCsGc = "获取电流， 发送报文：68 17 00 43 45 AA AA AA AA AA AA 00 5B 4F 05 01 01 20 01 02 00 00 75 A6 16";
        sendGcEvent();
        cmdByte = QByteArray::fromHex("68 17 00 43 45 AA AA AA AA AA AA 00 5B 4F 05 01 01 20 01 02 00 00 75 A6 16");
    }
    else if(m_nTcp == 2) // 获取零线电流
    {
        m_gntJyXl.strCsGc = "获取零线电流，发送报文：68 17 00 43 45 AA AA AA AA AA AA 00 5B 4F 05 01 00 20 01 04 00 00 87 74 16";
        sendGcEvent();
        cmdByte = QByteArray::fromHex("68 17 00 43 45 AA AA AA AA AA AA 00 5B 4F 05 01 00 20 01 04 00 00 87 74 16");
    }
    else if(m_nTcp == 3)
    {
        m_gntJyXl.strCsGc = "获取功率因数， 发送报文：68 17 00 43 45 AA AA AA AA AA AA 00 5B 4F 05 01 00 20 0A 02 00 00 4B 62 16";
        sendGcEvent();
        cmdByte = QByteArray::fromHex("68 17 00 43 45 AA AA AA AA AA AA 00 5B 4F 05 01 00 20 0A 02 00 00 4B 62 16");
    }
    else if (m_nTcp == 4)
    {
        m_gntJyXl.strCsGc = "获取有功功率， 发送报文：68 17 00 43 45 AA AA AA AA AA AA 00 5B 4F 05 01 04 20 04 02 00 00 A5 DC 16";
        sendGcEvent();
        cmdByte = QByteArray::fromHex("68 17 00 43 45 AA AA AA AA AA AA 00 5B 4F 05 01 04 20 04 02 00 00 A5 DC 16");
    }
    else if(m_nTcp == 5)
    {
        m_gntJyXl.strCsGc = "获取无功功率， 发送报文：68 17 00 43 45 AA AA AA AA AA AA 00 5B 4F 05 01 1B 20 05 02 00 00 27 BF 16";
        sendGcEvent();
        cmdByte = QByteArray::fromHex("68 17 00 43 45 AA AA AA AA AA AA 00 5B 4F 05 01 1B 20 05 02 00 00 27 BF 16");
    }
    else if(m_nTcp == 9)  // 终端停上电事件有效标志为无效
    {
        m_gntJyJg.strCsXl = m_gntJyXl.strCsXl = "终端停上电事件有效标识";
        ++m_nLymm;
        m_gntJyXl.strCsGc = "发送报文：68 19 00 43 45 AA AA AA AA AA AA 00 86 06 06 01 01 31 06 09 00 03 00 00 2B AB 16";
        sendGcEvent();
        cmdByte = QByteArray::fromHex("68 19 00 43 45 AA AA AA AA AA AA 00 86 06 06 01 01 31 06 09 00 03 00 00 2B AB 16");
    }
    else if(m_nTcp == 10) // 终端停上电事件有效标志为有效
    {
        m_gntJyJg.strCsXl = m_gntJyXl.strCsXl = "设置终端停上电事件有效标识";
        ++m_nLymm;
        m_gntJyXl.strCsGc = "发送报文：68 19 00 43 45 AA AA AA AA AA AA 00 86 06 06 01 01 31 06 09 00 03 01 00 F3 B2 16";
        sendGcEvent();
        cmdByte = QByteArray::fromHex("68 19 00 43 45 AA AA AA AA AA AA 00 86 06 06 01 01 31 06 09 00 03 01 00 F3 B2 16 ");
    }
    m_pSocket->write(cmdByte);
    m_ntimeout = 9999;
    m_ptimer->start(20000);   // 等待20 秒

}

void CGntTest::rqappsjdb()
{
    // 先获取DeviceManager时间
    m_gntJyXl.strCsXl = m_gntJyJg.strCsXl = "app启动时间";
    m_gntJyXl.strCsGc = "获取DeviceManager时间， 命令:ps -eo pid,lstart,cmd|grep '/opt/om_core/bin/DeviceManager' | grep -v 'grep'| awk -F' ' '{printf$5}'";
    sendGcEvent();

    m_nsshLj = 47;
    sshConnect();
}

void CGntTest::sendGcEvent()
{
    QApplication::postEvent((QObject*)g_pFuncWindow, new CCsXlGcMsg(m_gntJyXl));
}

void CGntTest::sendJgEvent()
{
    QApplication::postEvent((QObject*)g_pFuncWindow, new CCsXlJgMsg(m_gntJyJg));
}

void CGntTest::jcjldy(QByteArray &data)
{
    m_bjccheck = true;
    if(data[21] == '\x0' || data[24] == '\x0')
    {
        m_bcheck = false;
        m_bjccheck = false;
        m_gntJyXl.strCsGc = "电压信息为无效值，失败";
        sendGcEvent();
    }
    else
    {
        bool ok;
        short nVa = data.mid(25, 2).toHex().toUShort(&ok, 16);
        short nVb = data.mid(28, 2).toHex().toUShort(&ok, 16);
        short nVc = data.mid(31, 2).toHex().toUShort(&ok, 16);

        float fVa = (float)qAbs(nVa) / 10;
        float fVb = (float)qAbs(nVb) / 10;
        float fVc = (float)qAbs(nVc) / 10;


        float fwc = 0.f;
        float fwc_2 = 0.f;

        g_mtxMonitor.lock();
        float fBzdyA = g_SMonitor.sU1.toFloat();
        float fBzdyB = g_SMonitor.sU2.toFloat();
        float fBzdyC = g_SMonitor.sU3.toFloat();
        g_mtxMonitor.unlock();
        QThread::sleep(5);
        g_mtxMonitor.lock();
        float fBzdyA_2 = g_SMonitor.sU1.toFloat();
        float fBzdyB_2 = g_SMonitor.sU2.toFloat();
        float fBzdyC_2 = g_SMonitor.sU3.toFloat();
        g_mtxMonitor.unlock();



        // 获取A相电压
        fwc = qAbs(fVa - fBzdyA) / fBzdyA *100;
        fwc_2 = qAbs(fVa - fBzdyA_2) / fBzdyA_2 *100;
        m_gntJyXl.strCsGc = "获取到A相电压：" + QString::number(fVa) + "，输出电压：" + QString::number(fBzdyA) + "，百分比为：" + QString::number(fwc, 'f', 3) + "%";
        if(fwc <= m_gntParam.other.fdywc)
        {
            m_gntJyXl.strCsGc += ", 合格";
        }
        else if(fwc_2 <= m_gntParam.other.fdywc)
        {
            m_gntJyXl.strCsGc = "获取到A相电压：" + QString::number(fVa) + "，输出电压：" + QString::number(fBzdyA_2) + "，百分比为：" + QString::number(fwc_2, 'f', 3) + "%";

            m_gntJyXl.strCsGc += ", 合格";
        }
        else
        {
            m_gntJyXl.strCsGc += ", 不合格";
            m_bcheck = false;
            m_bjccheck = false;
        }
        sendGcEvent();

        fwc = (qAbs(fVb - fBzdyB) / fBzdyB) *100;
        fwc_2 = (qAbs(fVb - fBzdyB_2) / fBzdyB_2) *100;
        m_gntJyXl.strCsGc = "获取到B相电压：" + QString::number(fVb) + "，输出电压：" + QString::number(fBzdyB) + "，百分比为：" + QString::number(fwc, 'f', 3)  + "%";
        if(fwc <= m_gntParam.other.fdywc)
        {
            m_gntJyXl.strCsGc += ", 合格";
        }
        else if(fwc_2 <= m_gntParam.other.fdywc)
        {
            m_gntJyXl.strCsGc = "获取到B相电压：" + QString::number(fVb) + "，输出电压：" + QString::number(fBzdyB_2) + "，百分比为：" + QString::number(fwc_2, 'f', 3)  + "%";

            m_gntJyXl.strCsGc += ", 合格";
        }
        else
        {
            m_gntJyXl.strCsGc += ", 不合格";
            m_bcheck = false;
            m_bjccheck = false;
        }
        sendGcEvent();

        fwc = qAbs(fVc - fBzdyC) / fBzdyC *100;
        fwc_2 = qAbs(fVc - fBzdyC_2) / fBzdyC_2 *100;
        m_gntJyXl.strCsGc = "获取到C相电压：" + QString::number(fVc) + "，输出电压：" + QString::number(fBzdyC) + "，百分比为：" + QString::number(fwc, 'f', 3) + "%";
        if(fwc <= m_gntParam.other.fdywc)
        {
            m_gntJyXl.strCsGc += ", 合格";
        }
        else if(fwc_2 <= m_gntParam.other.fdywc)
        {
            m_gntJyXl.strCsGc = "获取到C相电压：" + QString::number(fVc) + "，输出电压：" + QString::number(fBzdyC_2) + "，百分比为：" + QString::number(fwc_2, 'f', 3) + "%";

            m_gntJyXl.strCsGc += ", 合格";
        }
        else
        {
            m_gntJyXl.strCsGc += ", 不合格";
            m_bcheck = false;
            m_bjccheck = false;
        }
        sendGcEvent();
    }

    m_nTcp = 1;
    dk9001(QString("no dk"));
}

void CGntTest::jcjldl(QByteArray &data)
{
    if(data[21] == '\x0' || data[24] == '\x0')
    {
        m_bcheck = false;
        m_bjccheck = false;
        m_gntJyXl.strCsGc = "电流信息为无效值，失败";
        sendGcEvent();
    }
    else
    {

        bool ok;
        int nIa = data.mid(25, 4).toHex().toUInt(&ok, 16);
        int nIb = data.mid(30, 4).toHex().toUInt(&ok, 16);
        int nIc = data.mid(35, 4).toHex().toUInt(&ok, 16);

        float fIa =  (float)qAbs(nIa) / 1000;
        float fIb =  (float)qAbs(nIb) / 1000;
        float fIc =  (float)qAbs(nIc) / 1000;

        if(m_nJcLx == 19)
        {
            if(fIa == 0 || fIb == 0 || fIc == 0)
            {
                m_bcheck = false;
                m_bjccheck = false;
                m_gntJyXl.strCsGc = "A相电流或者B相电流或者C相电流为空, 不合格";
                sendGcEvent();
            }
            else
            {
                m_gntJyXl.strCsGc = "获取到A相电流：" + QString::number(fIa) + "合格";
                sendGcEvent();
                m_gntJyXl.strCsGc = "获取到B相电流：" + QString::number(fIb) + "合格";
                sendGcEvent();
                m_gntJyXl.strCsGc = "获取到C相电流：" + QString::number(fIc) + "合格";
                sendGcEvent();
            }

            m_gntJyJg.bCsXlJg = m_bjccheck;
            sendJgEvent();

            ++m_nJcLx;
            QApplication::postEvent((QObject*)g_pFuncWindow, new CCsJcjlMsg(m_nJcLx));

            while(!g_bJcjl[m_nJcLx])
            {
                if(!g_bTestSign)
                {
                    check();
                    return;
                }
                QThread::msleep(500);
            }

            m_nTcp = 0;
            dk9001(QString("no dk"));

            return;
        }
        else
        {
            float fwc = 0.f;
            float fwc_2 = 0.f;
            g_mtxMonitor.lock();
            float fBzdlA = g_SMonitor.sI1.toFloat();
            float fBzdlB = g_SMonitor.sI2.toFloat();
            float fBzdlC = g_SMonitor.sI3.toFloat();
            g_mtxMonitor.unlock();
            QThread::sleep(5);
            g_mtxMonitor.lock();
            float fBzdlA_2 = g_SMonitor.sI1.toFloat();
            float fBzdlB_2 = g_SMonitor.sI2.toFloat();
            float fBzdlC_2 = g_SMonitor.sI3.toFloat();
            g_mtxMonitor.unlock();


            fwc = qAbs(fIa - fBzdlA) / fBzdlA *100;
            fwc_2 = qAbs(fIa - fBzdlA_2) / fBzdlA_2 *100;
            m_gntJyXl.strCsGc = "获取到A相电流：" + QString::number(fIa) + "，输出电流：" + QString::number(fBzdlA) + "，百分比为：" + QString::number(fwc, 'f', 3) + "%";
            if(fwc <= m_gntParam.other.fdlwc)
            {
                m_gntJyXl.strCsGc += ", 合格";
            }
            else if(fwc_2 <= m_gntParam.other.fdlwc)
            {
                m_gntJyXl.strCsGc += ", 合格";
                m_gntJyXl.strCsGc = "获取到A相电流：" + QString::number(fIa) + "，输出电流：" + QString::number(fBzdlA_2) + "，百分比为：" + QString::number(fwc_2, 'f', 3) + "%";
            }
            else
            {
                m_gntJyXl.strCsGc += ", 不合格";
                m_bcheck = false;
                m_bjccheck = false;
            }
            sendGcEvent();

            fwc = qAbs(fIb - fBzdlB) / fBzdlB *100;
            fwc_2 = qAbs(fIb - fBzdlB_2) / fBzdlB_2 *100;
            m_gntJyXl.strCsGc = "获取到B相电流：" + QString::number(fIb) + "，输出电流：" + QString::number(fBzdlB) + "，百分比为：" + QString::number(fwc, 'f', 3) + "%";
            if(fwc <= m_gntParam.other.fdlwc)
            {
                m_gntJyXl.strCsGc += ", 合格";
            }
            else if(fwc_2 <= m_gntParam.other.fdlwc)
            {
                m_gntJyXl.strCsGc = "获取到B相电流：" + QString::number(fIb) + "，输出电流：" + QString::number(fBzdlB_2) + "，百分比为：" + QString::number(fwc_2, 'f', 3) + "%";
                m_gntJyXl.strCsGc += ", 合格";
            }
            else
            {
                m_gntJyXl.strCsGc += ", 不合格";
                m_bcheck = false;
                m_bjccheck = false;
            }
            sendGcEvent();
            if(m_nJcLx > 19)
            {
                fBzdlC = 0;
            }

            if(fBzdlC == 0)
            {
                m_gntJyXl.strCsGc = "获取到C相电流：" + QString::number(fIc) + "，输出电流：" + QString::number(fBzdlC) ;
                if(fIc == 0)
                {
                    m_gntJyXl.strCsGc += ", 合格";
                }
                else
                {
                    m_gntJyXl.strCsGc += ", 不合格";
                    m_bcheck = false;
                    m_bjccheck = false;
                }
            }
            else
            {
                fwc = qAbs(fIc - fBzdlC) / fBzdlC *100;
                fwc_2 = qAbs(fIc - fBzdlC_2) / fBzdlC_2 *100;
                m_gntJyXl.strCsGc = "获取到C相电流：" + QString::number(fIc) + "，输出电流：" + QString::number(fBzdlC) + "，百分比为：" + QString::number(fwc, 'f', 3) + "%";
                if(fwc <= m_gntParam.other.fdlwc)
                {
                    m_gntJyXl.strCsGc += ", 合格";
                }
                else  if(fwc_2 <= m_gntParam.other.fdlwc)
                {
                    m_gntJyXl.strCsGc = "获取到C相电流：" + QString::number(fIc) + "，输出电流：" + QString::number(fBzdlC_2) + "，百分比为：" + QString::number(fwc_2, 'f', 3) + "%";

                    m_gntJyXl.strCsGc += ", 合格";
                }
                else
                {
                    m_gntJyXl.strCsGc += ", 不合格";
                    m_bcheck = false;
                    m_bjccheck = false;
                }
            }
            sendGcEvent();
        }
    }


    if(m_nJcLx > 19)
    {
        m_nTcp = 2;
    }
    else
    {
        m_nTcp = 3;
    }
    dk9001(QString("no dk"));
}

void CGntTest::jcjllxdl(QByteArray &data)
{
    if(data[21] == '\x0' || data[22] == '\x0')
    {
        m_bcheck = false;
        m_bjccheck = false;
        m_gntJyXl.strCsGc = "零线电流为无效值，失败";
    }
    else
    {
        bool ok;
        int nLI = data.mid(23, 4).toHex().toUInt(&ok, 16);

        float fLI =  (float)qAbs(nLI) / 1000;

        float fwc = 0.f;
        float fwc_2 = 0.f;
        g_mtxMonitor.lock();
        float fBzdl = g_SMonitor.sI3.toFloat();
        g_mtxMonitor.unlock();

        QThread::sleep(5);
        g_mtxMonitor.lock();
        float fBzdl_2 = g_SMonitor.sI3.toFloat();
        g_mtxMonitor.unlock();

        if(fBzdl == 0)
        {
            m_gntJyXl.strCsGc = "获取到零线电流：" + QString::number(fLI) + "，输出电流：" + QString::number(fBzdl);
            if(fLI == 0)
            {
                m_gntJyXl.strCsGc += ", 合格";
            }
            else
            {
                m_gntJyXl.strCsGc += ", 不合格";
                m_bcheck = false;
                m_bjccheck = false;
            }
        }
        else
        {
            fwc = qAbs(fLI - fBzdl) / fBzdl *100;
            fwc_2 = qAbs(fLI - fBzdl_2) / fBzdl_2 *100;
            m_gntJyXl.strCsGc = "获取到零线电流：" + QString::number(fLI) + "，输出电流：" + QString::number(fBzdl) + "，百分比为：" + QString::number(fwc, 'f', 3) + "%";
            if(fwc <= m_gntParam.other.fdlwc)
            {
                m_gntJyXl.strCsGc += ", 合格";
            }
            else if(fwc_2 <= m_gntParam.other.fdlwc)
            {
                m_gntJyXl.strCsGc = "获取到零线电流：" + QString::number(fLI) + "，输出电流：" + QString::number(fBzdl_2) + "，百分比为：" + QString::number(fwc_2, 'f', 3) + "%";

                m_gntJyXl.strCsGc += ", 合格";
            }
            else
            {
                m_gntJyXl.strCsGc += ", 不合格";
                m_bcheck = false;
                m_bjccheck = false;
            }
        }
    }
    sendGcEvent();


    if(m_nJcLx > 21)
    {
        m_gntJyJg.bCsXlJg = m_bjccheck;
        sendJgEvent();

        QApplication::postEvent((QObject*)g_pFuncWindow, new CCsJcjlMsg(99));
        while(!g_bJcjl[99])
        {
            if(!g_bTestSign)
            {
                check();
                return;
            }
            QThread::msleep(500);
        }
        m_nLymm = 0;
        m_nsshLj = 129;
        sshConnect();
    }
    else if(m_nJcLx > 19)
    {

        m_gntJyJg.bCsXlJg = m_bjccheck;
        sendJgEvent();

        ++m_nJcLx;

        QApplication::postEvent((QObject*)g_pFuncWindow, new CCsJcjlMsg(m_nJcLx));

        while(!g_bJcjl[m_nJcLx])
        {
            if(!g_bTestSign)
            {
                check();
                return;
            }
            QThread::msleep(500);
        }
        m_nTcp = 0;
        dk9001(QString("no dk"));
    }
    else
    {
        m_nTcp = 3;
        dk9001(QString("no dk"));
    }
}

void CGntTest::jcjlglys(QByteArray &data)
{
    if(data[21] == '\x0' || data[24] == '\x0')
    {
        m_bcheck = false;
        m_bjccheck = false;
        m_gntJyXl.strCsGc = "功率因数为无效值，失败";
        sendGcEvent();
    }
    else
    {
        bool ok;
        short nglysz = data.mid(25,2).toHex().toUShort(&ok, 16);
        short nglysa = data.mid(28, 2).toHex().toUShort(&ok, 16);
        short nglysb = data.mid(31, 2).toHex().toUShort(&ok, 16);
        short nglysac = data.mid(34, 2).toHex().toUShort(&ok, 16);

        float fglysz = (float)qAbs(nglysz) / 1000;
        float fglysa = (float)qAbs(nglysa) / 1000;
        float fglysb = (float)qAbs(nglysb) / 1000;
        float fglysc = (float)qAbs(nglysac) / 1000;

        float fwc = 0.f;
        float fwc_2 = 0.f;

        g_mtxMonitor.lock();
        float fglysA = g_SMonitor.sX1.toFloat();
        float fglysB = g_SMonitor.sX2.toFloat();
        float fglysC = g_SMonitor.sX3.toFloat();
        g_mtxMonitor.unlock();
        QThread::sleep(5);
        g_mtxMonitor.lock();
        float fglysA_2 = g_SMonitor.sX1.toFloat();
        float fglysB_2 = g_SMonitor.sX2.toFloat();
        float fglysC_2 = g_SMonitor.sX3.toFloat();
        g_mtxMonitor.unlock();

        float fglys = (fglysA + fglysB + fglysC) / 3;
        float fglys_2 = (fglysA_2 + fglysB_2 + fglysC_2) / 3;

        fwc = qAbs(fglysz - fglys) / fglys *100;
        fwc_2 = qAbs(fglysz - fglys_2) / fglys_2 *100;
        m_gntJyXl.strCsGc = "获取到总功率因数：" + QString::number(fglysz) + "，输出总功率因数：" + QString::number(fglys) + "，百分比为：" + QString::number(fwc, 'f', 3) + "%";


        if(fwc <= m_gntParam.other.fglyswc)
        {
            m_gntJyXl.strCsGc += ", 合格";
        }
        else if(fwc_2 <= m_gntParam.other.fglyswc)
        {
            m_gntJyXl.strCsGc = "获取到总功率因数：" + QString::number(fglysz) + "，输出总功率因数：" + QString::number(fglys_2) + "，百分比为：" + QString::number(fwc_2, 'f', 3) + "%";

            m_gntJyXl.strCsGc += ", 合格";
        }
        else
        {
            m_gntJyXl.strCsGc += ", 不合格";
            m_bcheck = false;
            m_bjccheck = false;
        }
        sendGcEvent();


        fwc = qAbs(fglysa - fglysA) / fglysA *100;
        fwc_2 = qAbs(fglysa - fglysA_2) / fglysA_2 *100;
        m_gntJyXl.strCsGc = "获取到A相功率因数：" + QString::number(fglysa) + "，输出功率因数：" + QString::number(fglysA) + "，百分比为：" + QString::number(fwc, 'f', 3) + "%";

        if(fwc <= m_gntParam.other.fglyswc)
        {
            m_gntJyXl.strCsGc += ", 合格";
        }
        else if(fwc_2 <= m_gntParam.other.fglyswc)
        {
            m_gntJyXl.strCsGc = "获取到A相功率因数：" + QString::number(fglysa) + "，输出功率因数：" + QString::number(fglysA_2) + "，百分比为：" + QString::number(fwc_2, 'f', 3) + "%";

            m_gntJyXl.strCsGc += ", 合格";
        }
        else
        {
            m_gntJyXl.strCsGc += ", 不合格";
            m_bcheck = false;
            m_bjccheck = false;
        }

        sendGcEvent();

        fwc = qAbs(fglysb - fglysB) / fglysB *100;
        fwc_2 = qAbs(fglysb - fglysB_2) / fglysB_2 *100;
        m_gntJyXl.strCsGc = "获取到B相功率因数：" + QString::number(fglysb) + "，输出功率因数：" + QString::number(fglysB) + "，百分比为：" + QString::number(fwc, 'f', 3) + "%";

        if(fwc <= m_gntParam.other.fglyswc)
        {
            m_gntJyXl.strCsGc += ", 合格";
        }
        else if(fwc_2 <= m_gntParam.other.fglyswc)
        {
            m_gntJyXl.strCsGc = "获取到B相功率因数：" + QString::number(fglysb) + "，输出功率因数：" + QString::number(fglysB_2) + "，百分比为：" + QString::number(fwc_2, 'f', 3) + "%";

            m_gntJyXl.strCsGc += ", 合格";
        }
        else
        {
            m_gntJyXl.strCsGc += ", 不合格";
            m_bcheck = false;
            m_bjccheck = false;
        }

        sendGcEvent();

        fwc = qAbs(fglysc - fglysC) / fglysC *100;
        fwc_2 = qAbs(fglysc - fglysC_2) / fglysC_2 *100;
        m_gntJyXl.strCsGc = "获取到C相功率因数：" + QString::number(fglysc) + "，输出功率因数：" + QString::number(fglysC) + "，百分比为：" + QString::number(fwc, 'f', 3) + "%";

        if(fwc <= m_gntParam.other.fglyswc)
        {
            m_gntJyXl.strCsGc += ", 合格";
        }
        else if(fwc_2 <= m_gntParam.other.fglyswc)
        {
            m_gntJyXl.strCsGc = "获取到C相功率因数：" + QString::number(fglysc) + "，输出功率因数：" + QString::number(fglysC_2) + "，百分比为：" + QString::number(fwc_2, 'f', 3) + "%";

            m_gntJyXl.strCsGc += ", 合格";
        }
        else
        {
            m_gntJyXl.strCsGc += ", 不合格";
            m_bcheck = false;
            m_bjccheck = false;
        }
        sendGcEvent();
    }


    m_nTcp = 4;
    dk9001(QString("no dk"));
}

void CGntTest::jcjlyg(QByteArray &data)
{
    if(data[21] == '\x0' || data[24] == '\x0')
    {
        m_bcheck = false;
        m_bjccheck = false;
        m_gntJyXl.strCsGc = "有功功率为无效值，失败";
        sendGcEvent();
    }
    else
    {
        bool ok;
        int nygz = data.mid(25,4).toHex().toInt(&ok, 16);
        int nyga = data.mid(30, 4).toHex().toInt(&ok, 16);
        int nygb = data.mid(35, 4).toHex().toInt(&ok, 16);
        int nygc = data.mid(40, 4).toHex().toInt(&ok, 16);

        float fygz = (float)qAbs(nygz) / 10;
        float fyga = (float)qAbs(nyga) / 10;
        float fygb = (float)qAbs(nygb) / 10;
        float fygc = (float)qAbs(nygc) / 10;


        g_mtxMonitor.lock();
        float fttyga = g_SMonitor.sP1.toFloat();
        float fttygb = g_SMonitor.sP2.toFloat();
        float fttygc = g_SMonitor.sP3.toFloat();
        g_mtxMonitor.unlock();
        QThread::sleep(5);

        g_mtxMonitor.lock();
        float fttyga_2 = g_SMonitor.sP1.toFloat();
        float fttygb_2 = g_SMonitor.sP2.toFloat();
        float fttygc_2 = g_SMonitor.sP3.toFloat();
        g_mtxMonitor.unlock();

        float fttyg = fttyga + fttygb + fttygc;
        float fttyg_2 = fttyga_2 + fttygb_2 + fttygc_2;

        float fwc = qAbs(fttyg - fygz) / fttyg *100;
        float fwc_2 = qAbs(fttyg_2 - fygz) / fttyg_2 *100;
        m_gntJyXl.strCsGc = "获取到有功总：" + QString::number(fygz) + "，输出有功总：" + QString::number(fttyg) + "，百分比为：" + QString::number(fwc, 'f', 3) + "%";

        if(fwc <= m_gntParam.other.fygglwc)
        {
            m_gntJyXl.strCsGc += ", 合格";
        }
        else if(fwc_2 <= m_gntParam.other.fygglwc)
        {
            m_gntJyXl.strCsGc = "获取到有功总：" + QString::number(fygz) + "，输出有功总：" + QString::number(fttyg_2) + "，百分比为：" + QString::number(fwc_2, 'f', 3) + "%";
            m_gntJyXl.strCsGc += ", 合格";
        }
        else
        {
            m_gntJyXl.strCsGc += ", 不合格";
            m_bcheck = false;
            m_bjccheck = false;
        }

        sendGcEvent();

        fwc = qAbs(fyga - fttyga) / fttyga *100;
        fwc = qAbs(fyga - fttyga_2) / fttyga_2 *100;
        m_gntJyXl.strCsGc = "获取到A相有功：" + QString::number(fyga) + "，输出A相有功：" + QString::number(fttyga) + "，百分比为：" + QString::number(fwc, 'f', 3) + "%";

        if(fwc <= m_gntParam.other.fygglwc)
        {
            m_gntJyXl.strCsGc += ", 合格";
        }
        else if(fwc_2 <= m_gntParam.other.fygglwc)
        {
            m_gntJyXl.strCsGc = "获取到A相有功：" + QString::number(fyga) + "，输出A相有功：" + QString::number(fttyga_2) + "，百分比为：" + QString::number(fwc_2, 'f', 3) + "%";
            m_gntJyXl.strCsGc += ", 合格";
        }
        else
        {
            m_gntJyXl.strCsGc += ", 不合格";
            m_bcheck = false;
            m_bjccheck = false;
        }

        sendGcEvent();

        fwc = qAbs(fygb - fttygb) / fttygb *100;
        fwc_2 = qAbs(fygb - fttygb_2) / fttygb_2 *100;
        m_gntJyXl.strCsGc = "获取到B相有功：" + QString::number(fygb) + "，输出B相有功：" + QString::number(fttygb) + "，百分比为：" + QString::number(fwc, 'f', 3) + "%";

        if(fwc <= m_gntParam.other.fygglwc)
        {
            m_gntJyXl.strCsGc += ", 合格";
        }
        else if(fwc_2 <= m_gntParam.other.fygglwc)
        {
            m_gntJyXl.strCsGc = "获取到B相有功：" + QString::number(fygb) + "，输出B相有功：" + QString::number(fttygb_2) + "，百分比为：" + QString::number(fwc_2, 'f', 3) + "%";
            m_gntJyXl.strCsGc += ", 合格";
        }
        else
        {
            m_gntJyXl.strCsGc += ", 不合格";
            m_bcheck = false;
            m_bjccheck = false;
        }

        sendGcEvent();

        fwc = qAbs(fygc - fttygc) / fttygc *100;
        fwc_2 = qAbs(fygc - fttygc_2) / fttygc_2 *100;
        m_gntJyXl.strCsGc = "获取到C相有功：" + QString::number(fygc) + "，输出C相有功：" + QString::number(fttygc) + "，百分比为：" + QString::number(fwc, 'f', 3) + "%";

        if(fwc <= m_gntParam.other.fygglwc)
        {
            m_gntJyXl.strCsGc += ", 合格";
        }
        else if(fwc_2 <= m_gntParam.other.fygglwc)
        {
            m_gntJyXl.strCsGc = "获取到C相有功：" + QString::number(fygc) + "，输出C相有功：" + QString::number(fttygc_2) + "，百分比为：" + QString::number(fwc_2, 'f', 3) + "%";
            m_gntJyXl.strCsGc += ", 合格";
        }
        else
        {
            m_gntJyXl.strCsGc += ", 不合格";
            m_bcheck = false;
            m_bjccheck = false;
        }
        sendGcEvent();
    }


    m_nTcp = 5;
    dk9001(QString("no dk"));
}

void CGntTest::jcjlwg(QByteArray &data)
{
    if(data[21] == '\x0' || data[24] == '\x0')
    {
        m_bcheck = false;
        m_bjccheck = false;
        m_gntJyXl.strCsGc = "无功功率为无效值，失败";
        sendGcEvent();
    }
    else
    {
        bool ok;
        int nwgz = data.mid(25,4).toHex().toInt(&ok, 16);
        int nwga = data.mid(30, 4).toHex().toInt(&ok, 16);
        int nwgb = data.mid(35, 4).toHex().toInt(&ok, 16);
        int nwgc = data.mid(40, 4).toHex().toInt(&ok, 16);

        float fwgz = (float)qAbs(nwgz) / 10;
        float fwga = (float)qAbs(nwga) / 10;
        float fwgb = (float)qAbs(nwgb) / 10;
        float fwgc = (float)qAbs(nwgc) / 10;


        g_mtxMonitor.lock();
        float fttwga = g_SMonitor.sQ1.toFloat();
        float fttwgb = g_SMonitor.sQ2.toFloat();
        float fttwgc = g_SMonitor.sQ3.toFloat();
        g_mtxMonitor.unlock();

        QThread::sleep(5);

        g_mtxMonitor.lock();
        float fttwga_2 = g_SMonitor.sQ1.toFloat();
        float fttwgb_2 = g_SMonitor.sQ2.toFloat();
        float fttwgc_2 = g_SMonitor.sQ3.toFloat();
        g_mtxMonitor.unlock();

        float fttwgz = fttwga + fttwgb + fttwgc;
        float fttwgz_2 = fttwga_2 + fttwgb_2 + fttwgc_2;



        float fwc = qAbs(fwgz - fttwgz) / (fttwgz) *100;
        float fwc_2= qAbs(fwgz - fttwgz_2) / (fttwgz_2) *100;
        m_gntJyXl.strCsGc = "获取到无功总：" + QString::number(fwgz) + "，输出无功总：" + QString::number(fttwgz) + "，百分比为：" + QString::number(fwc, 'f', 3) + "%";

        if(fwc <= m_gntParam.other.fwgglwc)
        {
            m_gntJyXl.strCsGc += ", 合格";
        }
        else if(fwc_2 <= m_gntParam.other.fwgglwc)
        {
            m_gntJyXl.strCsGc = "获取到无功总：" + QString::number(fwgz) + "，输出无功总：" + QString::number(fttwgz_2) + "，百分比为：" + QString::number(fwc_2, 'f', 3) + "%";

            m_gntJyXl.strCsGc += ", 合格";
        }
        else
        {
            m_gntJyXl.strCsGc += ", 不合格";
            m_bcheck = false;
            m_bjccheck = false;
        }

        sendGcEvent();


        fwc = qAbs(fttwga - fwga) / fttwga *100;
        fwc_2 = qAbs(fttwga_2 - fwga) / fttwga_2 *100;
        m_gntJyXl.strCsGc = "获取到A相无功：" + QString::number(fwga) + "，输出A相无功：" + QString::number(fttwga) + "，百分比为：" + QString::number(fwc, 'f', 3) + "%";

        if(fwc <= m_gntParam.other.fwgglwc)
        {
            m_gntJyXl.strCsGc += ", 合格";
        }
        else if(fwc_2 <= m_gntParam.other.fwgglwc)
        {
            m_gntJyXl.strCsGc = "获取到A相无功：" + QString::number(fwga) + "，输出A相无功：" + QString::number(fttwga_2) + "，百分比为：" + QString::number(fwc_2, 'f', 3) + "%";

            m_gntJyXl.strCsGc += ", 合格";
        }
        else
        {
            m_gntJyXl.strCsGc += ", 不合格";
            m_bcheck = false;
            m_bjccheck = false;
        }

        sendGcEvent();

        fwc = qAbs(fttwgb - fwgb) / fttwgb *100;
        fwc_2 = qAbs(fttwgb_2 - fwgb) / fttwgb_2 *100;
        m_gntJyXl.strCsGc = "获取到B相无功：" + QString::number(fwgb) + "，输出B相无功：" + QString::number(fttwgb) + "，百分比为：" + QString::number(fwc, 'f', 3) + "%";

        if(fwc <= m_gntParam.other.fwgglwc)
        {
            m_gntJyXl.strCsGc += ", 合格";
        }
        else  if(fwc_2 <= m_gntParam.other.fwgglwc)
        {
            m_gntJyXl.strCsGc = "获取到B相无功：" + QString::number(fwgb) + "，输出B相无功：" + QString::number(fttwgb_2) + "，百分比为：" + QString::number(fwc_2, 'f', 3) + "%";

            m_gntJyXl.strCsGc += ", 合格";
        }
        else
        {
            m_gntJyXl.strCsGc += ", 不合格";
            m_bcheck = false;
            m_bjccheck = false;
        }

        sendGcEvent();

        fwc = qAbs(fttwgc - fwgc) / fttwgc *100;
        fwc_2 = qAbs(fttwgc_2 - fwgc) / fttwgc_2 *100;

        m_gntJyXl.strCsGc = "获取到C相无功：" + QString::number(fwgc) + "，输出C相无功：" + QString::number(fttwgc) + "，百分比为：" + QString::number(fwc, 'f', 3) + "%";

        if(fwc <= m_gntParam.other.fwgglwc)
        {
            m_gntJyXl.strCsGc += ", 合格";
        }
        else if(fwc_2 <= m_gntParam.other.fwgglwc)
        {
            m_gntJyXl.strCsGc = "获取到C相无功：" + QString::number(fwgc) + "，输出C相无功：" + QString::number(fttwgc_2) + "，百分比为：" + QString::number(fwc_2, 'f', 3) + "%";

            m_gntJyXl.strCsGc += ", 合格";
        }
        else
        {
            m_gntJyXl.strCsGc += ", 不合格";
            m_bcheck = false;
            m_bjccheck = false;
        }

        sendGcEvent();
    }

    m_gntJyJg.bCsXlJg = m_bjccheck;
    sendJgEvent();

    ++m_nJcLx;
    QApplication::postEvent((QObject*)g_pFuncWindow, new CCsJcjlMsg(m_nJcLx));

    while(!g_bJcjl[m_nJcLx])
    {
        if(!g_bTestSign)
        {
            check();
            return;
        }
        QThread::msleep(500);
    }

    m_nTcp = 0;
    dk9001(QString("no dk"));
}

void CGntTest::zdyxbs(QByteArray &data)
{
    if(m_nTcp == 9)
    {
        if(data[21] != '\x0')
        {
            if(m_nLymm < 5)
            {
                m_gntJyXl.strCsGc = "设置失败， 重新设置";
                sendGcEvent();
                m_nsshLj = 128;
                sshConnect();
            }
            else
            {
                m_gntJyXl.strCsGc = "设置失败";
                sendGcEvent();
                m_gntJyJg.bCsXlJg = false;
                sendJgEvent();
                check();
            }
        }
        else
        {
            m_gntJyXl.strCsGc = "设置成功";
            sendGcEvent();
            m_gntJyJg.bCsXlJg = true;
            sendJgEvent();

            m_ntimeout = 70;
            m_ptimer->start(1000);

        }
    }
    else if (m_nTcp == 10)
    {
        if(data[21] != '\x0')
        {
            if(m_nLymm < 5)
            {
                m_gntJyXl.strCsGc = "设置失败， 重新设置";
                sendGcEvent();
                m_nsshLj = 129;
                sshConnect();
            }
            else
            {
                m_gntJyXl.strCsGc = "设置失败";
                sendGcEvent();
                m_gntJyJg.bCsXlJg = false;
                sendJgEvent();
                check();
            }
        }
        else
        {
            m_gntJyXl.strCsGc = "设置成功";
            sendGcEvent();
            m_gntJyJg.bCsXlJg = true;
            sendJgEvent();
            check();
        }
    }
}

void CGntTest::psdev4(QString & byte)
{
    m_ptimer->stop();
    m_strKill = "echo 'Zgdky@guest123' | sudo -S kill  ";
    m_strKill += byte.trimmed();

    m_nsshLj = 66;
    sshConnect();
}

void CGntTest::createJm(QString & byte)
{
    m_ptimer->stop();
    if(!byte.contains("export cer file success"))
    {
        m_gntJyXl.strCsGc = "生成证书文件失败";
        sendGcEvent();
        m_gntJyJg.bCsXlJg = false;
        sendJgEvent();

        m_nsshLj = 73;
        sshConnect();
        return;
    }

    m_nsshLj = 74;
    sshConnect();
}

void CGntTest::fileExist(QString & byte)
{
    if(byte.contains("No such file or directory"))
    {
        m_gntJyXl.strCsGc = "文件夹：/usr/sec-app/package/work不存在";
        sendGcEvent();
        m_gntJyJg.bCsXlJg = false;
        sendJgEvent();

        check();
    }
    else
    {
        m_nsshLj = 68;
        sshConnect();
    }
}

void CGntTest::psdev(QString &byte)
{
    m_ptimer->stop();
    m_strKill = "echo 'Zgdky@guest123' | sudo -S kill  ";
    m_strKill += byte.trimmed();

    m_nsshLj = 76;
    sshConnect();
}

void CGntTest::psdev2(QString &byte)
{
    m_ptimer->stop();

    m_strKill = "echo 'Zgdky@guest123' | sudo -S kill  ";
    m_strKill += byte.trimmed();

    m_nsshLj = 80;
    sshConnect();
}

void CGntTest::psdev3(QString & byte)
{
    m_strKill = "echo 'Zgdky@guest123' | sudo -S kill  ";
    m_strKill += byte.trimmed();


    m_nsshLj = 84;
    sshConnect();
}

void CGntTest::mqttIoTFile(QString & param)
{
    QJsonParseError error;
    QJsonDocument doc = QJsonDocument::fromJson(param.toUtf8());
    if(doc.isEmpty() || !doc.isObject())
    {
        m_gntJyXl.strCsGc = "文件/data/app/SCMQTTIoT/configFile/paramFile.json解析失败";
        sendGcEvent();
        m_gntJyJg.bCsXlJg = false;
        sendJgEvent();
        check();
        return;
    }

    m_gntJyXl.strCsGc = "文件/data/app/SCMQTTIoT/configFile/paramFile.json读取成功";
    sendGcEvent();


    QJsonObject obj = doc.object();
    obj["HOST"] = m_gntParam.other.strMqttIotIp;
    obj["PORT"] = 1883;
    obj["USERNAME"] = m_strEsn;
    obj["PASSWORD"] =  "GWSX@" + calculateSHA256(m_strEsn).left(20);
    QJsonDocument mqttdoc(obj);
    QByteArray cData = mqttdoc.toJson();

    QString s = QDir::currentPath();
    int nindex = s.lastIndexOf("/");
    QString s2 = s.left(nindex+1);

    m_strDestFile = s2 + "download/" ;

    QDir dir;
    if(!dir.exists(m_strDestFile))
    {
        dir.mkdir(m_strDestFile);
    }
    m_strDestFile +=  QString::number(m_gntParam.nBw);
    m_strDestFile += "/";
    if(!dir.exists(m_strDestFile))
    {
        dir.mkdir(m_strDestFile);
    }

    // 删除所有文件
    QDir dir2(m_strDestFile);
    QStringList dirList = dir2.entryList();
    for(int m = 0; m < dirList.size(); ++m)
    {
        if(dirList[m] == "." || dirList[m] == "..")
            continue;
       dir2.remove(dirList[m]);
    }

    m_strDestFile +=  "paramFile.json";

    m_gntJyXl.strCsGc = "开始生成配置文件, 生成路径：";
    m_gntJyXl.strCsGc += m_strDestFile;
    sendGcEvent();

    QFile file(m_strDestFile);
    if(!file.open(QFile::WriteOnly))
    {
        m_gntJyXl.strCsGc = "生成配置文件失败";
        sendGcEvent();
        m_gntJyJg.bCsXlJg = false;
        sendJgEvent();

        checkItem();
        return;
    }
    file.write(cData);
    file.close();

    m_gntJyXl.strCsGc = "文件生成成功";
    sendGcEvent();

    m_gntJyXl.strCsGc = "开始上传文件, 上传路径/data/app/SCMQTTIoT/configFile/paramFile.json";
    sendGcEvent();

    m_nsshLj = 43;
    sshConnect();

}

void CGntTest::sshConnect()
{
    ++m_nReSsh;
    QSsh::SshConnectionParameters param;
    param.host = m_gntParam.scuParam.strIp;
    param.port = 8888;
    param.timeout = 5;
    param.userName = "sysadm";
    param.password = "Zgdky@guest123";
    param.authenticationType = QSsh::SshConnectionParameters::AuthenticationTypePassword;
    param.options = 0;
    if(m_pSsh != nullptr)
    {
        m_pSsh->disconnect();
        m_pSsh->disconnectFromHost();
        delete  m_pSsh;
        m_pSsh = nullptr;
    }

    m_pSsh = new QSsh::SshConnection(param);
    connect(m_pSsh, SIGNAL(connected()), this, SLOT(on_ssh_connected()));
    connect(m_pSsh, SIGNAL(disconnected()), this, SLOT(on_ssh_error()));
    connect(m_pSsh, SIGNAL(error(QSsh::SshError)), this, SLOT(on_ssh_error()));
    m_pSsh->connectToHost();
}

void CGntTest::sshClose()
{
    if(m_pSsh != nullptr)
    {
        m_pSsh->disconnect();
        m_pSsh->disconnectFromHost();
        delete  m_pSsh;
        m_pSsh = nullptr;
    }
}

bool CGntTest::serialConnect(QString sPortName)
{
    if( m_pRs485 != nullptr && m_pRs485->isOpen())
    {
        m_pRs485->disconnect();
        m_pRs485->close();
    }
    QThread::sleep(2);
    if(m_pRs485 == nullptr)
    {
        m_pRs485 = new QSerialPort(this);
    }

    m_pRs485->setPortName(sPortName.toUpper());
    m_pRs485->setBaudRate(9600);

    m_pRs485->setDataBits(QSerialPort::Data8);

    m_pRs485->setStopBits(QSerialPort::OneStop);

    m_pRs485->setParity(QSerialPort::NoParity);

    m_pRs485->setFlowControl(QSerialPort::NoFlowControl);
    m_pRs485->setReadBufferSize(0);
    if(!m_pRs485->open(QIODevice::ReadWrite))
    {
        return false;
    }
    else
    {
        connect(m_pRs485, SIGNAL(readyRead()), this, SLOT(on_rs485_readyRead()));
        return true;
    }
}

QString CGntTest::calculateSHA256(const QString &input)
{
    // 将字符串转换为 UTF-8 编码的 QByteArray
    QByteArray data = input.toUtf8();

    // 计算 SHA256 哈希
    QByteArray hash = QCryptographicHash::hash(data, QCryptographicHash::Sha256);

    // 将二进制哈希转换为十六进制字符串
    return QString(hash.toHex());
}

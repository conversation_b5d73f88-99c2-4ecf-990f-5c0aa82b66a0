#ifndef CSCANCODE_H
#define CSCANCODE_H

#include <QWidget>
#include<QMap>
#include<QTableWidgetItem>
#include"csetconfigure.h"

namespace Ui {
class cScanCode;
}

class cScanCode : public QWidget
{
    Q_OBJECT

public:
    explicit cScanCode(QWidget *parent = nullptr);
    ~cScanCode();


private slots:

    void on_CreatePcBt_clicked();

    void on_okBt_clicked();


    void on_tableWidget_itemDoubleClicked(QTableWidgetItem *item);

private:
    Ui::cScanCode *ui;
    cSetConfigure *m_set;






};

#endif // CSCANCODE_H

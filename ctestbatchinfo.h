#ifndef CTESTBATCHINFO_H
#define CTESTBATCHINFO_H

#include <QWidget>
#include"database.h"

namespace Ui {
class cTestBatchInfo;
}

class cTestBatchInfo : public QWidget
{
    Q_OBJECT

public:
    explicit cTestBatchInfo(QWidget *parent = nullptr);
    ~cTestBatchInfo();

private slots:
    void on_comboBox_currentIndexChanged(const QString &arg1);

    void on_exportBtn_clicked();

private:
    Ui::cTestBatchInfo *ui;
    Database *m_database;
    void UpdateTable(QStringList sl);
    void exportFile(QString s);
    QString m_batch;
};

#endif // CTESTBATCHINFO_H

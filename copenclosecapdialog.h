﻿#ifndef COPENCLOSECAPDIALOG_H
#define COPENCLOSECAPDIALOG_H

#include <QDialog>

namespace Ui {
class COpenCloseCapDialog;
}

class COpenCloseCapDialog : public QDialog
{
    Q_OBJECT

public:
    explicit COpenCloseCapDialog(QWidget *parent = nullptr);
    ~COpenCloseCapDialog();

private slots:
    void on_pushButton_clicked();
Q_SIGNALS:
    void on_allFinish();

private:
    Ui::COpenCloseCapDialog *ui;
};

#endif // COPENCLOSECAPDIALOG_H

﻿#ifndef CGNTTESTWIDGET_H
#define CGNTTESTWIDGET_H
#include <QTableWidget>
#include <QWidget>
#include <QCheckBox>
#include "msk_global.h"
#include <QMessageBox>
#include "cgntscuinfodialog.h"
#include "cgnttestprocess.h"
#include "ccomportdialog.h"
#include "comequipment.h"
#include "cswdialog.h"
#include "cdbthread.h"
#include <queue>
#include <QProcess>
#include "tcpserver.h"
#include "copenclosecapdialog.h"
#include "cfinishdialog.h"
class CVerdictQueryWg;
namespace Ui {
class CGntTestWidget;
}


enum EGntTest
{
    EGntTest_BW,        // 表位
    EGntTest_Zddz,      // 逻辑地址
    EGntTest_ID,        // ID
    EGntTest_ESN,        // esn
    EGntTest_CS,        // 厂商
    EGntTest_XH,         // 型号
    EGntTest_IP,         // IP
    EGntTest_YJBB,      // 硬件版本
    EGntTest_SCRQ,      // 生产日期
    EGntTest_ZT,        // 状态
    EGntTest_CHECKING,  // 当前测试项
    EGntTest_JL,        // 结论
    EGntTest_FA,        // 方案
    EGntTest_PC,        // 批次
    EGntTest_FAID,        // 方案id
    EGntTest_PCID,        // 批次id
    EGntTest_OID,        // oid
    EGntTest_MAX

};

class CGntTestWidget : public QWidget
{
    Q_OBJECT

public:
    explicit CGntTestWidget(QWidget *parent = nullptr);
    ~CGntTestWidget();
    void init(std::map<int, SSCUParam>&);
    void pushGc(CCsXlGcMsg *);
    void pushJg(CCsXlJgMsg *);
    void pushJs(CCsJsMsg *);
    void pushYx(CCsYxMsg *);
    void setExport();

    void setNormal(int, int);
    void setSshOff(CSSHOFF *);

    void setYlmz(CCsLyMsg &);
    void setComPort(CComPortDialog *);
    void setQuery(CVerdictQueryWg *);
    void setHbdy();
    void setSW();
    void setJcjl(int nLx, bool badd = true);
    void setTtKz(int nTtkz);
    void setYgmc(CCsYgmcMsg *pMsg);
    void setWgmc(CCsWgmcMsg *pMsg);
    void setMmc(CCsmmcMsg *pMsg);
    void setYx(int num);
    void setRtc(int num);
    bool threadCloseZt();           // 子线程关闭状态

private slots:
    void on_table_customContextMenuRequested(const QPoint &);       // 右键
    void on_table_itemClicked(QTableWidgetItem *);                  // 单击
    void on_cdDLTW_itemClicked(QTableWidgetItem *);

    void on_readData(QByteArray);

    void on_scuXq_trigger();                // SCU详情
    void on_testBegintBtn_clicked();        // 开始测试
    void on_testEndBtn_clicked();           // 结束测试
    void on_sdBtn_clicked();                // 上电
    void on_ddBtn_clicked();                // 断电

    void on_SWOK();
    void on_timer();

    void on_monitor();
    void on_jctimer();
    void on_tcpservertimer();

    void on_openclosecap_allFinish();

private:
    void gntJyXlGc(CCsXlGcMsg *);
    bool gntJxXlJg(CCsXlJgMsg *);
    void setJsXc(CCsJsMsg *);

    void tableInit();
    void csDLResultTWinit();                // 测试大类初始化
    void csXlResultTWinit();                // 测试小类初始化

    void readCfgBeforeCheck();              // 校验之间读取配置

    void comeEquipmentInit();

    bool saveHtmlToWord(int m, QString &);

    bool isProcessRunning(const QString &processName);

    bool killProcess(const QString &processName);

    QString leaveThreeDecPlace(QString);


    void createReport(int m);

private:
    std::map<int, SSCUParam> m_mapScuParam;
    std::map<QString, qint16> m_mapSshOff;      // 异常情况关闭的ssh
    std::vector<CGntTestProcess*> m_vtTestProcess;

    SGntParam   m_gntParam;

    std::map<int, std::map<QString, SGntJyParam> > m_mapGntJyParam;
    std::map<int, QString> m_mapTypeCcom;
    std::map<int, QString> m_mapRs485;

    int m_nXcNum;           // 开始检测的线程个数
    int m_nQuitXcNum;       // 退出的线程个数
    int m_nQuitNum;         // 退出
    int m_nExportNum;       // 导出数量
    int m_nNeedExportNum;       // 需要导出的数量
    int m_nBwCount;
    int m_nHbdyNum;
    int m_nSWNum;
    int m_nJcjlNum;
    int m_njcjlhfNum ;
    int m_nLymz;
    int m_nCurBW;
    int m_nCurDl;
    int m_nYgmcNum;
    int m_nWgmcNum;
    int m_nmmcNum;
    int m_nyxNum;
    int m_nJcExit;
    int m_nJcLx;
    int m_nRtcNum;
    int m_nCertExportNum;
    int m_nCertExportFinishNum;
    int m_nKhgNum;
    int m_nScuIpModify;
    int m_nstartExport;
    int m_nstartImport;
    int m_nlxdydl;
    int m_nlxdydl_2;
    int m_nLytxchkNum;
    int m_nsbdschkNum;

    bool m_bFirstSd;

    bool m_bHidden;

    bool m_bYx;
    int m_nBw;

    QString m_strCheckItem;

private:
    Ui::CGntTestWidget *ui;
    CGntScuInfoDialog  *m_pGntScuInfo;

    CComPortDialog * m_pComPort;

    COMEquipment::EquipmentNz2230 *m_pEquipmentsjj;
    COMEquipment::EquipmentWc    *m_pEquipmentwc;

    CSWDialog *m_pSW;
    CVerdictQueryWg *m_pQueryRs;

    CDBThread  m_dbThread;

    std::queue<CCsXlGcMsg>m_queueGc;
    std::queue<CCsXlJgMsg>m_queueJg;
    std::queue<CCsJsMsg>m_queueJs;
    std::queue<CCsJsMsg>m_queueJsMsg;

    std::queue<CCsYxMsg>m_queueYx;

    QTimer m_timer;
    QTimer m_monitor;
    QTimer m_jctimer;
    QTimer m_tcpServertimer;

    QProcess m_pProcess;

    TcpServer *m_ptcpServer;


    COpenCloseCapDialog *m_pOpenCloseCap;


    CFinishDialog *m_pFinishDialog;


};

#endif // CGNTTESTWIDGET_H

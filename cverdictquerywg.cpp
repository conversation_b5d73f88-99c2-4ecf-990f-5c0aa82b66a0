﻿#include "cverdictquerywg.h"
#include "ui_cverdictquerywg.h"
#include <QDateTime>
#include <QSettings>
#include <QTextCodec>
#include <QDebug>
#include "msk_global.h"
#include <QMessageBox>
#include "cgnttestwidget.h"

CVerdictQueryWg::CVerdictQueryWg(QWidget *parent) :
    QWidget(parent),
    ui(new Ui::CVerdictQueryWg)
{
    ui->setupUi(this);
    connect(ui->faCB, SIGNAL(currentTextChanged(const QString &)), this, SLOT(on_fa_currentTextChanged(const QString &)));

    ui->gcTE->setReadOnly(true);
    csDLTWInit();
    csXLTWInit();
}

CVerdictQueryWg::~CVerdictQueryWg()
{
    delete ui;
}

void CVerdictQueryWg::init(CGntTestWidget *pGntTest)
{
    m_pGntTest = pGntTest;

    QDateTime dt = QDateTime::currentDateTime();
    ui->beginDT->setDateTime(dt.addDays(-1));
    ui->endDT->setDateTime(dt);

    QSettings  settings(strScuOutAutoCheckIni, QSettings::IniFormat);
    settings.setIniCodec(QTextCodec::codecForName("UTF-8"));

    QStringList faList;
    settings.beginGroup("FA");
    QStringList sList = settings.allKeys();
    for (int m = 0;  m < sList.size(); ++m)
    {
        if(sList[m] == "FAXH" || sList[m] == "FAID")
        {
            continue;
        }
        faList << settings.value(sList[m]).toString();
    }
    settings.endGroup();
    ui->faCB->clear();
    ui->faCB->addItems(faList);

}

void CVerdictQueryWg::setJlcx(std::vector<SDLQuery> vtdl )
{
    ui->csdlTW->setRowCount(vtdl.size());

    for (int m = 0 ; m < vtdl.size(); ++m)
    {
        SDLQuery &dl = vtdl[m];
        QTableWidgetItem *pitem = new QTableWidgetItem();
        pitem->setText(dl.strRq);
        ui->csdlTW->setItem(m, EDL_RQ, pitem);

        pitem = new QTableWidgetItem();
        pitem->setText(dl.strFA);
        ui->csdlTW->setItem(m, EDL_FA, pitem);

        pitem = new QTableWidgetItem();
        pitem->setText(dl.strPC);
        ui->csdlTW->setItem(m, EDL_PC, pitem);

        pitem = new QTableWidgetItem();
        pitem->setText(dl.strEsn);
        ui->csdlTW->setItem(m, EDL_ESN, pitem);

        pitem = new QTableWidgetItem();
        pitem->setText(dl.strDevID);
        ui->csdlTW->setItem(m, EDL_DEVID, pitem);

        pitem = new QTableWidgetItem();
        pitem->setText(dl.strLjdz);
        ui->csdlTW->setItem(m, EDL_LJDZ, pitem);

        pitem = new QTableWidgetItem();
        pitem->setText(dl.CHECKRESULT);
        if(dl.CHECKRESULT == "不合格")
        {
            pitem->setTextColor(QColor(255,0,0));
        }
        ui->csdlTW->setItem(m, EDL_JL, pitem);

        pitem = new QTableWidgetItem();
        pitem->setText(dl.USRPASSWD);
        ui->csdlTW->setItem(m, EDL_YHMMM, pitem);

        pitem = new QTableWidgetItem();
        pitem->setText(dl.SYSTEMPARAM);
        ui->csdlTW->setItem(m, EDL_XTBD, pitem);

        pitem = new QTableWidgetItem();
        pitem->setText(dl.strSCURQ);
        ui->csdlTW->setItem(m, EDL_SCURQ, pitem);

        pitem = new QTableWidgetItem();
        pitem->setText(dl.strApp);
        ui->csdlTW->setItem(m, EDL_APP, pitem);

        pitem = new QTableWidgetItem();
        pitem->setText(dl.SCUPARAM);
        ui->csdlTW->setItem(m, EDL_ZDCS, pitem);

        pitem = new QTableWidgetItem();
        pitem->setText(dl.IOTPARAM);
        ui->csdlTW->setItem(m, EDL_MQTTIOT, pitem);

        pitem = new QTableWidgetItem();
        pitem->setText(dl.PARAM698);
        ui->csdlTW->setItem(m, EDL_PARAM698, pitem);

        pitem = new QTableWidgetItem();
        pitem->setText(dl.SCUTIMING);
        ui->csdlTW->setItem(m, EDL_SBDS, pitem);

        pitem = new QTableWidgetItem();
        pitem->setText(dl.GORGE);
        ui->csdlTW->setItem(m, EDL_CKTX, pitem);

        pitem = new QTableWidgetItem();
        pitem->setText(dl.YXDPI);
        ui->csdlTW->setItem(m, EDL_YXFBL, pitem);

        pitem = new QTableWidgetItem();
        pitem->setText(dl.TGWGMMC);
        ui->csdlTW->setItem(m, EDL_YWGMMC, pitem);

        pitem = new QTableWidgetItem();
        pitem->setText(dl.MEASURE);
        ui->csdlTW->setItem(m, EDL_JCJL, pitem);

        pitem = new QTableWidgetItem();
        pitem->setText(dl.BLUETOOTH);
        ui->csdlTW->setItem(m, EDL_LYTX, pitem);

        pitem = new QTableWidgetItem();
        pitem->setText(dl.HPLC4G);
        ui->csdlTW->setItem(m, EDL_HPLC, pitem);

        pitem = new QTableWidgetItem();
        pitem->setText(dl.DATACLEAR);
        ui->csdlTW->setItem(m, EDL_SJQL, pitem);

        pitem = new QTableWidgetItem();
        pitem->setText(dl.SWTEST);
        ui->csdlTW->setItem(m, EDL_SW, pitem);

        pitem = new QTableWidgetItem();
        pitem->setText(dl.strHLXJ);
        ui->csdlTW->setItem(m, EDL_HLXJ, pitem);

        pitem = new QTableWidgetItem();
        pitem->setText(dl.strCCZT);
        ui->csdlTW->setItem(m, EDL_CCZT, pitem);

        pitem = new QTableWidgetItem();
        pitem->setText(dl.PORTS);
        ui->csdlTW->setItem(m, EDL_DKJC, pitem);

        pitem = new QTableWidgetItem();
        pitem->setText(dl.RESERVEPOWER);
        ui->csdlTW->setItem(m, EDL_HBDY, pitem);

        pitem = new QTableWidgetItem();
        pitem->setText(QString::number(dl.nOID));
        ui->csdlTW->setItem(m, EDL_OID, pitem);

        pitem = new QTableWidgetItem();
        pitem->setText(dl.strIp);
        ui->csdlTW->setItem(m, EDL_IP, pitem);

        pitem = new QTableWidgetItem();
        pitem->setText(dl.strcleardata);
        ui->csdlTW->setItem(m, EDL_CLEARNR, pitem);

        pitem = new QTableWidgetItem();
        pitem->setText(dl.strZdIp);
        ui->csdlTW->setItem(m, EDL_ZDIP, pitem);

        pitem = new QTableWidgetItem();
        pitem->setText(dl.strExPort);
        ui->csdlTW->setItem(m, EDL_EXPORT, pitem);

        pitem = new QTableWidgetItem();
        pitem->setText(dl.strImPort);
        ui->csdlTW->setItem(m, EDL_IMPORT, pitem);

        pitem = new QTableWidgetItem();
        pitem->setText(dl.strLxDyDl);
        ui->csdlTW->setItem(m, EDL_LXDYDL, pitem);
    }

    ui->csdlTW->resizeColumnsToContents();
}

void CVerdictQueryWg::setXlcx(std::vector<SXLQuery> vtxl)
{
    ui->csxlTW->setRowCount(vtxl.size());
    for (int m = 0 ; m < vtxl.size(); ++m)
    {
        SXLQuery &xl = vtxl[m];
        QTableWidgetItem *pitem = new QTableWidgetItem();
        pitem->setText(QString::number(xl.noid));
        ui->csxlTW->setItem(m, EXL_OID, pitem);

        pitem = new QTableWidgetItem();
        pitem->setText(xl.strDL);
        ui->csxlTW->setItem(m, EXL_DL, pitem);

        pitem = new QTableWidgetItem();
        pitem->setText(xl.strXL);
        ui->csxlTW->setItem(m, EXL_XL, pitem);

        pitem = new QTableWidgetItem();
        pitem->setText(xl.strRes);
        ui->csxlTW->setItem(m, EXL_RES, pitem);
    }
}

void CVerdictQueryWg::setGc(QString  gc)
{
    ui->gcTE->setText(gc);
}

bool CVerdictQueryWg::isQuery()
{
    if(m_resultQuery.isQuery())
        return true;
    return false;
}

void CVerdictQueryWg::on_checkBox_toggled(bool checked)
{
    ui->idLE->setEnabled(checked);
}

void CVerdictQueryWg::on_fa_currentTextChanged(const QString &s)
{
    QStringList sList;
    QStringList pcList;
    pcList << "全部";
    QSettings  settings(strScuOutAutoCheckIni, QSettings::IniFormat);
    settings.setIniCodec(QTextCodec::codecForName("UTF-8"));
    if(s == "全部")
    {
        sList = settings.childGroups();
        for (int m = 0; m < sList.size(); ++m)
        {
            if(!sList[m].startsWith("PC_"))
                continue;
            settings.beginGroup(sList[m]);
            QStringList strList = settings.allKeys();
            for (int n = 0; n < strList.size(); ++n)
            {
                pcList << settings.value(strList[n]).toString() ;
            }
            settings.endGroup();
        }
        ui->pcCB->clear();
        ui->pcCB->addItems(pcList);
    }
    else
    {
        int nfaid = 0;
        settings.beginGroup("FA");
        sList = settings.allKeys();
        for (int m = 0; m < sList.size(); ++m)
        {
            if(sList[m] == "FAXH" || sList[m] == "FAID")
            {
                continue;
            }
            if(settings.value(sList[m]).toString() == s)
            {
                nfaid = sList[m].toInt();
                break;
            }
        }
        settings.endGroup();

        settings.beginGroup("PC_" + QString::number(nfaid));
        sList = settings.allKeys();
        for (int m = 0; m < sList.size(); ++m)
        {
            pcList << settings.value(sList[m]).toString();
        }
        settings.endGroup();
        ui->pcCB->clear();
        ui->pcCB->addItems(pcList);
    }
}

void CVerdictQueryWg::on_csDL_itemClicked(QTableWidgetItem *pitem)
{
    if(m_resultQuery.isQuery())
    {
        QMessageBox::about(this, "查询", "正在执行查询任务， 请稍后再查");
        return;
    }

    if(m_pGntTest != nullptr && !m_pGntTest->threadCloseZt())
    {
        QMessageBox::about(this, "查询", "正在校验中，禁止查询");
        return;
    }

    int nRows = pitem->row();
    quint64 noid = ui->csdlTW->item(nRows, EDL_OID)->text().toULongLong();

    ui->csxlTW->clearContents();
    ui->csxlTW->setRowCount(0);
    ui->gcTE->clear();
    m_resultQuery.queryXl(noid);
}

void CVerdictQueryWg::on_csXL_itemClicked(QTableWidgetItem *pitem)
{
    if(m_resultQuery.isQuery())
    {
        QMessageBox::about(this, "查询", "正在执行查询任务， 请稍后再查");
        return;
    }
    if(m_pGntTest != nullptr && !m_pGntTest->threadCloseZt())
    {
        QMessageBox::about(this, "查询", "正在校验中，禁止查询");
        return;
    }
    int nRows = pitem->row();
    ui->gcTE->clear();
    SGntJyXlGc gc;
    gc.oid = ui->csxlTW->item(nRows, EXL_OID)->text().toULongLong();
    gc.strCsDl = ui->csxlTW->item(nRows, EXL_DL)->text();
    gc.strCsXl = ui->csxlTW->item(nRows, EXL_XL)->text();
    m_resultQuery.queryGc(gc);
}

// 查询按钮
void CVerdictQueryWg::on_queryBtn_clicked()
{
    if(m_pGntTest != nullptr && !m_pGntTest->threadCloseZt())
    {
        QMessageBox::about(this, "查询", "正在校验中，禁止查询");
        return;
    }
    if(m_resultQuery.isQuery())
    {
        QMessageBox::about(this, "查询", "正在执行查询任务， 请稍后再查");
        return;
    }
    SDLQueryParam param;
    param.strFA = ui->faCB->currentText();
    param.strPC = ui->pcCB->currentText();
    param.bID = ui->checkBox->isChecked();
    param.strID = ui->idLE->text();
    param.beginDT = ui->beginDT->dateTime();
    param.endDT = ui->endDT->dateTime();

    ui->gcTE->clear();
    ui->csdlTW->clearContents();
    ui->csdlTW->setRowCount(0);
    ui->csxlTW->clearContents();
    ui->csxlTW->setRowCount(0);

    m_resultQuery.queryDl(param);
    if(!m_resultQuery.isRunning())
    {
        m_resultQuery.start();
    }
}

void CVerdictQueryWg::csDLTWInit()
{
    ui->csdlTW->setEditTriggers(QTableView::NoEditTriggers);
    ui->csdlTW->setSelectionBehavior(QAbstractItemView::SelectRows);
    ui->csdlTW->setSelectionMode(QAbstractItemView::SingleSelection);
    ui->csdlTW->setAlternatingRowColors(true);
    ui->csdlTW->setColumnCount(EDL_MAX);


    QTableWidgetItem *pitem = new QTableWidgetItem();
    pitem->setText("日期");
    ui->csdlTW->setHorizontalHeaderItem(EDL_RQ, pitem);

    pitem = new QTableWidgetItem();
    pitem->setText("方案");
    ui->csdlTW->setHorizontalHeaderItem(EDL_FA, pitem);

    pitem = new QTableWidgetItem();
    pitem->setText("批次");
    ui->csdlTW->setHorizontalHeaderItem(EDL_PC, pitem);

    pitem = new QTableWidgetItem();
    pitem->setText("ESN");
    ui->csdlTW->setHorizontalHeaderItem(EDL_ESN, pitem);

    pitem = new QTableWidgetItem();
    pitem->setText("ID");
    ui->csdlTW->setHorizontalHeaderItem(EDL_DEVID, pitem);

    pitem = new QTableWidgetItem();
    pitem->setText("逻辑地址");
    ui->csdlTW->setHorizontalHeaderItem(EDL_LJDZ, pitem);

    pitem = new QTableWidgetItem();
    pitem->setText("总体结论");
    ui->csdlTW->setHorizontalHeaderItem(EDL_JL, pitem);

    pitem = new QTableWidgetItem();
    pitem->setText("用户名密码");
    ui->csdlTW->setHorizontalHeaderItem(EDL_YHMMM, pitem);

    pitem = new QTableWidgetItem();
    pitem->setText("清除多余文件");
    ui->csdlTW->setHorizontalHeaderItem(EDL_CLEARNR, pitem);

    pitem = new QTableWidgetItem();
    pitem->setText("系统和补丁版本");
    ui->csdlTW->setHorizontalHeaderItem(EDL_XTBD, pitem);

    pitem = new QTableWidgetItem();
    pitem->setText("容器");
    ui->csdlTW->setHorizontalHeaderItem(EDL_SCURQ, pitem);

    pitem = new QTableWidgetItem();
    pitem->setText("APP运行状态");
    ui->csdlTW->setHorizontalHeaderItem(EDL_APP, pitem);

    pitem = new QTableWidgetItem();
    pitem->setText("终端参数");
    ui->csdlTW->setHorizontalHeaderItem(EDL_ZDCS, pitem);

    pitem = new QTableWidgetItem();
    pitem->setText("MQTTIOT");
    ui->csdlTW->setHorizontalHeaderItem(EDL_MQTTIOT, pitem);

    pitem = new QTableWidgetItem();
    pitem->setText("698参数配置");
    ui->csdlTW->setHorizontalHeaderItem(EDL_PARAM698, pitem);

    pitem = new QTableWidgetItem();
    pitem->setText("设备对时");
    ui->csdlTW->setHorizontalHeaderItem(EDL_SBDS, pitem);

    pitem = new QTableWidgetItem();
    pitem->setText("串口通信");
    ui->csdlTW->setHorizontalHeaderItem(EDL_CKTX, pitem);

    pitem = new QTableWidgetItem();
    pitem->setText("遥信分辨率");
    ui->csdlTW->setHorizontalHeaderItem(EDL_YXFBL, pitem);

    pitem = new QTableWidgetItem();
    pitem->setText("有功、无功、秒脉冲");
    ui->csdlTW->setHorizontalHeaderItem(EDL_YWGMMC, pitem);

    pitem = new QTableWidgetItem();
    pitem->setText("交采计量");
    ui->csdlTW->setHorizontalHeaderItem(EDL_JCJL, pitem);

    pitem = new QTableWidgetItem();
    pitem->setText("蓝牙通信");
    ui->csdlTW->setHorizontalHeaderItem(EDL_LYTX, pitem);

    pitem = new QTableWidgetItem();
    pitem->setText("HPLC/4G");
    ui->csdlTW->setHorizontalHeaderItem(EDL_HPLC, pitem);

    pitem = new QTableWidgetItem();
    pitem->setText("数据清零");
    ui->csdlTW->setHorizontalHeaderItem(EDL_SJQL, pitem);

    pitem = new QTableWidgetItem();
    pitem->setText("SW1/2");
    ui->csdlTW->setHorizontalHeaderItem(EDL_SW, pitem);

    pitem = new QTableWidgetItem();
    pitem->setText("回路巡检");
    ui->csdlTW->setHorizontalHeaderItem(EDL_HLXJ, pitem);

    pitem = new QTableWidgetItem();
    pitem->setText("磁场状态");
    ui->csdlTW->setHorizontalHeaderItem(EDL_CCZT, pitem);

    pitem = new QTableWidgetItem();
    pitem->setText("端口检测");
    ui->csdlTW->setHorizontalHeaderItem(EDL_DKJC, pitem);

    pitem = new QTableWidgetItem();
    pitem->setText("后备电源");
    ui->csdlTW->setHorizontalHeaderItem(EDL_HBDY, pitem);

    pitem = new QTableWidgetItem();
    pitem->setText("OID");
    ui->csdlTW->setHorizontalHeaderItem(EDL_OID, pitem);

    pitem = new QTableWidgetItem();
    pitem->setText("IP");
    ui->csdlTW->setHorizontalHeaderItem(EDL_IP, pitem);

    pitem = new QTableWidgetItem();
    pitem->setText("参数设置");
    ui->csdlTW->setHorizontalHeaderItem(EDL_ZDIP, pitem);

    pitem = new QTableWidgetItem();
    pitem->setText("证书导出");
    ui->csdlTW->setHorizontalHeaderItem(EDL_EXPORT, pitem);

    pitem = new QTableWidgetItem();
    pitem->setText("证书导入");
    ui->csdlTW->setHorizontalHeaderItem(EDL_IMPORT, pitem);

    pitem = new QTableWidgetItem();
    pitem->setText("零序");
    ui->csdlTW->setHorizontalHeaderItem(EDL_LXDYDL, pitem);

    ui->csdlTW->hideColumn(EDL_OID);
    connect(ui->csdlTW, SIGNAL( itemDoubleClicked(QTableWidgetItem *)), this, SLOT(on_csDL_itemClicked(QTableWidgetItem *)));
}

void CVerdictQueryWg::csXLTWInit()
{
    ui->csxlTW->setEditTriggers(QTableView::NoEditTriggers);
    ui->csxlTW->setSelectionBehavior(QAbstractItemView::SelectRows);
    ui->csxlTW->setSelectionMode(QAbstractItemView::SingleSelection);
    ui->csxlTW->setAlternatingRowColors(true);
    ui->csxlTW->setColumnCount(EXL_MAX);

    QTableWidgetItem *pitem = new QTableWidgetItem();
    pitem->setText("测试大类");
    ui->csxlTW->setHorizontalHeaderItem(EXL_DL, pitem);

    pitem = new QTableWidgetItem();
    pitem->setText("测试小类");
    ui->csxlTW->setHorizontalHeaderItem(EXL_XL, pitem);

    pitem = new QTableWidgetItem();
    pitem->setText("结论");
    ui->csxlTW->setHorizontalHeaderItem(EXL_RES, pitem);

    pitem = new QTableWidgetItem();
    pitem->setText("OID");
    ui->csxlTW->setHorizontalHeaderItem(EXL_OID, pitem);
    ui->csxlTW->hideColumn(EXL_OID);

    connect(ui->csxlTW, SIGNAL( itemDoubleClicked(QTableWidgetItem *)), this, SLOT(on_csXL_itemClicked(QTableWidgetItem *)));
}
